{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\guideList\\\\PopupList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext } from \"react\";\nimport { Dialog, DialogContent, TextField, InputAdornment, IconButton, Tab, Tabs, Tooltip, DialogTitle, DialogContentText, DialogActions, Button, Typography } from \"@mui/material\";\nimport { DataGrid } from \"@mui/x-data-grid\";\nimport SearchIcon from \"@mui/icons-material/Search\";\nimport ClearIcon from \"@mui/icons-material/Clear\";\nimport { getAllGuides, DeleteGuideByGuideId } from \"../../../services/GuideListServices\";\nimport { ListEditIcon, CopyListIcon, DeleteIconList, NoData } from \"../../../assets/icons/icons\";\nimport DeleteOutlineOutlinedIcon from \"@mui/icons-material/DeleteOutlineOutlined\";\nimport \"./GuideMenuOptions.css\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport CloneInteractionDialog from \"./CloneGuidePopUp\";\nimport { AccountContext } from \"../../login/AccountContext\";\nimport { useSnackbar } from \"./SnackbarContext\";\nimport { formatDateTime } from \"../../guideSetting/guideList/TimeZoneConversion\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport useUserSession from \"../../../store/userSession\";\nimport { useTranslation } from 'react-i18next';\nimport useInfoStore from \"../../../store/UserInfoStore\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet editedguide;\nconst PopupModal = ({\n  Open,\n  onClose,\n  title,\n  searchText,\n  onAddClick,\n  isAIGuidePersisted,\n  setIsAIGuidePersisted\n}) => {\n  _s();\n  const {\n    setCurrentGuideId,\n    currentGuideId\n  } = useUserSession(state => state);\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    setBannerPopup,\n    setOpenTooltip,\n    setElementSelected,\n    setBannerButtonSelected,\n    selectedTemplateTour,\n    isUnSavedChanges,\n    setIsUnSavedChanges,\n    openWarning,\n    setOpenWarning,\n    setEditClicked,\n    setActiveMenu,\n    setSearchText\n  } = useDrawerStore(state => state);\n  const [activeTab, setActiveTab] = useState(0);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [filteredData, setFilteredData] = useState([]);\n  const [isCloneDialogOpen, setIsCloneDialogOpen] = useState(false);\n  const [cloneAnnouncementName, setCloneAnnouncementName] = useState(null);\n  const [guideIdToDelete, setGuideIdToDelete] = useState(null);\n  const [GuidenametoDelete, setGuideNametoDelete] = useState(\"\");\n  const [GuideTypetoDelete, setGuideTypetoDelete] = useState(\"\");\n  const [openDialog, setOpenDialog] = useState(false);\n  const userType = useInfoStore(state => state.userType);\n  const [paginationModel, setPaginationModel] = useState({\n    page: 0,\n    pageSize: 15\n  });\n  const {\n    accountId,\n    roles\n  } = useContext(AccountContext);\n  const {\n    openSnackbar\n  } = useSnackbar();\n  const [totalCount, setTotalCount] = useState(0);\n  const [name, setName] = useState(\"Announcement\");\n  const handleEditClick = guide => {\n    setBannerButtonSelected(true);\n    setIsAIGuidePersisted(true);\n    setIsUnSavedChanges(false);\n    setEditClicked(true);\n    setOpenWarning(false);\n    let targetUrl = \"\";\n    editedguide = true;\n    if (guide.GuideType.toLowerCase() == \"announcement\" || guide.GuideType.toLowerCase() === \"tooltip\" || guide.GuideType.toLowerCase() === \"hotspot\" || guide.GuideType.toLowerCase() === \"tour\" || guide.GuideType.toLowerCase() === \"checklist\") {\n      if (guide.GuideType.toLowerCase() === \"tooltip\" || guide.GuideType.toLowerCase() === \"hotspot\" || guide.GuideType.toLowerCase() === \"banner\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Banner\" || selectedTemplateTour === \"Hotspot\") {\n        setOpenTooltip(true);\n        setElementSelected(true);\n        let styleTag = document.getElementById(\"dynamic-body-style\");\n        const bodyElement = document.body;\n\n        // Add a dynamic class to the body\n        bodyElement.classList.add(\"dynamic-body-style\");\n        if (!styleTag) {\n          styleTag = document.createElement(\"style\");\n          styleTag.id = \"dynamic-body-style\";\n\n          // Add styles for body and nested elements\n          let styles = `\n\t\t\t\t\t\t.dynamic-body-style {\n\t\t\t\t\t\t\tpadding-top: 50px !important;\n\t\t\t\t\t\t\tmax-height:calc(100% - 55px);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t`;\n          styleTag.innerHTML = styles;\n          document.head.appendChild(styleTag);\n        }\n      }\n      targetUrl = `${guide === null || guide === void 0 ? void 0 : guide.TargetUrl}`;\n      if (targetUrl !== window.location.href) {\n        setCurrentGuideId(guide.GuideId);\n        window.open(targetUrl);\n      } else {\n        setCurrentGuideId(guide.GuideId);\n      }\n      return;\n    } else if (guide.GuideType.toLowerCase() == \"banner\" || selectedTemplateTour === \"Banner\") {\n      //targetUrl = `${guide?.TargetUrl}#bannerEdit`;\n      setCurrentGuideId(guide.GuideId);\n      setBannerPopup(true);\n    }\n    if (targetUrl) {\n      //onAddClick(guide.GuideType, true, guide);\n      window.open(targetUrl);\n    }\n  };\n  const handleCopyClick = announcement => {\n    setCloneAnnouncementName(announcement);\n    setIsCloneDialogOpen(true);\n  };\n  const handleDeleteConfirmation = guideId => {\n    setGuideIdToDelete(guideId);\n    setOpenDialog(true);\n  };\n  const handleKeyDown = event => {\n    if (event.key === \"Enter\") {\n      handleSearch();\n    }\n  };\n  const columns = [{\n    field: \"Name\",\n    headerName: translate(\"Name\"),\n    // width: 300,\n    hideable: true,\n    resizable: false\n  }, {\n    field: \"UpdatedDate\",\n    headerName: translate(\"Last Edited\"),\n    // width: 250,\n    hideable: true,\n    renderCell: params => /*#__PURE__*/_jsxDEV(\"span\", {\n      children: [\" \", `${formatDateTime(params.row.UpdatedDate, \"dd-MM-yyyy\")}`]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 5\n    }, this),\n    resizable: false\n  }, {\n    field: \"actions\",\n    headerName: translate(\"Actions\"),\n    // width: 302,\n    hideable: true,\n    sortable: false,\n    renderCell: params => /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: roles != null && roles && [\"Account Admin\", \"Editor\"].some(role => roles.includes(role)) && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n          arrow: true,\n          title: translate(\"Edit\"),\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => handleEditClick(params.row),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: ListEditIcon\n              },\n              style: {\n                zoom: 0.7\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          arrow: true,\n          title: translate(\"Clone\"),\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => handleCopyClick(params.row),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: CopyListIcon\n              },\n              style: {\n                zoom: 0.7\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          arrow: true,\n          title: translate(\"Delete\"),\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => {\n              handleDeleteConfirmation(params.row.GuideId);\n              setGuideNametoDelete(params.row.Name);\n              setGuideTypetoDelete(params.row.GuideType);\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: DeleteIconList\n              },\n              style: {\n                zoom: 0.7\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true)\n    }, void 0, false),\n    resizable: false\n  }];\n  const fetchAnnouncements = async () => {\n    var _data$results;\n    const {\n      page,\n      pageSize\n    } = paginationModel;\n    const offset = page * pageSize;\n    const statusFilter = activeTab === 0 ? \"Active\" : activeTab === 1 ? \"InActive\" : \"Draft\";\n    const filters = [{\n      FieldName: \"GuideType\",\n      ElementType: \"string\",\n      Condition: \"equals\",\n      Value: title === \"Product Tours\" ? \"Tour\" : title,\n      IsCustomField: false\n    }, {\n      FieldName: \"GuideStatus\",\n      ElementType: \"string\",\n      Condition: \"equals\",\n      Value: statusFilter,\n      IsCustomField: false\n    }, {\n      FieldName: \"Name\",\n      ElementType: \"string\",\n      Condition: \"contains\",\n      Value: searchQuery,\n      IsCustomField: false\n    }, {\n      FieldName: \"AccountId\",\n      ElementType: \"string\",\n      Condition: \"contains\",\n      Value: accountId,\n      IsCustomField: false\n    }];\n    const data = await getAllGuides(offset, pageSize, filters, \"\");\n    const rowsWithIds = data === null || data === void 0 ? void 0 : (_data$results = data.results) === null || _data$results === void 0 ? void 0 : _data$results.map(item => ({\n      ...item,\n      id: item.GuideId\n    }));\n    setFilteredData(rowsWithIds || []);\n    setTotalCount(data === null || data === void 0 ? void 0 : data._count);\n  };\n  useEffect(() => {\n    if (Open || accountId) {\n      fetchAnnouncements();\n    }\n  }, [paginationModel, activeTab, Open, accountId]);\n\n  // useEffect(() => {\n  //     if (accountId) {\n  //       fetchAnnouncements();\n  //     }\n  //   }, [paginationModel, activeTab,accountId]);\n\n  const handleSearch = () => {\n    fetchAnnouncements();\n  };\n  useEffect(() => {\n    if (searchQuery.trim() === \"\") {\n      fetchAnnouncements();\n    }\n  }, [searchQuery]);\n  const handleClearSearch = () => {\n    setSearchQuery(\"\");\n    fetchAnnouncements();\n  };\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setPaginationModel(prev => ({\n      ...prev,\n      page: 0\n    })); // Reset pagination when the tab changes\n  };\n  const getRowSpacing = React.useCallback(params => {\n    return {\n      top: params.isFirstVisible ? 0 : 5,\n      bottom: params.isLastVisible ? 0 : 5\n    };\n  }, []);\n  const handleDelete = async () => {\n    if (guideIdToDelete) {\n      try {\n        const response = await DeleteGuideByGuideId(guideIdToDelete);\n        if (response.Success) {\n          openSnackbar(`${GuidenametoDelete} ${translate(GuideTypetoDelete)} ${translate(\"Deleted Successfully\")}`, \"success\");\n          await fetchAnnouncements();\n        } else {\n          openSnackbar(response.ErrorMessage, \"error\");\n        }\n      } catch (error) {}\n    }\n    setOpenDialog(false);\n    setGuideIdToDelete(null);\n    setGuideNametoDelete(\"\");\n  };\n  const handleCloneSuccess = async () => {\n    await fetchAnnouncements();\n  };\n  const getNoRowsLabel = () => {\n    const tabLabels = [translate(\"Active\"), translate(\"Inactive\"), translate(\"Draft\")];\n    const currentTabLabel = tabLabels[activeTab] || searchText;\n    return `${translate('No')} ${translate(currentTabLabel, {\n      defaultValue: currentTabLabel\n    })} ${translate(searchText, {\n      defaultValue: `${searchText}s`\n    })}`;\n  };\n  const NoRowsOverlay = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: \"flex\",\n      alignItems: \"center\",\n      flexDirection: \"column\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"qadpt-hotsicon\",\n      dangerouslySetInnerHTML: {\n        __html: NoData\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 4\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      sx: {\n        fontWeight: \"600\"\n      },\n      children: getNoRowsLabel()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 367,\n    columnNumber: 3\n  }, this);\n  const handleClosePopup = () => {\n    //if further ever need of closing popup when clicked outside the popup then please uncomment below code\n    // setActiveMenu(null);\n    // setSearchText(\"\");\n    // onClose();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"popuplistmenu\",\n    children: [/*#__PURE__*/_jsxDEV(Dialog, {\n      slotProps: {\n        root: {\n          id: \"tooltipdialog\"\n        },\n        backdrop: {\n          sx: {\n            position: \"absolute !important\"\n          }\n        }\n      },\n      open: Open,\n      onClose: handleClosePopup,\n      fullWidth: true,\n      maxWidth: \"md\",\n      className: \"qadpt-gud-menupopup\",\n      children: /*#__PURE__*/_jsxDEV(DialogContent, {\n        className: \"qadpt-gud-menupopup-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-subhead\",\n          id: \"tablesubhead\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"title\",\n            style: {\n              fontWeight: \"600 !important\"\n            },\n            children: translate(`${searchText}`, {\n              defaultValue: `${translate(searchText)}s`\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-head\",\n          id: \"table-head\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-titsection\",\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              variant: \"outlined\",\n              placeholder: translate(\"Search\") + \" \" + translate(title),\n              value: searchQuery,\n              onChange: e => {\n                const newValue = e.target.value;\n                setSearchQuery(newValue);\n                if (newValue === \"\") {\n                  handleClearSearch();\n                }\n              },\n              onKeyDown: e => {\n                if (e.key === \"Enter\") {\n                  handleSearch();\n                }\n              },\n              className: \"qadpt-extsearch\",\n              InputProps: {\n                sx: {\n                  \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                    borderColor: \"#a8a8a8\"\n                  },\n                  // Prevents color change on hover\n                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                    border: \"1px solid #a8a8a8\"\n                  }\n                },\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    \"aria-label\": \"search\",\n                    onClick: () => handleSearch(),\n                    onMouseDown: event => event.preventDefault(),\n                    children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 460,\n                      columnNumber: 13\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 11\n                }, this),\n                endAdornment: searchQuery && /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    \"aria-label\": \"clear\",\n                    onClick: () => {\n                      setSearchQuery(\"\");\n                      handleClearSearch();\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ClearIcon, {\n                      sx: {\n                        zoom: \"1.2\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 473,\n                      columnNumber: 13\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 11\n                }, this)\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-right-part\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => onAddClick(searchText),\n                className: \"qadpt-memberButton\",\n                disabled: userType.toLocaleLowerCase() != \"admin\" ? roles == null || !roles || ![\"Account Admin\", \"Editor\"].some(role => roles.includes(role)) : false,\n                children: [/*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: `${translate(\"Create\")} ${translate(searchText)}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-tabs-container\",\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            value: activeTab,\n            onChange: handleTabChange,\n            children: [/*#__PURE__*/_jsxDEV(Tab, {\n              label: translate(\"Active\"),\n              sx: {\n                backgroundColor: \"inherit !important\",\n                border: \"inherit !important\",\n                color: \"inherit !important\",\n                fontSize: \"14px !important\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              label: translate(\"Inactive\"),\n              sx: {\n                backgroundColor: \"inherit !important\",\n                border: \"inherit !important\",\n                color: \"inherit !important\",\n                fontSize: \"14px !important\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              label: translate(\"Draft\"),\n              sx: {\n                backgroundColor: \"inherit !important\",\n                border: \"inherit !important\",\n                color: \"inherit !important\",\n                fontSize: \"14px !important\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-webgird\",\n          children: /*#__PURE__*/_jsxDEV(DataGrid, {\n            rows: filteredData,\n            columns: columns,\n            getRowId: row => row.GuideId,\n            getRowSpacing: getRowSpacing,\n            pagination: true,\n            paginationModel: paginationModel,\n            paginationMode: \"server\",\n            onPaginationModelChange: setPaginationModel,\n            rowCount: totalCount,\n            pageSizeOptions: [15, 25, 50, 100],\n            localeText: {\n              MuiTablePagination: {\n                labelRowsPerPage: translate(\"Records Per Page\")\n              },\n              noRowsLabel: getNoRowsLabel()\n            },\n            disableColumnMenu: true,\n            disableRowSelectionOnClick: true,\n            className: \"qadpt-grdcont\",\n            slots: {\n              noRowsOverlay: NoRowsOverlay // Using the 'slots' prop for NoRowsOverlay\n            },\n            sx: {\n              \"& .MuiDataGrid-row\": {\n                maxWidth: \"calc(100% - 30px)\",\n                \"--rowBorderColor\": \"transparent\"\n                //   marginTop: \"17px\",\n                // marginBottom:\"0 !important\"\n              },\n              \"& .MuiDataGrid-cell\": {\n                padding: \"0 15px !important\"\n              },\n              \".MuiTablePagination-toolbar\": {\n                display: \"flex !important\",\n                alignItems: \"baseline !important\"\n              },\n              \".MuiTablePagination-actions button\": {\n                border: \"none !important\",\n                color: \"inherit !important\",\n                backgroundColor: \"initial !important\",\n                \"&:hover\": {\n                  backgroundColor: \"initial !important\" // Hover background\n                }\n              },\n              \"& .MuiDataGrid-columnHeader\": {\n                background: \"linear-gradient(to right, #f6eeee, #f6eeee)\",\n                padding: \"0 15px !important\",\n                borderRight: \"1px solid #f6eeee\",\n                height: \"40px !important\"\n              },\n              \"& .MuiDataGrid-columnHeaderTitle\": {\n                fontWeight: \"600\"\n              },\n              \"& .MuiDataGrid-filler\": {\n                backgroundColor: \"var(--ext-background)\",\n                \"--rowBorderColor\": \"transparent !important\"\n              },\n              \"& .MuiDataGrid-scrollbarFiller\": {\n                backgroundColor: \"var(--ext-background)\",\n                display: \"none\"\n              }\n            },\n            rowHeight: 38\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 4\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: () => setOpenDialog(false),\n      PaperProps: {\n        style: {\n          borderRadius: \"4px\",\n          maxWidth: \"400px\",\n          textAlign: \"center\",\n          maxHeight: \"300px\",\n          boxShadow: \"none\"\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          padding: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            justifyContent: \"center\",\n            padding: \"10px\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              backgroundColor: \"#e4b6b0\",\n              borderRadius: \"50%\",\n              width: \"40px\",\n              height: \"40px\",\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\"\n            },\n            children: /*#__PURE__*/_jsxDEV(DeleteOutlineOutlinedIcon, {\n              sx: {\n                color: \"#F44336\",\n                height: \"20px\",\n                width: \"20px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontSize: \"16px !important\",\n            fontWeight: 600,\n            padding: \"0 10px\"\n          },\n          children: [translate(\"Delete\"), \" \", translate(GuideTypetoDelete)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        sx: {\n          padding: \"20px !important\"\n        },\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          style: {\n            fontSize: \"14px\",\n            color: \"#000\"\n          },\n          children: translate(`${translate('The')} ${GuidenametoDelete} ${translate(\"cannot be restored once it is deleted.\")}`)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 630,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          justifyContent: \"space-between\",\n          borderTop: \"1px solid var(--border-color)\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setOpenDialog(false),\n          sx: {\n            color: \"#9E9E9E\",\n            border: \"1px solid #9E9E9E\",\n            borderRadius: \"4px\",\n            textTransform: \"capitalize\",\n            padding: \"var(--button-padding)\",\n            lineHeight: \"var(--button-lineheight)\"\n          },\n          children: translate(\"Cancel\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDelete,\n          sx: {\n            backgroundColor: \"var(--error-color)\",\n            color: \"#FFF\",\n            borderRadius: \"4px\",\n            textTransform: \"capitalize\",\n            padding: \"var(--button-padding)\",\n            lineHeight: \"var(--button-lineheight)\"\n            // \"&:hover\": {\n            // \tbackgroundColor: \"#D32F2F\",\n            // },\n          },\n          children: translate(\"Delete\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 636,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 596,\n      columnNumber: 4\n    }, this), isCloneDialogOpen && cloneAnnouncementName && /*#__PURE__*/_jsxDEV(CloneInteractionDialog, {\n      open: isCloneDialogOpen,\n      handleClose: () => setIsCloneDialogOpen(false),\n      initialName: cloneAnnouncementName,\n      onCloneSuccess: handleCloneSuccess,\n      name: name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 669,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 384,\n    columnNumber: 3\n  }, this);\n};\n_s(PopupModal, \"mfpAjrzlysDvkMCMxS6nyIq+/f0=\", false, function () {\n  return [useUserSession, useTranslation, useDrawerStore, useInfoStore, useSnackbar];\n});\n_c = PopupModal;\nexport { editedguide };\nexport default PopupModal;\nvar _c;\n$RefreshReg$(_c, \"PopupModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TextField", "InputAdornment", "IconButton", "Tab", "Tabs", "<PERSON><PERSON><PERSON>", "DialogTitle", "DialogContentText", "DialogActions", "<PERSON><PERSON>", "Typography", "DataGrid", "SearchIcon", "ClearIcon", "getAllGuides", "DeleteGuideByGuideId", "ListEditIcon", "CopyListIcon", "DeleteIconList", "NoData", "DeleteOutlineOutlinedIcon", "AddIcon", "CloneInteractionDialog", "AccountContext", "useSnackbar", "formatDateTime", "useDrawerStore", "useUserSession", "useTranslation", "useInfoStore", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "editedguide", "PopupModal", "Open", "onClose", "title", "searchText", "onAddClick", "isAIGuidePersisted", "setIsAIGuidePersisted", "_s", "setCurrentGuideId", "currentGuideId", "state", "t", "translate", "setBannerPopup", "setOpenTooltip", "setElementSelected", "setBannerButtonSelected", "selectedTemplateTour", "isUnSavedChanges", "setIsUnSavedChanges", "openWarning", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setEditClicked", "setActiveMenu", "setSearchText", "activeTab", "setActiveTab", "searchQuery", "setSearch<PERSON>uery", "filteredData", "setFilteredData", "isCloneDialogOpen", "setIsCloneDialogOpen", "cloneAnnouncementName", "setCloneAnnouncementName", "guideIdToDelete", "setGuideIdToDelete", "GuidenametoDelete", "setGuideNametoDelete", "GuideTypetoDelete", "setGuideTypetoDelete", "openDialog", "setOpenDialog", "userType", "paginationModel", "setPaginationModel", "page", "pageSize", "accountId", "roles", "openSnackbar", "totalCount", "setTotalCount", "name", "setName", "handleEditClick", "guide", "targetUrl", "GuideType", "toLowerCase", "styleTag", "document", "getElementById", "bodyElement", "body", "classList", "add", "createElement", "id", "styles", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "TargetUrl", "window", "location", "href", "GuideId", "open", "handleCopyClick", "announcement", "handleDeleteConfirmation", "guideId", "handleKeyDown", "event", "key", "handleSearch", "columns", "field", "headerName", "hideable", "resizable", "renderCell", "params", "children", "row", "UpdatedDate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sortable", "some", "role", "includes", "arrow", "onClick", "dangerouslySetInnerHTML", "__html", "style", "zoom", "Name", "fetchAnnouncements", "_data$results", "offset", "statusFilter", "filters", "FieldName", "ElementType", "Condition", "Value", "IsCustomField", "data", "rowsWithIds", "results", "map", "item", "_count", "trim", "handleClearSearch", "handleTabChange", "newValue", "prev", "getRowSpacing", "useCallback", "top", "isFirstVisible", "bottom", "isLastVisible", "handleDelete", "response", "Success", "ErrorMessage", "error", "handleCloneSuccess", "getNoRowsLabel", "tabLabels", "currentTabLabel", "defaultValue", "NoRowsOverlay", "display", "alignItems", "flexDirection", "className", "sx", "fontWeight", "handleClosePopup", "slotProps", "root", "backdrop", "position", "fullWidth", "max<PERSON><PERSON><PERSON>", "variant", "placeholder", "value", "onChange", "e", "target", "onKeyDown", "InputProps", "borderColor", "border", "startAdornment", "onMouseDown", "preventDefault", "endAdornment", "disabled", "toLocaleLowerCase", "label", "backgroundColor", "color", "fontSize", "rows", "getRowId", "pagination", "paginationMode", "onPaginationModelChange", "rowCount", "pageSizeOptions", "localeText", "MuiTablePagination", "labelRowsPerPage", "noRowsLabel", "disableColumnMenu", "disableRowSelectionOnClick", "slots", "noRowsOverlay", "padding", "background", "borderRight", "height", "rowHeight", "PaperProps", "borderRadius", "textAlign", "maxHeight", "boxShadow", "justifyContent", "width", "borderTop", "textTransform", "lineHeight", "handleClose", "initialName", "onCloneSuccess", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideSetting/guideList/PopupList.tsx"], "sourcesContent": ["import React, { useState, useEffect, useContext } from \"react\";\r\nimport {\r\n\t<PERSON><PERSON>,\r\n\tDialogContent,\r\n\tTextField,\r\n\tInputAdornment,\r\n\tIconButton,\r\n\tTab,\r\n\tTabs,\r\n\tTooltip,\r\n\tDialogTitle,\r\n\tDialogContentText,\r\n\tDialogActions,\r\n\tButton,\r\n\tTypography,\r\n} from \"@mui/material\";\r\nimport { DataGrid, GridColDef, GridRenderCellParams, GridRowSpacingParams } from \"@mui/x-data-grid\";\r\nimport SearchIcon from \"@mui/icons-material/Search\";\r\nimport ClearIcon from \"@mui/icons-material/Clear\";\r\n\r\nimport { getAllGuides, DeleteGuideByGuideId } from \"../../../services/GuideListServices\";\r\nimport { ListEditIcon, CopyListIcon, DeleteIconList, NoData } from \"../../../assets/icons/icons\";\r\nimport DeleteOutlineOutlinedIcon from \"@mui/icons-material/DeleteOutlineOutlined\";\r\nimport \"./GuideMenuOptions.css\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport CloneInteractionDialog from \"./CloneGuidePopUp\";\r\nimport { AccountContext } from \"../../login/AccountContext\";\r\nimport { useSnackbar } from \"./SnackbarContext\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { formatDateTime } from \"../../guideSetting/guideList/TimeZoneConversion\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport useUserSession from \"../../../store/userSession\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport { useAuth } from \"../../auth/AuthProvider\";\r\nimport useInfoStore from \"../../../store/UserInfoStore\";\r\n\r\nlet editedguide: any;\r\ninterface PopupModalProps {\r\n\ttitle: string;\r\n\tOpen: boolean;\r\n\tonClose: () => void;\r\n\tsearchText: string;\r\n\tonAddClick: (searchText: string, isEditing?: boolean, guideDetails?: any) => void;\r\n\tisAIGuidePersisted:boolean;\r\n\tsetIsAIGuidePersisted:any;\r\n}\r\ninterface Announcement {\r\n\tAccountId: string;\r\n\tContent: string;\r\n\tCreatedBy: string;\r\n\tCreatedDate: string;\r\n\tFrequency: string;\r\n\tGuideId: string;\r\n\tGuideStatus: string;\r\n\tGuideType: string;\r\n\tName: string;\r\n\tOrganizationId: string;\r\n\tSegment: string;\r\n\tTargetUrl: string;\r\n\tTemplateId: string;\r\n\tUpdatedBy: string;\r\n\tUpdatedDate: string;\r\n}\r\n\r\nconst PopupModal: React.FC<PopupModalProps> = ({ Open, onClose, title, searchText, onAddClick ,isAIGuidePersisted,\r\n\t\tsetIsAIGuidePersisted}) => {\r\n\tconst { setCurrentGuideId, currentGuideId } = useUserSession((state) => state);\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tsetBannerPopup,\r\n\t\tsetOpenTooltip,\r\n\t\tsetElementSelected,\r\n\t\tsetBannerButtonSelected,\r\n\t\tselectedTemplateTour,\r\n\t\tisUnSavedChanges,\r\n\t\tsetIsUnSavedChanges,\r\n\t\topenWarning,\r\n\t\tsetOpenWarning,\r\n\t\tsetEditClicked,\r\n\t\tsetActiveMenu,\r\n\t\tsetSearchText,\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [activeTab, setActiveTab] = useState(0);\r\n\tconst [searchQuery, setSearchQuery] = useState(\"\");\r\n\tconst [filteredData, setFilteredData] = useState<any[]>([]);\r\n\tconst [isCloneDialogOpen, setIsCloneDialogOpen] = useState(false);\r\n\tconst [cloneAnnouncementName, setCloneAnnouncementName] = useState<Announcement | null>(null);\r\n\tconst [guideIdToDelete, setGuideIdToDelete] = useState<string | null>(null);\r\n\tconst [GuidenametoDelete, setGuideNametoDelete] = useState(\"\");\r\n\tconst [GuideTypetoDelete, setGuideTypetoDelete] = useState(\"\");\r\n\tconst [openDialog, setOpenDialog] = useState(false);\r\n\tconst userType = useInfoStore((state) => state.userType); \r\n\tconst [paginationModel, setPaginationModel] = useState({\r\n\t\tpage: 0,\r\n\t\tpageSize: 15,\r\n\t});\r\n\tconst { accountId,roles } = useContext(AccountContext);\r\n\tconst { openSnackbar } = useSnackbar();\r\n\tconst [totalCount, setTotalCount] = useState(0);\r\n\tconst [name, setName] = useState(\"Announcement\");\r\n\tconst handleEditClick = (guide: Announcement) => {\r\n\t\tsetBannerButtonSelected(true);\r\n\t\tsetIsAIGuidePersisted(true);\r\n\t\tsetIsUnSavedChanges(false);\r\n\t\tsetEditClicked(true);\r\n\t\tsetOpenWarning(false);\r\n\t\tlet targetUrl = \"\";\r\n\t\teditedguide = true;\r\n\t\tif (\r\n\t\t\tguide.GuideType.toLowerCase() == \"announcement\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"tooltip\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"hotspot\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"tour\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"checklist\"\r\n\t\t) {\r\n\t\t\tif (\r\n\t\t\t\tguide.GuideType.toLowerCase() === \"tooltip\" ||\r\n\t\t\t\tguide.GuideType.toLowerCase() === \"hotspot\" ||\r\n\t\t\t\tguide.GuideType.toLowerCase() === \"banner\" ||\r\n\t\t\t\tselectedTemplateTour === \"Tooltip\" ||\r\n\t\t\t\tselectedTemplateTour === \"Banner\" ||\r\n\t\t\t\tselectedTemplateTour === \"Hotspot\"\r\n\t\t\t) {\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\tsetElementSelected(true);\r\n\t\t\t\tlet styleTag = document.getElementById(\"dynamic-body-style\") as HTMLStyleElement;\r\n\t\t\t\tconst bodyElement = document.body;\r\n\r\n\t\t\t\t// Add a dynamic class to the body\r\n\t\t\t\tbodyElement.classList.add(\"dynamic-body-style\");\r\n\r\n\t\t\t\tif (!styleTag) {\r\n\t\t\t\t\tstyleTag = document.createElement(\"style\");\r\n\t\t\t\t\tstyleTag.id = \"dynamic-body-style\";\r\n\r\n\t\t\t\t\t// Add styles for body and nested elements\r\n\t\t\t\t\tlet styles = `\r\n\t\t\t\t\t\t.dynamic-body-style {\r\n\t\t\t\t\t\t\tpadding-top: 50px !important;\r\n\t\t\t\t\t\t\tmax-height:calc(100% - 55px);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t`;\r\n\r\n\t\t\t\t\tstyleTag.innerHTML = styles;\r\n\t\t\t\t\tdocument.head.appendChild(styleTag);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\ttargetUrl = `${guide?.TargetUrl}`;\r\n\t\t\tif (targetUrl !== window.location.href) {\r\n\t\t\t\tsetCurrentGuideId(guide.GuideId);\r\n\t\t\t\twindow.open(targetUrl);\r\n\t\t\t} else {\r\n\t\t\t\tsetCurrentGuideId(guide.GuideId);\r\n\t\t\t}\r\n\r\n\t\t\treturn;\r\n\t\t} else if (guide.GuideType.toLowerCase() == \"banner\" || selectedTemplateTour === \"Banner\") {\r\n\t\t\t//targetUrl = `${guide?.TargetUrl}#bannerEdit`;\r\n\t\t\tsetCurrentGuideId(guide.GuideId);\r\n\t\t\tsetBannerPopup(true);\r\n\t\t}\r\n\t\tif (targetUrl) {\r\n\t\t\t//onAddClick(guide.GuideType, true, guide);\r\n\t\t\twindow.open(targetUrl);\r\n\t\t}\r\n\t};\r\n\t\r\n\tconst handleCopyClick = (announcement: Announcement) => {\r\n\t\tsetCloneAnnouncementName(announcement);\r\n\t\tsetIsCloneDialogOpen(true);\r\n\t};\r\n\tconst handleDeleteConfirmation = (guideId: string) => {\r\n\t\tsetGuideIdToDelete(guideId);\r\n\t\tsetOpenDialog(true);\r\n\t};\r\n\tconst handleKeyDown = (event: React.KeyboardEvent) => {\r\n\t\tif (event.key === \"Enter\") {\r\n\t\t\thandleSearch();\r\n\t\t}\r\n\t};\r\n\tconst columns: GridColDef[] = [\r\n\t\t{\r\n\t\t\tfield: \"Name\",\r\n\t\t\theaderName: translate(\"Name\"),\r\n\t\t\t// width: 300,\r\n\t\t\thideable: true,\r\n\t\t\tresizable: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tfield: \"UpdatedDate\",\r\n\t\t\theaderName: translate(\"Last Edited\"),\r\n\t\t\t// width: 250,\r\n\t\t\thideable: true,\r\n\t\t\trenderCell: (params: GridRenderCellParams) => (\r\n\t\t\t\t<span> {`${formatDateTime(params.row.UpdatedDate, \"dd-MM-yyyy\")}`}</span>\r\n\t\t\t),\r\n\t\t\tresizable: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tfield: \"actions\",\r\n\t\t\theaderName: translate(\"Actions\"),\r\n\t\t\t// width: 302,\r\n\t\t\thideable: true,\r\n\t\t\tsortable: false,\r\n\t\t\trenderCell: (params: GridRenderCellParams) => (\r\n\t\t\t\t<>\r\n\t\t\t\t\t{ roles != null && roles && [\"Account Admin\",\"Editor\"].some(role => roles.includes(role)) &&<>\r\n\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\t\ttitle={translate(\"Edit\")}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<IconButton onClick={() => handleEditClick(params.row)}>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: ListEditIcon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ zoom: 0.7 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\ttitle={translate(\"Clone\")}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<IconButton onClick={() => handleCopyClick(params.row)}>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: CopyListIcon }}\r\n\t\t\t\t\t\t\t\tstyle={{ zoom: 0.7 }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\ttitle={translate(\"Delete\")}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\thandleDeleteConfirmation(params.row.GuideId);\r\n\t\t\t\t\t\t\t\tsetGuideNametoDelete(params.row.Name);\r\n\t\t\t\t\t\t\t\tsetGuideTypetoDelete(params.row.GuideType);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: DeleteIconList }}\r\n\t\t\t\t\t\t\t\tstyle={{ zoom: 0.7 }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t</>\r\n\t\t\t\t\t}\r\n\t\t\t\t</>\r\n\t\t\t),\r\n\t\t\tresizable: false,\r\n\t\t},\r\n\t];\r\n\r\n\tconst fetchAnnouncements = async () => {\r\n\t\tconst { page, pageSize } = paginationModel;\r\n\t\tconst offset = page * pageSize;\r\n\t\tconst statusFilter = activeTab === 0 ? \"Active\" : activeTab === 1 ? \"InActive\" : \"Draft\";\r\n\r\n\t\tconst filters = [\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"GuideType\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"equals\",\r\n\t\t\t\tValue: title === \"Product Tours\" ? \"Tour\" : title,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"GuideStatus\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"equals\",\r\n\t\t\t\tValue: statusFilter,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"Name\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"contains\",\r\n\t\t\t\tValue: searchQuery,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"AccountId\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"contains\",\r\n\t\t\t\tValue: accountId,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t];\r\n\t\tconst data = await getAllGuides(offset, pageSize, filters, \"\");\r\n\t\tconst rowsWithIds = data?.results?.map((item: any) => ({\r\n\t\t\t...item,\r\n\t\t\tid: item.GuideId,\r\n\t\t}));\r\n\r\n\t\tsetFilteredData(rowsWithIds || []);\r\n\t\tsetTotalCount(data?._count);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (Open || accountId) {\r\n\t\t\tfetchAnnouncements();\r\n\t\t}\r\n\t}, [paginationModel, activeTab, Open, accountId]);\r\n\r\n\t// useEffect(() => {\r\n\t//     if (accountId) {\r\n\t//       fetchAnnouncements();\r\n\t//     }\r\n\t//   }, [paginationModel, activeTab,accountId]);\r\n\r\n\tconst handleSearch = () => {\r\n\t\tfetchAnnouncements();\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (searchQuery.trim() === \"\") {\r\n\t\t\tfetchAnnouncements();\r\n\t\t}\r\n\t}, [searchQuery]);\r\n\tconst handleClearSearch = () => {\r\n\t\tsetSearchQuery(\"\");\r\n\t\tfetchAnnouncements();\r\n\t};\r\n\r\n\tconst handleTabChange = (event: React.SyntheticEvent, newValue: number) => {\r\n\t\tsetActiveTab(newValue);\r\n\t\tsetPaginationModel((prev) => ({ ...prev, page: 0 })); // Reset pagination when the tab changes\r\n\t};\r\n\tconst getRowSpacing = React.useCallback((params: GridRowSpacingParams) => {\r\n\t\treturn {\r\n\t\t\ttop: params.isFirstVisible ? 0 : 5,\r\n\t\t\tbottom: params.isLastVisible ? 0 : 5,\r\n\t\t};\r\n\t}, []);\r\n\r\n\tconst handleDelete = async () => {\r\n\t\tif (guideIdToDelete) {\r\n\t\t\ttry {\r\n\t\t\t\tconst response = await DeleteGuideByGuideId(guideIdToDelete);\r\n\t\t\t\tif (response.Success) {\r\n\t\t\t\t\topenSnackbar(\r\n\t\t\t\t\t\t`${GuidenametoDelete} ${translate(GuideTypetoDelete)} ${translate(\"Deleted Successfully\")}`,\r\n\t\t\t\t\t\t\"success\"\r\n\t\t\t\t\t);\r\n\t\t\t\t\tawait fetchAnnouncements();\r\n\t\t\t\t} else {\r\n\t\t\t\t\topenSnackbar(response.ErrorMessage, \"error\");\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {}\r\n\t\t}\r\n\t\tsetOpenDialog(false);\r\n\t\tsetGuideIdToDelete(null);\r\n\t\tsetGuideNametoDelete(\"\");\r\n\t};\r\n\tconst handleCloneSuccess = async () => {\r\n\t\tawait fetchAnnouncements();\r\n\t};\r\n\tconst getNoRowsLabel = () => {\r\n\t\tconst tabLabels = [translate(\"Active\"), translate(\"Inactive\"), translate(\"Draft\")];\r\n\t\tconst currentTabLabel = tabLabels[activeTab] || searchText;\r\n\t\treturn `${translate('No')} ${translate(currentTabLabel, { defaultValue: currentTabLabel })} ${translate(searchText, { defaultValue: `${searchText}s` })}`;\r\n\t};\r\n\tconst NoRowsOverlay = () => (\r\n\t\t<div style={{ display: \"flex\", alignItems: \"center\", flexDirection: \"column\" }}>\r\n\t\t\t<span\r\n\t\t\t\tclassName=\"qadpt-hotsicon\"\r\n\t\t\t\tdangerouslySetInnerHTML={{ __html: NoData }}\r\n\t\t\t/>\r\n\t\t\t<Typography sx={{ fontWeight: \"600\" }}>{getNoRowsLabel()}</Typography>\r\n\t\t</div>\r\n\t);\r\n\r\n\tconst handleClosePopup = () => {\r\n\t\t//if further ever need of closing popup when clicked outside the popup then please uncomment below code\r\n\t\t// setActiveMenu(null);\r\n\t\t// setSearchText(\"\");\r\n\t\t// onClose();\r\n\t};\r\n\r\n\treturn (\r\n\t\t<div id=\"popuplistmenu\">\r\n\t\t\t<Dialog\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\troot: {\r\n\t\t\t\t\t\tid: \"tooltipdialog\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\tbackdrop: {\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tposition: \"absolute !important\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t\topen={Open}\r\n\t\t\t\tonClose={handleClosePopup}\r\n\t\t\t\tfullWidth\r\n\t\t\t\tmaxWidth=\"md\"\r\n\t\t\t\tclassName=\"qadpt-gud-menupopup\"\r\n\t\t\t>\r\n\t\t\t\t<DialogContent className=\"qadpt-gud-menupopup-content\">\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tclassName=\"qadpt-subhead\"\r\n\t\t\t\t\t\tid=\"tablesubhead\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<span\r\n\tclassName=\"title\"\r\n\tstyle={{ fontWeight: \"600 !important\" }}\r\n>\r\n\t\t\t\t\t\t\t{translate(`${searchText}`, { defaultValue: `${translate(searchText)}s` })}\r\n</span>\r\n\t\t\t\t\t\t{/* <IconButton\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\tborderWidth: \"1px\",\r\n\t\t\t\t\t\t\t\tborderStyle: \"solid\",\r\n\t\t\t\t\t\t\t\tpadding: \"5px\",\r\n\t\t\t\t\t\t\t\tborderColor: \"var(--border-color)\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={onClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon style={{ color: \"#000\" }} />\r\n\t\t\t\t\t\t</IconButton> */}\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tclassName=\"qadpt-head\"\r\n\t\t\t\t\t\tid=\"table-head\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div className=\"qadpt-titsection\">\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tplaceholder={translate(\"Search\") + \" \" + translate(title)}\r\n\t\t\t\t\t\t\t\tvalue={searchQuery}\r\n\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\tconst newValue = e.target.value;\r\n\t\t\t\t\t\t\t\t\tsetSearchQuery(newValue);\r\n\t\t\t\t\t\t\t\t\tif (newValue === \"\") {\r\n\t\t\t\t\t\t\t\t\t\thandleClearSearch();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonKeyDown={(e) => {\r\n\t\t\t\t\t\t\t\t\tif (e.key === \"Enter\") {\r\n\t\t\t\t\t\t\t\t\t\thandleSearch();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-extsearch\"\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { borderColor: \"#a8a8a8\" }, // Prevents color change on hover\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"1px solid #a8a8a8\" },\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tstartAdornment: (\r\n\t\t\t\t\t\t\t\t\t\t<InputAdornment position=\"start\">\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\taria-label=\"search\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleSearch()}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonMouseDown={(event) => event.preventDefault()}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<SearchIcon />\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</InputAdornment>\r\n\t\t\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\t\t\tendAdornment: searchQuery && (\r\n\t\t\t\t\t\t\t\t\t\t<InputAdornment position=\"end\">\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\taria-label=\"clear\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsetSearchQuery(\"\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\thandleClearSearch();\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<ClearIcon sx={{ zoom: \"1.2\" }} />\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</InputAdornment>\r\n\t\t\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-right-part\">\r\n\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\tonClick={() => onAddClick(searchText)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-memberButton\"\r\n\t\t\t\t\t\t\t\t\tdisabled={userType.toLocaleLowerCase()!=\"admin\" ? roles==null || !roles || ![\"Account Admin\", \"Editor\"].some(role => roles.includes(role)): false}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<AddIcon />\r\n\t\t\t\t\t\t\t\t\t<span>{`${translate(\"Create\")} ${translate(searchText)}`}</span>\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-tabs-container\">\r\n\t\t\t\t\t\t<Tabs\r\n\t\t\t\t\t\t\tvalue={activeTab}\r\n\t\t\t\t\t\t\tonChange={handleTabChange}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Tab\r\n\t\t\t\t\t\t\t\tlabel={translate(\"Active\")}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tborder: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tfontSize: \"14px !important\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<Tab\r\n\t\t\t\t\t\t\t\tlabel={translate(\"Inactive\")}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tborder: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tfontSize: \"14px !important\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<Tab\r\n\t\t\t\t\t\t\t\tlabel={translate(\"Draft\")}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tborder: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tfontSize: \"14px !important\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Tabs>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-webgird\">\r\n\t\t\t\t\t\t<DataGrid\r\n\t\t\t\t\t\t\trows={filteredData}\r\n\t\t\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\t\t\tgetRowId={(row) => row.GuideId}\r\n\t\t\t\t\t\t\tgetRowSpacing={getRowSpacing}\r\n\t\t\t\t\t\t\tpagination\r\n\t\t\t\t\t\t\tpaginationModel={paginationModel}\r\n\t\t\t\t\t\t\tpaginationMode=\"server\"\r\n\t\t\t\t\t\t\tonPaginationModelChange={setPaginationModel}\r\n\t\t\t\t\t\t\trowCount={totalCount}\r\n\t\t\t\t\t\t\tpageSizeOptions={[15, 25, 50, 100]}\r\n\t\t\t\t\t\t\tlocaleText={{\r\n\t\t\t\t\t\t\t\tMuiTablePagination: {\r\n\t\t\t\t\t\t\t\t\tlabelRowsPerPage: translate(\"Records Per Page\"),\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tnoRowsLabel: getNoRowsLabel(),\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisableColumnMenu\r\n\t\t\t\t\t\t\tdisableRowSelectionOnClick\r\n\t\t\t\t\t\t\tclassName=\"qadpt-grdcont\"\r\n\t\t\t\t\t\t\tslots={{\r\n\t\t\t\t\t\t\t\tnoRowsOverlay: NoRowsOverlay, // Using the 'slots' prop for NoRowsOverlay\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-row\": {\r\n\t\t\t\t\t\t\t\t\tmaxWidth: \"calc(100% - 30px)\",\r\n\t\t\t\t\t\t\t\t\t\"--rowBorderColor\": \"transparent\",\r\n\t\t\t\t\t\t\t\t\t//   marginTop: \"17px\",\r\n\t\t\t\t\t\t\t\t\t// marginBottom:\"0 !important\"\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-cell\": {\r\n\t\t\t\t\t\t\t\t\tpadding: \"0 15px !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\".MuiTablePagination-toolbar\": {\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex !important\",\r\n\t\t\t\t\t\t\t\t\talignItems: \"baseline !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\".MuiTablePagination-actions button\": {\r\n\t\t\t\t\t\t\t\t\tborder: \"none !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"initial !important\",\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"initial !important\", // Hover background\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-columnHeader\": {\r\n\t\t\t\t\t\t\t\t\tbackground: \"linear-gradient(to right, #f6eeee, #f6eeee)\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"0 15px !important\",\r\n\t\t\t\t\t\t\t\t\tborderRight: \"1px solid #f6eeee\",\r\n\t\t\t\t\t\t\t\t\theight: \"40px !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-columnHeaderTitle\": {\r\n\t\t\t\t\t\t\t\t\tfontWeight: \"600\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-filler\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"var(--ext-background)\",\r\n\t\t\t\t\t\t\t\t\t\"--rowBorderColor\": \"transparent !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-scrollbarFiller\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"var(--ext-background)\",\r\n\t\t\t\t\t\t\t\t\tdisplay: \"none\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\trowHeight={38}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</DialogContent>\r\n\t\t\t</Dialog>\r\n\t\t\t<Dialog\r\n\t\t\t\topen={openDialog}\r\n\t\t\t\tonClose={() => setOpenDialog(false)}\r\n\t\t\t\tPaperProps={{\r\n\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\tmaxWidth: \"400px\",\r\n\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\tmaxHeight: \"300px\",\r\n\t\t\t\t\t\tboxShadow: \"none\",\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<DialogTitle sx={{ padding: 0 }}>\r\n\t\t\t\t\t<div style={{ display: \"flex\", justifyContent: \"center\", padding: \"10px\" }}>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#e4b6b0\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\twidth: \"40px\",\r\n\t\t\t\t\t\t\t\theight: \"40px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<DeleteOutlineOutlinedIcon sx={{ color: \"#F44336\", height: \"20px\", width: \"20px\" }} />\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<Typography sx={{ fontSize: \"16px !important\", fontWeight: 600, padding: \"0 10px\" }}>\r\n\t\t\t\t\t\t{translate(\"Delete\")} {translate(GuideTypetoDelete)}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</DialogTitle>\r\n\r\n\t\t\t\t<DialogContent sx={{ padding: \"20px !important\" }}>\r\n\t\t\t\t\t<DialogContentText style={{ fontSize: \"14px\", color: \"#000\" }}>\r\n\t\t\t\t\t\t{translate(`${translate('The')} ${GuidenametoDelete} ${translate(\"cannot be restored once it is deleted.\")}`)}\r\n\t\t\t\t\t</DialogContentText>\r\n\t\t\t\t</DialogContent>\r\n\r\n\t\t\t\t<DialogActions sx={{ justifyContent: \"space-between\", borderTop: \"1px solid var(--border-color)\" }}>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={() => setOpenDialog(false)}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tcolor: \"#9E9E9E\",\r\n\t\t\t\t\t\t\tborder: \"1px solid #9E9E9E\",\r\n\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Cancel\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={handleDelete}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tbackgroundColor: \"var(--error-color)\",\r\n\t\t\t\t\t\t\tcolor: \"#FFF\",\r\n\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t\t// \"&:hover\": {\r\n\t\t\t\t\t\t\t// \tbackgroundColor: \"#D32F2F\",\r\n\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Delete\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</DialogActions>\r\n\t\t\t</Dialog>\r\n\t\t\t{isCloneDialogOpen && cloneAnnouncementName && (\r\n\t\t\t\t<CloneInteractionDialog\r\n\t\t\t\t\topen={isCloneDialogOpen}\r\n\t\t\t\t\thandleClose={() => setIsCloneDialogOpen(false)}\r\n\t\t\t\t\tinitialName={cloneAnnouncementName}\r\n\t\t\t\t\tonCloneSuccess={handleCloneSuccess}\r\n\t\t\t\t\tname={name}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t</div>\r\n\t);\r\n};\r\nexport { editedguide };\r\nexport default PopupModal;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SACCC,MAAM,EACNC,aAAa,EACbC,SAAS,EACTC,cAAc,EACdC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,OAAO,EACPC,WAAW,EACXC,iBAAiB,EACjBC,aAAa,EACbC,MAAM,EACNC,UAAU,QACJ,eAAe;AACtB,SAASC,QAAQ,QAAgE,kBAAkB;AACnG,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AAEjD,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,qCAAqC;AACxF,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,MAAM,QAAQ,6BAA6B;AAChG,OAAOC,yBAAyB,MAAM,2CAA2C;AACjF,OAAO,wBAAwB;AAC/B,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,sBAAsB,MAAM,mBAAmB;AACtD,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,WAAW,QAAQ,mBAAmB;AAE/C,SAASC,cAAc,QAAQ,iDAAiD;AAChF,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,cAAc,QAAQ,eAAe;AAE9C,OAAOC,YAAY,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExD,IAAIC,WAAgB;AA4BpB,MAAMC,UAAqC,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,KAAK;EAAEC,UAAU;EAAEC,UAAU;EAAEC,kBAAkB;EAC/GC;AAAqB,CAAC,KAAK;EAAAC,EAAA;EAC5B,MAAM;IAAEC,iBAAiB;IAAEC;EAAe,CAAC,GAAGlB,cAAc,CAAEmB,KAAK,IAAKA,KAAK,CAAC;EAC9E,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGpB,cAAc,CAAC,CAAC;EACzC,MAAM;IACLqB,cAAc;IACdC,cAAc;IACdC,kBAAkB;IAClBC,uBAAuB;IACvBC,oBAAoB;IACpBC,gBAAgB;IAChBC,mBAAmB;IACnBC,WAAW;IACXC,cAAc;IACdC,cAAc;IACdC,aAAa;IACbC;EACD,CAAC,GAAGlC,cAAc,CAAEoB,KAAK,IAAKA,KAAK,CAAC;EACpC,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACoE,WAAW,EAAEC,cAAc,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsE,YAAY,EAAEC,eAAe,CAAC,GAAGvE,QAAQ,CAAQ,EAAE,CAAC;EAC3D,MAAM,CAACwE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC0E,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG3E,QAAQ,CAAsB,IAAI,CAAC;EAC7F,MAAM,CAAC4E,eAAe,EAAEC,kBAAkB,CAAC,GAAG7E,QAAQ,CAAgB,IAAI,CAAC;EAC3E,MAAM,CAAC8E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACgF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACkF,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMoF,QAAQ,GAAGlD,YAAY,CAAEiB,KAAK,IAAKA,KAAK,CAACiC,QAAQ,CAAC;EACxD,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtF,QAAQ,CAAC;IACtDuF,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACX,CAAC,CAAC;EACF,MAAM;IAAEC,SAAS;IAACC;EAAM,CAAC,GAAGxF,UAAU,CAAC0B,cAAc,CAAC;EACtD,MAAM;IAAE+D;EAAa,CAAC,GAAG9D,WAAW,CAAC,CAAC;EACtC,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAG7F,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC8F,IAAI,EAAEC,OAAO,CAAC,GAAG/F,QAAQ,CAAC,cAAc,CAAC;EAChD,MAAMgG,eAAe,GAAIC,KAAmB,IAAK;IAChDxC,uBAAuB,CAAC,IAAI,CAAC;IAC7BV,qBAAqB,CAAC,IAAI,CAAC;IAC3Ba,mBAAmB,CAAC,KAAK,CAAC;IAC1BG,cAAc,CAAC,IAAI,CAAC;IACpBD,cAAc,CAAC,KAAK,CAAC;IACrB,IAAIoC,SAAS,GAAG,EAAE;IAClB3D,WAAW,GAAG,IAAI;IAClB,IACC0D,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,IAAI,cAAc,IAC/CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,KAAK,SAAS,IAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,KAAK,SAAS,IAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,KAAK,MAAM,IACxCH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,KAAK,WAAW,EAC5C;MACD,IACCH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,KAAK,SAAS,IAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,KAAK,SAAS,IAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ,IAC1C1C,oBAAoB,KAAK,SAAS,IAClCA,oBAAoB,KAAK,QAAQ,IACjCA,oBAAoB,KAAK,SAAS,EACjC;QACDH,cAAc,CAAC,IAAI,CAAC;QACpBC,kBAAkB,CAAC,IAAI,CAAC;QACxB,IAAI6C,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAqB;QAChF,MAAMC,WAAW,GAAGF,QAAQ,CAACG,IAAI;;QAEjC;QACAD,WAAW,CAACE,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;QAE/C,IAAI,CAACN,QAAQ,EAAE;UACdA,QAAQ,GAAGC,QAAQ,CAACM,aAAa,CAAC,OAAO,CAAC;UAC1CP,QAAQ,CAACQ,EAAE,GAAG,oBAAoB;;UAElC;UACA,IAAIC,MAAM,GAAG;AAClB;AACA;AACA;AACA;AACA;AACA,MAAM;UAEDT,QAAQ,CAACU,SAAS,GAAGD,MAAM;UAC3BR,QAAQ,CAACU,IAAI,CAACC,WAAW,CAACZ,QAAQ,CAAC;QACpC;MACD;MACAH,SAAS,GAAG,GAAGD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiB,SAAS,EAAE;MACjC,IAAIhB,SAAS,KAAKiB,MAAM,CAACC,QAAQ,CAACC,IAAI,EAAE;QACvCpE,iBAAiB,CAACgD,KAAK,CAACqB,OAAO,CAAC;QAChCH,MAAM,CAACI,IAAI,CAACrB,SAAS,CAAC;MACvB,CAAC,MAAM;QACNjD,iBAAiB,CAACgD,KAAK,CAACqB,OAAO,CAAC;MACjC;MAEA;IACD,CAAC,MAAM,IAAIrB,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,IAAI,QAAQ,IAAI1C,oBAAoB,KAAK,QAAQ,EAAE;MAC1F;MACAT,iBAAiB,CAACgD,KAAK,CAACqB,OAAO,CAAC;MAChChE,cAAc,CAAC,IAAI,CAAC;IACrB;IACA,IAAI4C,SAAS,EAAE;MACd;MACAiB,MAAM,CAACI,IAAI,CAACrB,SAAS,CAAC;IACvB;EACD,CAAC;EAED,MAAMsB,eAAe,GAAIC,YAA0B,IAAK;IACvD9C,wBAAwB,CAAC8C,YAAY,CAAC;IACtChD,oBAAoB,CAAC,IAAI,CAAC;EAC3B,CAAC;EACD,MAAMiD,wBAAwB,GAAIC,OAAe,IAAK;IACrD9C,kBAAkB,CAAC8C,OAAO,CAAC;IAC3BxC,aAAa,CAAC,IAAI,CAAC;EACpB,CAAC;EACD,MAAMyC,aAAa,GAAIC,KAA0B,IAAK;IACrD,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;MAC1BC,YAAY,CAAC,CAAC;IACf;EACD,CAAC;EACD,MAAMC,OAAqB,GAAG,CAC7B;IACCC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE7E,SAAS,CAAC,MAAM,CAAC;IAC7B;IACA8E,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE;EACZ,CAAC,EACD;IACCH,KAAK,EAAE,aAAa;IACpBC,UAAU,EAAE7E,SAAS,CAAC,aAAa,CAAC;IACpC;IACA8E,QAAQ,EAAE,IAAI;IACdE,UAAU,EAAGC,MAA4B,iBACxClG,OAAA;MAAAmG,QAAA,GAAM,GAAC,EAAC,GAAGzG,cAAc,CAACwG,MAAM,CAACE,GAAG,CAACC,WAAW,EAAE,YAAY,CAAC,EAAE;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACxE;IACDT,SAAS,EAAE;EACZ,CAAC,EACD;IACCH,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE7E,SAAS,CAAC,SAAS,CAAC;IAChC;IACA8E,QAAQ,EAAE,IAAI;IACdW,QAAQ,EAAE,KAAK;IACfT,UAAU,EAAGC,MAA4B,iBACxClG,OAAA,CAAAE,SAAA;MAAAiG,QAAA,EACG7C,KAAK,IAAI,IAAI,IAAIA,KAAK,IAAI,CAAC,eAAe,EAAC,QAAQ,CAAC,CAACqD,IAAI,CAACC,IAAI,IAAItD,KAAK,CAACuD,QAAQ,CAACD,IAAI,CAAC,CAAC,iBAAG5G,OAAA,CAAAE,SAAA;QAAAiG,QAAA,gBAC3FnG,OAAA,CAAC1B,OAAO;UACPwI,KAAK;UACLvG,KAAK,EAAEU,SAAS,CAAC,MAAM,CAAE;UAAAkF,QAAA,eAEzBnG,OAAA,CAAC7B,UAAU;YAAC4I,OAAO,EAAEA,CAAA,KAAMnD,eAAe,CAACsC,MAAM,CAACE,GAAG,CAAE;YAAAD,QAAA,eACtDnG,OAAA;cACCgH,uBAAuB,EAAE;gBAAEC,MAAM,EAAEhI;cAAa,CAAE;cAClDiI,KAAK,EAAE;gBAAEC,IAAI,EAAE;cAAI;YAAE;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEXzG,OAAA,CAAC1B,OAAO;UACPwI,KAAK;UACLvG,KAAK,EAAEU,SAAS,CAAC,OAAO,CAAE;UAAAkF,QAAA,eAE1BnG,OAAA,CAAC7B,UAAU;YAAC4I,OAAO,EAAEA,CAAA,KAAM3B,eAAe,CAACc,MAAM,CAACE,GAAG,CAAE;YAAAD,QAAA,eACtDnG,OAAA;cACCgH,uBAAuB,EAAE;gBAAEC,MAAM,EAAE/H;cAAa,CAAE;cAClDgI,KAAK,EAAE;gBAAEC,IAAI,EAAE;cAAI;YAAE;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACVzG,OAAA,CAAC1B,OAAO;UACPwI,KAAK;UACLvG,KAAK,EAAEU,SAAS,CAAC,QAAQ,CAAE;UAAAkF,QAAA,eAE3BnG,OAAA,CAAC7B,UAAU;YACV4I,OAAO,EAAEA,CAAA,KAAM;cACdzB,wBAAwB,CAACY,MAAM,CAACE,GAAG,CAAClB,OAAO,CAAC;cAC5CvC,oBAAoB,CAACuD,MAAM,CAACE,GAAG,CAACgB,IAAI,CAAC;cACrCvE,oBAAoB,CAACqD,MAAM,CAACE,GAAG,CAACrC,SAAS,CAAC;YAC3C,CAAE;YAAAoC,QAAA,eAEFnG,OAAA;cACCgH,uBAAuB,EAAE;gBAAEC,MAAM,EAAE9H;cAAe,CAAE;cACpD+H,KAAK,EAAE;gBAAEC,IAAI,EAAE;cAAI;YAAE;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA,eACR;IAAC,gBAEH,CACF;IACDT,SAAS,EAAE;EACZ,CAAC,CACD;EAED,MAAMqB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IAAA,IAAAC,aAAA;IACtC,MAAM;MAAEnE,IAAI;MAAEC;IAAS,CAAC,GAAGH,eAAe;IAC1C,MAAMsE,MAAM,GAAGpE,IAAI,GAAGC,QAAQ;IAC9B,MAAMoE,YAAY,GAAG1F,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAGA,SAAS,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO;IAExF,MAAM2F,OAAO,GAAG,CACf;MACCC,SAAS,EAAE,WAAW;MACtBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAEtH,KAAK,KAAK,eAAe,GAAG,MAAM,GAAGA,KAAK;MACjDuH,aAAa,EAAE;IAChB,CAAC,EACD;MACCJ,SAAS,EAAE,aAAa;MACxBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAEL,YAAY;MACnBM,aAAa,EAAE;IAChB,CAAC,EACD;MACCJ,SAAS,EAAE,MAAM;MACjBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAE7F,WAAW;MAClB8F,aAAa,EAAE;IAChB,CAAC,EACD;MACCJ,SAAS,EAAE,WAAW;MACtBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAExE,SAAS;MAChByE,aAAa,EAAE;IAChB,CAAC,CACD;IACD,MAAMC,IAAI,GAAG,MAAMhJ,YAAY,CAACwI,MAAM,EAAEnE,QAAQ,EAAEqE,OAAO,EAAE,EAAE,CAAC;IAC9D,MAAMO,WAAW,GAAGD,IAAI,aAAJA,IAAI,wBAAAT,aAAA,GAAJS,IAAI,CAAEE,OAAO,cAAAX,aAAA,uBAAbA,aAAA,CAAeY,GAAG,CAAEC,IAAS,KAAM;MACtD,GAAGA,IAAI;MACP1D,EAAE,EAAE0D,IAAI,CAACjD;IACV,CAAC,CAAC,CAAC;IAEH/C,eAAe,CAAC6F,WAAW,IAAI,EAAE,CAAC;IAClCvE,aAAa,CAACsE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,MAAM,CAAC;EAC5B,CAAC;EAEDvK,SAAS,CAAC,MAAM;IACf,IAAIwC,IAAI,IAAIgD,SAAS,EAAE;MACtBgE,kBAAkB,CAAC,CAAC;IACrB;EACD,CAAC,EAAE,CAACpE,eAAe,EAAEnB,SAAS,EAAEzB,IAAI,EAAEgD,SAAS,CAAC,CAAC;;EAEjD;EACA;EACA;EACA;EACA;;EAEA,MAAMsC,YAAY,GAAGA,CAAA,KAAM;IAC1B0B,kBAAkB,CAAC,CAAC;EACrB,CAAC;EAEDxJ,SAAS,CAAC,MAAM;IACf,IAAImE,WAAW,CAACqG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC9BhB,kBAAkB,CAAC,CAAC;IACrB;EACD,CAAC,EAAE,CAACrF,WAAW,CAAC,CAAC;EACjB,MAAMsG,iBAAiB,GAAGA,CAAA,KAAM;IAC/BrG,cAAc,CAAC,EAAE,CAAC;IAClBoF,kBAAkB,CAAC,CAAC;EACrB,CAAC;EAED,MAAMkB,eAAe,GAAGA,CAAC9C,KAA2B,EAAE+C,QAAgB,KAAK;IAC1EzG,YAAY,CAACyG,QAAQ,CAAC;IACtBtF,kBAAkB,CAAEuF,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEtF,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACvD,CAAC;EACD,MAAMuF,aAAa,GAAG/K,KAAK,CAACgL,WAAW,CAAEzC,MAA4B,IAAK;IACzE,OAAO;MACN0C,GAAG,EAAE1C,MAAM,CAAC2C,cAAc,GAAG,CAAC,GAAG,CAAC;MAClCC,MAAM,EAAE5C,MAAM,CAAC6C,aAAa,GAAG,CAAC,GAAG;IACpC,CAAC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAIxG,eAAe,EAAE;MACpB,IAAI;QACH,MAAMyG,QAAQ,GAAG,MAAMjK,oBAAoB,CAACwD,eAAe,CAAC;QAC5D,IAAIyG,QAAQ,CAACC,OAAO,EAAE;UACrB3F,YAAY,CACX,GAAGb,iBAAiB,IAAIzB,SAAS,CAAC2B,iBAAiB,CAAC,IAAI3B,SAAS,CAAC,sBAAsB,CAAC,EAAE,EAC3F,SACD,CAAC;UACD,MAAMoG,kBAAkB,CAAC,CAAC;QAC3B,CAAC,MAAM;UACN9D,YAAY,CAAC0F,QAAQ,CAACE,YAAY,EAAE,OAAO,CAAC;QAC7C;MACD,CAAC,CAAC,OAAOC,KAAK,EAAE,CAAC;IAClB;IACArG,aAAa,CAAC,KAAK,CAAC;IACpBN,kBAAkB,CAAC,IAAI,CAAC;IACxBE,oBAAoB,CAAC,EAAE,CAAC;EACzB,CAAC;EACD,MAAM0G,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACtC,MAAMhC,kBAAkB,CAAC,CAAC;EAC3B,CAAC;EACD,MAAMiC,cAAc,GAAGA,CAAA,KAAM;IAC5B,MAAMC,SAAS,GAAG,CAACtI,SAAS,CAAC,QAAQ,CAAC,EAAEA,SAAS,CAAC,UAAU,CAAC,EAAEA,SAAS,CAAC,OAAO,CAAC,CAAC;IAClF,MAAMuI,eAAe,GAAGD,SAAS,CAACzH,SAAS,CAAC,IAAItB,UAAU;IAC1D,OAAO,GAAGS,SAAS,CAAC,IAAI,CAAC,IAAIA,SAAS,CAACuI,eAAe,EAAE;MAAEC,YAAY,EAAED;IAAgB,CAAC,CAAC,IAAIvI,SAAS,CAACT,UAAU,EAAE;MAAEiJ,YAAY,EAAE,GAAGjJ,UAAU;IAAI,CAAC,CAAC,EAAE;EAC1J,CAAC;EACD,MAAMkJ,aAAa,GAAGA,CAAA,kBACrB1J,OAAA;IAAKkH,KAAK,EAAE;MAAEyC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE,QAAQ;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAA1D,QAAA,gBAC9EnG,OAAA;MACC8J,SAAS,EAAC,gBAAgB;MAC1B9C,uBAAuB,EAAE;QAAEC,MAAM,EAAE7H;MAAO;IAAE;MAAAkH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eACFzG,OAAA,CAACrB,UAAU;MAACoL,EAAE,EAAE;QAAEC,UAAU,EAAE;MAAM,CAAE;MAAA7D,QAAA,EAAEmD,cAAc,CAAC;IAAC;MAAAhD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClE,CACL;EAED,MAAMwD,gBAAgB,GAAGA,CAAA,KAAM;IAC9B;IACA;IACA;IACA;EAAA,CACA;EAED,oBACCjK,OAAA;IAAKyE,EAAE,EAAC,eAAe;IAAA0B,QAAA,gBACtBnG,OAAA,CAACjC,MAAM;MACNmM,SAAS,EAAE;QACVC,IAAI,EAAE;UACL1F,EAAE,EAAE;QACL,CAAC;QACD2F,QAAQ,EAAE;UACTL,EAAE,EAAE;YACHM,QAAQ,EAAE;UACX;QACD;MACD,CAAE;MACFlF,IAAI,EAAE9E,IAAK;MACXC,OAAO,EAAE2J,gBAAiB;MAC1BK,SAAS;MACTC,QAAQ,EAAC,IAAI;MACbT,SAAS,EAAC,qBAAqB;MAAA3D,QAAA,eAE/BnG,OAAA,CAAChC,aAAa;QAAC8L,SAAS,EAAC,6BAA6B;QAAA3D,QAAA,gBACrDnG,OAAA;UACC8J,SAAS,EAAC,eAAe;UACzBrF,EAAE,EAAC,cAAc;UAAA0B,QAAA,eAEjBnG,OAAA;YACL8J,SAAS,EAAC,OAAO;YACjB5C,KAAK,EAAE;cAAE8C,UAAU,EAAE;YAAiB,CAAE;YAAA7D,QAAA,EAEjClF,SAAS,CAAC,GAAGT,UAAU,EAAE,EAAE;cAAEiJ,YAAY,EAAE,GAAGxI,SAAS,CAACT,UAAU,CAAC;YAAI,CAAC;UAAC;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaG,CAAC,eACNzG,OAAA;UACC8J,SAAS,EAAC,YAAY;UACtBrF,EAAE,EAAC,YAAY;UAAA0B,QAAA,eAEfnG,OAAA;YAAK8J,SAAS,EAAC,kBAAkB;YAAA3D,QAAA,gBAChCnG,OAAA,CAAC/B,SAAS;cACTuM,OAAO,EAAC,UAAU;cAClBC,WAAW,EAAExJ,SAAS,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAGA,SAAS,CAACV,KAAK,CAAE;cAC1DmK,KAAK,EAAE1I,WAAY;cACnB2I,QAAQ,EAAGC,CAAC,IAAK;gBAChB,MAAMpC,QAAQ,GAAGoC,CAAC,CAACC,MAAM,CAACH,KAAK;gBAC/BzI,cAAc,CAACuG,QAAQ,CAAC;gBACxB,IAAIA,QAAQ,KAAK,EAAE,EAAE;kBACpBF,iBAAiB,CAAC,CAAC;gBACpB;cACD,CAAE;cACFwC,SAAS,EAAGF,CAAC,IAAK;gBACjB,IAAIA,CAAC,CAAClF,GAAG,KAAK,OAAO,EAAE;kBACtBC,YAAY,CAAC,CAAC;gBACf;cACD,CAAE;cACFmE,SAAS,EAAC,iBAAiB;cAC3BiB,UAAU,EAAE;gBACXhB,EAAE,EAAE;kBACH,0CAA0C,EAAE;oBAAEiB,WAAW,EAAE;kBAAU,CAAC;kBAAE;kBACxE,gDAAgD,EAAE;oBAAEC,MAAM,EAAE;kBAAoB;gBACjF,CAAC;gBACDC,cAAc,eACblL,OAAA,CAAC9B,cAAc;kBAACmM,QAAQ,EAAC,OAAO;kBAAAlE,QAAA,eAC/BnG,OAAA,CAAC7B,UAAU;oBACV,cAAW,QAAQ;oBACnB4I,OAAO,EAAEA,CAAA,KAAMpB,YAAY,CAAC,CAAE;oBAC9BwF,WAAW,EAAG1F,KAAK,IAAKA,KAAK,CAAC2F,cAAc,CAAC,CAAE;oBAAAjF,QAAA,eAE/CnG,OAAA,CAACnB,UAAU;sBAAAyH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAChB;gBACD4E,YAAY,EAAErJ,WAAW,iBACxBhC,OAAA,CAAC9B,cAAc;kBAACmM,QAAQ,EAAC,KAAK;kBAAAlE,QAAA,eAC7BnG,OAAA,CAAC7B,UAAU;oBACV,cAAW,OAAO;oBAClB4I,OAAO,EAAEA,CAAA,KAAM;sBACd9E,cAAc,CAAC,EAAE,CAAC;sBAClBqG,iBAAiB,CAAC,CAAC;oBACpB,CAAE;oBAAAnC,QAAA,eAEFnG,OAAA,CAAClB,SAAS;sBAACiL,EAAE,EAAE;wBAAE5C,IAAI,EAAE;sBAAM;oBAAE;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAElB;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFzG,OAAA;cAAK8J,SAAS,EAAC,kBAAkB;cAAA3D,QAAA,eAChCnG,OAAA;gBACC+G,OAAO,EAAEA,CAAA,KAAMtG,UAAU,CAACD,UAAU,CAAE;gBACtCsJ,SAAS,EAAC,oBAAoB;gBAC9BwB,QAAQ,EAAEtI,QAAQ,CAACuI,iBAAiB,CAAC,CAAC,IAAE,OAAO,GAAGjI,KAAK,IAAE,IAAI,IAAI,CAACA,KAAK,IAAI,CAAC,CAAC,eAAe,EAAE,QAAQ,CAAC,CAACqD,IAAI,CAACC,IAAI,IAAItD,KAAK,CAACuD,QAAQ,CAACD,IAAI,CAAC,CAAC,GAAE,KAAM;gBAAAT,QAAA,gBAElJnG,OAAA,CAACV,OAAO;kBAAAgH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACXzG,OAAA;kBAAAmG,QAAA,EAAO,GAAGlF,SAAS,CAAC,QAAQ,CAAC,IAAIA,SAAS,CAACT,UAAU,CAAC;gBAAE;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENzG,OAAA;UAAK8J,SAAS,EAAC,sBAAsB;UAAA3D,QAAA,eACpCnG,OAAA,CAAC3B,IAAI;YACJqM,KAAK,EAAE5I,SAAU;YACjB6I,QAAQ,EAAEpC,eAAgB;YAAApC,QAAA,gBAE1BnG,OAAA,CAAC5B,GAAG;cACHoN,KAAK,EAAEvK,SAAS,CAAC,QAAQ,CAAE;cAC3B8I,EAAE,EAAE;gBACH0B,eAAe,EAAE,oBAAoB;gBACrCR,MAAM,EAAE,oBAAoB;gBAC5BS,KAAK,EAAE,oBAAoB;gBAC3BC,QAAQ,EAAE;cACX;YAAE;cAAArF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFzG,OAAA,CAAC5B,GAAG;cACHoN,KAAK,EAAEvK,SAAS,CAAC,UAAU,CAAE;cAC7B8I,EAAE,EAAE;gBACH0B,eAAe,EAAE,oBAAoB;gBACrCR,MAAM,EAAE,oBAAoB;gBAC5BS,KAAK,EAAE,oBAAoB;gBAC3BC,QAAQ,EAAE;cACX;YAAE;cAAArF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFzG,OAAA,CAAC5B,GAAG;cACHoN,KAAK,EAAEvK,SAAS,CAAC,OAAO,CAAE;cAC1B8I,EAAE,EAAE;gBACH0B,eAAe,EAAE,oBAAoB;gBACrCR,MAAM,EAAE,oBAAoB;gBAC5BS,KAAK,EAAE,oBAAoB;gBAC3BC,QAAQ,EAAE;cACX;YAAE;cAAArF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzG,OAAA;UAAK8J,SAAS,EAAC,eAAe;UAAA3D,QAAA,eAC7BnG,OAAA,CAACpB,QAAQ;YACRgN,IAAI,EAAE1J,YAAa;YACnB0D,OAAO,EAAEA,OAAQ;YACjBiG,QAAQ,EAAGzF,GAAG,IAAKA,GAAG,CAAClB,OAAQ;YAC/BwD,aAAa,EAAEA,aAAc;YAC7BoD,UAAU;YACV7I,eAAe,EAAEA,eAAgB;YACjC8I,cAAc,EAAC,QAAQ;YACvBC,uBAAuB,EAAE9I,kBAAmB;YAC5C+I,QAAQ,EAAEzI,UAAW;YACrB0I,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;YACnCC,UAAU,EAAE;cACXC,kBAAkB,EAAE;gBACnBC,gBAAgB,EAAEpL,SAAS,CAAC,kBAAkB;cAC/C,CAAC;cACDqL,WAAW,EAAEhD,cAAc,CAAC;YAC7B,CAAE;YACFiD,iBAAiB;YACjBC,0BAA0B;YAC1B1C,SAAS,EAAC,eAAe;YACzB2C,KAAK,EAAE;cACNC,aAAa,EAAEhD,aAAa,CAAE;YAC/B,CAAE;YACFK,EAAE,EAAE;cACH,oBAAoB,EAAE;gBACrBQ,QAAQ,EAAE,mBAAmB;gBAC7B,kBAAkB,EAAE;gBACpB;gBACA;cACD,CAAC;cACD,qBAAqB,EAAE;gBACtBoC,OAAO,EAAE;cACV,CAAC;cACD,6BAA6B,EAAE;gBAC9BhD,OAAO,EAAE,iBAAiB;gBAC1BC,UAAU,EAAE;cACb,CAAC;cACD,oCAAoC,EAAE;gBACrCqB,MAAM,EAAE,iBAAiB;gBACzBS,KAAK,EAAE,oBAAoB;gBAC3BD,eAAe,EAAE,oBAAoB;gBACrC,SAAS,EAAE;kBACVA,eAAe,EAAE,oBAAoB,CAAE;gBACxC;cACD,CAAC;cACD,6BAA6B,EAAE;gBAC9BmB,UAAU,EAAE,6CAA6C;gBACzDD,OAAO,EAAE,mBAAmB;gBAC5BE,WAAW,EAAE,mBAAmB;gBAChCC,MAAM,EAAE;cACT,CAAC;cACD,kCAAkC,EAAE;gBACnC9C,UAAU,EAAE;cACb,CAAC;cACD,uBAAuB,EAAE;gBACxByB,eAAe,EAAE,uBAAuB;gBACxC,kBAAkB,EAAE;cACrB,CAAC;cACD,gCAAgC,EAAE;gBACjCA,eAAe,EAAE,uBAAuB;gBACxC9B,OAAO,EAAE;cACV;YACD,CAAE;YACFoD,SAAS,EAAE;UAAG;YAAAzG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eACTzG,OAAA,CAACjC,MAAM;MACNoH,IAAI,EAAErC,UAAW;MACjBxC,OAAO,EAAEA,CAAA,KAAMyC,aAAa,CAAC,KAAK,CAAE;MACpCiK,UAAU,EAAE;QACX9F,KAAK,EAAE;UACN+F,YAAY,EAAE,KAAK;UACnB1C,QAAQ,EAAE,OAAO;UACjB2C,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE,OAAO;UAClBC,SAAS,EAAE;QACZ;MACD,CAAE;MAAAjH,QAAA,gBAEFnG,OAAA,CAACzB,WAAW;QAACwL,EAAE,EAAE;UAAE4C,OAAO,EAAE;QAAE,CAAE;QAAAxG,QAAA,gBAC/BnG,OAAA;UAAKkH,KAAK,EAAE;YAAEyC,OAAO,EAAE,MAAM;YAAE0D,cAAc,EAAE,QAAQ;YAAEV,OAAO,EAAE;UAAO,CAAE;UAAAxG,QAAA,eAC1EnG,OAAA;YACCkH,KAAK,EAAE;cACNuE,eAAe,EAAE,SAAS;cAC1BwB,YAAY,EAAE,KAAK;cACnBK,KAAK,EAAE,MAAM;cACbR,MAAM,EAAE,MAAM;cACdnD,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByD,cAAc,EAAE;YACjB,CAAE;YAAAlH,QAAA,eAEFnG,OAAA,CAACX,yBAAyB;cAAC0K,EAAE,EAAE;gBAAE2B,KAAK,EAAE,SAAS;gBAAEoB,MAAM,EAAE,MAAM;gBAAEQ,KAAK,EAAE;cAAO;YAAE;cAAAhH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNzG,OAAA,CAACrB,UAAU;UAACoL,EAAE,EAAE;YAAE4B,QAAQ,EAAE,iBAAiB;YAAE3B,UAAU,EAAE,GAAG;YAAE2C,OAAO,EAAE;UAAS,CAAE;UAAAxG,QAAA,GAClFlF,SAAS,CAAC,QAAQ,CAAC,EAAC,GAAC,EAACA,SAAS,CAAC2B,iBAAiB,CAAC;QAAA;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEdzG,OAAA,CAAChC,aAAa;QAAC+L,EAAE,EAAE;UAAE4C,OAAO,EAAE;QAAkB,CAAE;QAAAxG,QAAA,eACjDnG,OAAA,CAACxB,iBAAiB;UAAC0I,KAAK,EAAE;YAAEyE,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE;UAAO,CAAE;UAAAvF,QAAA,EAC5DlF,SAAS,CAAC,GAAGA,SAAS,CAAC,KAAK,CAAC,IAAIyB,iBAAiB,IAAIzB,SAAS,CAAC,wCAAwC,CAAC,EAAE;QAAC;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEhBzG,OAAA,CAACvB,aAAa;QAACsL,EAAE,EAAE;UAAEsD,cAAc,EAAE,eAAe;UAAEE,SAAS,EAAE;QAAgC,CAAE;QAAApH,QAAA,gBAClGnG,OAAA,CAACtB,MAAM;UACNqI,OAAO,EAAEA,CAAA,KAAMhE,aAAa,CAAC,KAAK,CAAE;UACpCgH,EAAE,EAAE;YACH2B,KAAK,EAAE,SAAS;YAChBT,MAAM,EAAE,mBAAmB;YAC3BgC,YAAY,EAAE,KAAK;YACnBO,aAAa,EAAE,YAAY;YAC3Bb,OAAO,EAAE,uBAAuB;YAChCc,UAAU,EAAE;UACb,CAAE;UAAAtH,QAAA,EAEDlF,SAAS,CAAC,QAAQ;QAAC;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACTzG,OAAA,CAACtB,MAAM;UACNqI,OAAO,EAAEiC,YAAa;UACtBe,EAAE,EAAE;YACH0B,eAAe,EAAE,oBAAoB;YACrCC,KAAK,EAAE,MAAM;YACbuB,YAAY,EAAE,KAAK;YACnBO,aAAa,EAAE,YAAY;YAC3Bb,OAAO,EAAE,uBAAuB;YAChCc,UAAU,EAAE;YACZ;YACA;YACA;UACD,CAAE;UAAAtH,QAAA,EAEDlF,SAAS,CAAC,QAAQ;QAAC;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,EACRrE,iBAAiB,IAAIE,qBAAqB,iBAC1CtC,OAAA,CAACT,sBAAsB;MACtB4F,IAAI,EAAE/C,iBAAkB;MACxBsL,WAAW,EAAEA,CAAA,KAAMrL,oBAAoB,CAAC,KAAK,CAAE;MAC/CsL,WAAW,EAAErL,qBAAsB;MACnCsL,cAAc,EAAEvE,kBAAmB;MACnC3F,IAAI,EAAEA;IAAK;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CACD;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAER,CAAC;AAAC7F,EAAA,CAtmBIR,UAAqC;EAAA,QAEIR,cAAc,EACnCC,cAAc,EAcnCF,cAAc,EAUDG,YAAY,EAMJL,WAAW;AAAA;AAAAoO,EAAA,GAjC/BzN,UAAqC;AAumB3C,SAASD,WAAW;AACpB,eAAeC,UAAU;AAAC,IAAAyN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}