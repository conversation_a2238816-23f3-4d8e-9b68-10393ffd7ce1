{"ast": null, "code": "import React,{useState}from'react';import Dialog from'@mui/material/Dialog';import DialogTitle from'@mui/material/DialogTitle';import DialogContent from'@mui/material/DialogContent';import DialogActions from'@mui/material/DialogActions';import Button from'@mui/material/Button';import TextareaAutosize from'@mui/material/TextareaAutosize';import useDrawerStore from'../../store/drawerStore';import{cancelTraining}from'../../services/ScrapingService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AgentAdditionalContextPopup=_ref=>{let{open,onClose,onSaved,onCancel}=_ref;const{agentAdditionalContext,setAgentAdditionalContext,setIsAgentTraining}=useDrawerStore(state=>state);const[saving,setSaving]=useState(false);const[error,setError]=useState(null);const handleSave=async()=>{setSaving(true);setError(null);try{// Process the save - the actual agent training is handled in the parent component\nonSaved();}catch(e){setError('Failed to save agent.');}finally{setSaving(false);}};const handleCancel=async()=>{setSaving(true);setError(null);try{// Cancel the training process (stops scraping and clears data)\nawait cancelTraining();// Update the training state\nsetIsAgentTraining(false);// Call the onCancel callback if provided, otherwise use onClose\nif(onCancel){onCancel();}else{onClose();}}catch(e){setError('Failed to cancel training.');console.error('Error canceling training:',e);}finally{setSaving(false);}};return/*#__PURE__*/_jsx(\"div\",{className:\"AgentAdditionalContextPopup\",id:\"AgentAdditionalContextPopup\",children:/*#__PURE__*/_jsxs(Dialog,{open:open,onClose:onClose,maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Agent Additional Context\"}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(TextareaAutosize,{id:\"textareaadditionalcontextpopup\",minRows:5,style:{width:'100%',marginTop:16},placeholder:\"Enter additional context for the agent...\",value:agentAdditionalContext,onChange:e=>setAgentAdditionalContext(e.target.value)}),error&&/*#__PURE__*/_jsx(\"div\",{style:{color:'red',marginTop:8},children:error})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{id:\"textareaadditionalcontextpopup\",onClick:handleCancel,disabled:saving,children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{id:\"textareaadditionalcontextpopup\",onClick:handleSave,disabled:saving,variant:\"contained\",color:\"primary\",children:\"Save\"})]})]})});};export default AgentAdditionalContextPopup;", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextareaAutosize", "useDrawerStore", "cancelTraining", "jsx", "_jsx", "jsxs", "_jsxs", "AgentAdditionalContextPopup", "_ref", "open", "onClose", "onSaved", "onCancel", "agentAdditionalContext", "setAgentAdditionalContext", "setIsAgentTraining", "state", "saving", "setSaving", "error", "setError", "handleSave", "e", "handleCancel", "console", "className", "id", "children", "max<PERSON><PERSON><PERSON>", "fullWidth", "minRows", "style", "width", "marginTop", "placeholder", "value", "onChange", "target", "color", "onClick", "disabled", "variant"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/AI/AgentAdditionalContextPopup.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport Dialog from '@mui/material/Dialog';\r\nimport DialogTitle from '@mui/material/DialogTitle';\r\nimport DialogContent from '@mui/material/DialogContent';\r\nimport DialogActions from '@mui/material/DialogActions';\r\nimport Button from '@mui/material/Button';\r\nimport TextareaAutosize from '@mui/material/TextareaAutosize';\r\nimport useDrawerStore, { DrawerState } from '../../store/drawerStore';\r\nimport { cancelTraining } from '../../services/ScrapingService';\r\n\r\ninterface AgentAdditionalContextPopupProps {\r\n  open: boolean;\r\n  onClose: () => void;\r\n  onSaved: () => void;\r\n  onCancel?: () => void;\r\n}\r\n\r\nconst AgentAdditionalContextPopup: React.FC<AgentAdditionalContextPopupProps> = ({ open, onClose, onSaved, onCancel }) => {\r\n  const {\r\n    agentAdditionalContext,\r\n    setAgentAdditionalContext,\r\n    setIsAgentTraining\r\n  } = useDrawerStore((state: DrawerState) => state);\r\n  const [saving, setSaving] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const handleSave = async () => {\r\n    setSaving(true);\r\n    setError(null);\r\n    try {\r\n      // Process the save - the actual agent training is handled in the parent component\r\n      onSaved();\r\n    } catch (e) {\r\n      setError('Failed to save agent.');\r\n    } finally {\r\n      setSaving(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = async () => {\r\n    setSaving(true);\r\n    setError(null);\r\n    try {\r\n      // Cancel the training process (stops scraping and clears data)\r\n      await cancelTraining();\r\n\r\n      // Update the training state\r\n      setIsAgentTraining(false);\r\n\r\n      // Call the onCancel callback if provided, otherwise use onClose\r\n      if (onCancel) {\r\n        onCancel();\r\n      } else {\r\n        onClose();\r\n      }\r\n    } catch (e) {\r\n      setError('Failed to cancel training.');\r\n      console.error('Error canceling training:', e);\r\n    } finally {\r\n      setSaving(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className='AgentAdditionalContextPopup' id='AgentAdditionalContextPopup'>\r\n    <Dialog open={open} onClose={onClose} maxWidth=\"sm\" fullWidth>\r\n      <DialogTitle>Agent Additional Context</DialogTitle>\r\n      <DialogContent>\r\n        <TextareaAutosize\r\n        id=\"textareaadditionalcontextpopup\"\r\n          minRows={5}\r\n          style={{ width: '100%', marginTop: 16 }}\r\n          placeholder=\"Enter additional context for the agent...\"\r\n          value={agentAdditionalContext}\r\n          onChange={e => setAgentAdditionalContext(e.target.value)}\r\n        />\r\n        {error && <div style={{ color: 'red', marginTop: 8 }}>{error}</div>}\r\n      </DialogContent>\r\n      <DialogActions>\r\n        <Button id=\"textareaadditionalcontextpopup\" onClick={handleCancel} disabled={saving}>Cancel</Button>\r\n        <Button id=\"textareaadditionalcontextpopup\" onClick={handleSave} disabled={saving} variant=\"contained\" color=\"primary\">Save</Button>\r\n      </DialogActions>\r\n    </Dialog>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AgentAdditionalContextPopup; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CACnD,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,gBAAgB,KAAM,gCAAgC,CAC7D,MAAO,CAAAC,cAAc,KAAuB,yBAAyB,CACrE,OAASC,cAAc,KAAQ,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAShE,KAAM,CAAAC,2BAAuE,CAAGC,IAAA,EAA0C,IAAzC,CAAEC,IAAI,CAAEC,OAAO,CAAEC,OAAO,CAAEC,QAAS,CAAC,CAAAJ,IAAA,CACnH,KAAM,CACJK,sBAAsB,CACtBC,yBAAyB,CACzBC,kBACF,CAAC,CAAGd,cAAc,CAAEe,KAAkB,EAAKA,KAAK,CAAC,CACjD,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAACyB,KAAK,CAAEC,QAAQ,CAAC,CAAG1B,QAAQ,CAAgB,IAAI,CAAC,CAEvD,KAAM,CAAA2B,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7BH,SAAS,CAAC,IAAI,CAAC,CACfE,QAAQ,CAAC,IAAI,CAAC,CACd,GAAI,CACF;AACAT,OAAO,CAAC,CAAC,CACX,CAAE,MAAOW,CAAC,CAAE,CACVF,QAAQ,CAAC,uBAAuB,CAAC,CACnC,CAAC,OAAS,CACRF,SAAS,CAAC,KAAK,CAAC,CAClB,CACF,CAAC,CAED,KAAM,CAAAK,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/BL,SAAS,CAAC,IAAI,CAAC,CACfE,QAAQ,CAAC,IAAI,CAAC,CACd,GAAI,CACF;AACA,KAAM,CAAAlB,cAAc,CAAC,CAAC,CAEtB;AACAa,kBAAkB,CAAC,KAAK,CAAC,CAEzB;AACA,GAAIH,QAAQ,CAAE,CACZA,QAAQ,CAAC,CAAC,CACZ,CAAC,IAAM,CACLF,OAAO,CAAC,CAAC,CACX,CACF,CAAE,MAAOY,CAAC,CAAE,CACVF,QAAQ,CAAC,4BAA4B,CAAC,CACtCI,OAAO,CAACL,KAAK,CAAC,2BAA2B,CAAEG,CAAC,CAAC,CAC/C,CAAC,OAAS,CACRJ,SAAS,CAAC,KAAK,CAAC,CAClB,CACF,CAAC,CAED,mBACEd,IAAA,QAAKqB,SAAS,CAAC,6BAA6B,CAACC,EAAE,CAAC,6BAA6B,CAAAC,QAAA,cAC7ErB,KAAA,CAACX,MAAM,EAACc,IAAI,CAAEA,IAAK,CAACC,OAAO,CAAEA,OAAQ,CAACkB,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAF,QAAA,eAC3DvB,IAAA,CAACR,WAAW,EAAA+B,QAAA,CAAC,0BAAwB,CAAa,CAAC,cACnDrB,KAAA,CAACT,aAAa,EAAA8B,QAAA,eACZvB,IAAA,CAACJ,gBAAgB,EACjB0B,EAAE,CAAC,gCAAgC,CACjCI,OAAO,CAAE,CAAE,CACXC,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAM,CAAEC,SAAS,CAAE,EAAG,CAAE,CACxCC,WAAW,CAAC,2CAA2C,CACvDC,KAAK,CAAEtB,sBAAuB,CAC9BuB,QAAQ,CAAEd,CAAC,EAAIR,yBAAyB,CAACQ,CAAC,CAACe,MAAM,CAACF,KAAK,CAAE,CAC1D,CAAC,CACDhB,KAAK,eAAIf,IAAA,QAAK2B,KAAK,CAAE,CAAEO,KAAK,CAAE,KAAK,CAAEL,SAAS,CAAE,CAAE,CAAE,CAAAN,QAAA,CAAER,KAAK,CAAM,CAAC,EACtD,CAAC,cAChBb,KAAA,CAACR,aAAa,EAAA6B,QAAA,eACZvB,IAAA,CAACL,MAAM,EAAC2B,EAAE,CAAC,gCAAgC,CAACa,OAAO,CAAEhB,YAAa,CAACiB,QAAQ,CAAEvB,MAAO,CAAAU,QAAA,CAAC,QAAM,CAAQ,CAAC,cACpGvB,IAAA,CAACL,MAAM,EAAC2B,EAAE,CAAC,gCAAgC,CAACa,OAAO,CAAElB,UAAW,CAACmB,QAAQ,CAAEvB,MAAO,CAACwB,OAAO,CAAC,WAAW,CAACH,KAAK,CAAC,SAAS,CAAAX,QAAA,CAAC,MAAI,CAAQ,CAAC,EACvH,CAAC,EACV,CAAC,CACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAApB,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}