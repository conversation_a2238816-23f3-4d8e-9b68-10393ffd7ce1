{"ast": null, "code": "import React from'react';import'./EnableAIButton.css';import{Grid,Button,TextField,FormControl}from'@mui/material';import useDrawerStore from'../../store/drawerStore';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AgentTrainingFields=_ref=>{let{setShowtrainingAgentFields,showtrainingAgentFields,handleEnableAgentTraining}=_ref;const{agentName,agentDescription,agentUrl,setAgentName,setAgentDescription,setAgentUrl}=useDrawerStore(state=>state);const handleClick=()=>{setShowtrainingAgentFields(false);handleEnableAgentTraining();};return/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-modal-overlay\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-accountcreatepopup qadpt-agent-training-popup\",style:{marginTop:\"-106px\",height:\"489px\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title-sec\",children:/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:\"Create Agent\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-accountcreatefield\",children:/*#__PURE__*/_jsx(Grid,{container:true,spacing:2,children:/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,children:[/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,required:true,style:{marginBottom:\"20px\"},children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"account-name\",style:{textAlign:\"left\"},children:\"Agent Name\"}),/*#__PURE__*/_jsx(TextField,{style:{marginTop:\"10px\"},id:\"account-name\",name:\"AccountName\",required:true,value:agentName,onChange:e=>{setAgentName(e.target.value);},variant:\"outlined\",inputProps:{maxLength:50},className:\"qadpt-acctfield\"})]}),/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,required:true,style:{marginBottom:\"20px\"},children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"agent-description\",style:{textAlign:\"left\"},children:\"Description\"}),/*#__PURE__*/_jsx(\"textarea\",{style:{marginTop:\"10px\",minHeight:\"60px\",resize:\"vertical\"},id:\"agent-description\",name:\"DomainUrl\",required:true,value:agentDescription,onChange:e=>setAgentDescription(e.target.value),maxLength:200,className:\"qadpt-acctfield\"})]}),/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,required:true,style:{marginBottom:\"20px\"},children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"agent-url\",style:{textAlign:\"left\"},children:\"URL\"}),/*#__PURE__*/_jsx(TextField,{style:{marginTop:\"10px\"},id:\"agent-url\",name:\"AgentUrl\",required:true,value:agentUrl,onChange:e=>setAgentUrl(e.target.value),slotProps:{htmlInput:{maxLength:500}},className:\"qadpt-acctfield\",placeholder:\"Enter the URL for the agent...\"})]})]})})}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:\"-19px\"},children:/*#__PURE__*/_jsx(Button,{onClick:handleClick,sx:{backgroundColor:\"#04417F\",color:\"#FFF\",borderRadius:\"8px\",textTransform:\"capitalize\",fontSize:\"16px\",fontWeight:\"400\",minWidth:\"120px\",\"&:hover\":{backgroundColor:\"#0776E5\",transform:\"translateY(-1px)\",boxShadow:\"0 4px 12px rgba(4, 65, 127, 0.3)\"},transition:\"all 0.3s ease\"},children:\"Start Training\"})})]})})});};export default AgentTrainingFields;", "map": {"version": 3, "names": ["React", "Grid", "<PERSON><PERSON>", "TextField", "FormControl", "useDrawerStore", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Agent<PERSON><PERSON><PERSON><PERSON><PERSON>s", "_ref", "setShowtrainingAgentFields", "showtrainingAgentFields", "handleEnableAgentTraining", "<PERSON><PERSON><PERSON>", "agentDescription", "agentUrl", "setAgentName", "setAgentDescription", "setAgentUrl", "state", "handleClick", "children", "className", "style", "marginTop", "height", "container", "spacing", "item", "xs", "fullWidth", "required", "marginBottom", "htmlFor", "textAlign", "id", "name", "value", "onChange", "e", "target", "variant", "inputProps", "max<PERSON><PERSON><PERSON>", "minHeight", "resize", "slotProps", "htmlInput", "placeholder", "onClick", "sx", "backgroundColor", "color", "borderRadius", "textTransform", "fontSize", "fontWeight", "min<PERSON><PERSON><PERSON>", "transform", "boxShadow", "transition"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/AI/AgentTraining.tsx"], "sourcesContent": ["import React, { useContext,useState } from 'react';\r\nimport { stopScraping } from '../../services/ScrapingService';\r\nimport './EnableAIButton.css';\r\nimport { AccountContext } from '../../components/login/AccountContext';\r\nimport {\r\n    Dialog,\r\n\tDialogContent,\r\n\tInputAdornment,\r\n\tDialogContentText,\r\n    Grid,\r\n    Box,\r\n    Button,\r\n    Container,\r\n    TextField,\r\n    DialogTitle,\r\n    DialogActions,\r\n    FormControl,\r\n    InputLabel,\r\n    Select,\r\n    MenuItem,\r\n    IconButton,\r\n    Tooltip,\r\n    Alert,\r\n    Chip,\r\n    TextareaAutosize\r\n} from '@mui/material';\r\nimport useDrawerStore, { DrawerState } from '../../store/drawerStore';\r\nconst AgentTrainingFields = ({ setShowtrainingAgentFields, showtrainingAgentFields, handleEnableAgentTraining }: { setShowtrainingAgentFields: any; showtrainingAgentFields: any; handleEnableAgentTraining:any}) => {\r\n\r\n    const {\r\n        agentName,\r\n        agentDescription,\r\n        agentUrl,\r\n        setAgentName,\r\n        setAgentDescription,\r\n        setAgentUrl\r\n    } = useDrawerStore((state: DrawerState) => state);\r\n\r\n    const handleClick = () =>\r\n    {\r\n        setShowtrainingAgentFields(false);\r\n        handleEnableAgentTraining();\r\n\r\n        }\r\n    return (\r\n      <>\r\n\r\n<div className=\"qadpt-modal-overlay\">\r\n<div className=\"qadpt-accountcreatepopup qadpt-agent-training-popup\" style={{marginTop:\"-106px\",height:\"489px\"}}>\r\n\t\t\t\t  <div className=\"qadpt-title-sec\">\r\n\t\t\t\t\t<div className=\"qadpt-title\">Create Agent</div>\r\n\r\n\t\t\t\t  </div>\r\n\t\t\t\t  <div className=\"qadpt-accountcreatefield\">\r\n\t\t\t\t\t<Grid container spacing={2}>\r\n\t\t\t\t\t  <Grid item xs={12}>\r\n\t\t\t\t\t\t<FormControl fullWidth required style={{marginBottom:\"20px\"}}>\r\n\t\t\t\t\t\t  <label htmlFor=\"account-name\" style={{textAlign:\"left\"}}>Agent Name</label>\r\n\t\t\t\t\t\t  <TextField\r\n                          style={{marginTop:\"10px\"}}\r\n\t\t\t\t\t\t\tid=\"account-name\"\r\n\t\t\t\t\t\t\tname=\"AccountName\"\r\n\t\t\t\t\t\t\trequired\r\n\t\t\t\t\t\t\tvalue={agentName}\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n                  setAgentName(e.target.value)\r\n              }\r\n            }\r\n\t\t\t\t\t        variant=\"outlined\"\r\n\r\n\t\t\t\t\t\t\tinputProps={{ maxLength: 50 }}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-acctfield\"\r\n\t\t\t\t\t\t  />\r\n\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t<FormControl fullWidth required style={{marginBottom:\"20px\"}}>\r\n  <label htmlFor=\"agent-description\" style={{textAlign:\"left\"}}>Description</label>\r\n  <textarea\r\n  style={{marginTop:\"10px\", minHeight:\"60px\", resize:\"vertical\"}}\r\n    id=\"agent-description\"\r\n    name=\"DomainUrl\"\r\n    required\r\n    value={agentDescription}\r\n    onChange={(e) => setAgentDescription(e.target.value)}\r\n    maxLength={200}\r\n    className=\"qadpt-acctfield\"\r\n  />\r\n</FormControl>\r\n\r\n<FormControl fullWidth required style={{marginBottom:\"20px\"}}>\r\n  <label htmlFor=\"agent-url\" style={{textAlign:\"left\"}}>URL</label>\r\n  <TextField\r\n  style={{marginTop:\"10px\"}}\r\n    id=\"agent-url\"\r\n    name=\"AgentUrl\"\r\n    required\r\n    value={agentUrl}\r\n    onChange={(e) => setAgentUrl(e.target.value)}\r\n    slotProps={{ htmlInput: { maxLength: 500 } }}\r\n    className=\"qadpt-acctfield\"\r\n    placeholder=\"Enter the URL for the agent...\"\r\n  />\r\n</FormControl>\r\n\r\n\t\t\t\t\t  </Grid>\r\n\t\t\t\t\t</Grid>\r\n\t\t\t\t  </div>\r\n\t\t\t\t  <div style={{marginTop:\"-19px\"}}>\r\n     <Button\r\n\t\t\t\t\t\tonClick={handleClick}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tbackgroundColor: \"#04417F\",\r\n\t\t\t\t\t\t\tcolor: \"#FFF\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tfontSize: \"16px\",\r\n\t\t\t\t\t\t\tfontWeight: \"400\",\r\n\t\t\t\t\t\t\tminWidth: \"120px\",\r\n\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#0776E5\",\r\n\t\t\t\t\t\t\t\ttransform: \"translateY(-1px)\",\r\n\t\t\t\t\t\t\t\tboxShadow: \"0 4px 12px rgba(4, 65, 127, 0.3)\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\ttransition: \"all 0.3s ease\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tStart Training\r\n\t\t\t\t\t</Button>\r\n    </div>\r\n\t\t\t\t</div>\r\n                </div>\r\n\t\t\r\n\r\n\r\n            </>\r\n  );\r\n};\r\n\r\nexport default AgentTrainingFields;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAA+B,OAAO,CAElD,MAAO,sBAAsB,CAE7B,OAKIC,IAAI,CAEJC,MAAM,CAENC,SAAS,CAGTC,WAAW,KASR,eAAe,CACtB,MAAO,CAAAC,cAAc,KAAuB,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACtE,KAAM,CAAAC,mBAAmB,CAAGC,IAAA,EAAyL,IAAxL,CAAEC,0BAA0B,CAAEC,uBAAuB,CAAEC,yBAA2H,CAAC,CAAAH,IAAA,CAE5M,KAAM,CACFI,SAAS,CACTC,gBAAgB,CAChBC,QAAQ,CACRC,YAAY,CACZC,mBAAmB,CACnBC,WACJ,CAAC,CAAGjB,cAAc,CAAEkB,KAAkB,EAAKA,KAAK,CAAC,CAEjD,KAAM,CAAAC,WAAW,CAAGA,CAAA,GACpB,CACIV,0BAA0B,CAAC,KAAK,CAAC,CACjCE,yBAAyB,CAAC,CAAC,CAE3B,CAAC,CACL,mBACET,IAAA,CAAAI,SAAA,EAAAc,QAAA,cAENlB,IAAA,QAAKmB,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cACpChB,KAAA,QAAKiB,SAAS,CAAC,qDAAqD,CAACC,KAAK,CAAE,CAACC,SAAS,CAAC,QAAQ,CAACC,MAAM,CAAC,OAAO,CAAE,CAAAJ,QAAA,eAC1GlB,IAAA,QAAKmB,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cACjClB,IAAA,QAAKmB,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,cAAY,CAAK,CAAC,CAEzC,CAAC,cACNlB,IAAA,QAAKmB,SAAS,CAAC,0BAA0B,CAAAD,QAAA,cAC1ClB,IAAA,CAACN,IAAI,EAAC6B,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAN,QAAA,cACzBhB,KAAA,CAACR,IAAI,EAAC+B,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAR,QAAA,eACnBhB,KAAA,CAACL,WAAW,EAAC8B,SAAS,MAACC,QAAQ,MAACR,KAAK,CAAE,CAACS,YAAY,CAAC,MAAM,CAAE,CAAAX,QAAA,eAC3DlB,IAAA,UAAO8B,OAAO,CAAC,cAAc,CAACV,KAAK,CAAE,CAACW,SAAS,CAAC,MAAM,CAAE,CAAAb,QAAA,CAAC,YAAU,CAAO,CAAC,cAC3ElB,IAAA,CAACJ,SAAS,EACQwB,KAAK,CAAE,CAACC,SAAS,CAAC,MAAM,CAAE,CAC7CW,EAAE,CAAC,cAAc,CACjBC,IAAI,CAAC,aAAa,CAClBL,QAAQ,MACRM,KAAK,CAAExB,SAAU,CACjByB,QAAQ,CAAGC,CAAC,EAAK,CACNvB,YAAY,CAACuB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAChC,CACD,CACAI,OAAO,CAAC,UAAU,CAExBC,UAAU,CAAE,CAAEC,SAAS,CAAE,EAAG,CAAE,CAC9BrB,SAAS,CAAC,iBAAiB,CACzB,CAAC,EACS,CAAC,cACdjB,KAAA,CAACL,WAAW,EAAC8B,SAAS,MAACC,QAAQ,MAACR,KAAK,CAAE,CAACS,YAAY,CAAC,MAAM,CAAE,CAAAX,QAAA,eACjElB,IAAA,UAAO8B,OAAO,CAAC,mBAAmB,CAACV,KAAK,CAAE,CAACW,SAAS,CAAC,MAAM,CAAE,CAAAb,QAAA,CAAC,aAAW,CAAO,CAAC,cACjFlB,IAAA,aACAoB,KAAK,CAAE,CAACC,SAAS,CAAC,MAAM,CAAEoB,SAAS,CAAC,MAAM,CAAEC,MAAM,CAAC,UAAU,CAAE,CAC7DV,EAAE,CAAC,mBAAmB,CACtBC,IAAI,CAAC,WAAW,CAChBL,QAAQ,MACRM,KAAK,CAAEvB,gBAAiB,CACxBwB,QAAQ,CAAGC,CAAC,EAAKtB,mBAAmB,CAACsB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACrDM,SAAS,CAAE,GAAI,CACfrB,SAAS,CAAC,iBAAiB,CAC5B,CAAC,EACS,CAAC,cAEdjB,KAAA,CAACL,WAAW,EAAC8B,SAAS,MAACC,QAAQ,MAACR,KAAK,CAAE,CAACS,YAAY,CAAC,MAAM,CAAE,CAAAX,QAAA,eAC3DlB,IAAA,UAAO8B,OAAO,CAAC,WAAW,CAACV,KAAK,CAAE,CAACW,SAAS,CAAC,MAAM,CAAE,CAAAb,QAAA,CAAC,KAAG,CAAO,CAAC,cACjElB,IAAA,CAACJ,SAAS,EACVwB,KAAK,CAAE,CAACC,SAAS,CAAC,MAAM,CAAE,CACxBW,EAAE,CAAC,WAAW,CACdC,IAAI,CAAC,UAAU,CACfL,QAAQ,MACRM,KAAK,CAAEtB,QAAS,CAChBuB,QAAQ,CAAGC,CAAC,EAAKrB,WAAW,CAACqB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC7CS,SAAS,CAAE,CAAEC,SAAS,CAAE,CAAEJ,SAAS,CAAE,GAAI,CAAE,CAAE,CAC7CrB,SAAS,CAAC,iBAAiB,CAC3B0B,WAAW,CAAC,gCAAgC,CAC7C,CAAC,EACS,CAAC,EAED,CAAC,CACH,CAAC,CACD,CAAC,cACN7C,IAAA,QAAKoB,KAAK,CAAE,CAACC,SAAS,CAAC,OAAO,CAAE,CAAAH,QAAA,cACjClB,IAAA,CAACL,MAAM,EACNmD,OAAO,CAAE7B,WAAY,CACrB8B,EAAE,CAAE,CACHC,eAAe,CAAE,SAAS,CAC1BC,KAAK,CAAE,MAAM,CACbC,YAAY,CAAE,KAAK,CACnBC,aAAa,CAAE,YAAY,CAC3BC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBC,QAAQ,CAAE,OAAO,CACjB,SAAS,CAAE,CACVN,eAAe,CAAE,SAAS,CAC1BO,SAAS,CAAE,kBAAkB,CAC7BC,SAAS,CAAE,kCACZ,CAAC,CACDC,UAAU,CAAE,eACb,CAAE,CAAAvC,QAAA,CACF,gBAED,CAAQ,CAAC,CACL,CAAC,EACD,CAAC,CACW,CAAC,CAIR,CAAC,CAEf,CAAC,CAED,cAAe,CAAAb,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}