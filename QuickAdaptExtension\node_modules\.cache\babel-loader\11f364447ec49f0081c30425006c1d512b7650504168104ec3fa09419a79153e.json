{"ast": null, "code": "import React,{useState}from'react';import{TextField,Button,IconButton,InputAdornment,FormHelperText}from'@mui/material';import{GetUserDetails}from'../../services/UserService';import JSEncrypt from'jsencrypt';import{LoginService}from'../../services/LoginService';import{getOrganizationById}from'../../services/OrganizationService';import useDrawerStore from\"../../store/drawerStore\";import useInfoStore from\"../../store/UserInfoStore\";import userSession from\"../../store/userSession\";import{pwdeye,eyeclose}from'../../assets/icons/icons';import'../login/ExtensionLogin.css';import{getRolesByUser}from'../../services/UserRoleService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{setAccessToken,setOidcInfo,setUser,setOrgDetails,setUserType,setUserRoles}=useInfoStore.getState();const{setHasAnnouncementOpened,hasAnnouncementOpened}=userSession.getState();const{clearAll,clearAccessToken}=useInfoStore.getState();const{clearGuideDetails,setActiveMenu,setSearchText}=useDrawerStore.getState();const{clearUserSession}=userSession.getState();let userLocalData={};let SAinitialsData;let userDetails;const ExtensionLogin=_ref=>{let{setIsLoggedIn}=_ref;const[email,setEmail]=useState('');const[password,setPassword]=useState('');const[showPassword,setShowPassword]=useState(false);const[error,setError]=useState(null);const[loginUserDetails,setUserDetails]=useState(null);//const { setUserRoles } = useContext(AuthProvider);\n// const loginStyles = {\n//   qadptDrawerContent: {\n//     width: '100%',\n//     backgroundColor: 'var(--ext - background)',\n//     marginTop: '20px',\n//   },\n//   qadptWelcomeMessage: {\n//     fontWeight: \"600\",\n//     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\n//     padding: \"14px\",\n//   },\n//       // container: {\n//   //   padding: '10px',\n//   //   backgroundColor: '#F6EEEE',\n//   //   borderRadius: '8px',\n//   // },\n//   // welcomeText: {\n//   //   fontFamily: 'Poppins, sans-serif',\n//   //   fontSize: '16px',\n//   //   fontWeight: 600,\n//   //   lineHeight: '24px',\n//   //   textAlign: 'left',\n//   //   color: 'rgba(34, 34, 34, 1)',\n//   // },\n//   // headerText: {\n//   //   fontFamily: 'Poppins, sans-serif',\n//   //   fontSize: '14px',\n//   //   fontWeight: 400,\n//   //   lineHeight: '24px',\n//   //   textAlign: 'left',\n//   //   color: 'rgba(68, 68, 68, 1)',\n//   //   marginTop: '10px',\n//   // },\n//   // textField: {\n//   //   width: '100%',\n//   //   backgroundColor: 'rgba(255, 255, 255, 1)',\n//   //   borderRadius: '6px',\n//   //   height: '46px',\n//   // },\n//   // textFieldInput: {\n//   //   fontFamily: 'Poppins, sans-serif',\n//   //   fontSize: '16px',\n//   //   fontWeight: 400,\n//   //   color: 'rgba(68, 68, 68, 1)',\n//   //   padding: '12px',\n//   //   border: '1px solid rgba(213, 213, 213, 1)',\n//   //   borderRadius: '6px',\n//   //   height: '46px',\n//   // },\n//   // loginButton: {\n//   //   backgroundColor: '#5F9EA0',\n//   //   color: '#fff',\n//   //   borderRadius: '25px',\n//   //   width: '100%',\n//   //   marginTop: '20px',\n//   //   textTransform: 'none',\n//   //   fontFamily: 'Poppins, sans-serif',\n//   //   fontSize: '16px',\n//   //   fontWeight: 500,\n//   // },\n//   // qadptTextdanger: {\n//   //   color: '#d9534f',\n//   //   fontSize: '0.9rem',\n//   // },\n//   qadptLoginForm: {\n//     marginTop: '20px',\n//     padding: '0px 10px 20px 10px', \n//   },\n//   qadptFormLabel: {\n//     fontSize: \"14px\",\n//     marginTop: \"10px\",\n//     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\n//   },\n//   qadptInvalidCreds: {\n//     fontSize: \"14px\",\n//   },\n//       // qadpteyeicon: {\n//   //   \"& .MuiIconButton-root\": {\n//   //     backgroundColor: \"transparent !important\",\n//   //     border: \"none !important\",\n//   //     padding: \"0 !important\", // Note: Correct \"Padding\" to \"padding\"\n//   //   }\n//   // },\n//   qadptForgotPwd: {\n//     color: \"var(--primarycolor)\", \n//     cursor: \"pointer\",\n//     fontSize: \"16px\",\n//     fontWeight: \"400\",\n//     lineHeight: \"24px\",\n//     marginTop: \"10px\",\n//     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\n//   },\n//   qadptBtn: {\n//     backgroundColor: \"var(--primarycolor) !important\",  \n//     color: \"#fff\",\n//     border: \"none\",\n//     padding: \"10px 12px\",\n//     cursor: \"pointer\",\n//     fontSize: \"16px\",\n//     borderRadius: \"12px\",\n//     width: \"100%\",\n//     marginTop: \"10px\",\n//     lineHeight: \"20px\",\n//     textTransform:\"none\",\n//   } as React.CSSProperties,\n// };\nconst GetUserRoles=async()=>{try{const rolesData=await getRolesByUser();// console.log(rolesData);\nconst dist=rolesData.reduce((acc,curr)=>{if(!acc[curr.AccountId]){acc[curr.AccountId]=[];}if(!acc[curr.AccountId].includes(curr.RoleName)){acc[curr.AccountId].push(curr.RoleName);}return acc;},{});setUserRoles(dist);}catch(e){}};const validateForm=()=>{if(!email.trim()&&!password.trim()){setError(\"Email and Password are required.\");return false;}if(!email.trim()){setError(\"Email is required.\");return false;}if(!validateEmail(email)){setError(\"Enter a valid email address.\");return false;}if(!password.trim()){setError(\"Password is required.\");return false;}return true;};const handleLoginSuccess=async()=>{try{if(!validateForm())return;clearAll();clearGuideDetails();clearUserSession();setActiveMenu(null);setSearchText(\"\");const organizationId=\"1\";const rememberLogin=true;const returnUrl=\"\";const authType=\"admin\";const tenantId=\"web\";const isEncryptionEnabled=process.env.REACT_APP_ENABLE_ENCRYPTION==='true';const publicKey=process.env.REACT_APP_PUBLIC_ENCRYPT_KEY||'';const encryptor=new JSEncrypt();encryptor.setPublicKey(publicKey);const now=new Date().toISOString();const encryptedPassword=encryptor.encrypt(password+'|'+now.trim()).toString();if(!encryptedPassword){console.error(\"Encryption failed\");return;}const response=await LoginService(email,isEncryptionEnabled?encryptedPassword:password,organizationId,rememberLogin,returnUrl,authType,tenantId);if(response.access_token){setAccessToken(response.access_token);setOidcInfo(response);const userResponse=await GetUserDetails();if(userResponse){var _userResponse$FirstNa,_userResponse$LastNam,_userResponse$UserTyp,_userResponse$Organiz;setUser(userResponse);GetUserRoles();const firstNameInitials=(userResponse===null||userResponse===void 0?void 0:(_userResponse$FirstNa=userResponse.FirstName)===null||_userResponse$FirstNa===void 0?void 0:_userResponse$FirstNa.charAt(0).toUpperCase())||'';const lastNameInitials=(userResponse===null||userResponse===void 0?void 0:(_userResponse$LastNam=userResponse.LastName)===null||_userResponse$LastNam===void 0?void 0:_userResponse$LastNam.charAt(0).toUpperCase())||'';SAinitialsData=firstNameInitials+lastNameInitials;setUserType((_userResponse$UserTyp=userResponse===null||userResponse===void 0?void 0:userResponse.UserType)!==null&&_userResponse$UserTyp!==void 0?_userResponse$UserTyp:\"\");const orgDetails=await getOrganizationById((_userResponse$Organiz=userResponse===null||userResponse===void 0?void 0:userResponse.OrganizationId)!==null&&_userResponse$Organiz!==void 0?_userResponse$Organiz:\"\");setOrgDetails(orgDetails);setIsLoggedIn(true);if(!hasAnnouncementOpened)setHasAnnouncementOpened(true);// --- Post-login redirect logic ---\n}}else{setIsLoggedIn(false);setError(response.error_description||\"Login failed. Please check your credentials.\");}}catch(err){console.error(err);setError(\"An unexpected error occurred. Please try again later.\");}};const handleClickShowPassword=()=>setShowPassword(!showPassword);const handlePasswordChange=event=>{setPassword(event.target.value);setError(null);};return/*#__PURE__*/_jsxs(\"div\",{className:\"qadptDrawerContent\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadptWelcomeMessage\",children:\"Welcome back\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"qadptLoginForm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadptFormLabel\",children:\"Email\"}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,autoFocus:true,value:email,onChange:e=>{setEmail(e.target.value);setError(null);},className:\"qadpt-txtfld\",placeholder:\"Enter your email\"// sx={{\n//   \"& .MuiInputBase-root\":{\n//     fontSize: \"16px !important\",\n//     fontWeight: \"400 !important\",\n//     padding: \"12px !important\",\n//     border: \"1px solid var(--border-color) !important\",\n//     borderRadius: \"6px !important\",\n//     boxShadow: \"none !important\",\n//     height: \"42px !important\",\n//     backgroundColor: \"var(--white-color) !important\",\n//     marginTop: \"10px !important\",\n//   },\n//   \"& .MuiInputBase-input\": {\n//     height: \"34px !important\",\n//     border: \"none !important\",\n//     padding:\"0 !important\",\n//   }\n// }}\n,InputProps:{// className: \"qadpt-input-field\",\ndisableUnderline:true},variant:\"standard\"}),/*#__PURE__*/_jsx(\"div\",{className:\"qadptFormLabel\",children:\"Password\"}),/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,type:showPassword?\"text\":\"password\",id:\"password\",name:\"password\",autoComplete:\"password\",value:password,onChange:handlePasswordChange,className:\"qadpt-txtfld\",placeholder:\"Enter your password\"// sx={{\n//   \"& .MuiInputBase-root\":{\n//     fontSize: \"16px !important\",\n//     fontWeight: \"400 !important\",\n//     padding: \"12px !important\",\n//     border: \"1px solid var(--border-color) !important\",\n//     borderRadius: \"6px !important\",\n//     boxShadow: \"none !important\",\n//     height: \"46px !important\",\n//     backgroundColor: \"var(--white-color) !important\",\n//     marginTop: \"10px !important\",\n//   },\n//   \"& .MuiInputBase-input\": {\n//     height: \"34px !important\",\n//     border: \"none !important\",\n//     padding:\"0 !important\",\n//   }\n// }}\n,InputProps:{endAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"end\",className:\"pwdicon-blk\",children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"toggle password visibility\",onClick:handleClickShowPassword,edge:\"end\"// sx={{\n//   backgroundColor: \"transparent !important\",\n//   border: \"none !important\",\n//   padding: \"0 !important\",\n//   margin: \"0 !important\",\n// }}\n,className:\"qadpt-pwdicon\"//style={loginStyles.qadpteyeicon} \n,children:showPassword?/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:pwdeye}}):/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:eyeclose}})})}),disableUnderline:true},variant:\"standard\"}),error&&/*#__PURE__*/_jsx(FormHelperText,{error:true,className:\"qadptFormLabel\",children:error}),/*#__PURE__*/_jsx(\"div\",{className:\"qadptForgotPwd\",children:/*#__PURE__*/_jsx(\"span\",{onClick:()=>window.open(`${process.env.REACT_APP_WEB_API}/forgotpassword`,'_blank'),children:\"Forgot password?\"})}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleLoginSuccess// style={loginStyles.qadptBtn}\n,className:\"qadptBtn\",children:\"Log in\"})]})]});};export default ExtensionLogin;function validateEmail(email){const emailRegex=/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;return emailRegex.test(email);}", "map": {"version": 3, "names": ["React", "useState", "TextField", "<PERSON><PERSON>", "IconButton", "InputAdornment", "FormHelperText", "GetUserDetails", "JSEncrypt", "LoginService", "getOrganizationById", "useDrawerStore", "useInfoStore", "userSession", "pwdeye", "eyeclose", "getRolesByUser", "jsx", "_jsx", "jsxs", "_jsxs", "setAccessToken", "setOidcInfo", "setUser", "setOrgDetails", "setUserType", "setUserRoles", "getState", "setHasAnnouncementOpened", "hasAnnouncementOpened", "clearAll", "clearAccessToken", "clearGuideDetails", "setActiveMenu", "setSearchText", "clearUserSession", "userLocalData", "SAinitialsData", "userDetails", "ExtensionLogin", "_ref", "setIsLoggedIn", "email", "setEmail", "password", "setPassword", "showPassword", "setShowPassword", "error", "setError", "loginUserDetails", "setUserDetails", "GetUserRoles", "rolesData", "dist", "reduce", "acc", "curr", "AccountId", "includes", "RoleName", "push", "e", "validateForm", "trim", "validateEmail", "handleLoginSuccess", "organizationId", "<PERSON><PERSON><PERSON><PERSON>", "returnUrl", "authType", "tenantId", "isEncryptionEnabled", "process", "env", "REACT_APP_ENABLE_ENCRYPTION", "public<PERSON>ey", "REACT_APP_PUBLIC_ENCRYPT_KEY", "encryptor", "setPublicKey", "now", "Date", "toISOString", "encryptedPassword", "encrypt", "toString", "console", "response", "access_token", "userResponse", "_userResponse$FirstNa", "_userResponse$LastNam", "_userResponse$UserTyp", "_userResponse$Organiz", "firstNameInitials", "FirstName", "char<PERSON>t", "toUpperCase", "lastNameInitials", "LastName", "UserType", "orgDetails", "OrganizationId", "error_description", "err", "handleClickShowPassword", "handlePasswordChange", "event", "target", "value", "className", "children", "fullWidth", "autoFocus", "onChange", "placeholder", "InputProps", "disableUnderline", "variant", "required", "type", "id", "name", "autoComplete", "endAdornment", "position", "onClick", "edge", "dangerouslySetInnerHTML", "__html", "window", "open", "REACT_APP_WEB_API", "emailRegex", "test"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/login/ExtensionLogin.tsx"], "sourcesContent": ["import React, { useContext, useState } from 'react';\r\nimport { TextField, Button, Typography, IconButton, InputAdornment, FormHelperText } from '@mui/material';\r\nimport VisibilityOff from '@mui/icons-material/VisibilityOff';\r\nimport Visibility from '@mui/icons-material/Visibility';\r\nimport { GetUserDetails, GetUserDetailsById, UserLogin } from '../../services/UserService';\r\nimport { AuthProvider, useAuth } from '../auth/AuthProvider';\r\nimport JSEncrypt from 'jsencrypt';\r\nimport { LoginService } from '../../services/LoginService';\r\nimport { User } from '../../models/User';\r\nimport { getOrganizationById } from '../../services/OrganizationService';\r\nimport { Padding } from '@mui/icons-material';\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport useInfoStore from \"../../store/UserInfoStore\";\r\nimport userSession from \"../../store/userSession\";\r\nimport { pwdeye, eyeclose } from '../../assets/icons/icons';\r\nimport '../login/ExtensionLogin.css';\r\nimport { AccountContext } from './AccountContext';\r\nimport { getRolesByUser } from '../../services/UserRoleService';\r\nconst { setAccessToken, setOidcInfo, setUser, setOrgDetails, setUserType,setUserRoles } = useInfoStore.getState();\r\nconst { setHasAnnouncementOpened, hasAnnouncementOpened } = userSession.getState();\r\nconst { clearAll, clearAccessToken } = useInfoStore.getState();\r\nconst { clearGuideDetails, setActiveMenu, setSearchText } = useDrawerStore.getState();\r\nconst {\tclearUserSession} = userSession.getState();\r\nlet userLocalData: { [key: string]: any } = {}\r\nlet SAinitialsData: string;\r\nlet userDetails: User;\r\ninterface ExtensionLoginProps {\r\n    setIsLoggedIn: (value: boolean) => void;\r\n}\r\nconst ExtensionLogin: React.FC<ExtensionLoginProps> = ({ setIsLoggedIn }) => {\r\n  const [email, setEmail] = useState<string>('');\r\n  const [password, setPassword] = useState<string>('');\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [loginUserDetails, setUserDetails] = useState<User | null>(null);\r\n  //const { setUserRoles } = useContext(AuthProvider);\r\n\r\n  // const loginStyles = {\r\n  //   qadptDrawerContent: {\r\n  //     width: '100%',\r\n  //     backgroundColor: 'var(--ext - background)',\r\n  //     marginTop: '20px',\r\n  //   },\r\n  //   qadptWelcomeMessage: {\r\n  //     fontWeight: \"600\",\r\n  //     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\r\n  //     padding: \"14px\",\r\n  //   },\r\n  //       // container: {\r\n  //   //   padding: '10px',\r\n  //   //   backgroundColor: '#F6EEEE',\r\n  //   //   borderRadius: '8px',\r\n  //   // },\r\n  //   // welcomeText: {\r\n  //   //   fontFamily: 'Poppins, sans-serif',\r\n  //   //   fontSize: '16px',\r\n  //   //   fontWeight: 600,\r\n  //   //   lineHeight: '24px',\r\n  //   //   textAlign: 'left',\r\n  //   //   color: 'rgba(34, 34, 34, 1)',\r\n  //   // },\r\n  //   // headerText: {\r\n  //   //   fontFamily: 'Poppins, sans-serif',\r\n  //   //   fontSize: '14px',\r\n  //   //   fontWeight: 400,\r\n  //   //   lineHeight: '24px',\r\n  //   //   textAlign: 'left',\r\n  //   //   color: 'rgba(68, 68, 68, 1)',\r\n  //   //   marginTop: '10px',\r\n  //   // },\r\n  //   // textField: {\r\n  //   //   width: '100%',\r\n  //   //   backgroundColor: 'rgba(255, 255, 255, 1)',\r\n  //   //   borderRadius: '6px',\r\n  //   //   height: '46px',\r\n  //   // },\r\n  //   // textFieldInput: {\r\n  //   //   fontFamily: 'Poppins, sans-serif',\r\n  //   //   fontSize: '16px',\r\n  //   //   fontWeight: 400,\r\n  //   //   color: 'rgba(68, 68, 68, 1)',\r\n  //   //   padding: '12px',\r\n  //   //   border: '1px solid rgba(213, 213, 213, 1)',\r\n  //   //   borderRadius: '6px',\r\n  //   //   height: '46px',\r\n  //   // },\r\n  //   // loginButton: {\r\n  //   //   backgroundColor: '#5F9EA0',\r\n  //   //   color: '#fff',\r\n  //   //   borderRadius: '25px',\r\n  //   //   width: '100%',\r\n  //   //   marginTop: '20px',\r\n  //   //   textTransform: 'none',\r\n  //   //   fontFamily: 'Poppins, sans-serif',\r\n  //   //   fontSize: '16px',\r\n  //   //   fontWeight: 500,\r\n  //   // },\r\n  //   // qadptTextdanger: {\r\n  //   //   color: '#d9534f',\r\n  //   //   fontSize: '0.9rem',\r\n  //   // },\r\n\r\n  //   qadptLoginForm: {\r\n  //     marginTop: '20px',\r\n  //     padding: '0px 10px 20px 10px', \r\n  //   },\r\n  //   qadptFormLabel: {\r\n  //     fontSize: \"14px\",\r\n  //     marginTop: \"10px\",\r\n  //     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\r\n  //   },\r\n  //   qadptInvalidCreds: {\r\n  //     fontSize: \"14px\",\r\n  //   },\r\n  //       // qadpteyeicon: {\r\n  //   //   \"& .MuiIconButton-root\": {\r\n  //   //     backgroundColor: \"transparent !important\",\r\n  //   //     border: \"none !important\",\r\n  //   //     padding: \"0 !important\", // Note: Correct \"Padding\" to \"padding\"\r\n  //   //   }\r\n  //   // },\r\n    \r\n  //   qadptForgotPwd: {\r\n  //     color: \"var(--primarycolor)\", \r\n  //     cursor: \"pointer\",\r\n  //     fontSize: \"16px\",\r\n  //     fontWeight: \"400\",\r\n  //     lineHeight: \"24px\",\r\n  //     marginTop: \"10px\",\r\n  //     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\r\n  //   },\r\n  //   qadptBtn: {\r\n  //     backgroundColor: \"var(--primarycolor) !important\",  \r\n  //     color: \"#fff\",\r\n  //     border: \"none\",\r\n  //     padding: \"10px 12px\",\r\n  //     cursor: \"pointer\",\r\n  //     fontSize: \"16px\",\r\n  //     borderRadius: \"12px\",\r\n  //     width: \"100%\",\r\n  //     marginTop: \"10px\",\r\n  //     lineHeight: \"20px\",\r\n  //     textTransform:\"none\",\r\n      \r\n  //   } as React.CSSProperties,\r\n  // };\r\n  \r\n  const GetUserRoles = async () => {\r\n    try {\r\n      const rolesData = await getRolesByUser();\r\n      // console.log(rolesData);\r\n      const dist = rolesData.reduce((acc: { [x: string]: any[]; }, curr: { AccountId: string | number; RoleName: any; }) => {\r\n        if (!acc[curr.AccountId]) {\r\n          acc[curr.AccountId] = [];\r\n        }\r\n        if (!acc[curr.AccountId].includes(curr.RoleName)) {\r\n          acc[curr.AccountId].push(curr.RoleName);\r\n        }\r\n        return acc;\r\n      }, {});\r\n\r\n      setUserRoles(dist);\r\n              \r\n      \r\n    } catch (e) {\r\n      \r\n    }\r\n  }\r\n\r\n  const validateForm = (): boolean => {\r\n    if (!email.trim() && !password.trim()) {\r\n      setError(\"Email and Password are required.\");\r\n      return false;\r\n    }\r\n    if (!email.trim()) {\r\n      setError(\"Email is required.\");\r\n      return false;\r\n    }\r\n    if (!validateEmail(email)) {\r\n      setError(\"Enter a valid email address.\");\r\n      return false;\r\n    }\r\n    if (!password.trim()) {\r\n      setError(\"Password is required.\");\r\n      return false;\r\n    }\r\n    return true;\r\n  };\r\n  \r\n  \r\n\r\n  const handleLoginSuccess = async () => {\r\n    try {\r\n      if (!validateForm()) return;\r\n  \r\n      clearAll();\r\n      clearGuideDetails();\r\n      clearUserSession();\r\n      setActiveMenu(null);\r\n      setSearchText(\"\");\r\n  \r\n      const organizationId = \"1\";\r\n      const rememberLogin = true;\r\n      const returnUrl = \"\";\r\n      const authType = \"admin\";\r\n      const tenantId = \"web\";\r\n\r\n      const isEncryptionEnabled = process.env.REACT_APP_ENABLE_ENCRYPTION === 'true';\r\n\r\n      const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY || '';\r\n                const encryptor = new JSEncrypt();\r\n                encryptor.setPublicKey(publicKey);\r\n                const now = new Date().toISOString();\r\n                const encryptedPassword = encryptor.encrypt(password + '|' + now.trim()).toString();\r\n                if (!encryptedPassword) {\r\n                  console.error(\"Encryption failed\");\r\n                  return; \r\n                }\r\n  \r\n      const response = await LoginService(email, isEncryptionEnabled ? encryptedPassword : password, organizationId, rememberLogin, returnUrl, authType, tenantId);\r\n      if (response.access_token) {\r\n        setAccessToken(response.access_token);\r\n        setOidcInfo(response);\r\n        const userResponse = await GetUserDetails();\r\n        if (userResponse) {\r\n          setUser(userResponse);\r\n          GetUserRoles();\r\n          const firstNameInitials = userResponse?.FirstName?.charAt(0).toUpperCase() || '';\r\n          const lastNameInitials = userResponse?.LastName?.charAt(0).toUpperCase() || '';\r\n          SAinitialsData = firstNameInitials + lastNameInitials;\r\n          setUserType(userResponse?.UserType ?? \"\");\r\n          const orgDetails = await getOrganizationById(userResponse?.OrganizationId ?? \"\");\r\n          setOrgDetails(orgDetails);\r\n          setIsLoggedIn(true);\r\n          if (!hasAnnouncementOpened) setHasAnnouncementOpened(true);\r\n                    // --- Post-login redirect logic ---\r\n        }\r\n      } else {\r\n        setIsLoggedIn(false);\r\n        setError(response.error_description || \"Login failed. Please check your credentials.\");\r\n      }\r\n    } catch (err) {\r\n      console.error(err);\r\n      setError(\"An unexpected error occurred. Please try again later.\");\r\n    }\r\n  };\r\n  \r\n\r\n  const handleClickShowPassword = () => setShowPassword(!showPassword);\r\n\r\n  const handlePasswordChange = (event: any) => {\r\n    setPassword(event.target.value);\r\n    setError(null);\r\n  };\r\n\r\n  return (\r\n    <div className='qadptDrawerContent'>\t\r\n<div className='qadptWelcomeMessage'>\r\n  Welcome back\r\n</div>\r\n    <div className='qadptLoginForm'>\r\n\r\n        <div className='qadptFormLabel'>Email</div>\r\n\r\n        <TextField\r\n            fullWidth\r\n            autoFocus            \r\n            value={email}\r\n            onChange={(e) => {\r\n                setEmail(e.target.value); \r\n                setError(null); \r\n          }}\r\n          className='qadpt-txtfld'\r\n          placeholder=\"Enter your email\"\r\n          // sx={{\r\n          //   \"& .MuiInputBase-root\":{\r\n          //     fontSize: \"16px !important\",\r\n          //     fontWeight: \"400 !important\",\r\n          //     padding: \"12px !important\",\r\n          //     border: \"1px solid var(--border-color) !important\",\r\n          //     borderRadius: \"6px !important\",\r\n          //     boxShadow: \"none !important\",\r\n          //     height: \"42px !important\",\r\n          //     backgroundColor: \"var(--white-color) !important\",\r\n          //     marginTop: \"10px !important\",\r\n          //   },\r\n          //   \"& .MuiInputBase-input\": {\r\n          //     height: \"34px !important\",\r\n          //     border: \"none !important\",\r\n          //     padding:\"0 !important\",\r\n              \r\n          //   }\r\n          // }}\r\n            InputProps={{\t\t\t\t\t\t\t\t\t\t\r\n              // className: \"qadpt-input-field\",\r\n              disableUnderline: true,\r\n          }}\r\n          variant=\"standard\"\r\n        />\r\n\r\n        <div className='qadptFormLabel'>Password</div>\r\n        <TextField\r\n            required\r\n            fullWidth\r\n            type={showPassword ? \"text\" : \"password\"}\r\n            id=\"password\"\r\n            name=\"password\"\r\n            autoComplete=\"password\"           \r\n            value={password}\r\n          onChange={handlePasswordChange}\r\n          className='qadpt-txtfld'\r\n          placeholder=\"Enter your password\"\r\n          // sx={{\r\n          //   \"& .MuiInputBase-root\":{\r\n          //     fontSize: \"16px !important\",\r\n          //     fontWeight: \"400 !important\",\r\n          //     padding: \"12px !important\",\r\n          //     border: \"1px solid var(--border-color) !important\",\r\n          //     borderRadius: \"6px !important\",\r\n          //     boxShadow: \"none !important\",\r\n          //     height: \"46px !important\",\r\n          //     backgroundColor: \"var(--white-color) !important\",\r\n          //     marginTop: \"10px !important\",\r\n          //   },\r\n          //   \"& .MuiInputBase-input\": {\r\n          //     height: \"34px !important\",\r\n          //     border: \"none !important\",\r\n          //     padding:\"0 !important\",\r\n          //   }\r\n          // }}\r\n            InputProps={{ \r\n              endAdornment: (\r\n                <InputAdornment \r\n                position=\"end\" \r\n                className='pwdicon-blk'\r\n              >\r\n                <IconButton\r\n                  aria-label=\"toggle password visibility\"\r\n                  onClick={handleClickShowPassword}\r\n                    edge=\"end\"\r\n                    // sx={{\r\n                    //   backgroundColor: \"transparent !important\",\r\n                    //   border: \"none !important\",\r\n                    //   padding: \"0 !important\",\r\n                    //   margin: \"0 !important\",\r\n                    // }}\r\n                    className='qadpt-pwdicon'\r\n                    //style={loginStyles.qadpteyeicon} \r\n                >\r\n{showPassword ?  <span dangerouslySetInnerHTML={{ __html: pwdeye }}/>: <span dangerouslySetInnerHTML={{ __html: eyeclose }}/>}\r\n</IconButton>\r\n              </InputAdornment>\r\n                    \r\n                ),\r\n               \r\n                disableUnderline: true,\r\n            }}\r\n            variant=\"standard\"\r\n           \r\n        />\t\r\n         {error && (\r\n          <FormHelperText error className='qadptFormLabel'>\r\n              {error}\r\n          </FormHelperText>\r\n        )}\r\n\r\n        <div className='qadptForgotPwd'>\r\n        <span \r\n        onClick={() => window.open(`${process.env.REACT_APP_WEB_API}/forgotpassword`, '_blank')}\r\n        >\r\n          Forgot password?\r\n        </span>\r\n        </div>\r\n\r\n        <Button\r\n        variant=\"contained\"\r\n        onClick={handleLoginSuccess}\r\n          // style={loginStyles.qadptBtn}\r\n          className='qadptBtn'\r\n    >\r\n        Log in\r\n    </Button>\r\n    </div>\r\n</div>\r\n  );\r\n};\r\n\r\nexport default ExtensionLogin;\r\nfunction validateEmail(email: string): boolean {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(email);\r\n}\r\n\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAgBC,QAAQ,KAAQ,OAAO,CACnD,OAASC,SAAS,CAAEC,MAAM,CAAcC,UAAU,CAAEC,cAAc,CAAEC,cAAc,KAAQ,eAAe,CAGzG,OAASC,cAAc,KAAuC,4BAA4B,CAE1F,MAAO,CAAAC,SAAS,KAAM,WAAW,CACjC,OAASC,YAAY,KAAQ,6BAA6B,CAE1D,OAASC,mBAAmB,KAAQ,oCAAoC,CAExE,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,MAAO,CAAAC,YAAY,KAAM,2BAA2B,CACpD,MAAO,CAAAC,WAAW,KAAM,yBAAyB,CACjD,OAASC,MAAM,CAAEC,QAAQ,KAAQ,0BAA0B,CAC3D,MAAO,6BAA6B,CAEpC,OAASC,cAAc,KAAQ,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAChE,KAAM,CAAEC,cAAc,CAAEC,WAAW,CAAEC,OAAO,CAAEC,aAAa,CAAEC,WAAW,CAACC,YAAa,CAAC,CAAGd,YAAY,CAACe,QAAQ,CAAC,CAAC,CACjH,KAAM,CAAEC,wBAAwB,CAAEC,qBAAsB,CAAC,CAAGhB,WAAW,CAACc,QAAQ,CAAC,CAAC,CAClF,KAAM,CAAEG,QAAQ,CAAEC,gBAAiB,CAAC,CAAGnB,YAAY,CAACe,QAAQ,CAAC,CAAC,CAC9D,KAAM,CAAEK,iBAAiB,CAAEC,aAAa,CAAEC,aAAc,CAAC,CAAGvB,cAAc,CAACgB,QAAQ,CAAC,CAAC,CACrF,KAAM,CAAEQ,gBAAgB,CAAC,CAAGtB,WAAW,CAACc,QAAQ,CAAC,CAAC,CAClD,GAAI,CAAAS,aAAqC,CAAG,CAAC,CAAC,CAC9C,GAAI,CAAAC,cAAsB,CAC1B,GAAI,CAAAC,WAAiB,CAIrB,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAAuB,IAAtB,CAAEC,aAAc,CAAC,CAAAD,IAAA,CACtE,KAAM,CAACE,KAAK,CAAEC,QAAQ,CAAC,CAAG1C,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAAC2C,QAAQ,CAAEC,WAAW,CAAC,CAAG5C,QAAQ,CAAS,EAAE,CAAC,CACpD,KAAM,CAAC6C,YAAY,CAAEC,eAAe,CAAC,CAAG9C,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC+C,KAAK,CAAEC,QAAQ,CAAC,CAAGhD,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACiD,gBAAgB,CAAEC,cAAc,CAAC,CAAGlD,QAAQ,CAAc,IAAI,CAAC,CACtE;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA,KAAM,CAAAmD,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAArC,cAAc,CAAC,CAAC,CACxC;AACA,KAAM,CAAAsC,IAAI,CAAGD,SAAS,CAACE,MAAM,CAAC,CAACC,GAA4B,CAAEC,IAAoD,GAAK,CACpH,GAAI,CAACD,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,CAAE,CACxBF,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,CAAG,EAAE,CAC1B,CACA,GAAI,CAACF,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,CAACC,QAAQ,CAACF,IAAI,CAACG,QAAQ,CAAC,CAAE,CAChDJ,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,CAACG,IAAI,CAACJ,IAAI,CAACG,QAAQ,CAAC,CACzC,CACA,MAAO,CAAAJ,GAAG,CACZ,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN9B,YAAY,CAAC4B,IAAI,CAAC,CAGpB,CAAE,MAAOQ,CAAC,CAAE,CAEZ,CACF,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAe,CAClC,GAAI,CAACrB,KAAK,CAACsB,IAAI,CAAC,CAAC,EAAI,CAACpB,QAAQ,CAACoB,IAAI,CAAC,CAAC,CAAE,CACrCf,QAAQ,CAAC,kCAAkC,CAAC,CAC5C,MAAO,MAAK,CACd,CACA,GAAI,CAACP,KAAK,CAACsB,IAAI,CAAC,CAAC,CAAE,CACjBf,QAAQ,CAAC,oBAAoB,CAAC,CAC9B,MAAO,MAAK,CACd,CACA,GAAI,CAACgB,aAAa,CAACvB,KAAK,CAAC,CAAE,CACzBO,QAAQ,CAAC,8BAA8B,CAAC,CACxC,MAAO,MAAK,CACd,CACA,GAAI,CAACL,QAAQ,CAACoB,IAAI,CAAC,CAAC,CAAE,CACpBf,QAAQ,CAAC,uBAAuB,CAAC,CACjC,MAAO,MAAK,CACd,CACA,MAAO,KAAI,CACb,CAAC,CAID,KAAM,CAAAiB,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACF,GAAI,CAACH,YAAY,CAAC,CAAC,CAAE,OAErBjC,QAAQ,CAAC,CAAC,CACVE,iBAAiB,CAAC,CAAC,CACnBG,gBAAgB,CAAC,CAAC,CAClBF,aAAa,CAAC,IAAI,CAAC,CACnBC,aAAa,CAAC,EAAE,CAAC,CAEjB,KAAM,CAAAiC,cAAc,CAAG,GAAG,CAC1B,KAAM,CAAAC,aAAa,CAAG,IAAI,CAC1B,KAAM,CAAAC,SAAS,CAAG,EAAE,CACpB,KAAM,CAAAC,QAAQ,CAAG,OAAO,CACxB,KAAM,CAAAC,QAAQ,CAAG,KAAK,CAEtB,KAAM,CAAAC,mBAAmB,CAAGC,OAAO,CAACC,GAAG,CAACC,2BAA2B,GAAK,MAAM,CAE9E,KAAM,CAAAC,SAAS,CAAGH,OAAO,CAACC,GAAG,CAACG,4BAA4B,EAAI,EAAE,CACtD,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAtE,SAAS,CAAC,CAAC,CACjCsE,SAAS,CAACC,YAAY,CAACH,SAAS,CAAC,CACjC,KAAM,CAAAI,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACpC,KAAM,CAAAC,iBAAiB,CAAGL,SAAS,CAACM,OAAO,CAACxC,QAAQ,CAAG,GAAG,CAAGoC,GAAG,CAAChB,IAAI,CAAC,CAAC,CAAC,CAACqB,QAAQ,CAAC,CAAC,CACnF,GAAI,CAACF,iBAAiB,CAAE,CACtBG,OAAO,CAACtC,KAAK,CAAC,mBAAmB,CAAC,CAClC,OACF,CAEV,KAAM,CAAAuC,QAAQ,CAAG,KAAM,CAAA9E,YAAY,CAACiC,KAAK,CAAE8B,mBAAmB,CAAGW,iBAAiB,CAAGvC,QAAQ,CAAEuB,cAAc,CAAEC,aAAa,CAAEC,SAAS,CAAEC,QAAQ,CAAEC,QAAQ,CAAC,CAC5J,GAAIgB,QAAQ,CAACC,YAAY,CAAE,CACzBnE,cAAc,CAACkE,QAAQ,CAACC,YAAY,CAAC,CACrClE,WAAW,CAACiE,QAAQ,CAAC,CACrB,KAAM,CAAAE,YAAY,CAAG,KAAM,CAAAlF,cAAc,CAAC,CAAC,CAC3C,GAAIkF,YAAY,CAAE,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAChBtE,OAAO,CAACkE,YAAY,CAAC,CACrBrC,YAAY,CAAC,CAAC,CACd,KAAM,CAAA0C,iBAAiB,CAAG,CAAAL,YAAY,SAAZA,YAAY,kBAAAC,qBAAA,CAAZD,YAAY,CAAEM,SAAS,UAAAL,qBAAA,iBAAvBA,qBAAA,CAAyBM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAI,EAAE,CAChF,KAAM,CAAAC,gBAAgB,CAAG,CAAAT,YAAY,SAAZA,YAAY,kBAAAE,qBAAA,CAAZF,YAAY,CAAEU,QAAQ,UAAAR,qBAAA,iBAAtBA,qBAAA,CAAwBK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAI,EAAE,CAC9E5D,cAAc,CAAGyD,iBAAiB,CAAGI,gBAAgB,CACrDzE,WAAW,EAAAmE,qBAAA,CAACH,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEW,QAAQ,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACzC,KAAM,CAAAS,UAAU,CAAG,KAAM,CAAA3F,mBAAmB,EAAAmF,qBAAA,CAACJ,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEa,cAAc,UAAAT,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAChFrE,aAAa,CAAC6E,UAAU,CAAC,CACzB5D,aAAa,CAAC,IAAI,CAAC,CACnB,GAAI,CAACZ,qBAAqB,CAAED,wBAAwB,CAAC,IAAI,CAAC,CAChD;AACZ,CACF,CAAC,IAAM,CACLa,aAAa,CAAC,KAAK,CAAC,CACpBQ,QAAQ,CAACsC,QAAQ,CAACgB,iBAAiB,EAAI,8CAA8C,CAAC,CACxF,CACF,CAAE,MAAOC,GAAG,CAAE,CACZlB,OAAO,CAACtC,KAAK,CAACwD,GAAG,CAAC,CAClBvD,QAAQ,CAAC,uDAAuD,CAAC,CACnE,CACF,CAAC,CAGD,KAAM,CAAAwD,uBAAuB,CAAGA,CAAA,GAAM1D,eAAe,CAAC,CAACD,YAAY,CAAC,CAEpE,KAAM,CAAA4D,oBAAoB,CAAIC,KAAU,EAAK,CAC3C9D,WAAW,CAAC8D,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAC/B5D,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAC,CAED,mBACE7B,KAAA,QAAK0F,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACvC7F,IAAA,QAAK4F,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,cAErC,CAAK,CAAC,cACF3F,KAAA,QAAK0F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAE3B7F,IAAA,QAAK4F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,OAAK,CAAK,CAAC,cAE3C7F,IAAA,CAAChB,SAAS,EACN8G,SAAS,MACTC,SAAS,MACTJ,KAAK,CAAEnE,KAAM,CACbwE,QAAQ,CAAGpD,CAAC,EAAK,CACbnB,QAAQ,CAACmB,CAAC,CAAC8C,MAAM,CAACC,KAAK,CAAC,CACxB5D,QAAQ,CAAC,IAAI,CAAC,CACpB,CAAE,CACF6D,SAAS,CAAC,cAAc,CACxBK,WAAW,CAAC,kBACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA,CACEC,UAAU,CAAE,CACV;AACAC,gBAAgB,CAAE,IACtB,CAAE,CACFC,OAAO,CAAC,UAAU,CACnB,CAAC,cAEFpG,IAAA,QAAK4F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAAC,cAC9C7F,IAAA,CAAChB,SAAS,EACNqH,QAAQ,MACRP,SAAS,MACTQ,IAAI,CAAE1E,YAAY,CAAG,MAAM,CAAG,UAAW,CACzC2E,EAAE,CAAC,UAAU,CACbC,IAAI,CAAC,UAAU,CACfC,YAAY,CAAC,UAAU,CACvBd,KAAK,CAAEjE,QAAS,CAClBsE,QAAQ,CAAER,oBAAqB,CAC/BI,SAAS,CAAC,cAAc,CACxBK,WAAW,CAAC,qBACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACEC,UAAU,CAAE,CACVQ,YAAY,cACV1G,IAAA,CAACb,cAAc,EACfwH,QAAQ,CAAC,KAAK,CACdf,SAAS,CAAC,aAAa,CAAAC,QAAA,cAEvB7F,IAAA,CAACd,UAAU,EACT,aAAW,4BAA4B,CACvC0H,OAAO,CAAErB,uBAAwB,CAC/BsB,IAAI,CAAC,KACL;AACA;AACA;AACA;AACA;AACA;AAAA,CACAjB,SAAS,CAAC,eACV;AAAA,CAAAC,QAAA,CAEnBjE,YAAY,cAAI5B,IAAA,SAAM8G,uBAAuB,CAAE,CAAEC,MAAM,CAAEnH,MAAO,CAAE,CAAC,CAAC,cAAEI,IAAA,SAAM8G,uBAAuB,CAAE,CAAEC,MAAM,CAAElH,QAAS,CAAE,CAAC,CAAC,CACjH,CAAC,CACiB,CAEb,CAEDsG,gBAAgB,CAAE,IACtB,CAAE,CACFC,OAAO,CAAC,UAAU,CAErB,CAAC,CACAtE,KAAK,eACL9B,IAAA,CAACZ,cAAc,EAAC0C,KAAK,MAAC8D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC3C/D,KAAK,CACM,CACjB,cAED9B,IAAA,QAAK4F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC/B7F,IAAA,SACA4G,OAAO,CAAEA,CAAA,GAAMI,MAAM,CAACC,IAAI,CAAC,GAAG1D,OAAO,CAACC,GAAG,CAAC0D,iBAAiB,iBAAiB,CAAE,QAAQ,CAAE,CAAArB,QAAA,CACvF,kBAED,CAAM,CAAC,CACF,CAAC,cAEN7F,IAAA,CAACf,MAAM,EACPmH,OAAO,CAAC,WAAW,CACnBQ,OAAO,CAAE5D,kBACP;AAAA,CACA4C,SAAS,CAAC,UAAU,CAAAC,QAAA,CACzB,QAED,CAAQ,CAAC,EACJ,CAAC,EACL,CAAC,CAEN,CAAC,CAED,cAAe,CAAAxE,cAAc,CAC7B,QAAS,CAAA0B,aAAaA,CAACvB,KAAa,CAAW,CAC7C,KAAM,CAAA2F,UAAU,CAAG,4BAA4B,CAC/C,MAAO,CAAAA,UAAU,CAACC,IAAI,CAAC5F,KAAK,CAAC,CAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}