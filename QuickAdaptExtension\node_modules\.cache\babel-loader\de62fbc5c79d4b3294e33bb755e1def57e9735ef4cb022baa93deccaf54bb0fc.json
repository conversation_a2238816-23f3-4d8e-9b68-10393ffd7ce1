{"ast": null, "code": "import React,{createContext,useState,useEffect,useRef}from'react';// Create the context\nimport{jsx as _jsx}from\"react/jsx-runtime\";export const AccountContext=/*#__PURE__*/createContext({accountId:'',// Default value\nsetAccountId:()=>{},// Empty function as a placeholder\nroles:[],setRoles:()=>[]});// Provider component\nexport const AccountProvider=_ref=>{let{children}=_ref;const[roles,setRoles]=useState([]);// Get the default accountId from localStorage\nconst getDefaultAccountId=()=>localStorage.getItem(\"accountId\")||\"\";const[accountId,setAccountId]=useState(getDefaultAccountId());const originalAccountIdRef=useRef(null);useEffect(()=>{// On mount, check for qa_account_id in the URL\nconst params=new URLSearchParams(window.location.search);const overrideAccountId=params.get('qa_account_id');const defaultAccountId=getDefaultAccountId();// --- Store intended URL if special param is present and not logged in ---\nconst guideId=params.get('quickadopt_guide_id');const isLoggedIn=!!localStorage.getItem('access_token');if((overrideAccountId||guideId)&&!isLoggedIn){sessionStorage.setItem('postLoginRedirect',window.location.href);}if(overrideAccountId&&overrideAccountId!==defaultAccountId){// Cache the original accountId (if not already cached)\nif(originalAccountIdRef.current===null){originalAccountIdRef.current=defaultAccountId;}setAccountId(overrideAccountId);// Store override in localStorage for later use\nlocalStorage.setItem('qa_account_id_override',overrideAccountId);// Remove qa_account_id from the URL after use\nparams.delete('qa_account_id');const newUrl=`${window.location.pathname}${params.toString()?`?${params.toString()}`:''}${window.location.hash}`;window.history.replaceState({},'',newUrl);}else{setAccountId(defaultAccountId);// Remove any previous override if not using it\nlocalStorage.removeItem('qa_account_id_override');}// On unload, restore the original accountId and clean up override\nconst handleUnload=()=>{if(originalAccountIdRef.current!==null){setAccountId(originalAccountIdRef.current);originalAccountIdRef.current=null;}localStorage.removeItem('qa_account_id_override');};window.addEventListener('beforeunload',handleUnload);return()=>{window.removeEventListener('beforeunload',handleUnload);// Also restore on unmount\nhandleUnload();};// eslint-disable-next-line react-hooks/exhaustive-deps\n},[]);return/*#__PURE__*/_jsx(AccountContext.Provider,{value:{accountId,setAccountId,roles,setRoles},children:children});};", "map": {"version": 3, "names": ["React", "createContext", "useState", "useEffect", "useRef", "jsx", "_jsx", "AccountContext", "accountId", "setAccountId", "roles", "setRoles", "Account<PERSON><PERSON><PERSON>", "_ref", "children", "getDefaultAccountId", "localStorage", "getItem", "originalAccountIdRef", "params", "URLSearchParams", "window", "location", "search", "overrideAccountId", "get", "defaultAccountId", "guideId", "isLoggedIn", "sessionStorage", "setItem", "href", "current", "delete", "newUrl", "pathname", "toString", "hash", "history", "replaceState", "removeItem", "handleUnload", "addEventListener", "removeEventListener", "Provider", "value"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/login/AccountContext.tsx"], "sourcesContent": ["import React, { createContext, useState, ReactNode, useEffect, useRef } from 'react';\r\n\r\n// Create the context\r\nexport const AccountContext = createContext<{\r\n  accountId: string;\r\n  setAccountId: (id: string) => void;\r\n  roles: string[];\r\n  setRoles: (roles: [])=>void;\r\n}>({\r\n  accountId: '',      // Default value\r\n  setAccountId: () => { },  // Empty function as a placeholder\r\n  roles: [],\r\n  setRoles: () =>[],\r\n});\r\n\r\n// Provider component\r\nexport const AccountProvider = ({ children }: { children: ReactNode }) => {\r\n  const [roles, setRoles] = useState<string[]>([]); \r\n  // Get the default accountId from localStorage\r\n  const getDefaultAccountId = () => localStorage.getItem(\"accountId\") || \"\";\r\n  const [accountId, setAccountId] = useState<string>(getDefaultAccountId());\r\n  const originalAccountIdRef = useRef<string | null>(null);\r\n\r\n  useEffect(() => {\r\n\r\n    // On mount, check for qa_account_id in the URL\r\n\r\n    const params = new URLSearchParams(window.location.search);\r\n    \r\n    const overrideAccountId = params.get('qa_account_id');\r\n    const defaultAccountId = getDefaultAccountId();\r\n    \r\n    // --- Store intended URL if special param is present and not logged in ---\r\n    const guideId = params.get('quickadopt_guide_id');\r\n    const isLoggedIn = !!localStorage.getItem('access_token');\r\n    if ((overrideAccountId || guideId) && !isLoggedIn) {\r\n      sessionStorage.setItem('postLoginRedirect', window.location.href);\r\n    }\r\n    \r\n    if (overrideAccountId && overrideAccountId !== defaultAccountId) {\r\n      // Cache the original accountId (if not already cached)\r\n      if (originalAccountIdRef.current === null) {\r\n        originalAccountIdRef.current = defaultAccountId;\r\n      }\r\n      setAccountId(overrideAccountId);\r\n      // Store override in localStorage for later use\r\n      localStorage.setItem('qa_account_id_override', overrideAccountId);\r\n      // Remove qa_account_id from the URL after use\r\n      params.delete('qa_account_id');\r\n      const newUrl = `${window.location.pathname}${params.toString() ? `?${params.toString()}` : ''}${window.location.hash}`;\r\n      window.history.replaceState({}, '', newUrl);\r\n    } else {\r\n      setAccountId(defaultAccountId);\r\n      // Remove any previous override if not using it\r\n      localStorage.removeItem('qa_account_id_override');\r\n    }\r\n    // On unload, restore the original accountId and clean up override\r\n    const handleUnload = () => {\r\n      if (originalAccountIdRef.current !== null) {\r\n        setAccountId(originalAccountIdRef.current);\r\n        originalAccountIdRef.current = null;\r\n      }\r\n      localStorage.removeItem('qa_account_id_override');\r\n    };\r\n    window.addEventListener('beforeunload', handleUnload);\r\n    return () => {\r\n      window.removeEventListener('beforeunload', handleUnload);\r\n      // Also restore on unmount\r\n      handleUnload();\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n\r\n  return (\r\n    <AccountContext.Provider value={{ accountId, setAccountId,roles,setRoles }}>\r\n      {children}\r\n    </AccountContext.Provider>\r\n  );\r\n};\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,QAAQ,CAAaC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAEpF;AAAA,OAAAC,GAAA,IAAAC,IAAA,yBACA,MAAO,MAAM,CAAAC,cAAc,cAAGN,aAAa,CAKxC,CACDO,SAAS,CAAE,EAAE,CAAO;AACpBC,YAAY,CAAEA,CAAA,GAAM,CAAE,CAAC,CAAG;AAC1BC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAEA,CAAA,GAAK,EACjB,CAAC,CAAC,CAEF;AACA,MAAO,MAAM,CAAAC,eAAe,CAAGC,IAAA,EAA2C,IAA1C,CAAEC,QAAkC,CAAC,CAAAD,IAAA,CACnE,KAAM,CAACH,KAAK,CAAEC,QAAQ,CAAC,CAAGT,QAAQ,CAAW,EAAE,CAAC,CAChD;AACA,KAAM,CAAAa,mBAAmB,CAAGA,CAAA,GAAMC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,EAAI,EAAE,CACzE,KAAM,CAACT,SAAS,CAAEC,YAAY,CAAC,CAAGP,QAAQ,CAASa,mBAAmB,CAAC,CAAC,CAAC,CACzE,KAAM,CAAAG,oBAAoB,CAAGd,MAAM,CAAgB,IAAI,CAAC,CAExDD,SAAS,CAAC,IAAM,CAEd;AAEA,KAAM,CAAAgB,MAAM,CAAG,GAAI,CAAAC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAE1D,KAAM,CAAAC,iBAAiB,CAAGL,MAAM,CAACM,GAAG,CAAC,eAAe,CAAC,CACrD,KAAM,CAAAC,gBAAgB,CAAGX,mBAAmB,CAAC,CAAC,CAE9C;AACA,KAAM,CAAAY,OAAO,CAAGR,MAAM,CAACM,GAAG,CAAC,qBAAqB,CAAC,CACjD,KAAM,CAAAG,UAAU,CAAG,CAAC,CAACZ,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CACzD,GAAI,CAACO,iBAAiB,EAAIG,OAAO,GAAK,CAACC,UAAU,CAAE,CACjDC,cAAc,CAACC,OAAO,CAAC,mBAAmB,CAAET,MAAM,CAACC,QAAQ,CAACS,IAAI,CAAC,CACnE,CAEA,GAAIP,iBAAiB,EAAIA,iBAAiB,GAAKE,gBAAgB,CAAE,CAC/D;AACA,GAAIR,oBAAoB,CAACc,OAAO,GAAK,IAAI,CAAE,CACzCd,oBAAoB,CAACc,OAAO,CAAGN,gBAAgB,CACjD,CACAjB,YAAY,CAACe,iBAAiB,CAAC,CAC/B;AACAR,YAAY,CAACc,OAAO,CAAC,wBAAwB,CAAEN,iBAAiB,CAAC,CACjE;AACAL,MAAM,CAACc,MAAM,CAAC,eAAe,CAAC,CAC9B,KAAM,CAAAC,MAAM,CAAG,GAAGb,MAAM,CAACC,QAAQ,CAACa,QAAQ,GAAGhB,MAAM,CAACiB,QAAQ,CAAC,CAAC,CAAG,IAAIjB,MAAM,CAACiB,QAAQ,CAAC,CAAC,EAAE,CAAG,EAAE,GAAGf,MAAM,CAACC,QAAQ,CAACe,IAAI,EAAE,CACtHhB,MAAM,CAACiB,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,CAAE,EAAE,CAAEL,MAAM,CAAC,CAC7C,CAAC,IAAM,CACLzB,YAAY,CAACiB,gBAAgB,CAAC,CAC9B;AACAV,YAAY,CAACwB,UAAU,CAAC,wBAAwB,CAAC,CACnD,CACA;AACA,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIvB,oBAAoB,CAACc,OAAO,GAAK,IAAI,CAAE,CACzCvB,YAAY,CAACS,oBAAoB,CAACc,OAAO,CAAC,CAC1Cd,oBAAoB,CAACc,OAAO,CAAG,IAAI,CACrC,CACAhB,YAAY,CAACwB,UAAU,CAAC,wBAAwB,CAAC,CACnD,CAAC,CACDnB,MAAM,CAACqB,gBAAgB,CAAC,cAAc,CAAED,YAAY,CAAC,CACrD,MAAO,IAAM,CACXpB,MAAM,CAACsB,mBAAmB,CAAC,cAAc,CAAEF,YAAY,CAAC,CACxD;AACAA,YAAY,CAAC,CAAC,CAChB,CAAC,CACD;AACF,CAAC,CAAE,EAAE,CAAC,CAGN,mBACEnC,IAAA,CAACC,cAAc,CAACqC,QAAQ,EAACC,KAAK,CAAE,CAAErC,SAAS,CAAEC,YAAY,CAACC,KAAK,CAACC,QAAS,CAAE,CAAAG,QAAA,CACxEA,QAAQ,CACc,CAAC,CAE9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}