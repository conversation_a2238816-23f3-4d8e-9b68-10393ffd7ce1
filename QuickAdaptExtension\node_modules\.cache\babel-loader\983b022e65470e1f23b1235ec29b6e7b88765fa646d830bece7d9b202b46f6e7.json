{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{Box,Typo<PERSON>,TextField,Grid,IconButton,Button}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import ArrowBackIosNewOutlinedIcon from\"@mui/icons-material/ArrowBackIosNewOutlined\";import{AlignHorizontalLeft as TopLeftIcon,AlignHorizontalCenter as TopCenterIcon,AlignHorizontalRight as TopRightIcon,AlignVerticalTop as MiddleLeftIcon,AlignVerticalCenter as MiddleCenterIcon,AlignVerticalBottom as MiddleRightIcon,AlignHorizontalLeft as BottomLeftIcon,AlignHorizontalCenter as BottomCenterIcon,AlignHorizontalRight as BottomRightIcon}from\"@mui/icons-material\";import\"./Canvas.module.css\";import useDrawerStore,{CANVAS_DEFAULT_VALUE}from\"../../../store/drawerStore\";import{tooltipLeft,tooltipRight,tooltipTop,tooltipBottom,tooltipCenter,warning}from\"../../../assets/icons/icons\";import{useTranslation}from\"react-i18next\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TooltipCanvasSettings=_ref=>{var _toolTipGuideMetaData2;let{zindeex,setZindeex,setShowTooltipCanvasSettings}=_ref;const{t:translate}=useTranslation();const{setTooltipXaxis,setTooltipYaxis,updateCanvasInTooltip,tooltipXaxis,tooltipYaxis,tooltipWidth,setTooltipWidth,setTooltipPadding,setTooltipBorderradius,setTooltipBordersize,setTooltipBordercolor,setTooltipBackgroundcolor,tooltippadding,tooltipborderradius,tooltipbordersize,tooltipBordercolor,tooltipBackgroundcolor,tooltipPosition,setTooltipPosition,setElementSelected,setIsTooltipPopup,toolTipGuideMetaData,currentStep,autoPosition,setAutoPosition,selectedTemplate,updateDesignelementInTooltip,selectedTemplateTour,CANVAS_DEFAULT_VALUE_HOTSPOT,setIsUnSavedChanges}=useDrawerStore(state=>state);const[isOpen,setIsOpen]=useState(true);const[dismiss,setDismiss]=useState(false);const[selectedPosition,setSelectedPosition]=useState(\"middle-center\");const[error,setError]=useState(false);const[paddingError,setPaddingError]=useState(false);const[cornerRadiusError,setCornerRadiusError]=useState(false);const[borderSizeError,setBorderSizeError]=useState(false);const[widthError,setWidthError]=useState(false);const[tempBorderColor,setTempBorderColor]=useState(tooltipBordercolor);useEffect(()=>{// Sync tempBorderColor with store, using default if empty or transparent\nconst validColor=tooltipBordercolor&&tooltipBordercolor!==\"transparent\"&&tooltipBordercolor!==\"\"?tooltipBordercolor:\"#000000\";setTempBorderColor(validColor);},[tooltipBordercolor]);// Validate initial width value\nuseEffect(()=>{const currentWidth=parseInt(formatValueWithPixelOrPercentage(tooltipWidth))||0;if(currentWidth<50){setWidthError(true);}else{setWidthError(false);}},[tooltipWidth]);const positions=[{label:translate(\"Top Left\",{defaultValue:\"Top Left\"}),icon:/*#__PURE__*/_jsx(TopLeftIcon,{fontSize:\"small\"}),value:\"top-left\"},{label:translate(\"Top Center\",{defaultValue:\"Top Center\"}),icon:selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\"?/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:tooltipTop},style:{fontSize:\"small\"}}):/*#__PURE__*/_jsx(TopCenterIcon,{fontSize:\"small\"}),value:\"top\"},{label:translate(\"Top Right\",{defaultValue:\"Top Right\"}),icon:/*#__PURE__*/_jsx(TopRightIcon,{fontSize:\"small\"}),value:\"top-right\"},{label:translate(\"Middle Left\",{defaultValue:\"Middle Left\"}),icon:selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\"?/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:tooltipLeft},style:{fontSize:\"small\"}}):/*#__PURE__*/_jsx(MiddleLeftIcon,{fontSize:\"small\"}),value:\"left\"},{label:translate(\"Middle Center\",{defaultValue:\"Middle Center\"}),icon:selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\"?/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:tooltipCenter},style:{fontSize:\"small\"}}):/*#__PURE__*/_jsx(MiddleCenterIcon,{fontSize:\"small\"}),value:\"center\"},{label:translate(\"Middle Right\",{defaultValue:\"Middle Right\"}),icon:selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\"?/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:tooltipRight},style:{fontSize:\"small\"}}):/*#__PURE__*/_jsx(MiddleRightIcon,{fontSize:\"small\"}),value:\"right\"},{label:translate(\"Bottom Left\",{defaultValue:\"Bottom Left\"}),icon:/*#__PURE__*/_jsx(BottomLeftIcon,{fontSize:\"small\"}),value:\"bottom-left\"},{label:translate(\"Bottom Center\",{defaultValue:\"Bottom Center\"}),icon:selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\"?/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:tooltipBottom},style:{fontSize:\"small\"}}):/*#__PURE__*/_jsx(BottomCenterIcon,{fontSize:\"small\"}),value:\"bottom\"},{label:translate(\"Bottom Right\",{defaultValue:\"Bottom Right\"}),icon:/*#__PURE__*/_jsx(BottomRightIcon,{fontSize:\"small\"}),value:\"bottom-right\"}];const handlePositionClick=e=>{var _e$target;if(e!==null&&e!==void 0&&(_e$target=e.target)!==null&&_e$target!==void 0&&_e$target.id){//setSelectedPosition(e.target.id);\nsetTooltipPosition(e.target.id);}};const onReselectElement=()=>{TooltipCanvasSettings({ReSelection:false,XPosition:4,YPosition:4,width:\"300\",Padding:\"2\",borderradius:\"8\",bordersize:\"0\",borderColor:\"\",backgroundColor:\"\",// PulseAnimation: true,\n// stopAnimationUponInteraction: true,\n// ShowUpon: \"Hovering Hotspot\",\nShowByDefault:false});updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);setElementSelected(true);setIsTooltipPopup(false);setShowTooltipCanvasSettings(false);};const handleBorderColorChange=e=>{var _e$target2;if(e!==null&&e!==void 0&&(_e$target2=e.target)!==null&&_e$target2!==void 0&&_e$target2.value){setTempBorderColor(e.target.value);}};const handleBackgroundColorChange=e=>{var _e$target3;if(e!==null&&e!==void 0&&(_e$target3=e.target)!==null&&_e$target3!==void 0&&_e$target3.value){setTooltipBackgroundcolor(e.target.value);}};const handleAutoSelect=e=>{//\tsetDismiss(e.target.checked);\nsetAutoPosition(e.target.checked);};const handleClose=()=>{setIsOpen(false);setShowTooltipCanvasSettings(false);};const handleApplyChanges=()=>{// Don't apply changes if there's any validation error\nif(widthError||paddingError||cornerRadiusError||borderSizeError){return;}// Create the new canvas settings\nconst updatedCanvasSettings={position:tooltipPosition,backgroundColor:tooltipBackgroundcolor,width:tooltipWidth,borderRadius:tooltipborderradius,padding:tooltippadding,borderColor:tempBorderColor,borderSize:tooltipbordersize,autoposition:autoPosition,xaxis:tooltipXaxis,yaxis:tooltipYaxis};// Apply the changes - updateCanvasInTooltip will handle recording the change for undo/redo\nupdateCanvasInTooltip(updatedCanvasSettings);// Updates canvas and tooltipBordercolor\nhandleClose();setIsUnSavedChanges(true);};useEffect(()=>{var _toolTipGuideMetaData;if((_toolTipGuideMetaData=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData!==void 0&&_toolTipGuideMetaData.canvas){const canvasData=toolTipGuideMetaData[currentStep-1].canvas;// Handle border color - use default if empty, transparent, or invalid\nconst borderColor=canvasData===null||canvasData===void 0?void 0:canvasData.borderColor;const validBorderColor=borderColor&&borderColor!==\"transparent\"&&borderColor!==\"\"?borderColor:\"#000000\";setTooltipPosition((canvasData===null||canvasData===void 0?void 0:canvasData.position)||\"middle-center\");setTooltipPadding((canvasData===null||canvasData===void 0?void 0:canvasData.padding)||\"10px\");setTooltipBorderradius((canvasData===null||canvasData===void 0?void 0:canvasData.borderRadius)||\"8px\");setTooltipBordersize((canvasData===null||canvasData===void 0?void 0:canvasData.borderSize)||\"0px\");setTooltipBordercolor(borderColor||\"\");setTempBorderColor(validBorderColor);// Use valid color for the input\nsetTooltipBackgroundcolor((canvasData===null||canvasData===void 0?void 0:canvasData.backgroundColor)||\"#FFFFFF\");setTooltipWidth((canvasData===null||canvasData===void 0?void 0:canvasData.width)||\"300px\");if(selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\"){setTooltipXaxis((canvasData===null||canvasData===void 0?void 0:canvasData.xaxis)||\"2px\");setTooltipYaxis((canvasData===null||canvasData===void 0?void 0:canvasData.yaxis)||\"2px\");}else{setTooltipXaxis((canvasData===null||canvasData===void 0?void 0:canvasData.xaxis)||\"100px\");setTooltipYaxis((canvasData===null||canvasData===void 0?void 0:canvasData.yaxis)||\"100px\");}}},[(_toolTipGuideMetaData2=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData2===void 0?void 0:_toolTipGuideMetaData2.canvas]);if(!isOpen)return null;const formatValueWithPixelOrPercentage=value=>{const v=String(value);let newValue=v;if(v!==null&&v!==void 0&&v.endsWith(\"px\")||v!==null&&v!==void 0&&v.endsWith(\"%\")){newValue=v.split(/px|%/)[0];}return newValue;};const handleChange=e=>{// Only allow numeric input\nconst value=e.target.value;if(value===''){setTooltipWidth('0px');setWidthError(true);// Empty value is invalid\nreturn;}if(!/^-?\\d*$/.test(value)){return;}let inputValue=parseInt(value)||0;// Validate width - minimum 50 pixels\nif(inputValue<50){setWidthError(true);}else{setWidthError(false);}setTooltipWidth(`${inputValue}px`);};return/*#__PURE__*/_jsx(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"back\",onClick:handleClose,children:/*#__PURE__*/_jsx(ArrowBackIosNewOutlinedIcon,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\"?translate(\"Canvas\"):translate(\"Canvas\")}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":\"close\",onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-canblock\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\",children:[/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Auto Position\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:autoPosition,onChange:handleAutoSelect,name:\"autoPosition\"}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-position-grid\",sx:{opacity:selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Hotspot\"?0.5:1,cursor:selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Hotspot\"?\"not-allowed\":\"\"},children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-ctrl-title\",children:translate(\"Position\")}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:1,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:4,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",disabled:autoPosition,sx:{opacity:selectedPosition===positions[0].value?1:0.5}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:4,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",id:positions[1].value,onClick:()=>{//setSelectedPosition(positions[1].value);\nsetTooltipPosition(positions[1].value);},disabled:autoPosition||selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Hotspot\",disableRipple:true,sx:{opacity:tooltipPosition===positions[1].value?1:0.5},children:selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\"?/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:tooltipTop},id:positions[1].value}):/*#__PURE__*/_jsx(TopCenterIcon,{fontSize:\"small\"})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:4,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",disabled:autoPosition,sx:{opacity:selectedPosition===positions[2].value?1:0.5}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:4,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",id:positions[3].value,onClick:()=>{// setSelectedPosition(positions[3].value);\nsetTooltipPosition(positions[3].value);},disabled:autoPosition,disableRipple:true,sx:{opacity:tooltipPosition===positions[3].value?1:0.5},children:selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\"?/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:tooltipLeft},id:positions[3].value}):/*#__PURE__*/_jsx(MiddleLeftIcon,{fontSize:\"small\"})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:4,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",id:positions[4].value,onClick:()=>{// setSelectedPosition(positions[4].value);\nsetTooltipPosition(positions[4].value);},disabled:autoPosition,disableRipple:true,sx:{opacity:tooltipPosition===positions[4].value?1:0.5,paddingLeft:\"0 !important\"},children:selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\"?/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:tooltipCenter},id:positions[4].value}):/*#__PURE__*/_jsx(MiddleCenterIcon,{fontSize:\"small\"})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:4,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",id:positions[5].value,onClick:()=>{// setSelectedPosition(positions[5].value);\nsetTooltipPosition(positions[5].value);},disabled:autoPosition,disableRipple:true,sx:{opacity:tooltipPosition===positions[5].value?1:0.5},children:selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\"?/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:tooltipRight},id:positions[5].value}):/*#__PURE__*/_jsx(MiddleRightIcon,{fontSize:\"small\"})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:4,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",disabled:autoPosition,sx:{opacity:selectedPosition===positions[6].value?1:0.5}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:4,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",id:positions[7].value,onClick:()=>{// setSelectedPosition(positions[7].value);\nsetTooltipPosition(positions[7].value);},disabled:autoPosition,disableRipple:true,sx:{opacity:tooltipPosition===positions[7].value?1:0.5},children:selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\"?/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:tooltipBottom},id:positions[7].value}):/*#__PURE__*/_jsx(BottomCenterIcon,{fontSize:\"small\"})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:4,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",disabled:autoPosition,sx:{opacity:selectedPosition===positions[8].value?1:0.5}})})]})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-control-label\",children:[\"X \",translate(\"Axis Offset\")]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:formatValueWithPixelOrPercentage(tooltipXaxis),size:\"small\",className:\"qadpt-control-input\",onChange:e=>setTooltipXaxis(`${parseInt(e.target.value)||0}px`),InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}},disabled:autoPosition})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-control-label\",children:[\"Y \",translate(\"Axis Offset\")]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:formatValueWithPixelOrPercentage(tooltipYaxis),size:\"small\",className:\"qadpt-control-input\",onChange:e=>setTooltipYaxis(`${parseInt(e.target.value)||0}px`),InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}},disabled:autoPosition})})]}),/*#__PURE__*/_jsx(Box,{className:\"qadpt-control-box\",style:{flexDirection:\"column\",height:\"auto\"},children:/*#__PURE__*/_jsxs(Box,{style:{display:\"flex\",alignItems:\"center\",gap:\"8px\",width:\"100%\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Width\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:formatValueWithPixelOrPercentage(tooltipWidth),size:\"small\",className:\"qadpt-control-input\",onChange:handleChange,error:widthError,InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}}})})]})}),widthError&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\",alignItems:\"center\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate(\"Width must be at least 50 pixels\")]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Padding\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:formatValueWithPixelOrPercentage(tooltippadding),size:\"small\",className:\"qadpt-control-input\",onChange:e=>{// Only allow numeric input\nconst value=e.target.value;if(!/^-?\\d*$/.test(value)){return;}const inputValue=parseInt(value)||0;// Validate padding between 0px and 20px\nif(inputValue<0||inputValue>20){setPaddingError(true);}else{setPaddingError(false);}setTooltipPadding(`${inputValue}px`);},InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}},error:paddingError})})]}),paddingError&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate(\"Value must be between 0px and 20px.\")]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Corner Radius\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:formatValueWithPixelOrPercentage(tooltipborderradius),size:\"small\",className:\"qadpt-control-input\",onChange:e=>{// Only allow numeric input\nconst value=e.target.value;if(!/^-?\\d*$/.test(value)){return;}const inputValue=parseInt(value)||0;// Validate corner radius between 0px and 20px\nif(inputValue<0||inputValue>20){setCornerRadiusError(true);}else{setCornerRadiusError(false);}setTooltipBorderradius(`${inputValue}`);},InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}},error:cornerRadiusError})})]}),cornerRadiusError&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate(\"Value must be between 0px and 20px.\")]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Border Size\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:formatValueWithPixelOrPercentage(tooltipbordersize),size:\"small\",className:\"qadpt-control-input\",onChange:e=>{// Only allow numeric input\nconst value=e.target.value;if(!/^-?\\d*$/.test(value)){return;}const inputValue=parseInt(value)||0;// Validate border size between 0px and 20px\nif(inputValue<0||inputValue>5){setBorderSizeError(true);}else{setBorderSizeError(false);}setTooltipBordersize(`${inputValue}px`);},InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}},error:borderSizeError})})]}),borderSizeError&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),\"Value must be between 0px and 5px.\"]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Border\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:tempBorderColor||\"#000000\",onChange:handleBorderColorChange,className:\"qadpt-color-input\"})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Background\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:tooltipBackgroundcolor,onChange:handleBackgroundColorChange,className:\"qadpt-color-input\"})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-drawerFooter\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleApplyChanges,className:`qadpt-btn ${widthError||paddingError||cornerRadiusError||borderSizeError?\"disabled\":\"\"}`,disabled:widthError||paddingError||cornerRadiusError||borderSizeError// Disable button if any validation errors exist\n,children:translate(\"Apply\")})})]})});};export default TooltipCanvasSettings;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "TextField", "Grid", "IconButton", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "AlignHorizontalLeft", "TopLeftIcon", "AlignHorizontalCenter", "TopCenterIcon", "AlignHorizontalRight", "TopRightIcon", "AlignVerticalTop", "MiddleLeftIcon", "AlignVerticalCenter", "MiddleCenterIcon", "AlignVerticalBottom", "MiddleRightIcon", "BottomLeftIcon", "BottomCenterIcon", "BottomRightIcon", "useDrawerStore", "CANVAS_DEFAULT_VALUE", "tooltipLeft", "tooltipRight", "tooltipTop", "tooltipBottom", "tooltipCenter", "warning", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "TooltipCanvasSettings", "_ref", "_toolTipGuideMetaData2", "zindeex", "setZindeex", "setShowTooltipCanvasSettings", "t", "translate", "setTooltipXaxis", "setTooltipYaxis", "updateCanvasInTooltip", "tooltipXaxis", "tooltipYaxis", "tooltipWidth", "setTooltipWidth", "setTooltipPadding", "setTooltipBorderradius", "setTooltipBordersize", "setTooltipBordercolor", "setTooltipBackgroundcolor", "tooltippadding", "tooltipborderradius", "tooltipbordersize", "tooltipBordercolor", "tooltipBackgroundcolor", "tooltipPosition", "setTooltipPosition", "setElementSelected", "setIsTooltipPopup", "toolTipGuideMetaData", "currentStep", "autoPosition", "setAutoPosition", "selectedTemplate", "updateDesignelementInTooltip", "selectedTemplateTour", "CANVAS_DEFAULT_VALUE_HOTSPOT", "setIsUnSavedChanges", "state", "isOpen", "setIsOpen", "dismiss", "<PERSON><PERSON><PERSON><PERSON>", "selectedPosition", "setSelectedPosition", "error", "setError", "paddingError", "setPaddingError", "cornerRadiusError", "setCornerRadiusError", "borderSizeError", "setBorderSizeError", "widthError", "setWidthError", "tempBorderColor", "setTempBorderColor", "validColor", "currentWidth", "parseInt", "formatValueWithPixelOrPercentage", "positions", "label", "defaultValue", "icon", "fontSize", "value", "dangerouslySetInnerHTML", "__html", "style", "handlePositionClick", "e", "_e$target", "target", "id", "onReselectElement", "ReSelection", "XPosition", "YPosition", "width", "Padding", "<PERSON><PERSON><PERSON>", "bordersize", "borderColor", "backgroundColor", "ShowByDefault", "handleBorderColorChange", "_e$target2", "handleBackgroundColorChange", "_e$target3", "handleAutoSelect", "checked", "handleClose", "handleApplyChanges", "updatedCanvasSettings", "position", "borderRadius", "padding", "borderSize", "autoposition", "xaxis", "yaxis", "_toolTipGuideMetaData", "canvas", "canvasData", "validBorderColor", "v", "String", "newValue", "endsWith", "split", "handleChange", "test", "inputValue", "className", "children", "onClick", "size", "type", "onChange", "name", "sx", "opacity", "cursor", "container", "spacing", "item", "xs", "disabled", "disable<PERSON><PERSON><PERSON>", "paddingLeft", "variant", "InputProps", "endAdornment", "border", "flexDirection", "height", "display", "alignItems", "gap", "color", "textAlign", "top", "left", "marginBottom", "marginRight"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/Tooltips/designFields/TooltipCanvasSettings.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { <PERSON>, Typo<PERSON>, TextField, Grid, IconButton, Button, Tooltip, Switch } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport {\r\n\tAlignHorizontalLeft as TopLeftIcon,\r\n\tAlignHorizontalCenter as TopCenterIcon,\r\n\tAlignHorizontalRight as TopRightIcon,\r\n\tAlignVerticalTop as MiddleLeftIcon,\r\n\tAlignVerticalCenter as MiddleCenterIcon,\r\n\tAlignVerticalBottom as MiddleRightIcon,\r\n\tAlignHorizontalLeft as BottomLeftIcon,\r\n\tAlignHorizontalCenter as BottomCenterIcon,\r\n\tAlignHorizontalRight as BottomRightIcon,\r\n\tPadding,\r\n} from \"@mui/icons-material\";\r\nimport \"./Canvas.module.css\";\r\nimport useDrawerStore, { CANVAS_DEFAULT_VALUE, CANVAS_DEFAULT_VALUE_HOTSPOT, TCan<PERSON> } from \"../../../store/drawerStore\";\r\nimport { tooltipLeft, tooltipRight, tooltipTop, tooltipBottom, tooltipCenter, warning } from \"../../../assets/icons/icons\";\r\nimport { TouchAppSharp } from \"@mui/icons-material\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst TooltipCanvasSettings = ({ zindeex, setZindeex, setShowTooltipCanvasSettings }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tsetTooltipXaxis,\r\n\t\tsetTooltipYaxis,\r\n\t\tupdateCanvasInTooltip,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\ttooltipWidth,\r\n\t\tsetTooltipWidth,\r\n\t\tsetTooltipPadding,\r\n\t\tsetTooltipBorderradius,\r\n\t\tsetTooltipBordersize,\r\n\t\tsetTooltipBordercolor,\r\n\t\tsetTooltipBackgroundcolor,\r\n\t\ttooltippadding,\r\n\t\ttooltipborderradius,\r\n\t\ttooltipbordersize,\r\n\t\ttooltipBordercolor,\r\n\t\ttooltipBackgroundcolor,\r\n\t\ttooltipPosition,\r\n\t\tsetTooltipPosition,\r\n\t\tsetElementSelected,\r\n\t\tsetIsTooltipPopup,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tcurrentStep,\r\n\t\tautoPosition,\r\n\t\tsetAutoPosition,\r\n\t\tselectedTemplate,\r\n\t\tupdateDesignelementInTooltip,\r\n\t\tselectedTemplateTour,\r\n\t\tCANVAS_DEFAULT_VALUE_HOTSPOT,\r\n\t\tsetIsUnSavedChanges\r\n\t} = useDrawerStore((state: any) => state);\r\n\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [dismiss, setDismiss] = useState(false);\r\n\tconst [selectedPosition, setSelectedPosition] = useState(\"middle-center\");\r\n\tconst [error, setError] = useState(false);\r\n\tconst [paddingError, setPaddingError] = useState(false);\r\n\tconst [cornerRadiusError, setCornerRadiusError] = useState(false);\r\n\tconst [borderSizeError, setBorderSizeError] = useState(false);\r\n\tconst [widthError, setWidthError] = useState(false);\r\n\tconst [tempBorderColor, setTempBorderColor] = useState(tooltipBordercolor);\r\n\r\n\tuseEffect(() => {\r\n\t\t// Sync tempBorderColor with store, using default if empty or transparent\r\n\t\tconst validColor = tooltipBordercolor && tooltipBordercolor !== \"transparent\" && tooltipBordercolor !== \"\" ? tooltipBordercolor : \"#000000\";\r\n\t\tsetTempBorderColor(validColor);\r\n\t  }, [tooltipBordercolor]);\r\n\r\n\t// Validate initial width value\r\n\tuseEffect(() => {\r\n\t\tconst currentWidth = parseInt(formatValueWithPixelOrPercentage(tooltipWidth)) || 0;\r\n\t\tif (currentWidth < 50 ) {\r\n\t\t\tsetWidthError(true);\r\n\t\t} else {\r\n\t\t\tsetWidthError(false);\r\n\t\t}\r\n\t}, [tooltipWidth]);\r\n\r\n\tconst positions = [\r\n\t\t{ label: translate(\"Top Left\", { defaultValue: \"Top Left\" }), icon: <TopLeftIcon fontSize=\"small\" />, value: \"top-left\" },\r\n\t\t{ label: translate(\"Top Center\", { defaultValue: \"Top Center\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipTop }} style={{ fontSize: \"small\" }} /> : <TopCenterIcon fontSize=\"small\" />, value: \"top\" },\r\n\t\t{ label: translate(\"Top Right\", { defaultValue: \"Top Right\" }), icon: <TopRightIcon fontSize=\"small\" />, value: \"top-right\" },\r\n\t\t{ label: translate(\"Middle Left\", { defaultValue: \"Middle Left\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipLeft }} style={{ fontSize: \"small\" }} /> : <MiddleLeftIcon fontSize=\"small\" />, value: \"left\" },\r\n\t\t{ label: translate(\"Middle Center\", { defaultValue: \"Middle Center\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipCenter }} style={{ fontSize: \"small\" }} /> : <MiddleCenterIcon fontSize=\"small\" />, value: \"center\" },\r\n\t\t{ label: translate(\"Middle Right\", { defaultValue: \"Middle Right\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipRight }} style={{ fontSize: \"small\" }} /> : <MiddleRightIcon fontSize=\"small\" />, value: \"right\" },\r\n\t\t{ label: translate(\"Bottom Left\", { defaultValue: \"Bottom Left\" }), icon: <BottomLeftIcon fontSize=\"small\" />, value: \"bottom-left\" },\r\n\t\t{ label: translate(\"Bottom Center\", { defaultValue: \"Bottom Center\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipBottom }} style={{ fontSize: \"small\" }} /> : <BottomCenterIcon fontSize=\"small\" />, value: \"bottom\" },\r\n\t\t{ label: translate(\"Bottom Right\", { defaultValue: \"Bottom Right\" }), icon: <BottomRightIcon fontSize=\"small\" />, value: \"bottom-right\" },\r\n\t];\r\n\r\n\tconst handlePositionClick = (e: any) => {\r\n\t\tif (e?.target?.id) {\r\n\t\t\t//setSelectedPosition(e.target.id);\r\n\t\t\tsetTooltipPosition(e.target.id);\r\n\t\t}\r\n\t};\r\n\r\n\tconst onReselectElement = () => {\r\n\r\n\t\tTooltipCanvasSettings({\r\n\t\t\tReSelection: false,\r\n\t\t\tXPosition: 4,\r\n\t\t\tYPosition: 4,\r\n\t\t\twidth: \"300\",\r\n\t\t\tPadding: \"2\",\r\n\t\t\tborderradius: \"8\",\r\n\t\t\tbordersize: \"0\",\r\n\t\t\tborderColor: \"\",\r\n\t\t\tbackgroundColor: \"\",\r\n\t\t\t// PulseAnimation: true,\r\n\t\t\t// stopAnimationUponInteraction: true,\r\n\t\t\t// ShowUpon: \"Hovering Hotspot\",\r\n\t\t\tShowByDefault: false,\r\n\t\t});\r\n\t\tupdateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\r\n\t\tsetElementSelected(true);\r\n\t\tsetIsTooltipPopup(false);\r\n\t\tsetShowTooltipCanvasSettings(false);\r\n\t};\r\n\tconst handleBorderColorChange = (e: any) => {\r\n\t\tif (e?.target?.value) {\r\n\t\t\tsetTempBorderColor(e.target.value);\r\n\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleBackgroundColorChange = (e: any) => {\r\n\t\tif (e?.target?.value) {\r\n\t\t\tsetTooltipBackgroundcolor(e.target.value);\r\n\t\t}\r\n\t};\r\n\tconst handleAutoSelect = (e: any) => {\r\n\t\t//\tsetDismiss(e.target.checked);\r\n\t\tsetAutoPosition(e.target.checked);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowTooltipCanvasSettings(false);\r\n\t};\r\n\r\n\tconst handleApplyChanges = () => {\r\n\t\t// Don't apply changes if there's any validation error\r\n\t\tif (widthError || paddingError || cornerRadiusError || borderSizeError) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// Create the new canvas settings\r\n\t\tconst updatedCanvasSettings = {\r\n\t\t\tposition: tooltipPosition,\r\n\t\t\tbackgroundColor: tooltipBackgroundcolor,\r\n\t\t\twidth: tooltipWidth,\r\n\t\t\tborderRadius: tooltipborderradius,\r\n\t\t\tpadding: tooltippadding,\r\n\t\t\tborderColor: tempBorderColor,\r\n\t\t\tborderSize: tooltipbordersize,\r\n\t\t\tautoposition: autoPosition,\r\n\t\t\txaxis: tooltipXaxis,\r\n\t\t\tyaxis: tooltipYaxis,\r\n\t\t};\r\n\r\n\t\t// Apply the changes - updateCanvasInTooltip will handle recording the change for undo/redo\r\n\t\tupdateCanvasInTooltip(updatedCanvasSettings); // Updates canvas and tooltipBordercolor\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (toolTipGuideMetaData[currentStep - 1]?.canvas) {\r\n\t\t\tconst canvasData = toolTipGuideMetaData[currentStep - 1].canvas;\r\n\r\n\t\t\t// Handle border color - use default if empty, transparent, or invalid\r\n\t\t\tconst borderColor = canvasData?.borderColor;\r\n\t\t\tconst validBorderColor = borderColor && borderColor !== \"transparent\" && borderColor !== \"\" ? borderColor : \"#000000\";\r\n\r\n\t\t\tsetTooltipPosition(canvasData?.position || \"middle-center\");\r\n\t\t\tsetTooltipPadding(canvasData?.padding || \"10px\");\r\n\t\t\tsetTooltipBorderradius(canvasData?.borderRadius || \"8px\");\r\n\t\t\tsetTooltipBordersize(canvasData?.borderSize || \"0px\");\r\n\t\t\tsetTooltipBordercolor(borderColor || \"\");\r\n\t\t\tsetTempBorderColor(validBorderColor); // Use valid color for the input\r\n\t\t\tsetTooltipBackgroundcolor(canvasData?.backgroundColor || \"#FFFFFF\");\r\n\t\t\tsetTooltipWidth(canvasData?.width || \"300px\");\r\n\t\t\tif (selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") {\r\n\t\t\t\tsetTooltipXaxis(canvasData?.xaxis || \"2px\");\r\n\t\t\t\tsetTooltipYaxis(canvasData?.yaxis || \"2px\");\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tsetTooltipXaxis(canvasData?.xaxis || \"100px\");\r\n\t\t\t\tsetTooltipYaxis(canvasData?.yaxis || \"100px\");\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}, [toolTipGuideMetaData[currentStep - 1]?.canvas]);\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\tconst formatValueWithPixelOrPercentage = (value: string) => {\r\n\t\tconst v = String(value);\r\n\t\tlet newValue = v;\r\n\t\tif (v?.endsWith(\"px\") || v?.endsWith(\"%\")) {\r\n\t\t\tnewValue = v.split(/px|%/)[0];\r\n\t\t}\r\n\t\treturn newValue;\r\n\t};\r\n\tconst handleChange = (e: any) => {\r\n\t\t// Only allow numeric input\r\n\t\tconst value = e.target.value;\r\n\t\tif (value === '') {\r\n\t\t\tsetTooltipWidth('0px');\r\n\t\t\tsetWidthError(true); // Empty value is invalid\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tlet inputValue = parseInt(value) || 0;\r\n\r\n\t\t// Validate width - minimum 50 pixels\r\n\t\tif (inputValue < 50) {\r\n\t\t\tsetWidthError(true);\r\n\t\t} else {\r\n\t\t\tsetWidthError(false);\r\n\t\t}\r\n\r\n\t\tsetTooltipWidth(`${inputValue}px`);\r\n\t};\r\n\r\n\r\n\r\n\treturn (\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"back\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? translate(\"Canvas\") : translate(\"Canvas\")}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t{/* <Button\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\tonClick={onReselectElement}\r\n\t\t\t\t\t\t\t\t\t//startIcon={<DesignServicesIcon />}\r\n\t\t\t\t\t\t\t\t\tendIcon={<TouchAppSharp />}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tReselect Element\r\n\t\t\t\t\t\t\t\t</Button> */}\r\n\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{translate(\"Auto Position\")}\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={autoPosition}\r\n        onChange={handleAutoSelect}\r\n        name=\"autoPosition\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-position-grid\"\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\topacity: selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\"? 0.5 : 1,\r\n\t\t\t\t\t\t\t\tcursor:selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\"?\"not-allowed\":\"\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-ctrl-title\"\r\n\t\t\t\t\t\t\t>{translate(\"Position\")}</Typography>\r\n\t\t\t\t\t\t\t<Grid container spacing={1}>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[0].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Top Left - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[1].value}\r\n      onClick={() => {\r\n\r\n        //setSelectedPosition(positions[1].value);\r\n        setTooltipPosition(positions[1].value);\r\n      }}\r\n\tdisabled={autoPosition || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\"}\r\n\tdisableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[1].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipTop }}  id={positions[1].value} />\r\n      ) : (\r\n        <TopCenterIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[2].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Top Right - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[3].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[3].value);\r\n        setTooltipPosition(positions[3].value);\r\n      }}\r\n      disabled={autoPosition}\r\n\t  disableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[3].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipLeft }} id={positions[3].value} />\r\n      ) : (\r\n        <MiddleLeftIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[4].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[4].value);\r\n        setTooltipPosition(positions[4].value);\r\n      }}\r\n\t\t\t\t\t\t\t\t\t\tdisabled={autoPosition}\r\n\t\t\t\t\t\t\t\t\t\tdisableRipple\r\n      sx={{\r\n\t\t  opacity: tooltipPosition === positions[4].value ? 1 : 0.5,\r\n\t\t  paddingLeft:\"0 !important\"\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipCenter }} id={positions[4].value}/>\r\n      ) : (\r\n        <MiddleCenterIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[5].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[5].value);\r\n        setTooltipPosition(positions[5].value);\r\n      }}\r\n      disabled={autoPosition}\r\n\t  disableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[5].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipRight }}  id={positions[5].value} />\r\n      ) : (\r\n        <MiddleRightIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[6].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Bottom Left - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[7].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[7].value);\r\n        setTooltipPosition(positions[7].value);\r\n      }}\r\n      disabled={autoPosition}\r\n\t  disableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[7].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipBottom }} id={positions[7].value} />\r\n      ) : (\r\n        <BottomCenterIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[8].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Bottom Right - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n</Grid>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{/* Width Control */}\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">X {translate(\"Axis Offset\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipXaxis)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => setTooltipXaxis(`${parseInt(e.target.value) || 0}px`)}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdisabled={autoPosition}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">Y {translate(\"Axis Offset\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipYaxis)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => setTooltipYaxis(`${parseInt(e.target.value) || 0}px`)}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdisabled={autoPosition}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\tstyle={{ flexDirection: \"column\", height: \"auto\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box style={{ display: \"flex\", alignItems: \"center\", gap: \"8px\", width: \"100%\" }}>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Width\")}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipWidth)}\r\n\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\t\terror={widthError}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{widthError && (\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\talignItems: \"center\"\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Width must be at least 50 pixels\")}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Padding\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltippadding)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\t\t\t\t\t\t\t\t\t// Validate padding between 0px and 20px\r\n\t\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\t\tsetPaddingError(true);\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tsetPaddingError(false);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tsetTooltipPadding(`${inputValue}px`);\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\terror={paddingError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{paddingError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Corner Radius\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipborderradius)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\t\t\t\t\t\t\t\t\t// Validate corner radius between 0px and 20px\r\n\t\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\t\tsetCornerRadiusError(true);\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tsetCornerRadiusError(false);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tsetTooltipBorderradius(`${inputValue}`);\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\terror={cornerRadiusError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{cornerRadiusError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\r\n\r\n<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Border Size\")}</div>\r\n  <div>\r\n    <TextField\r\n      variant=\"outlined\"\r\n      value={formatValueWithPixelOrPercentage(tooltipbordersize)}\r\n      size=\"small\"\r\n      className=\"qadpt-control-input\"\r\n      onChange={(e) => {\r\n        // Only allow numeric input\r\n        const value = e.target.value;\r\n        if (!/^-?\\d*$/.test(value)) {\r\n          return;\r\n        }\r\n        const inputValue = parseInt(value) || 0;\r\n        // Validate border size between 0px and 20px\r\n        if (inputValue < 0 || inputValue > 5) {\r\n          setBorderSizeError(true);\r\n        } else {\r\n          setBorderSizeError(false);\r\n        }\r\n        setTooltipBordersize(`${inputValue}px`);\r\n      }}\r\n      InputProps={{\r\n        endAdornment: \"px\",\r\n        sx: {\r\n          \"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n          \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n          \"& fieldset\": { border: \"none\" },\r\n        },\r\n      }}\r\n      error={borderSizeError}\r\n    />\r\n  </div>\r\n</Box>\r\n{borderSizeError && (\r\n  <Typography\r\n    style={{\r\n      fontSize: \"12px\",\r\n      color: \"#e9a971\",\r\n      textAlign: \"left\",\r\n      top: \"100%\",\r\n      left: 0,\r\n      marginBottom: \"5px\",\r\n      display: \"flex\",\r\n    }}\r\n  >\r\n    <span\r\n      style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight: \"4px\" }}\r\n      dangerouslySetInnerHTML={{ __html: warning }}\r\n    />\r\n    Value must be between 0px and 5px.\r\n  </Typography>\r\n)}\r\n\r\n{/* Button Selection Dropdown - Only show for Tooltip template */}\r\n{/* {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") && (\r\n  <Box className=\"qadpt-control-box\" style={{ flexDirection: \"column\", height: \"auto\" }}>\r\n    <Typography className=\"qadpt-ctrl-title\">Selected Button</Typography>\r\n    <Box style={{ width: \"100%\" }}>\r\n      {toolTipGuideMetaData?.[currentStep - 1]?.containers?.[1]?.buttons ? (\r\n        <Select\r\n          value={\r\n            toolTipGuideMetaData[currentStep - 1].containers[1].buttons.find(\r\n              (button: any) =>\r\n                button.name === toolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonName\r\n            )?.id || \"\"\r\n          }\r\n          onChange={(event) => {\r\n            const selectedValue = event.target.value;\r\n            const selectedButton = toolTipGuideMetaData[currentStep - 1]?.containers[1]?.buttons?.find(\r\n              (button: any) => button.id === selectedValue\r\n            );\r\n\r\n            // Set dropdown value and button name\r\n            setDropdownValue(selectedValue);\r\n            setElementButtonName(selectedValue);\r\n            setbtnidss(selectedValue);\r\n            setElementClick(\"button\");\r\n\r\n            // Save button ID, button name, and \"NextStep\" in the Design object's \"goToNext\" object\r\n            const updatedCanvasSettings = {\r\n              NextStep: \"button\",\r\n              ButtonId: selectedValue,\r\n              ElementPath: \"\",\r\n              ButtonName: selectedButton?.name,\r\n            };\r\n\r\n            updateDesignelementInTooltip(updatedCanvasSettings);\r\n\r\n            // Set the button action to \"Next\" using updateTooltipButtonAction\r\n            if (selectedButton && selectedButton.id) {\r\n              // Find the container ID for the button\r\n              const containerId = toolTipGuideMetaData[currentStep - 1]?.containers[1]?.id;\r\n\r\n              // Update the button action to \"Next\"\r\n              updateTooltipButtonAction(containerId, selectedValue, {\r\n                value: \"Next\",\r\n                targetURL: \"\",\r\n                tab: \"same-tab\",\r\n                interaction: null,\r\n              });\r\n\r\n              // Set the selected action to \"Next\" in the UI\r\n              setSelectActions(\"Next\");\r\n\r\n              // Mark changes as unsaved\r\n              setIsUnSavedChanges(true);\r\n            }\r\n          }}\r\n          displayEmpty\r\n          style={{ width: \"100%\" }}\r\n          sx={{\r\n            \"& .MuiSelect-select\": {\r\n              textAlign: \"left\",\r\n              padding: \"9px 14px !important\",\r\n            },\r\n            \"& .MuiSvgIcon-root\": {\r\n              height: \"20px\",\r\n              width: \"20px\",\r\n              top: \"10px\",\r\n            },\r\n          }}\r\n        >\r\n          <MenuItem value=\"\" disabled>\r\n            Select a button\r\n          </MenuItem>\r\n          {toolTipGuideMetaData[currentStep - 1].containers[1].buttons.map(\r\n            (button: any, buttonIndex: number) => (\r\n              <MenuItem key={buttonIndex} value={button.id}>\r\n                {button.name}\r\n              </MenuItem>\r\n            )\r\n          )}\r\n        </Select>\r\n      ) : (\r\n        <Typography variant=\"body2\" color=\"textSecondary\">\r\n          No buttons available\r\n        </Typography>\r\n      )}\r\n    </Box>\r\n  </Box>\r\n)} */}\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Border\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={tempBorderColor || \"#000000\"}\r\n\t\t\t\t\t\t\t\tonChange={handleBorderColorChange}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Background\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={tooltipBackgroundcolor}\r\n\t\t\t\t\t\t\t\tonChange={handleBackgroundColorChange}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t<Button\r\n\t\t\t\tvariant=\"contained\"\r\n\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\tclassName={`qadpt-btn ${widthError || paddingError || cornerRadiusError || borderSizeError ? \"disabled\" : \"\"}`}\r\n\t\t\t\tdisabled={widthError || paddingError || cornerRadiusError || borderSizeError} // Disable button if any validation errors exist\r\n\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t</Button>\r\n\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default TooltipCanvasSettings;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,GAAG,CAAEC,UAAU,CAAEC,SAAS,CAAEC,IAAI,CAAEC,UAAU,CAAEC,MAAM,KAAyB,eAAe,CACrG,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,2BAA2B,KAAM,6CAA6C,CACrF,OACCC,mBAAmB,GAAI,CAAAC,WAAW,CAClCC,qBAAqB,GAAI,CAAAC,aAAa,CACtCC,oBAAoB,GAAI,CAAAC,YAAY,CACpCC,gBAAgB,GAAI,CAAAC,cAAc,CAClCC,mBAAmB,GAAI,CAAAC,gBAAgB,CACvCC,mBAAmB,GAAI,CAAAC,eAAe,CACtCX,mBAAmB,GAAI,CAAAY,cAAc,CACrCV,qBAAqB,GAAI,CAAAW,gBAAgB,CACzCT,oBAAoB,GAAI,CAAAU,eAAe,KAEjC,qBAAqB,CAC5B,MAAO,qBAAqB,CAC5B,MAAO,CAAAC,cAAc,EAAIC,oBAAoB,KAA+C,4BAA4B,CACxH,OAASC,WAAW,CAAEC,YAAY,CAAEC,UAAU,CAAEC,aAAa,CAAEC,aAAa,CAAEC,OAAO,KAAQ,6BAA6B,CAE1H,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,qBAAqB,CAAGC,IAAA,EAAgE,KAAAC,sBAAA,IAA/D,CAAEC,OAAO,CAAEC,UAAU,CAAEC,4BAAkC,CAAC,CAAAJ,IAAA,CACxF,KAAM,CAAEK,CAAC,CAAEC,SAAU,CAAC,CAAGZ,cAAc,CAAC,CAAC,CACzC,KAAM,CACLa,eAAe,CACfC,eAAe,CACfC,qBAAqB,CACrBC,YAAY,CACZC,YAAY,CACZC,YAAY,CACZC,eAAe,CACfC,iBAAiB,CACjBC,sBAAsB,CACtBC,oBAAoB,CACpBC,qBAAqB,CACrBC,yBAAyB,CACzBC,cAAc,CACdC,mBAAmB,CACnBC,iBAAiB,CACjBC,kBAAkB,CAClBC,sBAAsB,CACtBC,eAAe,CACfC,kBAAkB,CAClBC,kBAAkB,CAClBC,iBAAiB,CACjBC,oBAAoB,CACpBC,WAAW,CACXC,YAAY,CACZC,eAAe,CACfC,gBAAgB,CAChBC,4BAA4B,CAC5BC,oBAAoB,CACpBC,4BAA4B,CAC5BC,mBACD,CAAC,CAAGlD,cAAc,CAAEmD,KAAU,EAAKA,KAAK,CAAC,CAEzC,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAG7E,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAAC8E,OAAO,CAAEC,UAAU,CAAC,CAAG/E,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACgF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGjF,QAAQ,CAAC,eAAe,CAAC,CACzE,KAAM,CAACkF,KAAK,CAAEC,QAAQ,CAAC,CAAGnF,QAAQ,CAAC,KAAK,CAAC,CACzC,KAAM,CAACoF,YAAY,CAAEC,eAAe,CAAC,CAAGrF,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACsF,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGvF,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACwF,eAAe,CAAEC,kBAAkB,CAAC,CAAGzF,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC0F,UAAU,CAAEC,aAAa,CAAC,CAAG3F,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC4F,eAAe,CAAEC,kBAAkB,CAAC,CAAG7F,QAAQ,CAAC4D,kBAAkB,CAAC,CAE1E7D,SAAS,CAAC,IAAM,CACf;AACA,KAAM,CAAA+F,UAAU,CAAGlC,kBAAkB,EAAIA,kBAAkB,GAAK,aAAa,EAAIA,kBAAkB,GAAK,EAAE,CAAGA,kBAAkB,CAAG,SAAS,CAC3IiC,kBAAkB,CAACC,UAAU,CAAC,CAC7B,CAAC,CAAE,CAAClC,kBAAkB,CAAC,CAAC,CAE1B;AACA7D,SAAS,CAAC,IAAM,CACf,KAAM,CAAAgG,YAAY,CAAGC,QAAQ,CAACC,gCAAgC,CAAC/C,YAAY,CAAC,CAAC,EAAI,CAAC,CAClF,GAAI6C,YAAY,CAAG,EAAE,CAAG,CACvBJ,aAAa,CAAC,IAAI,CAAC,CACpB,CAAC,IAAM,CACNA,aAAa,CAAC,KAAK,CAAC,CACrB,CACD,CAAC,CAAE,CAACzC,YAAY,CAAC,CAAC,CAElB,KAAM,CAAAgD,SAAS,CAAG,CACjB,CAAEC,KAAK,CAAEvD,SAAS,CAAC,UAAU,CAAE,CAAEwD,YAAY,CAAE,UAAW,CAAC,CAAC,CAAEC,IAAI,cAAEnE,IAAA,CAACxB,WAAW,EAAC4F,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,UAAW,CAAC,CACzH,CAAEJ,KAAK,CAAEvD,SAAS,CAAC,YAAY,CAAE,CAAEwD,YAAY,CAAE,YAAa,CAAC,CAAC,CAAEC,IAAI,CAAE/B,gBAAgB,GAAK,SAAS,EAAIE,oBAAoB,GAAK,SAAS,cAAGtC,IAAA,SAAMsE,uBAAuB,CAAE,CAAEC,MAAM,CAAE7E,UAAW,CAAE,CAAC8E,KAAK,CAAE,CAAEJ,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,cAAGpE,IAAA,CAACtB,aAAa,EAAC0F,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,KAAM,CAAC,CAC3R,CAAEJ,KAAK,CAAEvD,SAAS,CAAC,WAAW,CAAE,CAAEwD,YAAY,CAAE,WAAY,CAAC,CAAC,CAAEC,IAAI,cAAEnE,IAAA,CAACpB,YAAY,EAACwF,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,WAAY,CAAC,CAC7H,CAAEJ,KAAK,CAAEvD,SAAS,CAAC,aAAa,CAAE,CAAEwD,YAAY,CAAE,aAAc,CAAC,CAAC,CAAEC,IAAI,CAAE/B,gBAAgB,GAAK,SAAS,EAAIE,oBAAoB,GAAK,SAAS,cAAGtC,IAAA,SAAMsE,uBAAuB,CAAE,CAAEC,MAAM,CAAE/E,WAAY,CAAE,CAACgF,KAAK,CAAE,CAAEJ,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,cAAGpE,IAAA,CAAClB,cAAc,EAACsF,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,MAAO,CAAC,CAChS,CAAEJ,KAAK,CAAEvD,SAAS,CAAC,eAAe,CAAE,CAAEwD,YAAY,CAAE,eAAgB,CAAC,CAAC,CAAEC,IAAI,CAAE/B,gBAAgB,GAAK,SAAS,EAAIE,oBAAoB,GAAK,SAAS,cAAGtC,IAAA,SAAMsE,uBAAuB,CAAE,CAAEC,MAAM,CAAE3E,aAAc,CAAE,CAAC4E,KAAK,CAAE,CAAEJ,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,cAAGpE,IAAA,CAAChB,gBAAgB,EAACoF,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,QAAS,CAAC,CAC1S,CAAEJ,KAAK,CAAEvD,SAAS,CAAC,cAAc,CAAE,CAAEwD,YAAY,CAAE,cAAe,CAAC,CAAC,CAAEC,IAAI,CAAE/B,gBAAgB,GAAK,SAAS,EAAIE,oBAAoB,GAAK,SAAS,cAAGtC,IAAA,SAAMsE,uBAAuB,CAAE,CAAEC,MAAM,CAAE9E,YAAa,CAAE,CAAC+E,KAAK,CAAE,CAAEJ,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,cAAGpE,IAAA,CAACd,eAAe,EAACkF,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,OAAQ,CAAC,CACrS,CAAEJ,KAAK,CAAEvD,SAAS,CAAC,aAAa,CAAE,CAAEwD,YAAY,CAAE,aAAc,CAAC,CAAC,CAAEC,IAAI,cAAEnE,IAAA,CAACb,cAAc,EAACiF,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,aAAc,CAAC,CACrI,CAAEJ,KAAK,CAAEvD,SAAS,CAAC,eAAe,CAAE,CAAEwD,YAAY,CAAE,eAAgB,CAAC,CAAC,CAAEC,IAAI,CAAE/B,gBAAgB,GAAK,SAAS,EAAIE,oBAAoB,GAAK,SAAS,cAAGtC,IAAA,SAAMsE,uBAAuB,CAAE,CAAEC,MAAM,CAAE5E,aAAc,CAAE,CAAC6E,KAAK,CAAE,CAAEJ,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,cAAGpE,IAAA,CAACZ,gBAAgB,EAACgF,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,QAAS,CAAC,CAC1S,CAAEJ,KAAK,CAAEvD,SAAS,CAAC,cAAc,CAAE,CAAEwD,YAAY,CAAE,cAAe,CAAC,CAAC,CAAEC,IAAI,cAAEnE,IAAA,CAACX,eAAe,EAAC+E,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,cAAe,CAAC,CACzI,CAED,KAAM,CAAAI,mBAAmB,CAAIC,CAAM,EAAK,KAAAC,SAAA,CACvC,GAAID,CAAC,SAADA,CAAC,YAAAC,SAAA,CAADD,CAAC,CAAEE,MAAM,UAAAD,SAAA,WAATA,SAAA,CAAWE,EAAE,CAAE,CAClB;AACAhD,kBAAkB,CAAC6C,CAAC,CAACE,MAAM,CAACC,EAAE,CAAC,CAChC,CACD,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAE/B3E,qBAAqB,CAAC,CACrB4E,WAAW,CAAE,KAAK,CAClBC,SAAS,CAAE,CAAC,CACZC,SAAS,CAAE,CAAC,CACZC,KAAK,CAAE,KAAK,CACZC,OAAO,CAAE,GAAG,CACZC,YAAY,CAAE,GAAG,CACjBC,UAAU,CAAE,GAAG,CACfC,WAAW,CAAE,EAAE,CACfC,eAAe,CAAE,EAAE,CACnB;AACA;AACA;AACAC,aAAa,CAAE,KAChB,CAAC,CAAC,CACF3E,qBAAqB,CAACtB,oBAAoB,CAAC,CAC3CuC,kBAAkB,CAAC,IAAI,CAAC,CACxBC,iBAAiB,CAAC,KAAK,CAAC,CACxBvB,4BAA4B,CAAC,KAAK,CAAC,CACpC,CAAC,CACD,KAAM,CAAAiF,uBAAuB,CAAIf,CAAM,EAAK,KAAAgB,UAAA,CAC3C,GAAIhB,CAAC,SAADA,CAAC,YAAAgB,UAAA,CAADhB,CAAC,CAAEE,MAAM,UAAAc,UAAA,WAATA,UAAA,CAAWrB,KAAK,CAAE,CACrBV,kBAAkB,CAACe,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC,CAEnC,CACD,CAAC,CAED,KAAM,CAAAsB,2BAA2B,CAAIjB,CAAM,EAAK,KAAAkB,UAAA,CAC/C,GAAIlB,CAAC,SAADA,CAAC,YAAAkB,UAAA,CAADlB,CAAC,CAAEE,MAAM,UAAAgB,UAAA,WAATA,UAAA,CAAWvB,KAAK,CAAE,CACrB/C,yBAAyB,CAACoD,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC,CAC1C,CACD,CAAC,CACD,KAAM,CAAAwB,gBAAgB,CAAInB,CAAM,EAAK,CACpC;AACAvC,eAAe,CAACuC,CAAC,CAACE,MAAM,CAACkB,OAAO,CAAC,CAClC,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACzBpD,SAAS,CAAC,KAAK,CAAC,CAChBnC,4BAA4B,CAAC,KAAK,CAAC,CACpC,CAAC,CAED,KAAM,CAAAwF,kBAAkB,CAAGA,CAAA,GAAM,CAChC;AACA,GAAIxC,UAAU,EAAIN,YAAY,EAAIE,iBAAiB,EAAIE,eAAe,CAAE,CACvE,OACD,CAEA;AACA,KAAM,CAAA2C,qBAAqB,CAAG,CAC7BC,QAAQ,CAAEtE,eAAe,CACzB2D,eAAe,CAAE5D,sBAAsB,CACvCuD,KAAK,CAAElE,YAAY,CACnBmF,YAAY,CAAE3E,mBAAmB,CACjC4E,OAAO,CAAE7E,cAAc,CACvB+D,WAAW,CAAE5B,eAAe,CAC5B2C,UAAU,CAAE5E,iBAAiB,CAC7B6E,YAAY,CAAEpE,YAAY,CAC1BqE,KAAK,CAAEzF,YAAY,CACnB0F,KAAK,CAAEzF,YACR,CAAC,CAED;AACAF,qBAAqB,CAACoF,qBAAqB,CAAC,CAAE;AAC9CF,WAAW,CAAC,CAAC,CACbvD,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED3E,SAAS,CAAC,IAAM,KAAA4I,qBAAA,CACf,IAAAA,qBAAA,CAAIzE,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,UAAAwE,qBAAA,WAArCA,qBAAA,CAAuCC,MAAM,CAAE,CAClD,KAAM,CAAAC,UAAU,CAAG3E,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,CAACyE,MAAM,CAE/D;AACA,KAAM,CAAApB,WAAW,CAAGqB,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAErB,WAAW,CAC3C,KAAM,CAAAsB,gBAAgB,CAAGtB,WAAW,EAAIA,WAAW,GAAK,aAAa,EAAIA,WAAW,GAAK,EAAE,CAAGA,WAAW,CAAG,SAAS,CAErHzD,kBAAkB,CAAC,CAAA8E,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAET,QAAQ,GAAI,eAAe,CAAC,CAC3DhF,iBAAiB,CAAC,CAAAyF,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEP,OAAO,GAAI,MAAM,CAAC,CAChDjF,sBAAsB,CAAC,CAAAwF,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAER,YAAY,GAAI,KAAK,CAAC,CACzD/E,oBAAoB,CAAC,CAAAuF,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEN,UAAU,GAAI,KAAK,CAAC,CACrDhF,qBAAqB,CAACiE,WAAW,EAAI,EAAE,CAAC,CACxC3B,kBAAkB,CAACiD,gBAAgB,CAAC,CAAE;AACtCtF,yBAAyB,CAAC,CAAAqF,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEpB,eAAe,GAAI,SAAS,CAAC,CACnEtE,eAAe,CAAC,CAAA0F,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEzB,KAAK,GAAI,OAAO,CAAC,CAC7C,GAAI9C,gBAAgB,GAAK,SAAS,EAAIE,oBAAoB,GAAK,SAAS,CAAE,CACzE3B,eAAe,CAAC,CAAAgG,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEJ,KAAK,GAAI,KAAK,CAAC,CAC3C3F,eAAe,CAAC,CAAA+F,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEH,KAAK,GAAI,KAAK,CAAC,CAC5C,CAAC,IACI,CACJ7F,eAAe,CAAC,CAAAgG,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEJ,KAAK,GAAI,OAAO,CAAC,CAC7C3F,eAAe,CAAC,CAAA+F,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEH,KAAK,GAAI,OAAO,CAAC,CAE9C,CACD,CACD,CAAC,CAAE,EAAAnG,sBAAA,CAAC2B,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,UAAA5B,sBAAA,iBAArCA,sBAAA,CAAuCqG,MAAM,CAAC,CAAC,CAEnD,GAAI,CAAChE,MAAM,CAAE,MAAO,KAAI,CAExB,KAAM,CAAAqB,gCAAgC,CAAIM,KAAa,EAAK,CAC3D,KAAM,CAAAwC,CAAC,CAAGC,MAAM,CAACzC,KAAK,CAAC,CACvB,GAAI,CAAA0C,QAAQ,CAAGF,CAAC,CAChB,GAAIA,CAAC,SAADA,CAAC,WAADA,CAAC,CAAEG,QAAQ,CAAC,IAAI,CAAC,EAAIH,CAAC,SAADA,CAAC,WAADA,CAAC,CAAEG,QAAQ,CAAC,GAAG,CAAC,CAAE,CAC1CD,QAAQ,CAAGF,CAAC,CAACI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAC9B,CACA,MAAO,CAAAF,QAAQ,CAChB,CAAC,CACD,KAAM,CAAAG,YAAY,CAAIxC,CAAM,EAAK,CAChC;AACA,KAAM,CAAAL,KAAK,CAAGK,CAAC,CAACE,MAAM,CAACP,KAAK,CAC5B,GAAIA,KAAK,GAAK,EAAE,CAAE,CACjBpD,eAAe,CAAC,KAAK,CAAC,CACtBwC,aAAa,CAAC,IAAI,CAAC,CAAE;AACrB,OACD,CAEA,GAAI,CAAC,SAAS,CAAC0D,IAAI,CAAC9C,KAAK,CAAC,CAAE,CAC3B,OACD,CAEA,GAAI,CAAA+C,UAAU,CAAGtD,QAAQ,CAACO,KAAK,CAAC,EAAI,CAAC,CAErC;AACA,GAAI+C,UAAU,CAAG,EAAE,CAAE,CACpB3D,aAAa,CAAC,IAAI,CAAC,CACpB,CAAC,IAAM,CACNA,aAAa,CAAC,KAAK,CAAC,CACrB,CAEAxC,eAAe,CAAC,GAAGmG,UAAU,IAAI,CAAC,CACnC,CAAC,CAID,mBACCpH,IAAA,QACC6E,EAAE,CAAC,mBAAmB,CACtBwC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAE7BpH,KAAA,QAAKmH,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC7BpH,KAAA,QAAKmH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnCtH,IAAA,CAAC7B,UAAU,EACV,aAAW,MAAM,CACjBoJ,OAAO,CAAExB,WAAY,CAAAuB,QAAA,cAErBtH,IAAA,CAAC1B,2BAA2B,GAAE,CAAC,CACpB,CAAC,cACb0B,IAAA,QAAKqH,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAGlF,gBAAgB,GAAK,SAAS,EAAIE,oBAAoB,GAAK,SAAS,CAAI5B,SAAS,CAAC,QAAQ,CAAC,CAAGA,SAAS,CAAC,QAAQ,CAAC,CAAM,CAAC,cACvJV,IAAA,CAAC7B,UAAU,EACVqJ,IAAI,CAAC,OAAO,CACZ,aAAW,OAAO,CAClBD,OAAO,CAAExB,WAAY,CAAAuB,QAAA,cAErBtH,IAAA,CAAC3B,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cACN2B,IAAA,QAAKqH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC9BpH,KAAA,QAAKmH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC9BpH,KAAA,CAACnC,GAAG,EAACsJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAUjCtH,IAAA,QACCqH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAG9B5G,SAAS,CAAC,eAAe,CAAC,CACvB,CAAC,cACNV,IAAA,QAAAsH,QAAA,cACApH,KAAA,UAAOmH,SAAS,CAAC,eAAe,CAAAC,QAAA,eACnCtH,IAAA,UACIyH,IAAI,CAAC,UAAU,CACf3B,OAAO,CAAE5D,YAAa,CACtBwF,QAAQ,CAAE7B,gBAAiB,CAC3B8B,IAAI,CAAC,cAAc,CACtB,CAAC,cACF3H,IAAA,SAAMqH,SAAS,CAAC,QAAQ,CAAO,CAAC,EACrB,CAAC,CACH,CAAC,EACH,CAAC,cAGNnH,KAAA,CAACnC,GAAG,EAACsJ,SAAS,CAAC,qBAAqB,CACnCO,EAAE,CAAE,CACHC,OAAO,CAAEzF,gBAAgB,GAAK,SAAS,EAAIE,oBAAoB,GAAK,SAAS,CAAE,GAAG,CAAG,CAAC,CACtFwF,MAAM,CAAC1F,gBAAgB,GAAK,SAAS,EAAIE,oBAAoB,GAAK,SAAS,CAAC,aAAa,CAAC,EAC3F,CAAE,CAAAgF,QAAA,eAEFtH,IAAA,CAAChC,UAAU,EAACqJ,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CACtC5G,SAAS,CAAC,UAAU,CAAC,CAAa,CAAC,cACrCR,KAAA,CAAChC,IAAI,EAAC6J,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAV,QAAA,eAChCtH,IAAA,CAAC9B,IAAI,EAAC+J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACftH,IAAA,CAAC7B,UAAU,EACTqJ,IAAI,CAAC,OAAO,CACZW,QAAQ,CAAEjG,YAAa,CACvB0F,EAAE,CAAE,CACFC,OAAO,CAAE/E,gBAAgB,GAAKkB,SAAS,CAAC,CAAC,CAAC,CAACK,KAAK,CAAG,CAAC,CAAG,GACzD,CAAE,CAGQ,CAAC,CACT,CAAC,cACPrE,IAAA,CAAC9B,IAAI,EAAC+J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACftH,IAAA,CAAC7B,UAAU,EACTqJ,IAAI,CAAC,OAAO,CACZ3C,EAAE,CAAEb,SAAS,CAAC,CAAC,CAAC,CAACK,KAAM,CACvBkD,OAAO,CAAEA,CAAA,GAAM,CAEb;AACA1F,kBAAkB,CAACmC,SAAS,CAAC,CAAC,CAAC,CAACK,KAAK,CAAC,CACxC,CAAE,CACP8D,QAAQ,CAAEjG,YAAY,EAAIE,gBAAgB,GAAK,SAAS,EAAIE,oBAAoB,GAAK,SAAU,CAC/F8F,aAAa,MACRR,EAAE,CAAE,CACFC,OAAO,CAAEjG,eAAe,GAAKoC,SAAS,CAAC,CAAC,CAAC,CAACK,KAAK,CAAG,CAAC,CAAG,GACxD,CAAE,CAAAiD,QAAA,CAEAlF,gBAAgB,GAAK,SAAS,EAAIE,oBAAoB,GAAK,SAAS,cACpEtC,IAAA,SAAMsE,uBAAuB,CAAE,CAAEC,MAAM,CAAE7E,UAAW,CAAE,CAAEmF,EAAE,CAAEb,SAAS,CAAC,CAAC,CAAC,CAACK,KAAM,CAAE,CAAC,cAElFrE,IAAA,CAACtB,aAAa,EAAC0F,QAAQ,CAAC,OAAO,CAAE,CAClC,CACS,CAAC,CACT,CAAC,cACPpE,IAAA,CAAC9B,IAAI,EAAC+J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACftH,IAAA,CAAC7B,UAAU,EACTqJ,IAAI,CAAC,OAAO,CACZW,QAAQ,CAAEjG,YAAa,CACvB0F,EAAE,CAAE,CACFC,OAAO,CAAE/E,gBAAgB,GAAKkB,SAAS,CAAC,CAAC,CAAC,CAACK,KAAK,CAAG,CAAC,CAAG,GACzD,CAAE,CAGQ,CAAC,CACT,CAAC,cACPrE,IAAA,CAAC9B,IAAI,EAAC+J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACftH,IAAA,CAAC7B,UAAU,EACTqJ,IAAI,CAAC,OAAO,CACZ3C,EAAE,CAAEb,SAAS,CAAC,CAAC,CAAC,CAACK,KAAM,CACvBkD,OAAO,CAAEA,CAAA,GAAM,CACd;AACC1F,kBAAkB,CAACmC,SAAS,CAAC,CAAC,CAAC,CAACK,KAAK,CAAC,CACxC,CAAE,CACF8D,QAAQ,CAAEjG,YAAa,CAC1BkG,aAAa,MACVR,EAAE,CAAE,CACFC,OAAO,CAAEjG,eAAe,GAAKoC,SAAS,CAAC,CAAC,CAAC,CAACK,KAAK,CAAG,CAAC,CAAG,GACxD,CAAE,CAAAiD,QAAA,CAEAlF,gBAAgB,GAAK,SAAS,EAAIE,oBAAoB,GAAK,SAAS,cACpEtC,IAAA,SAAMsE,uBAAuB,CAAE,CAAEC,MAAM,CAAE/E,WAAY,CAAE,CAACqF,EAAE,CAAEb,SAAS,CAAC,CAAC,CAAC,CAACK,KAAM,CAAE,CAAC,cAElFrE,IAAA,CAAClB,cAAc,EAACsF,QAAQ,CAAC,OAAO,CAAE,CACnC,CACS,CAAC,CACT,CAAC,cACPpE,IAAA,CAAC9B,IAAI,EAAC+J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACftH,IAAA,CAAC7B,UAAU,EACTqJ,IAAI,CAAC,OAAO,CACZ3C,EAAE,CAAEb,SAAS,CAAC,CAAC,CAAC,CAACK,KAAM,CACvBkD,OAAO,CAAEA,CAAA,GAAM,CACd;AACC1F,kBAAkB,CAACmC,SAAS,CAAC,CAAC,CAAC,CAACK,KAAK,CAAC,CACxC,CAAE,CACE8D,QAAQ,CAAEjG,YAAa,CACvBkG,aAAa,MACjBR,EAAE,CAAE,CACNC,OAAO,CAAEjG,eAAe,GAAKoC,SAAS,CAAC,CAAC,CAAC,CAACK,KAAK,CAAG,CAAC,CAAG,GAAG,CACzDgE,WAAW,CAAC,cACV,CAAE,CAAAf,QAAA,CAEAlF,gBAAgB,GAAK,SAAS,EAAIE,oBAAoB,GAAK,SAAS,cACpEtC,IAAA,SAAMsE,uBAAuB,CAAE,CAAEC,MAAM,CAAE3E,aAAc,CAAE,CAACiF,EAAE,CAAEb,SAAS,CAAC,CAAC,CAAC,CAACK,KAAM,CAAC,CAAC,cAEnFrE,IAAA,CAAChB,gBAAgB,EAACoF,QAAQ,CAAC,OAAO,CAAE,CACrC,CACS,CAAC,CACT,CAAC,cACPpE,IAAA,CAAC9B,IAAI,EAAC+J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACftH,IAAA,CAAC7B,UAAU,EACTqJ,IAAI,CAAC,OAAO,CACZ3C,EAAE,CAAEb,SAAS,CAAC,CAAC,CAAC,CAACK,KAAM,CACvBkD,OAAO,CAAEA,CAAA,GAAM,CACd;AACC1F,kBAAkB,CAACmC,SAAS,CAAC,CAAC,CAAC,CAACK,KAAK,CAAC,CACxC,CAAE,CACF8D,QAAQ,CAAEjG,YAAa,CAC1BkG,aAAa,MACVR,EAAE,CAAE,CACFC,OAAO,CAAEjG,eAAe,GAAKoC,SAAS,CAAC,CAAC,CAAC,CAACK,KAAK,CAAG,CAAC,CAAG,GACxD,CAAE,CAAAiD,QAAA,CAEAlF,gBAAgB,GAAK,SAAS,EAAIE,oBAAoB,GAAK,SAAS,cACpEtC,IAAA,SAAMsE,uBAAuB,CAAE,CAAEC,MAAM,CAAE9E,YAAa,CAAE,CAAEoF,EAAE,CAAEb,SAAS,CAAC,CAAC,CAAC,CAACK,KAAM,CAAE,CAAC,cAEpFrE,IAAA,CAACd,eAAe,EAACkF,QAAQ,CAAC,OAAO,CAAE,CACpC,CACS,CAAC,CACT,CAAC,cACPpE,IAAA,CAAC9B,IAAI,EAAC+J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACftH,IAAA,CAAC7B,UAAU,EACTqJ,IAAI,CAAC,OAAO,CACZW,QAAQ,CAAEjG,YAAa,CACvB0F,EAAE,CAAE,CACFC,OAAO,CAAE/E,gBAAgB,GAAKkB,SAAS,CAAC,CAAC,CAAC,CAACK,KAAK,CAAG,CAAC,CAAG,GACzD,CAAE,CAGQ,CAAC,CACT,CAAC,cACPrE,IAAA,CAAC9B,IAAI,EAAC+J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACftH,IAAA,CAAC7B,UAAU,EACTqJ,IAAI,CAAC,OAAO,CACZ3C,EAAE,CAAEb,SAAS,CAAC,CAAC,CAAC,CAACK,KAAM,CACvBkD,OAAO,CAAEA,CAAA,GAAM,CACd;AACC1F,kBAAkB,CAACmC,SAAS,CAAC,CAAC,CAAC,CAACK,KAAK,CAAC,CACxC,CAAE,CACF8D,QAAQ,CAAEjG,YAAa,CAC1BkG,aAAa,MACVR,EAAE,CAAE,CACFC,OAAO,CAAEjG,eAAe,GAAKoC,SAAS,CAAC,CAAC,CAAC,CAACK,KAAK,CAAG,CAAC,CAAG,GACxD,CAAE,CAAAiD,QAAA,CAEAlF,gBAAgB,GAAK,SAAS,EAAIE,oBAAoB,GAAK,SAAS,cACpEtC,IAAA,SAAMsE,uBAAuB,CAAE,CAAEC,MAAM,CAAE5E,aAAc,CAAE,CAACkF,EAAE,CAAEb,SAAS,CAAC,CAAC,CAAC,CAACK,KAAM,CAAE,CAAC,cAEpFrE,IAAA,CAACZ,gBAAgB,EAACgF,QAAQ,CAAC,OAAO,CAAE,CACrC,CACS,CAAC,CACT,CAAC,cACPpE,IAAA,CAAC9B,IAAI,EAAC+J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACftH,IAAA,CAAC7B,UAAU,EACTqJ,IAAI,CAAC,OAAO,CACZW,QAAQ,CAAEjG,YAAa,CACvB0F,EAAE,CAAE,CACFC,OAAO,CAAE/E,gBAAgB,GAAKkB,SAAS,CAAC,CAAC,CAAC,CAACK,KAAK,CAAG,CAAC,CAAG,GACzD,CAAE,CAGQ,CAAC,CACT,CAAC,EACH,CAAC,EACI,CAAC,cAGNnE,KAAA,CAACnC,GAAG,EAACsJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjCpH,KAAA,QAAKmH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAC,IAAE,CAAC5G,SAAS,CAAC,aAAa,CAAC,EAAM,CAAC,cACvEV,IAAA,QAAAsH,QAAA,cACAtH,IAAA,CAAC/B,SAAS,EACTqK,OAAO,CAAC,UAAU,CAClBjE,KAAK,CAAEN,gCAAgC,CAACjD,YAAY,CAAE,CAEtD0G,IAAI,CAAC,OAAO,CACZH,SAAS,CAAC,qBAAqB,CAC/BK,QAAQ,CAAGhD,CAAC,EAAK/D,eAAe,CAAC,GAAGmD,QAAQ,CAACY,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC,EAAI,CAAC,IAAI,CAAE,CACvEkE,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBZ,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEa,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAE5B,CACD,CAAE,CACFN,QAAQ,CAAEjG,YAAa,CACtB,CAAC,CACG,CAAC,EACH,CAAC,cACNhC,KAAA,CAACnC,GAAG,EAACsJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjCpH,KAAA,QAAKmH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAC,IAAE,CAAC5G,SAAS,CAAC,aAAa,CAAC,EAAM,CAAC,cACvEV,IAAA,QAAAsH,QAAA,cACAtH,IAAA,CAAC/B,SAAS,EACTqK,OAAO,CAAC,UAAU,CAClBjE,KAAK,CAAEN,gCAAgC,CAAChD,YAAY,CAAE,CAEtDyG,IAAI,CAAC,OAAO,CACZH,SAAS,CAAC,qBAAqB,CAC/BK,QAAQ,CAAGhD,CAAC,EAAK9D,eAAe,CAAC,GAAGkD,QAAQ,CAACY,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC,EAAI,CAAC,IAAI,CAAE,CACvEkE,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBZ,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEa,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAE5B,CACD,CAAE,CACFN,QAAQ,CAAEjG,YAAa,CACtB,CAAC,CACG,CAAC,EACH,CAAC,cAENlC,IAAA,CAACjC,GAAG,EACHsJ,SAAS,CAAC,mBAAmB,CAC7B7C,KAAK,CAAE,CAAEkE,aAAa,CAAE,QAAQ,CAAEC,MAAM,CAAE,MAAO,CAAE,CAAArB,QAAA,cAEnDpH,KAAA,CAACnC,GAAG,EAACyG,KAAK,CAAE,CAAEoE,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,KAAK,CAAE5D,KAAK,CAAE,MAAO,CAAE,CAAAoC,QAAA,eAChFtH,IAAA,QACCqH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAG9B5G,SAAS,CAAC,OAAO,CAAC,CACf,CAAC,cACNV,IAAA,QAAAsH,QAAA,cACAtH,IAAA,CAAC/B,SAAS,EACTqK,OAAO,CAAC,UAAU,CAClBjE,KAAK,CAAEN,gCAAgC,CAAC/C,YAAY,CAAE,CAEtDwG,IAAI,CAAC,OAAO,CACZH,SAAS,CAAC,qBAAqB,CAC/BK,QAAQ,CAAER,YAAa,CACvBlE,KAAK,CAAEQ,UAAW,CAClB+E,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBZ,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEa,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAE5B,CACD,CAAE,CAGD,CAAC,CACG,CAAC,EACH,CAAC,CAEF,CAAC,CACLjF,UAAU,eACVtD,KAAA,CAAClC,UAAU,EACVwG,KAAK,CAAE,CACNJ,QAAQ,CAAE,MAAM,CAChB2E,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPC,YAAY,CAAE,KAAK,CACnBP,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QACb,CAAE,CAAAvB,QAAA,eAEFtH,IAAA,SAAMwE,KAAK,CAAE,CAAEoE,OAAO,CAAE,MAAM,CAAExE,QAAQ,CAAE,MAAM,CAAEyE,UAAU,CAAE,QAAQ,CAAEO,WAAW,CAAC,KAAM,CAAE,CAC3F9E,uBAAuB,CAAE,CAAEC,MAAM,CAAE1E,OAAQ,CAAE,CAC7C,CAAC,CACDa,SAAS,CAAC,kCAAkC,CAAC,EACnC,CACZ,cAGDR,KAAA,CAACnC,GAAG,EAACsJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjCtH,IAAA,QAAKqH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE5G,SAAS,CAAC,SAAS,CAAC,CAAM,CAAC,cACjEV,IAAA,QAAAsH,QAAA,cACAtH,IAAA,CAAC/B,SAAS,EACTqK,OAAO,CAAC,UAAU,CAClBjE,KAAK,CAAEN,gCAAgC,CAACxC,cAAc,CAAE,CAExDiG,IAAI,CAAC,OAAO,CACZH,SAAS,CAAC,qBAAqB,CAC/BK,QAAQ,CAAGhD,CAAC,EAAK,CAChB;AACA,KAAM,CAAAL,KAAK,CAAGK,CAAC,CAACE,MAAM,CAACP,KAAK,CAC5B,GAAI,CAAC,SAAS,CAAC8C,IAAI,CAAC9C,KAAK,CAAC,CAAE,CAC3B,OACD,CACA,KAAM,CAAA+C,UAAU,CAAGtD,QAAQ,CAACO,KAAK,CAAC,EAAI,CAAC,CACvC;AACA,GAAI+C,UAAU,CAAG,CAAC,EAAIA,UAAU,CAAG,EAAE,CAAE,CACtCjE,eAAe,CAAC,IAAI,CAAC,CACtB,CAAC,IAAM,CACNA,eAAe,CAAC,KAAK,CAAC,CACvB,CACAjC,iBAAiB,CAAC,GAAGkG,UAAU,IAAI,CAAC,CACrC,CAAE,CACFmB,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBZ,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEa,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAE5B,CACD,CAAE,CACFzF,KAAK,CAAEE,YAAa,CACnB,CAAC,CACG,CAAC,EACH,CAAC,CACLA,YAAY,eACbhD,KAAA,CAAClC,UAAU,EACXwG,KAAK,CAAE,CACNJ,QAAQ,CAAE,MAAM,CAChB2E,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPC,YAAY,CAAE,KAAK,CACnBP,OAAO,CAAE,MACV,CAAE,CAAAtB,QAAA,eACAtH,IAAA,SAAMwE,KAAK,CAAE,CAAEoE,OAAO,CAAE,MAAM,CAAExE,QAAQ,CAAE,MAAM,CAAEyE,UAAU,CAAE,QAAQ,CAAEO,WAAW,CAAC,KAAM,CAAE,CAE9F9E,uBAAuB,CAAE,CAAEC,MAAM,CAAE1E,OAAQ,CAAE,CAC7C,CAAC,CACEa,SAAS,CAAC,qCAAqC,CAAC,EACtC,CACZ,cAEDR,KAAA,CAACnC,GAAG,EAACsJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjCtH,IAAA,QAAKqH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE5G,SAAS,CAAC,eAAe,CAAC,CAAM,CAAC,cACvEV,IAAA,QAAAsH,QAAA,cACAtH,IAAA,CAAC/B,SAAS,EACTqK,OAAO,CAAC,UAAU,CAClBjE,KAAK,CAAEN,gCAAgC,CAACvC,mBAAmB,CAAE,CAE7DgG,IAAI,CAAC,OAAO,CACZH,SAAS,CAAC,qBAAqB,CAC/BK,QAAQ,CAAGhD,CAAC,EAAK,CAChB;AACA,KAAM,CAAAL,KAAK,CAAGK,CAAC,CAACE,MAAM,CAACP,KAAK,CAC5B,GAAI,CAAC,SAAS,CAAC8C,IAAI,CAAC9C,KAAK,CAAC,CAAE,CAC3B,OACD,CACA,KAAM,CAAA+C,UAAU,CAAGtD,QAAQ,CAACO,KAAK,CAAC,EAAI,CAAC,CACvC;AACA,GAAI+C,UAAU,CAAG,CAAC,EAAIA,UAAU,CAAG,EAAE,CAAE,CACtC/D,oBAAoB,CAAC,IAAI,CAAC,CAC3B,CAAC,IAAM,CACNA,oBAAoB,CAAC,KAAK,CAAC,CAC5B,CACAlC,sBAAsB,CAAC,GAAGiG,UAAU,EAAE,CAAC,CACxC,CAAE,CACFmB,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBZ,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEa,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAE5B,CACD,CAAE,CACFzF,KAAK,CAAEI,iBAAkB,CACxB,CAAC,CACG,CAAC,EACH,CAAC,CACLA,iBAAiB,eAClBlD,KAAA,CAAClC,UAAU,EACXwG,KAAK,CAAE,CACNJ,QAAQ,CAAE,MAAM,CAChB2E,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPC,YAAY,CAAE,KAAK,CACnBP,OAAO,CAAE,MACV,CAAE,CAAAtB,QAAA,eACAtH,IAAA,SAAMwE,KAAK,CAAE,CAAEoE,OAAO,CAAE,MAAM,CAAExE,QAAQ,CAAE,MAAM,CAAEyE,UAAU,CAAE,QAAQ,CAAEO,WAAW,CAAC,KAAM,CAAE,CAE9F9E,uBAAuB,CAAE,CAAEC,MAAM,CAAE1E,OAAQ,CAAE,CAC7C,CAAC,CACEa,SAAS,CAAC,qCAAqC,CAAC,EACtC,CACZ,cAGPR,KAAA,CAACnC,GAAG,EAACsJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC3BtH,IAAA,QAAKqH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE5G,SAAS,CAAC,aAAa,CAAC,CAAM,CAAC,cAC1EV,IAAA,QAAAsH,QAAA,cACEtH,IAAA,CAAC/B,SAAS,EACRqK,OAAO,CAAC,UAAU,CAClBjE,KAAK,CAAEN,gCAAgC,CAACtC,iBAAiB,CAAE,CAC3D+F,IAAI,CAAC,OAAO,CACZH,SAAS,CAAC,qBAAqB,CAC/BK,QAAQ,CAAGhD,CAAC,EAAK,CACf;AACA,KAAM,CAAAL,KAAK,CAAGK,CAAC,CAACE,MAAM,CAACP,KAAK,CAC5B,GAAI,CAAC,SAAS,CAAC8C,IAAI,CAAC9C,KAAK,CAAC,CAAE,CAC1B,OACF,CACA,KAAM,CAAA+C,UAAU,CAAGtD,QAAQ,CAACO,KAAK,CAAC,EAAI,CAAC,CACvC;AACA,GAAI+C,UAAU,CAAG,CAAC,EAAIA,UAAU,CAAG,CAAC,CAAE,CACpC7D,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,IAAM,CACLA,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CACAnC,oBAAoB,CAAC,GAAGgG,UAAU,IAAI,CAAC,CACzC,CAAE,CACFmB,UAAU,CAAE,CACVC,YAAY,CAAE,IAAI,CAClBZ,EAAE,CAAE,CACF,0CAA0C,CAAE,CAAEa,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CACjC,CACF,CAAE,CACFzF,KAAK,CAAEM,eAAgB,CACxB,CAAC,CACC,CAAC,EACH,CAAC,CACLA,eAAe,eACdpD,KAAA,CAAClC,UAAU,EACTwG,KAAK,CAAE,CACLJ,QAAQ,CAAE,MAAM,CAChB2E,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPC,YAAY,CAAE,KAAK,CACnBP,OAAO,CAAE,MACX,CAAE,CAAAtB,QAAA,eAEFtH,IAAA,SACEwE,KAAK,CAAE,CAAEoE,OAAO,CAAE,MAAM,CAAExE,QAAQ,CAAE,MAAM,CAAEyE,UAAU,CAAE,QAAQ,CAAEO,WAAW,CAAE,KAAM,CAAE,CACvF9E,uBAAuB,CAAE,CAAEC,MAAM,CAAE1E,OAAQ,CAAE,CAC9C,CAAC,qCAEJ,EAAY,CACb,cA2FKK,KAAA,CAACnC,GAAG,EAACsJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjCtH,IAAA,QAAKqH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE5G,SAAS,CAAC,QAAQ,CAAC,CAAM,CAAC,cAChEV,IAAA,QAAAsH,QAAA,cACAtH,IAAA,UACCyH,IAAI,CAAC,OAAO,CACZpD,KAAK,CAAEX,eAAe,EAAI,SAAU,CACpCgE,QAAQ,CAAEjC,uBAAwB,CAClC4B,SAAS,CAAC,mBAAmB,CAC5B,CAAC,CACG,CAAC,EACH,CAAC,cAENnH,KAAA,CAACnC,GAAG,EAACsJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjCtH,IAAA,QAAKqH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE5G,SAAS,CAAC,YAAY,CAAC,CAAM,CAAC,cACpEV,IAAA,QAAAsH,QAAA,cACAtH,IAAA,UACCyH,IAAI,CAAC,OAAO,CACZpD,KAAK,CAAE1C,sBAAuB,CAC9B+F,QAAQ,CAAE/B,2BAA4B,CACtC0B,SAAS,CAAC,mBAAmB,CAC5B,CAAC,CACG,CAAC,EACH,CAAC,EACF,CAAC,CACF,CAAC,cACNrH,IAAA,QAAKqH,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACnCtH,IAAA,CAAC5B,MAAM,EACPkK,OAAO,CAAC,WAAW,CACnBf,OAAO,CAAEvB,kBAAmB,CAC5BqB,SAAS,CAAE,aAAa7D,UAAU,EAAIN,YAAY,EAAIE,iBAAiB,EAAIE,eAAe,CAAG,UAAU,CAAG,EAAE,EAAG,CAC/G6E,QAAQ,CAAE3E,UAAU,EAAIN,YAAY,EAAIE,iBAAiB,EAAIE,eAAiB;AAAA,CAAAgE,QAAA,CAE3E5G,SAAS,CAAC,OAAO,CAAC,CACb,CAAC,CAEJ,CAAC,EACF,CAAC,CACF,CAAC,CAER,CAAC,CAED,cAAe,CAAAP,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}