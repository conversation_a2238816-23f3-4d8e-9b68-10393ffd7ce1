{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\Tooltips\\\\designFields\\\\TooltipCanvasSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography, TextField, Grid, IconButton, Button } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport { AlignHorizontalLeft as TopLeftIcon, AlignHorizontalCenter as TopCenterIcon, AlignHorizontalRight as TopRightIcon, AlignVerticalTop as MiddleLeftIcon, AlignVerticalCenter as MiddleCenterIcon, AlignVerticalBottom as MiddleRightIcon, AlignHorizontalLeft as BottomLeftIcon, AlignHorizontalCenter as BottomCenterIcon, AlignHorizontalRight as BottomRightIcon } from \"@mui/icons-material\";\nimport \"./Canvas.module.css\";\nimport useDrawerStore, { CANVAS_DEFAULT_VALUE } from \"../../../store/drawerStore\";\nimport { tooltipLeft, tooltipRight, tooltipTop, tooltipBottom, tooltipCenter, warning } from \"../../../assets/icons/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TooltipCanvasSettings = ({\n  zindeex,\n  setZindeex,\n  setShowTooltipCanvasSettings\n}) => {\n  _s();\n  var _toolTipGuideMetaData2;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    setTooltipXaxis,\n    setTooltipYaxis,\n    updateCanvasInTooltip,\n    tooltipXaxis,\n    tooltipYaxis,\n    tooltipWidth,\n    setTooltipWidth,\n    setTooltipPadding,\n    setTooltipBorderradius,\n    setTooltipBordersize,\n    setTooltipBordercolor,\n    setTooltipBackgroundcolor,\n    tooltippadding,\n    tooltipborderradius,\n    tooltipbordersize,\n    tooltipBordercolor,\n    tooltipBackgroundcolor,\n    tooltipPosition,\n    setTooltipPosition,\n    setElementSelected,\n    setIsTooltipPopup,\n    toolTipGuideMetaData,\n    currentStep,\n    autoPosition,\n    setAutoPosition,\n    selectedTemplate,\n    updateDesignelementInTooltip,\n    selectedTemplateTour,\n    CANVAS_DEFAULT_VALUE_HOTSPOT,\n    setIsUnSavedChanges\n  } = useDrawerStore(state => state);\n  const [isOpen, setIsOpen] = useState(true);\n  const [dismiss, setDismiss] = useState(false);\n  const [selectedPosition, setSelectedPosition] = useState(\"middle-center\");\n  const [error, setError] = useState(false);\n  const [paddingError, setPaddingError] = useState(false);\n  const [cornerRadiusError, setCornerRadiusError] = useState(false);\n  const [borderSizeError, setBorderSizeError] = useState(false);\n  const [widthError, setWidthError] = useState(false);\n  const [tempBorderColor, setTempBorderColor] = useState(tooltipBordercolor);\n  useEffect(() => {\n    // Sync tempBorderColor with store, using default if empty or transparent\n    const validColor = tooltipBordercolor && tooltipBordercolor !== \"transparent\" && tooltipBordercolor !== \"\" ? tooltipBordercolor : \"#000000\";\n    setTempBorderColor(validColor);\n  }, [tooltipBordercolor]);\n\n  // Validate initial width value\n  useEffect(() => {\n    const currentWidth = parseInt(formatValueWithPixelOrPercentage(tooltipWidth)) || 0;\n    if (currentWidth < 300) {\n      setWidthError(true);\n    } else {\n      setWidthError(false);\n    }\n  }, [tooltipWidth]);\n  const positions = [{\n    label: translate(\"Top Left\", {\n      defaultValue: \"Top Left\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(TopLeftIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 71\n    }, this),\n    value: \"top-left\"\n  }, {\n    label: translate(\"Top Center\", {\n      defaultValue: \"Top Center\"\n    }),\n    icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: tooltipTop\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 146\n    }, this) : /*#__PURE__*/_jsxDEV(TopCenterIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 236\n    }, this),\n    value: \"top\"\n  }, {\n    label: translate(\"Top Right\", {\n      defaultValue: \"Top Right\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(TopRightIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 73\n    }, this),\n    value: \"top-right\"\n  }, {\n    label: translate(\"Middle Left\", {\n      defaultValue: \"Middle Left\"\n    }),\n    icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: tooltipLeft\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 148\n    }, this) : /*#__PURE__*/_jsxDEV(MiddleLeftIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 239\n    }, this),\n    value: \"left\"\n  }, {\n    label: translate(\"Middle Center\", {\n      defaultValue: \"Middle Center\"\n    }),\n    icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: tooltipCenter\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 152\n    }, this) : /*#__PURE__*/_jsxDEV(MiddleCenterIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 245\n    }, this),\n    value: \"center\"\n  }, {\n    label: translate(\"Middle Right\", {\n      defaultValue: \"Middle Right\"\n    }),\n    icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: tooltipRight\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 150\n    }, this) : /*#__PURE__*/_jsxDEV(MiddleRightIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 242\n    }, this),\n    value: \"right\"\n  }, {\n    label: translate(\"Bottom Left\", {\n      defaultValue: \"Bottom Left\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(BottomLeftIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 77\n    }, this),\n    value: \"bottom-left\"\n  }, {\n    label: translate(\"Bottom Center\", {\n      defaultValue: \"Bottom Center\"\n    }),\n    icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: tooltipBottom\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 152\n    }, this) : /*#__PURE__*/_jsxDEV(BottomCenterIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 245\n    }, this),\n    value: \"bottom\"\n  }, {\n    label: translate(\"Bottom Right\", {\n      defaultValue: \"Bottom Right\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(BottomRightIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 79\n    }, this),\n    value: \"bottom-right\"\n  }];\n  const handlePositionClick = e => {\n    var _e$target;\n    if (e !== null && e !== void 0 && (_e$target = e.target) !== null && _e$target !== void 0 && _e$target.id) {\n      //setSelectedPosition(e.target.id);\n      setTooltipPosition(e.target.id);\n    }\n  };\n  const onReselectElement = () => {\n    TooltipCanvasSettings({\n      ReSelection: false,\n      XPosition: 4,\n      YPosition: 4,\n      width: \"300\",\n      Padding: \"2\",\n      borderradius: \"8\",\n      bordersize: \"0\",\n      borderColor: \"\",\n      backgroundColor: \"\",\n      // PulseAnimation: true,\n      // stopAnimationUponInteraction: true,\n      // ShowUpon: \"Hovering Hotspot\",\n      ShowByDefault: false\n    });\n    updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\n    setElementSelected(true);\n    setIsTooltipPopup(false);\n    setShowTooltipCanvasSettings(false);\n  };\n  const handleBorderColorChange = e => {\n    var _e$target2;\n    if (e !== null && e !== void 0 && (_e$target2 = e.target) !== null && _e$target2 !== void 0 && _e$target2.value) {\n      setTempBorderColor(e.target.value);\n    }\n  };\n  const handleBackgroundColorChange = e => {\n    var _e$target3;\n    if (e !== null && e !== void 0 && (_e$target3 = e.target) !== null && _e$target3 !== void 0 && _e$target3.value) {\n      setTooltipBackgroundcolor(e.target.value);\n    }\n  };\n  const handleAutoSelect = e => {\n    //\tsetDismiss(e.target.checked);\n    setAutoPosition(e.target.checked);\n  };\n  const handleClose = () => {\n    setIsOpen(false);\n    setShowTooltipCanvasSettings(false);\n  };\n  const handleApplyChanges = () => {\n    // Don't apply changes if there's any validation error\n    if (widthError || paddingError || cornerRadiusError || borderSizeError) {\n      return;\n    }\n\n    // Create the new canvas settings\n    const updatedCanvasSettings = {\n      position: tooltipPosition,\n      backgroundColor: tooltipBackgroundcolor,\n      width: tooltipWidth,\n      borderRadius: tooltipborderradius,\n      padding: tooltippadding,\n      borderColor: tempBorderColor,\n      borderSize: tooltipbordersize,\n      autoposition: autoPosition,\n      xaxis: tooltipXaxis,\n      yaxis: tooltipYaxis\n    };\n\n    // Apply the changes - updateCanvasInTooltip will handle recording the change for undo/redo\n    updateCanvasInTooltip(updatedCanvasSettings); // Updates canvas and tooltipBordercolor\n    handleClose();\n    setIsUnSavedChanges(true);\n  };\n  useEffect(() => {\n    var _toolTipGuideMetaData;\n    if ((_toolTipGuideMetaData = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData !== void 0 && _toolTipGuideMetaData.canvas) {\n      const canvasData = toolTipGuideMetaData[currentStep - 1].canvas;\n\n      // Handle border color - use default if empty, transparent, or invalid\n      const borderColor = canvasData === null || canvasData === void 0 ? void 0 : canvasData.borderColor;\n      const validBorderColor = borderColor && borderColor !== \"transparent\" && borderColor !== \"\" ? borderColor : \"#000000\";\n      setTooltipPosition((canvasData === null || canvasData === void 0 ? void 0 : canvasData.position) || \"middle-center\");\n      setTooltipPadding((canvasData === null || canvasData === void 0 ? void 0 : canvasData.padding) || \"10px\");\n      setTooltipBorderradius((canvasData === null || canvasData === void 0 ? void 0 : canvasData.borderRadius) || \"8px\");\n      setTooltipBordersize((canvasData === null || canvasData === void 0 ? void 0 : canvasData.borderSize) || \"0px\");\n      setTooltipBordercolor(borderColor || \"\");\n      setTempBorderColor(validBorderColor); // Use valid color for the input\n      setTooltipBackgroundcolor((canvasData === null || canvasData === void 0 ? void 0 : canvasData.backgroundColor) || \"#FFFFFF\");\n      setTooltipWidth((canvasData === null || canvasData === void 0 ? void 0 : canvasData.width) || \"300px\");\n      if (selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") {\n        setTooltipXaxis((canvasData === null || canvasData === void 0 ? void 0 : canvasData.xaxis) || \"2px\");\n        setTooltipYaxis((canvasData === null || canvasData === void 0 ? void 0 : canvasData.yaxis) || \"2px\");\n      } else {\n        setTooltipXaxis((canvasData === null || canvasData === void 0 ? void 0 : canvasData.xaxis) || \"100px\");\n        setTooltipYaxis((canvasData === null || canvasData === void 0 ? void 0 : canvasData.yaxis) || \"100px\");\n      }\n    }\n  }, [(_toolTipGuideMetaData2 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.canvas]);\n  if (!isOpen) return null;\n  const formatValueWithPixelOrPercentage = value => {\n    const v = String(value);\n    let newValue = v;\n    if (v !== null && v !== void 0 && v.endsWith(\"px\") || v !== null && v !== void 0 && v.endsWith(\"%\")) {\n      newValue = v.split(/px|%/)[0];\n    }\n    return newValue;\n  };\n  const handleChange = e => {\n    // Only allow numeric input\n    const value = e.target.value;\n    if (value === '') {\n      setTooltipWidth('0px');\n      setWidthError(true); // Empty value is invalid\n      return;\n    }\n    if (!/^-?\\d*$/.test(value)) {\n      return;\n    }\n    let inputValue = parseInt(value) || 0;\n\n    // Validate width - minimum 300 pixels\n    if (inputValue < 300) {\n      setWidthError(true);\n    } else {\n      setWidthError(false);\n    }\n    setTooltipWidth(`${inputValue}px`);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"qadpt-designpopup\",\n    className: \"qadpt-designpopup\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-design-header\",\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": \"back\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? translate(\"Canvas\") : translate(\"Canvas\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": \"close\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-canblock\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-controls\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Auto Position\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"toggle-switch\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: autoPosition,\n                  onChange: handleAutoSelect,\n                  name: \"autoPosition\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 5\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"slider\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 5\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-position-grid\",\n            sx: {\n              opacity: selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\" ? 0.5 : 1,\n              cursor: selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\" ? \"not-allowed\" : \"\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-ctrl-title\",\n              children: translate(\"Position\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  disabled: autoPosition,\n                  sx: {\n                    opacity: selectedPosition === positions[0].value ? 1 : 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  id: positions[1].value,\n                  onClick: () => {\n                    //setSelectedPosition(positions[1].value);\n                    setTooltipPosition(positions[1].value);\n                  },\n                  disabled: autoPosition || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\",\n                  disableRipple: true,\n                  sx: {\n                    opacity: tooltipPosition === positions[1].value ? 1 : 0.5\n                  },\n                  children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: tooltipTop\n                    },\n                    id: positions[1].value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 9\n                  }, this) : /*#__PURE__*/_jsxDEV(TopCenterIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  disabled: autoPosition,\n                  sx: {\n                    opacity: selectedPosition === positions[2].value ? 1 : 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  id: positions[3].value,\n                  onClick: () => {\n                    // setSelectedPosition(positions[3].value);\n                    setTooltipPosition(positions[3].value);\n                  },\n                  disabled: autoPosition,\n                  disableRipple: true,\n                  sx: {\n                    opacity: tooltipPosition === positions[3].value ? 1 : 0.5\n                  },\n                  children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: tooltipLeft\n                    },\n                    id: positions[3].value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 9\n                  }, this) : /*#__PURE__*/_jsxDEV(MiddleLeftIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  id: positions[4].value,\n                  onClick: () => {\n                    // setSelectedPosition(positions[4].value);\n                    setTooltipPosition(positions[4].value);\n                  },\n                  disabled: autoPosition,\n                  disableRipple: true,\n                  sx: {\n                    opacity: tooltipPosition === positions[4].value ? 1 : 0.5,\n                    paddingLeft: \"0 !important\"\n                  },\n                  children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: tooltipCenter\n                    },\n                    id: positions[4].value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 9\n                  }, this) : /*#__PURE__*/_jsxDEV(MiddleCenterIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  id: positions[5].value,\n                  onClick: () => {\n                    // setSelectedPosition(positions[5].value);\n                    setTooltipPosition(positions[5].value);\n                  },\n                  disabled: autoPosition,\n                  disableRipple: true,\n                  sx: {\n                    opacity: tooltipPosition === positions[5].value ? 1 : 0.5\n                  },\n                  children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: tooltipRight\n                    },\n                    id: positions[5].value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 9\n                  }, this) : /*#__PURE__*/_jsxDEV(MiddleRightIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  disabled: autoPosition,\n                  sx: {\n                    opacity: selectedPosition === positions[6].value ? 1 : 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  id: positions[7].value,\n                  onClick: () => {\n                    // setSelectedPosition(positions[7].value);\n                    setTooltipPosition(positions[7].value);\n                  },\n                  disabled: autoPosition,\n                  disableRipple: true,\n                  sx: {\n                    opacity: tooltipPosition === positions[7].value ? 1 : 0.5\n                  },\n                  children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: tooltipBottom\n                    },\n                    id: positions[7].value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 9\n                  }, this) : /*#__PURE__*/_jsxDEV(BottomCenterIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  disabled: autoPosition,\n                  sx: {\n                    opacity: selectedPosition === positions[8].value ? 1 : 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 3\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: [\"X \", translate(\"Axis Offset\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                value: formatValueWithPixelOrPercentage(tooltipXaxis),\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                onChange: e => setTooltipXaxis(`${parseInt(e.target.value) || 0}px`),\n                InputProps: {\n                  endAdornment: \"px\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    }\n                  }\n                },\n                disabled: autoPosition\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: [\"Y \", translate(\"Axis Offset\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                value: formatValueWithPixelOrPercentage(tooltipYaxis),\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                onChange: e => setTooltipYaxis(`${parseInt(e.target.value) || 0}px`),\n                InputProps: {\n                  endAdornment: \"px\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    }\n                  }\n                },\n                disabled: autoPosition\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            style: {\n              flexDirection: \"column\",\n              height: \"auto\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              style: {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: \"8px\",\n                width: \"100%\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate(\"Width\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: formatValueWithPixelOrPercentage(tooltipWidth),\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: handleChange,\n                  error: widthError,\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 9\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 7\n          }, this), widthError && /*#__PURE__*/_jsxDEV(Typography, {\n            style: {\n              fontSize: \"12px\",\n              color: \"#e9a971\",\n              textAlign: \"left\",\n              top: \"100%\",\n              left: 0,\n              marginBottom: \"5px\",\n              display: \"flex\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: \"flex\",\n                fontSize: \"12px\",\n                alignItems: \"center\",\n                marginRight: \"4px\"\n              },\n              dangerouslySetInnerHTML: {\n                __html: warning\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 9\n            }, this), translate(\"Width must be at least 300 pixels\")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Padding\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                value: formatValueWithPixelOrPercentage(tooltippadding),\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                onChange: e => {\n                  // Only allow numeric input\n                  const value = e.target.value;\n                  if (!/^-?\\d*$/.test(value)) {\n                    return;\n                  }\n                  const inputValue = parseInt(value) || 0;\n                  // Validate padding between 0px and 20px\n                  if (inputValue < 0 || inputValue > 20) {\n                    setPaddingError(true);\n                  } else {\n                    setPaddingError(false);\n                  }\n                  setTooltipPadding(`${inputValue}px`);\n                },\n                InputProps: {\n                  endAdornment: \"px\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    }\n                  }\n                },\n                error: paddingError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 7\n          }, this), paddingError && /*#__PURE__*/_jsxDEV(Typography, {\n            style: {\n              fontSize: \"12px\",\n              color: \"#e9a971\",\n              textAlign: \"left\",\n              top: \"100%\",\n              left: 0,\n              marginBottom: \"5px\",\n              display: \"flex\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: \"flex\",\n                fontSize: \"12px\",\n                alignItems: \"center\",\n                marginRight: \"4px\"\n              },\n              dangerouslySetInnerHTML: {\n                __html: warning\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 9\n            }, this), translate(\"Value must be between 0px and 20px.\")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Corner Radius\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                value: formatValueWithPixelOrPercentage(tooltipborderradius),\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                onChange: e => {\n                  // Only allow numeric input\n                  const value = e.target.value;\n                  if (!/^-?\\d*$/.test(value)) {\n                    return;\n                  }\n                  const inputValue = parseInt(value) || 0;\n                  // Validate corner radius between 0px and 20px\n                  if (inputValue < 0 || inputValue > 20) {\n                    setCornerRadiusError(true);\n                  } else {\n                    setCornerRadiusError(false);\n                  }\n                  setTooltipBorderradius(`${inputValue}`);\n                },\n                InputProps: {\n                  endAdornment: \"px\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    }\n                  }\n                },\n                error: cornerRadiusError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 7\n          }, this), cornerRadiusError && /*#__PURE__*/_jsxDEV(Typography, {\n            style: {\n              fontSize: \"12px\",\n              color: \"#e9a971\",\n              textAlign: \"left\",\n              top: \"100%\",\n              left: 0,\n              marginBottom: \"5px\",\n              display: \"flex\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: \"flex\",\n                fontSize: \"12px\",\n                alignItems: \"center\",\n                marginRight: \"4px\"\n              },\n              dangerouslySetInnerHTML: {\n                __html: warning\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 9\n            }, this), translate(\"Value must be between 0px and 20px.\")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Border Size\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                value: formatValueWithPixelOrPercentage(tooltipbordersize),\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                onChange: e => {\n                  // Only allow numeric input\n                  const value = e.target.value;\n                  if (!/^-?\\d*$/.test(value)) {\n                    return;\n                  }\n                  const inputValue = parseInt(value) || 0;\n                  // Validate border size between 0px and 20px\n                  if (inputValue < 0 || inputValue > 5) {\n                    setBorderSizeError(true);\n                  } else {\n                    setBorderSizeError(false);\n                  }\n                  setTooltipBordersize(`${inputValue}px`);\n                },\n                InputProps: {\n                  endAdornment: \"px\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    }\n                  }\n                },\n                error: borderSizeError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 5\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 3\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 1\n          }, this), borderSizeError && /*#__PURE__*/_jsxDEV(Typography, {\n            style: {\n              fontSize: \"12px\",\n              color: \"#e9a971\",\n              textAlign: \"left\",\n              top: \"100%\",\n              left: 0,\n              marginBottom: \"5px\",\n              display: \"flex\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: \"flex\",\n                fontSize: \"12px\",\n                alignItems: \"center\",\n                marginRight: \"4px\"\n              },\n              dangerouslySetInnerHTML: {\n                __html: warning\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 5\n            }, this), \"Value must be between 0px and 5px.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Border\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: tempBorderColor || \"#000000\",\n                onChange: handleBorderColorChange,\n                className: \"qadpt-color-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 822,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Background\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 835,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: tooltipBackgroundcolor,\n                onChange: handleBackgroundColorChange,\n                className: \"qadpt-color-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 837,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 834,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-drawerFooter\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleApplyChanges,\n          className: `qadpt-btn ${widthError || paddingError || cornerRadiusError || borderSizeError ? \"disabled\" : \"\"}`,\n          disabled: widthError || paddingError || cornerRadiusError || borderSizeError // Disable button if any validation errors exist\n          ,\n          children: translate(\"Apply\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 848,\n          columnNumber: 5\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 847,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 4\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 239,\n    columnNumber: 3\n  }, this);\n};\n_s(TooltipCanvasSettings, \"3jjRwuWJDR8zGPP+pvu9RPnd1Kc=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = TooltipCanvasSettings;\nexport default TooltipCanvasSettings;\nvar _c;\n$RefreshReg$(_c, \"TooltipCanvasSettings\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "TextField", "Grid", "IconButton", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "AlignHorizontalLeft", "TopLeftIcon", "AlignHorizontalCenter", "TopCenterIcon", "AlignHorizontalRight", "TopRightIcon", "AlignVerticalTop", "MiddleLeftIcon", "AlignVerticalCenter", "MiddleCenterIcon", "AlignVerticalBottom", "MiddleRightIcon", "BottomLeftIcon", "BottomCenterIcon", "BottomRightIcon", "useDrawerStore", "CANVAS_DEFAULT_VALUE", "tooltipLeft", "tooltipRight", "tooltipTop", "tooltipBottom", "tooltipCenter", "warning", "useTranslation", "jsxDEV", "_jsxDEV", "TooltipCanvasSettings", "zindeex", "setZindeex", "setShowTooltipCanvasSettings", "_s", "_toolTipGuideMetaData2", "t", "translate", "setTooltipXaxis", "setTooltipYaxis", "updateCanvasInTooltip", "tooltipXaxis", "tooltipYaxis", "tooltipWidth", "setTooltipWidth", "setTooltipPadding", "setTooltipBorderradius", "setTooltipBordersize", "setTooltipBordercolor", "setTooltipBackgroundcolor", "tooltippadding", "tooltipborderradius", "tooltipbordersize", "tooltipBordercolor", "tooltipBackgroundcolor", "tooltipPosition", "setTooltipPosition", "setElementSelected", "setIsTooltipPopup", "toolTipGuideMetaData", "currentStep", "autoPosition", "setAutoPosition", "selectedTemplate", "updateDesignelementInTooltip", "selectedTemplateTour", "CANVAS_DEFAULT_VALUE_HOTSPOT", "setIsUnSavedChanges", "state", "isOpen", "setIsOpen", "dismiss", "<PERSON><PERSON><PERSON><PERSON>", "selectedPosition", "setSelectedPosition", "error", "setError", "paddingError", "setPaddingError", "cornerRadiusError", "setCornerRadiusError", "borderSizeError", "setBorderSizeError", "widthError", "setWidthError", "tempBorderColor", "setTempBorderColor", "validColor", "currentWidth", "parseInt", "formatValueWithPixelOrPercentage", "positions", "label", "defaultValue", "icon", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "dangerouslySetInnerHTML", "__html", "style", "handlePositionClick", "e", "_e$target", "target", "id", "onReselectElement", "ReSelection", "XPosition", "YPosition", "width", "Padding", "<PERSON><PERSON><PERSON>", "bordersize", "borderColor", "backgroundColor", "ShowByDefault", "handleBorderColorChange", "_e$target2", "handleBackgroundColorChange", "_e$target3", "handleAutoSelect", "checked", "handleClose", "handleApplyChanges", "updatedCanvasSettings", "position", "borderRadius", "padding", "borderSize", "autoposition", "xaxis", "yaxis", "_toolTipGuideMetaData", "canvas", "canvasData", "validBorderColor", "v", "String", "newValue", "endsWith", "split", "handleChange", "test", "inputValue", "className", "children", "onClick", "size", "type", "onChange", "name", "sx", "opacity", "cursor", "container", "spacing", "item", "xs", "disabled", "disable<PERSON><PERSON><PERSON>", "paddingLeft", "variant", "InputProps", "endAdornment", "border", "flexDirection", "height", "display", "alignItems", "gap", "color", "textAlign", "top", "left", "marginBottom", "marginRight", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/Tooltips/designFields/TooltipCanvasSettings.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { <PERSON>, Typo<PERSON>, TextField, Grid, IconButton, Button, Tooltip, Switch } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport {\r\n\tAlignHorizontalLeft as TopLeftIcon,\r\n\tAlignHorizontalCenter as TopCenterIcon,\r\n\tAlignHorizontalRight as TopRightIcon,\r\n\tAlignVerticalTop as MiddleLeftIcon,\r\n\tAlignVerticalCenter as MiddleCenterIcon,\r\n\tAlignVerticalBottom as MiddleRightIcon,\r\n\tAlignHorizontalLeft as BottomLeftIcon,\r\n\tAlignHorizontalCenter as BottomCenterIcon,\r\n\tAlignHorizontalRight as BottomRightIcon,\r\n\tPadding,\r\n} from \"@mui/icons-material\";\r\nimport \"./Canvas.module.css\";\r\nimport useDrawerStore, { CANVAS_DEFAULT_VALUE, CANVAS_DEFAULT_VALUE_HOTSPOT, TCan<PERSON> } from \"../../../store/drawerStore\";\r\nimport { tooltipLeft, tooltipRight, tooltipTop, tooltipBottom, tooltipCenter, warning } from \"../../../assets/icons/icons\";\r\nimport { TouchAppSharp } from \"@mui/icons-material\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst TooltipCanvasSettings = ({ zindeex, setZindeex, setShowTooltipCanvasSettings }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tsetTooltipXaxis,\r\n\t\tsetTooltipYaxis,\r\n\t\tupdateCanvasInTooltip,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\ttooltipWidth,\r\n\t\tsetTooltipWidth,\r\n\t\tsetTooltipPadding,\r\n\t\tsetTooltipBorderradius,\r\n\t\tsetTooltipBordersize,\r\n\t\tsetTooltipBordercolor,\r\n\t\tsetTooltipBackgroundcolor,\r\n\t\ttooltippadding,\r\n\t\ttooltipborderradius,\r\n\t\ttooltipbordersize,\r\n\t\ttooltipBordercolor,\r\n\t\ttooltipBackgroundcolor,\r\n\t\ttooltipPosition,\r\n\t\tsetTooltipPosition,\r\n\t\tsetElementSelected,\r\n\t\tsetIsTooltipPopup,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tcurrentStep,\r\n\t\tautoPosition,\r\n\t\tsetAutoPosition,\r\n\t\tselectedTemplate,\r\n\t\tupdateDesignelementInTooltip,\r\n\t\tselectedTemplateTour,\r\n\t\tCANVAS_DEFAULT_VALUE_HOTSPOT,\r\n\t\tsetIsUnSavedChanges\r\n\t} = useDrawerStore((state: any) => state);\r\n\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [dismiss, setDismiss] = useState(false);\r\n\tconst [selectedPosition, setSelectedPosition] = useState(\"middle-center\");\r\n\tconst [error, setError] = useState(false);\r\n\tconst [paddingError, setPaddingError] = useState(false);\r\n\tconst [cornerRadiusError, setCornerRadiusError] = useState(false);\r\n\tconst [borderSizeError, setBorderSizeError] = useState(false);\r\n\tconst [widthError, setWidthError] = useState(false);\r\n\tconst [tempBorderColor, setTempBorderColor] = useState(tooltipBordercolor);\r\n\r\n\tuseEffect(() => {\r\n\t\t// Sync tempBorderColor with store, using default if empty or transparent\r\n\t\tconst validColor = tooltipBordercolor && tooltipBordercolor !== \"transparent\" && tooltipBordercolor !== \"\" ? tooltipBordercolor : \"#000000\";\r\n\t\tsetTempBorderColor(validColor);\r\n\t  }, [tooltipBordercolor]);\r\n\r\n\t// Validate initial width value\r\n\tuseEffect(() => {\r\n\t\tconst currentWidth = parseInt(formatValueWithPixelOrPercentage(tooltipWidth)) || 0;\r\n\t\tif (currentWidth < 300) {\r\n\t\t\tsetWidthError(true);\r\n\t\t} else {\r\n\t\t\tsetWidthError(false);\r\n\t\t}\r\n\t}, [tooltipWidth]);\r\n\r\n\tconst positions = [\r\n\t\t{ label: translate(\"Top Left\", { defaultValue: \"Top Left\" }), icon: <TopLeftIcon fontSize=\"small\" />, value: \"top-left\" },\r\n\t\t{ label: translate(\"Top Center\", { defaultValue: \"Top Center\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipTop }} style={{ fontSize: \"small\" }} /> : <TopCenterIcon fontSize=\"small\" />, value: \"top\" },\r\n\t\t{ label: translate(\"Top Right\", { defaultValue: \"Top Right\" }), icon: <TopRightIcon fontSize=\"small\" />, value: \"top-right\" },\r\n\t\t{ label: translate(\"Middle Left\", { defaultValue: \"Middle Left\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipLeft }} style={{ fontSize: \"small\" }} /> : <MiddleLeftIcon fontSize=\"small\" />, value: \"left\" },\r\n\t\t{ label: translate(\"Middle Center\", { defaultValue: \"Middle Center\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipCenter }} style={{ fontSize: \"small\" }} /> : <MiddleCenterIcon fontSize=\"small\" />, value: \"center\" },\r\n\t\t{ label: translate(\"Middle Right\", { defaultValue: \"Middle Right\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipRight }} style={{ fontSize: \"small\" }} /> : <MiddleRightIcon fontSize=\"small\" />, value: \"right\" },\r\n\t\t{ label: translate(\"Bottom Left\", { defaultValue: \"Bottom Left\" }), icon: <BottomLeftIcon fontSize=\"small\" />, value: \"bottom-left\" },\r\n\t\t{ label: translate(\"Bottom Center\", { defaultValue: \"Bottom Center\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipBottom }} style={{ fontSize: \"small\" }} /> : <BottomCenterIcon fontSize=\"small\" />, value: \"bottom\" },\r\n\t\t{ label: translate(\"Bottom Right\", { defaultValue: \"Bottom Right\" }), icon: <BottomRightIcon fontSize=\"small\" />, value: \"bottom-right\" },\r\n\t];\r\n\r\n\tconst handlePositionClick = (e: any) => {\r\n\t\tif (e?.target?.id) {\r\n\t\t\t//setSelectedPosition(e.target.id);\r\n\t\t\tsetTooltipPosition(e.target.id);\r\n\t\t}\r\n\t};\r\n\r\n\tconst onReselectElement = () => {\r\n\r\n\t\tTooltipCanvasSettings({\r\n\t\t\tReSelection: false,\r\n\t\t\tXPosition: 4,\r\n\t\t\tYPosition: 4,\r\n\t\t\twidth: \"300\",\r\n\t\t\tPadding: \"2\",\r\n\t\t\tborderradius: \"8\",\r\n\t\t\tbordersize: \"0\",\r\n\t\t\tborderColor: \"\",\r\n\t\t\tbackgroundColor: \"\",\r\n\t\t\t// PulseAnimation: true,\r\n\t\t\t// stopAnimationUponInteraction: true,\r\n\t\t\t// ShowUpon: \"Hovering Hotspot\",\r\n\t\t\tShowByDefault: false,\r\n\t\t});\r\n\t\tupdateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\r\n\t\tsetElementSelected(true);\r\n\t\tsetIsTooltipPopup(false);\r\n\t\tsetShowTooltipCanvasSettings(false);\r\n\t};\r\n\tconst handleBorderColorChange = (e: any) => {\r\n\t\tif (e?.target?.value) {\r\n\t\t\tsetTempBorderColor(e.target.value);\r\n\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleBackgroundColorChange = (e: any) => {\r\n\t\tif (e?.target?.value) {\r\n\t\t\tsetTooltipBackgroundcolor(e.target.value);\r\n\t\t}\r\n\t};\r\n\tconst handleAutoSelect = (e: any) => {\r\n\t\t//\tsetDismiss(e.target.checked);\r\n\t\tsetAutoPosition(e.target.checked);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowTooltipCanvasSettings(false);\r\n\t};\r\n\r\n\tconst handleApplyChanges = () => {\r\n\t\t// Don't apply changes if there's any validation error\r\n\t\tif (widthError || paddingError || cornerRadiusError || borderSizeError) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// Create the new canvas settings\r\n\t\tconst updatedCanvasSettings = {\r\n\t\t\tposition: tooltipPosition,\r\n\t\t\tbackgroundColor: tooltipBackgroundcolor,\r\n\t\t\twidth: tooltipWidth,\r\n\t\t\tborderRadius: tooltipborderradius,\r\n\t\t\tpadding: tooltippadding,\r\n\t\t\tborderColor: tempBorderColor,\r\n\t\t\tborderSize: tooltipbordersize,\r\n\t\t\tautoposition: autoPosition,\r\n\t\t\txaxis: tooltipXaxis,\r\n\t\t\tyaxis: tooltipYaxis,\r\n\t\t};\r\n\r\n\t\t// Apply the changes - updateCanvasInTooltip will handle recording the change for undo/redo\r\n\t\tupdateCanvasInTooltip(updatedCanvasSettings); // Updates canvas and tooltipBordercolor\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (toolTipGuideMetaData[currentStep - 1]?.canvas) {\r\n\t\t\tconst canvasData = toolTipGuideMetaData[currentStep - 1].canvas;\r\n\r\n\t\t\t// Handle border color - use default if empty, transparent, or invalid\r\n\t\t\tconst borderColor = canvasData?.borderColor;\r\n\t\t\tconst validBorderColor = borderColor && borderColor !== \"transparent\" && borderColor !== \"\" ? borderColor : \"#000000\";\r\n\r\n\t\t\tsetTooltipPosition(canvasData?.position || \"middle-center\");\r\n\t\t\tsetTooltipPadding(canvasData?.padding || \"10px\");\r\n\t\t\tsetTooltipBorderradius(canvasData?.borderRadius || \"8px\");\r\n\t\t\tsetTooltipBordersize(canvasData?.borderSize || \"0px\");\r\n\t\t\tsetTooltipBordercolor(borderColor || \"\");\r\n\t\t\tsetTempBorderColor(validBorderColor); // Use valid color for the input\r\n\t\t\tsetTooltipBackgroundcolor(canvasData?.backgroundColor || \"#FFFFFF\");\r\n\t\t\tsetTooltipWidth(canvasData?.width || \"300px\");\r\n\t\t\tif (selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") {\r\n\t\t\t\tsetTooltipXaxis(canvasData?.xaxis || \"2px\");\r\n\t\t\t\tsetTooltipYaxis(canvasData?.yaxis || \"2px\");\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tsetTooltipXaxis(canvasData?.xaxis || \"100px\");\r\n\t\t\t\tsetTooltipYaxis(canvasData?.yaxis || \"100px\");\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}, [toolTipGuideMetaData[currentStep - 1]?.canvas]);\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\tconst formatValueWithPixelOrPercentage = (value: string) => {\r\n\t\tconst v = String(value);\r\n\t\tlet newValue = v;\r\n\t\tif (v?.endsWith(\"px\") || v?.endsWith(\"%\")) {\r\n\t\t\tnewValue = v.split(/px|%/)[0];\r\n\t\t}\r\n\t\treturn newValue;\r\n\t};\r\n\tconst handleChange = (e: any) => {\r\n\t\t// Only allow numeric input\r\n\t\tconst value = e.target.value;\r\n\t\tif (value === '') {\r\n\t\t\tsetTooltipWidth('0px');\r\n\t\t\tsetWidthError(true); // Empty value is invalid\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tlet inputValue = parseInt(value) || 0;\r\n\r\n\t\t// Validate width - minimum 300 pixels\r\n\t\tif (inputValue < 300) {\r\n\t\t\tsetWidthError(true);\r\n\t\t} else {\r\n\t\t\tsetWidthError(false);\r\n\t\t}\r\n\r\n\t\tsetTooltipWidth(`${inputValue}px`);\r\n\t};\r\n\r\n\r\n\r\n\treturn (\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"back\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? translate(\"Canvas\") : translate(\"Canvas\")}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t{/* <Button\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\tonClick={onReselectElement}\r\n\t\t\t\t\t\t\t\t\t//startIcon={<DesignServicesIcon />}\r\n\t\t\t\t\t\t\t\t\tendIcon={<TouchAppSharp />}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tReselect Element\r\n\t\t\t\t\t\t\t\t</Button> */}\r\n\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{translate(\"Auto Position\")}\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={autoPosition}\r\n        onChange={handleAutoSelect}\r\n        name=\"autoPosition\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-position-grid\"\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\topacity: selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\"? 0.5 : 1,\r\n\t\t\t\t\t\t\t\tcursor:selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\"?\"not-allowed\":\"\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-ctrl-title\"\r\n\t\t\t\t\t\t\t>{translate(\"Position\")}</Typography>\r\n\t\t\t\t\t\t\t<Grid container spacing={1}>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[0].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Top Left - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[1].value}\r\n      onClick={() => {\r\n\r\n        //setSelectedPosition(positions[1].value);\r\n        setTooltipPosition(positions[1].value);\r\n      }}\r\n\tdisabled={autoPosition || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\"}\r\n\tdisableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[1].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipTop }}  id={positions[1].value} />\r\n      ) : (\r\n        <TopCenterIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[2].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Top Right - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[3].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[3].value);\r\n        setTooltipPosition(positions[3].value);\r\n      }}\r\n      disabled={autoPosition}\r\n\t  disableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[3].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipLeft }} id={positions[3].value} />\r\n      ) : (\r\n        <MiddleLeftIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[4].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[4].value);\r\n        setTooltipPosition(positions[4].value);\r\n      }}\r\n\t\t\t\t\t\t\t\t\t\tdisabled={autoPosition}\r\n\t\t\t\t\t\t\t\t\t\tdisableRipple\r\n      sx={{\r\n\t\t  opacity: tooltipPosition === positions[4].value ? 1 : 0.5,\r\n\t\t  paddingLeft:\"0 !important\"\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipCenter }} id={positions[4].value}/>\r\n      ) : (\r\n        <MiddleCenterIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[5].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[5].value);\r\n        setTooltipPosition(positions[5].value);\r\n      }}\r\n      disabled={autoPosition}\r\n\t  disableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[5].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipRight }}  id={positions[5].value} />\r\n      ) : (\r\n        <MiddleRightIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[6].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Bottom Left - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[7].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[7].value);\r\n        setTooltipPosition(positions[7].value);\r\n      }}\r\n      disabled={autoPosition}\r\n\t  disableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[7].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipBottom }} id={positions[7].value} />\r\n      ) : (\r\n        <BottomCenterIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[8].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Bottom Right - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n</Grid>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{/* Width Control */}\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">X {translate(\"Axis Offset\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipXaxis)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => setTooltipXaxis(`${parseInt(e.target.value) || 0}px`)}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdisabled={autoPosition}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">Y {translate(\"Axis Offset\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipYaxis)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => setTooltipYaxis(`${parseInt(e.target.value) || 0}px`)}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdisabled={autoPosition}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\tstyle={{ flexDirection: \"column\", height: \"auto\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box style={{ display: \"flex\", alignItems: \"center\", gap: \"8px\", width: \"100%\" }}>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Width\")}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipWidth)}\r\n\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\t\terror={widthError}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{widthError && (\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\talignItems: \"center\"\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Width must be at least 300 pixels\")}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Padding\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltippadding)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\t\t\t\t\t\t\t\t\t// Validate padding between 0px and 20px\r\n\t\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\t\tsetPaddingError(true);\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tsetPaddingError(false);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tsetTooltipPadding(`${inputValue}px`);\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\terror={paddingError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{paddingError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Corner Radius\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipborderradius)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\t\t\t\t\t\t\t\t\t// Validate corner radius between 0px and 20px\r\n\t\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\t\tsetCornerRadiusError(true);\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tsetCornerRadiusError(false);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tsetTooltipBorderradius(`${inputValue}`);\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\terror={cornerRadiusError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{cornerRadiusError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\r\n\r\n<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Border Size\")}</div>\r\n  <div>\r\n    <TextField\r\n      variant=\"outlined\"\r\n      value={formatValueWithPixelOrPercentage(tooltipbordersize)}\r\n      size=\"small\"\r\n      className=\"qadpt-control-input\"\r\n      onChange={(e) => {\r\n        // Only allow numeric input\r\n        const value = e.target.value;\r\n        if (!/^-?\\d*$/.test(value)) {\r\n          return;\r\n        }\r\n        const inputValue = parseInt(value) || 0;\r\n        // Validate border size between 0px and 20px\r\n        if (inputValue < 0 || inputValue > 5) {\r\n          setBorderSizeError(true);\r\n        } else {\r\n          setBorderSizeError(false);\r\n        }\r\n        setTooltipBordersize(`${inputValue}px`);\r\n      }}\r\n      InputProps={{\r\n        endAdornment: \"px\",\r\n        sx: {\r\n          \"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n          \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n          \"& fieldset\": { border: \"none\" },\r\n        },\r\n      }}\r\n      error={borderSizeError}\r\n    />\r\n  </div>\r\n</Box>\r\n{borderSizeError && (\r\n  <Typography\r\n    style={{\r\n      fontSize: \"12px\",\r\n      color: \"#e9a971\",\r\n      textAlign: \"left\",\r\n      top: \"100%\",\r\n      left: 0,\r\n      marginBottom: \"5px\",\r\n      display: \"flex\",\r\n    }}\r\n  >\r\n    <span\r\n      style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight: \"4px\" }}\r\n      dangerouslySetInnerHTML={{ __html: warning }}\r\n    />\r\n    Value must be between 0px and 5px.\r\n  </Typography>\r\n)}\r\n\r\n{/* Button Selection Dropdown - Only show for Tooltip template */}\r\n{/* {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") && (\r\n  <Box className=\"qadpt-control-box\" style={{ flexDirection: \"column\", height: \"auto\" }}>\r\n    <Typography className=\"qadpt-ctrl-title\">Selected Button</Typography>\r\n    <Box style={{ width: \"100%\" }}>\r\n      {toolTipGuideMetaData?.[currentStep - 1]?.containers?.[1]?.buttons ? (\r\n        <Select\r\n          value={\r\n            toolTipGuideMetaData[currentStep - 1].containers[1].buttons.find(\r\n              (button: any) =>\r\n                button.name === toolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonName\r\n            )?.id || \"\"\r\n          }\r\n          onChange={(event) => {\r\n            const selectedValue = event.target.value;\r\n            const selectedButton = toolTipGuideMetaData[currentStep - 1]?.containers[1]?.buttons?.find(\r\n              (button: any) => button.id === selectedValue\r\n            );\r\n\r\n            // Set dropdown value and button name\r\n            setDropdownValue(selectedValue);\r\n            setElementButtonName(selectedValue);\r\n            setbtnidss(selectedValue);\r\n            setElementClick(\"button\");\r\n\r\n            // Save button ID, button name, and \"NextStep\" in the Design object's \"goToNext\" object\r\n            const updatedCanvasSettings = {\r\n              NextStep: \"button\",\r\n              ButtonId: selectedValue,\r\n              ElementPath: \"\",\r\n              ButtonName: selectedButton?.name,\r\n            };\r\n\r\n            updateDesignelementInTooltip(updatedCanvasSettings);\r\n\r\n            // Set the button action to \"Next\" using updateTooltipButtonAction\r\n            if (selectedButton && selectedButton.id) {\r\n              // Find the container ID for the button\r\n              const containerId = toolTipGuideMetaData[currentStep - 1]?.containers[1]?.id;\r\n\r\n              // Update the button action to \"Next\"\r\n              updateTooltipButtonAction(containerId, selectedValue, {\r\n                value: \"Next\",\r\n                targetURL: \"\",\r\n                tab: \"same-tab\",\r\n                interaction: null,\r\n              });\r\n\r\n              // Set the selected action to \"Next\" in the UI\r\n              setSelectActions(\"Next\");\r\n\r\n              // Mark changes as unsaved\r\n              setIsUnSavedChanges(true);\r\n            }\r\n          }}\r\n          displayEmpty\r\n          style={{ width: \"100%\" }}\r\n          sx={{\r\n            \"& .MuiSelect-select\": {\r\n              textAlign: \"left\",\r\n              padding: \"9px 14px !important\",\r\n            },\r\n            \"& .MuiSvgIcon-root\": {\r\n              height: \"20px\",\r\n              width: \"20px\",\r\n              top: \"10px\",\r\n            },\r\n          }}\r\n        >\r\n          <MenuItem value=\"\" disabled>\r\n            Select a button\r\n          </MenuItem>\r\n          {toolTipGuideMetaData[currentStep - 1].containers[1].buttons.map(\r\n            (button: any, buttonIndex: number) => (\r\n              <MenuItem key={buttonIndex} value={button.id}>\r\n                {button.name}\r\n              </MenuItem>\r\n            )\r\n          )}\r\n        </Select>\r\n      ) : (\r\n        <Typography variant=\"body2\" color=\"textSecondary\">\r\n          No buttons available\r\n        </Typography>\r\n      )}\r\n    </Box>\r\n  </Box>\r\n)} */}\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Border\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={tempBorderColor || \"#000000\"}\r\n\t\t\t\t\t\t\t\tonChange={handleBorderColorChange}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Background\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={tooltipBackgroundcolor}\r\n\t\t\t\t\t\t\t\tonChange={handleBackgroundColorChange}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t<Button\r\n\t\t\t\tvariant=\"contained\"\r\n\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\tclassName={`qadpt-btn ${widthError || paddingError || cornerRadiusError || borderSizeError ? \"disabled\" : \"\"}`}\r\n\t\t\t\tdisabled={widthError || paddingError || cornerRadiusError || borderSizeError} // Disable button if any validation errors exist\r\n\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t</Button>\r\n\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default TooltipCanvasSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,EAAEC,MAAM,QAAyB,eAAe;AACrG,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,SACCC,mBAAmB,IAAIC,WAAW,EAClCC,qBAAqB,IAAIC,aAAa,EACtCC,oBAAoB,IAAIC,YAAY,EACpCC,gBAAgB,IAAIC,cAAc,EAClCC,mBAAmB,IAAIC,gBAAgB,EACvCC,mBAAmB,IAAIC,eAAe,EACtCX,mBAAmB,IAAIY,cAAc,EACrCV,qBAAqB,IAAIW,gBAAgB,EACzCT,oBAAoB,IAAIU,eAAe,QAEjC,qBAAqB;AAC5B,OAAO,qBAAqB;AAC5B,OAAOC,cAAc,IAAIC,oBAAoB,QAA+C,4BAA4B;AACxH,SAASC,WAAW,EAAEC,YAAY,EAAEC,UAAU,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AAE1H,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,qBAAqB,GAAGA,CAAC;EAAEC,OAAO;EAAEC,UAAU;EAAEC;AAAkC,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,sBAAA;EAC7F,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGV,cAAc,CAAC,CAAC;EACzC,MAAM;IACLW,eAAe;IACfC,eAAe;IACfC,qBAAqB;IACrBC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,eAAe;IACfC,iBAAiB;IACjBC,sBAAsB;IACtBC,oBAAoB;IACpBC,qBAAqB;IACrBC,yBAAyB;IACzBC,cAAc;IACdC,mBAAmB;IACnBC,iBAAiB;IACjBC,kBAAkB;IAClBC,sBAAsB;IACtBC,eAAe;IACfC,kBAAkB;IAClBC,kBAAkB;IAClBC,iBAAiB;IACjBC,oBAAoB;IACpBC,WAAW;IACXC,YAAY;IACZC,eAAe;IACfC,gBAAgB;IAChBC,4BAA4B;IAC5BC,oBAAoB;IACpBC,4BAA4B;IAC5BC;EACD,CAAC,GAAGhD,cAAc,CAAEiD,KAAU,IAAKA,KAAK,CAAC;EAEzC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC4E,OAAO,EAAEC,UAAU,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/E,QAAQ,CAAC,eAAe,CAAC;EACzE,MAAM,CAACgF,KAAK,EAAEC,QAAQ,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACzC,MAAM,CAACkF,YAAY,EAAEC,eAAe,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACsF,eAAe,EAAEC,kBAAkB,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACwF,UAAU,EAAEC,aAAa,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC0D,kBAAkB,CAAC;EAE1E3D,SAAS,CAAC,MAAM;IACf;IACA,MAAM6F,UAAU,GAAGlC,kBAAkB,IAAIA,kBAAkB,KAAK,aAAa,IAAIA,kBAAkB,KAAK,EAAE,GAAGA,kBAAkB,GAAG,SAAS;IAC3IiC,kBAAkB,CAACC,UAAU,CAAC;EAC7B,CAAC,EAAE,CAAClC,kBAAkB,CAAC,CAAC;;EAE1B;EACA3D,SAAS,CAAC,MAAM;IACf,MAAM8F,YAAY,GAAGC,QAAQ,CAACC,gCAAgC,CAAC/C,YAAY,CAAC,CAAC,IAAI,CAAC;IAClF,IAAI6C,YAAY,GAAG,GAAG,EAAE;MACvBJ,aAAa,CAAC,IAAI,CAAC;IACpB,CAAC,MAAM;MACNA,aAAa,CAAC,KAAK,CAAC;IACrB;EACD,CAAC,EAAE,CAACzC,YAAY,CAAC,CAAC;EAElB,MAAMgD,SAAS,GAAG,CACjB;IAAEC,KAAK,EAAEvD,SAAS,CAAC,UAAU,EAAE;MAAEwD,YAAY,EAAE;IAAW,CAAC,CAAC;IAAEC,IAAI,eAAEjE,OAAA,CAACxB,WAAW;MAAC0F,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAW,CAAC,EACzH;IAAER,KAAK,EAAEvD,SAAS,CAAC,YAAY,EAAE;MAAEwD,YAAY,EAAE;IAAa,CAAC,CAAC;IAAEC,IAAI,EAAE/B,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBAAGpC,OAAA;MAAMwE,uBAAuB,EAAE;QAAEC,MAAM,EAAE/E;MAAW,CAAE;MAACgF,KAAK,EAAE;QAAER,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGtE,OAAA,CAACtB,aAAa;MAACwF,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC3R;IAAER,KAAK,EAAEvD,SAAS,CAAC,WAAW,EAAE;MAAEwD,YAAY,EAAE;IAAY,CAAC,CAAC;IAAEC,IAAI,eAAEjE,OAAA,CAACpB,YAAY;MAACsF,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAY,CAAC,EAC7H;IAAER,KAAK,EAAEvD,SAAS,CAAC,aAAa,EAAE;MAAEwD,YAAY,EAAE;IAAc,CAAC,CAAC;IAAEC,IAAI,EAAE/B,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBAAGpC,OAAA;MAAMwE,uBAAuB,EAAE;QAAEC,MAAM,EAAEjF;MAAY,CAAE;MAACkF,KAAK,EAAE;QAAER,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGtE,OAAA,CAAClB,cAAc;MAACoF,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAO,CAAC,EAChS;IAAER,KAAK,EAAEvD,SAAS,CAAC,eAAe,EAAE;MAAEwD,YAAY,EAAE;IAAgB,CAAC,CAAC;IAAEC,IAAI,EAAE/B,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBAAGpC,OAAA;MAAMwE,uBAAuB,EAAE;QAAEC,MAAM,EAAE7E;MAAc,CAAE;MAAC8E,KAAK,EAAE;QAAER,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGtE,OAAA,CAAChB,gBAAgB;MAACkF,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAS,CAAC,EAC1S;IAAER,KAAK,EAAEvD,SAAS,CAAC,cAAc,EAAE;MAAEwD,YAAY,EAAE;IAAe,CAAC,CAAC;IAAEC,IAAI,EAAE/B,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBAAGpC,OAAA;MAAMwE,uBAAuB,EAAE;QAAEC,MAAM,EAAEhF;MAAa,CAAE;MAACiF,KAAK,EAAE;QAAER,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGtE,OAAA,CAACd,eAAe;MAACgF,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACrS;IAAER,KAAK,EAAEvD,SAAS,CAAC,aAAa,EAAE;MAAEwD,YAAY,EAAE;IAAc,CAAC,CAAC;IAAEC,IAAI,eAAEjE,OAAA,CAACb,cAAc;MAAC+E,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAc,CAAC,EACrI;IAAER,KAAK,EAAEvD,SAAS,CAAC,eAAe,EAAE;MAAEwD,YAAY,EAAE;IAAgB,CAAC,CAAC;IAAEC,IAAI,EAAE/B,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBAAGpC,OAAA;MAAMwE,uBAAuB,EAAE;QAAEC,MAAM,EAAE9E;MAAc,CAAE;MAAC+E,KAAK,EAAE;QAAER,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGtE,OAAA,CAACZ,gBAAgB;MAAC8E,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAS,CAAC,EAC1S;IAAER,KAAK,EAAEvD,SAAS,CAAC,cAAc,EAAE;MAAEwD,YAAY,EAAE;IAAe,CAAC,CAAC;IAAEC,IAAI,eAAEjE,OAAA,CAACX,eAAe;MAAC6E,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAe,CAAC,CACzI;EAED,MAAMI,mBAAmB,GAAIC,CAAM,IAAK;IAAA,IAAAC,SAAA;IACvC,IAAID,CAAC,aAADA,CAAC,gBAAAC,SAAA,GAADD,CAAC,CAAEE,MAAM,cAAAD,SAAA,eAATA,SAAA,CAAWE,EAAE,EAAE;MAClB;MACApD,kBAAkB,CAACiD,CAAC,CAACE,MAAM,CAACC,EAAE,CAAC;IAChC;EACD,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAE/B/E,qBAAqB,CAAC;MACrBgF,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,GAAG;MACZC,YAAY,EAAE,GAAG;MACjBC,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE,EAAE;MACnB;MACA;MACA;MACAC,aAAa,EAAE;IAChB,CAAC,CAAC;IACF/E,qBAAqB,CAACpB,oBAAoB,CAAC;IAC3CqC,kBAAkB,CAAC,IAAI,CAAC;IACxBC,iBAAiB,CAAC,KAAK,CAAC;IACxBzB,4BAA4B,CAAC,KAAK,CAAC;EACpC,CAAC;EACD,MAAMuF,uBAAuB,GAAIf,CAAM,IAAK;IAAA,IAAAgB,UAAA;IAC3C,IAAIhB,CAAC,aAADA,CAAC,gBAAAgB,UAAA,GAADhB,CAAC,CAAEE,MAAM,cAAAc,UAAA,eAATA,UAAA,CAAWrB,KAAK,EAAE;MACrBd,kBAAkB,CAACmB,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC;IAEnC;EACD,CAAC;EAED,MAAMsB,2BAA2B,GAAIjB,CAAM,IAAK;IAAA,IAAAkB,UAAA;IAC/C,IAAIlB,CAAC,aAADA,CAAC,gBAAAkB,UAAA,GAADlB,CAAC,CAAEE,MAAM,cAAAgB,UAAA,eAATA,UAAA,CAAWvB,KAAK,EAAE;MACrBnD,yBAAyB,CAACwD,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC;IAC1C;EACD,CAAC;EACD,MAAMwB,gBAAgB,GAAInB,CAAM,IAAK;IACpC;IACA3C,eAAe,CAAC2C,CAAC,CAACE,MAAM,CAACkB,OAAO,CAAC;EAClC,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACzBxD,SAAS,CAAC,KAAK,CAAC;IAChBrC,4BAA4B,CAAC,KAAK,CAAC;EACpC,CAAC;EAED,MAAM8F,kBAAkB,GAAGA,CAAA,KAAM;IAChC;IACA,IAAI5C,UAAU,IAAIN,YAAY,IAAIE,iBAAiB,IAAIE,eAAe,EAAE;MACvE;IACD;;IAEA;IACA,MAAM+C,qBAAqB,GAAG;MAC7BC,QAAQ,EAAE1E,eAAe;MACzB+D,eAAe,EAAEhE,sBAAsB;MACvC2D,KAAK,EAAEtE,YAAY;MACnBuF,YAAY,EAAE/E,mBAAmB;MACjCgF,OAAO,EAAEjF,cAAc;MACvBmE,WAAW,EAAEhC,eAAe;MAC5B+C,UAAU,EAAEhF,iBAAiB;MAC7BiF,YAAY,EAAExE,YAAY;MAC1ByE,KAAK,EAAE7F,YAAY;MACnB8F,KAAK,EAAE7F;IACR,CAAC;;IAED;IACAF,qBAAqB,CAACwF,qBAAqB,CAAC,CAAC,CAAC;IAC9CF,WAAW,CAAC,CAAC;IACb3D,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAEDzE,SAAS,CAAC,MAAM;IAAA,IAAA8I,qBAAA;IACf,KAAAA,qBAAA,GAAI7E,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAA4E,qBAAA,eAArCA,qBAAA,CAAuCC,MAAM,EAAE;MAClD,MAAMC,UAAU,GAAG/E,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,CAAC6E,MAAM;;MAE/D;MACA,MAAMpB,WAAW,GAAGqB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAErB,WAAW;MAC3C,MAAMsB,gBAAgB,GAAGtB,WAAW,IAAIA,WAAW,KAAK,aAAa,IAAIA,WAAW,KAAK,EAAE,GAAGA,WAAW,GAAG,SAAS;MAErH7D,kBAAkB,CAAC,CAAAkF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAET,QAAQ,KAAI,eAAe,CAAC;MAC3DpF,iBAAiB,CAAC,CAAA6F,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEP,OAAO,KAAI,MAAM,CAAC;MAChDrF,sBAAsB,CAAC,CAAA4F,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAER,YAAY,KAAI,KAAK,CAAC;MACzDnF,oBAAoB,CAAC,CAAA2F,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEN,UAAU,KAAI,KAAK,CAAC;MACrDpF,qBAAqB,CAACqE,WAAW,IAAI,EAAE,CAAC;MACxC/B,kBAAkB,CAACqD,gBAAgB,CAAC,CAAC,CAAC;MACtC1F,yBAAyB,CAAC,CAAAyF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEpB,eAAe,KAAI,SAAS,CAAC;MACnE1E,eAAe,CAAC,CAAA8F,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEzB,KAAK,KAAI,OAAO,CAAC;MAC7C,IAAIlD,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,EAAE;QACzE3B,eAAe,CAAC,CAAAoG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEJ,KAAK,KAAI,KAAK,CAAC;QAC3C/F,eAAe,CAAC,CAAAmG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEH,KAAK,KAAI,KAAK,CAAC;MAC5C,CAAC,MACI;QACJjG,eAAe,CAAC,CAAAoG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEJ,KAAK,KAAI,OAAO,CAAC;QAC7C/F,eAAe,CAAC,CAAAmG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEH,KAAK,KAAI,OAAO,CAAC;MAE9C;IACD;EACD,CAAC,EAAE,EAAApG,sBAAA,GAACwB,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAAzB,sBAAA,uBAArCA,sBAAA,CAAuCsG,MAAM,CAAC,CAAC;EAEnD,IAAI,CAACpE,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMqB,gCAAgC,GAAIU,KAAa,IAAK;IAC3D,MAAMwC,CAAC,GAAGC,MAAM,CAACzC,KAAK,CAAC;IACvB,IAAI0C,QAAQ,GAAGF,CAAC;IAChB,IAAIA,CAAC,aAADA,CAAC,eAADA,CAAC,CAAEG,QAAQ,CAAC,IAAI,CAAC,IAAIH,CAAC,aAADA,CAAC,eAADA,CAAC,CAAEG,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC1CD,QAAQ,GAAGF,CAAC,CAACI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9B;IACA,OAAOF,QAAQ;EAChB,CAAC;EACD,MAAMG,YAAY,GAAIxC,CAAM,IAAK;IAChC;IACA,MAAML,KAAK,GAAGK,CAAC,CAACE,MAAM,CAACP,KAAK;IAC5B,IAAIA,KAAK,KAAK,EAAE,EAAE;MACjBxD,eAAe,CAAC,KAAK,CAAC;MACtBwC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;MACrB;IACD;IAEA,IAAI,CAAC,SAAS,CAAC8D,IAAI,CAAC9C,KAAK,CAAC,EAAE;MAC3B;IACD;IAEA,IAAI+C,UAAU,GAAG1D,QAAQ,CAACW,KAAK,CAAC,IAAI,CAAC;;IAErC;IACA,IAAI+C,UAAU,GAAG,GAAG,EAAE;MACrB/D,aAAa,CAAC,IAAI,CAAC;IACpB,CAAC,MAAM;MACNA,aAAa,CAAC,KAAK,CAAC;IACrB;IAEAxC,eAAe,CAAC,GAAGuG,UAAU,IAAI,CAAC;EACnC,CAAC;EAID,oBACCtH,OAAA;IACC+E,EAAE,EAAC,mBAAmB;IACtBwC,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAE7BxH,OAAA;MAAKuH,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC7BxH,OAAA;QAAKuH,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACnCxH,OAAA,CAAC7B,UAAU;UACV,cAAW,MAAM;UACjBsJ,OAAO,EAAExB,WAAY;UAAAuB,QAAA,eAErBxH,OAAA,CAAC1B,2BAA2B;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACbtE,OAAA;UAAKuH,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAGtF,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,GAAI5B,SAAS,CAAC,QAAQ,CAAC,GAAGA,SAAS,CAAC,QAAQ;QAAC;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvJtE,OAAA,CAAC7B,UAAU;UACVuJ,IAAI,EAAC,OAAO;UACZ,cAAW,OAAO;UAClBD,OAAO,EAAExB,WAAY;UAAAuB,QAAA,eAErBxH,OAAA,CAAC3B,SAAS;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNtE,OAAA;QAAKuH,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC9BxH,OAAA;UAAKuH,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC9BxH,OAAA,CAACjC,GAAG;YAACwJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAUjCxH,OAAA;cACCuH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAG9BhH,SAAS,CAAC,eAAe;YAAC;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACNtE,OAAA;cAAAwH,QAAA,eACAxH,OAAA;gBAAOuH,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBACnCxH,OAAA;kBACI2H,IAAI,EAAC,UAAU;kBACf3B,OAAO,EAAEhE,YAAa;kBACtB4F,QAAQ,EAAE7B,gBAAiB;kBAC3B8B,IAAI,EAAC;gBAAc;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACFtE,OAAA;kBAAMuH,SAAS,EAAC;gBAAQ;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtE,OAAA,CAACjC,GAAG;YAACwJ,SAAS,EAAC,qBAAqB;YACnCO,EAAE,EAAE;cACHC,OAAO,EAAE7F,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,GAAE,GAAG,GAAG,CAAC;cACtF4F,MAAM,EAAC9F,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,GAAC,aAAa,GAAC;YAC3F,CAAE;YAAAoF,QAAA,gBAEFxH,OAAA,CAAChC,UAAU;cAACuJ,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EACtChH,SAAS,CAAC,UAAU;YAAC;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACrCtE,OAAA,CAAC9B,IAAI;cAAC+J,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAV,QAAA,gBAChCxH,OAAA,CAAC9B,IAAI;gBAACiK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfxH,OAAA,CAAC7B,UAAU;kBACTuJ,IAAI,EAAC,OAAO;kBACZW,QAAQ,EAAErG,YAAa;kBACvB8F,EAAE,EAAE;oBACFC,OAAO,EAAEnF,gBAAgB,KAAKkB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACzD;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPtE,OAAA,CAAC9B,IAAI;gBAACiK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfxH,OAAA,CAAC7B,UAAU;kBACTuJ,IAAI,EAAC,OAAO;kBACZ3C,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAM;kBACvBkD,OAAO,EAAEA,CAAA,KAAM;oBAEb;oBACA9F,kBAAkB,CAACmC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;kBACxC,CAAE;kBACP8D,QAAQ,EAAErG,YAAY,IAAIE,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAU;kBAC/FkG,aAAa;kBACRR,EAAE,EAAE;oBACFC,OAAO,EAAErG,eAAe,KAAKoC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACxD,CAAE;kBAAAiD,QAAA,EAEAtF,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBACpEpC,OAAA;oBAAMwE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE/E;oBAAW,CAAE;oBAAEqF,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAElFtE,OAAA,CAACtB,aAAa;oBAACwF,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAClC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPtE,OAAA,CAAC9B,IAAI;gBAACiK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfxH,OAAA,CAAC7B,UAAU;kBACTuJ,IAAI,EAAC,OAAO;kBACZW,QAAQ,EAAErG,YAAa;kBACvB8F,EAAE,EAAE;oBACFC,OAAO,EAAEnF,gBAAgB,KAAKkB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACzD;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPtE,OAAA,CAAC9B,IAAI;gBAACiK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfxH,OAAA,CAAC7B,UAAU;kBACTuJ,IAAI,EAAC,OAAO;kBACZ3C,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAM;kBACvBkD,OAAO,EAAEA,CAAA,KAAM;oBACd;oBACC9F,kBAAkB,CAACmC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;kBACxC,CAAE;kBACF8D,QAAQ,EAAErG,YAAa;kBAC1BsG,aAAa;kBACVR,EAAE,EAAE;oBACFC,OAAO,EAAErG,eAAe,KAAKoC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACxD,CAAE;kBAAAiD,QAAA,EAEAtF,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBACpEpC,OAAA;oBAAMwE,uBAAuB,EAAE;sBAAEC,MAAM,EAAEjF;oBAAY,CAAE;oBAACuF,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAElFtE,OAAA,CAAClB,cAAc;oBAACoF,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACnC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPtE,OAAA,CAAC9B,IAAI;gBAACiK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfxH,OAAA,CAAC7B,UAAU;kBACTuJ,IAAI,EAAC,OAAO;kBACZ3C,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAM;kBACvBkD,OAAO,EAAEA,CAAA,KAAM;oBACd;oBACC9F,kBAAkB,CAACmC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;kBACxC,CAAE;kBACE8D,QAAQ,EAAErG,YAAa;kBACvBsG,aAAa;kBACjBR,EAAE,EAAE;oBACNC,OAAO,EAAErG,eAAe,KAAKoC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG,GAAG;oBACzDgE,WAAW,EAAC;kBACV,CAAE;kBAAAf,QAAA,EAEAtF,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBACpEpC,OAAA;oBAAMwE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE7E;oBAAc,CAAE;oBAACmF,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,gBAEnFtE,OAAA,CAAChB,gBAAgB;oBAACkF,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACrC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPtE,OAAA,CAAC9B,IAAI;gBAACiK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfxH,OAAA,CAAC7B,UAAU;kBACTuJ,IAAI,EAAC,OAAO;kBACZ3C,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAM;kBACvBkD,OAAO,EAAEA,CAAA,KAAM;oBACd;oBACC9F,kBAAkB,CAACmC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;kBACxC,CAAE;kBACF8D,QAAQ,EAAErG,YAAa;kBAC1BsG,aAAa;kBACVR,EAAE,EAAE;oBACFC,OAAO,EAAErG,eAAe,KAAKoC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACxD,CAAE;kBAAAiD,QAAA,EAEAtF,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBACpEpC,OAAA;oBAAMwE,uBAAuB,EAAE;sBAAEC,MAAM,EAAEhF;oBAAa,CAAE;oBAAEsF,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEpFtE,OAAA,CAACd,eAAe;oBAACgF,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACpC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPtE,OAAA,CAAC9B,IAAI;gBAACiK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfxH,OAAA,CAAC7B,UAAU;kBACTuJ,IAAI,EAAC,OAAO;kBACZW,QAAQ,EAAErG,YAAa;kBACvB8F,EAAE,EAAE;oBACFC,OAAO,EAAEnF,gBAAgB,KAAKkB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACzD;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPtE,OAAA,CAAC9B,IAAI;gBAACiK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfxH,OAAA,CAAC7B,UAAU;kBACTuJ,IAAI,EAAC,OAAO;kBACZ3C,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAM;kBACvBkD,OAAO,EAAEA,CAAA,KAAM;oBACd;oBACC9F,kBAAkB,CAACmC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;kBACxC,CAAE;kBACF8D,QAAQ,EAAErG,YAAa;kBAC1BsG,aAAa;kBACVR,EAAE,EAAE;oBACFC,OAAO,EAAErG,eAAe,KAAKoC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACxD,CAAE;kBAAAiD,QAAA,EAEAtF,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBACpEpC,OAAA;oBAAMwE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE9E;oBAAc,CAAE;oBAACoF,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEpFtE,OAAA,CAACZ,gBAAgB;oBAAC8E,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACrC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPtE,OAAA,CAAC9B,IAAI;gBAACiK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfxH,OAAA,CAAC7B,UAAU;kBACTuJ,IAAI,EAAC,OAAO;kBACZW,QAAQ,EAAErG,YAAa;kBACvB8F,EAAE,EAAE;oBACFC,OAAO,EAAEnF,gBAAgB,KAAKkB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACzD;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGNtE,OAAA,CAACjC,GAAG;YAACwJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCxH,OAAA;cAAKuH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GAAC,IAAE,EAAChH,SAAS,CAAC,aAAa,CAAC;YAAA;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEtE,OAAA;cAAAwH,QAAA,eACAxH,OAAA,CAAC/B,SAAS;gBACTuK,OAAO,EAAC,UAAU;gBAClBjE,KAAK,EAAEV,gCAAgC,CAACjD,YAAY,CAAE;gBAEtD8G,IAAI,EAAC,OAAO;gBACZH,SAAS,EAAC,qBAAqB;gBAC/BK,QAAQ,EAAGhD,CAAC,IAAKnE,eAAe,CAAC,GAAGmD,QAAQ,CAACgB,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC,IAAI,CAAC,IAAI,CAAE;gBACvEkE,UAAU,EAAE;kBACXC,YAAY,EAAE,IAAI;kBAClBZ,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM;kBAE5B;gBACD,CAAE;gBACFN,QAAQ,EAAErG;cAAa;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtE,OAAA,CAACjC,GAAG;YAACwJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCxH,OAAA;cAAKuH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GAAC,IAAE,EAAChH,SAAS,CAAC,aAAa,CAAC;YAAA;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEtE,OAAA;cAAAwH,QAAA,eACAxH,OAAA,CAAC/B,SAAS;gBACTuK,OAAO,EAAC,UAAU;gBAClBjE,KAAK,EAAEV,gCAAgC,CAAChD,YAAY,CAAE;gBAEtD6G,IAAI,EAAC,OAAO;gBACZH,SAAS,EAAC,qBAAqB;gBAC/BK,QAAQ,EAAGhD,CAAC,IAAKlE,eAAe,CAAC,GAAGkD,QAAQ,CAACgB,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC,IAAI,CAAC,IAAI,CAAE;gBACvEkE,UAAU,EAAE;kBACXC,YAAY,EAAE,IAAI;kBAClBZ,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM;kBAE5B;gBACD,CAAE;gBACFN,QAAQ,EAAErG;cAAa;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtE,OAAA,CAACjC,GAAG;YACHwJ,SAAS,EAAC,mBAAmB;YAC7B7C,KAAK,EAAE;cAAEkE,aAAa,EAAE,QAAQ;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAArB,QAAA,eAEnDxH,OAAA,CAACjC,GAAG;cAAC2G,KAAK,EAAE;gBAAEoE,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE,KAAK;gBAAE5D,KAAK,EAAE;cAAO,CAAE;cAAAoC,QAAA,gBAChFxH,OAAA;gBACCuH,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAG9BhH,SAAS,CAAC,OAAO;cAAC;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACNtE,OAAA;gBAAAwH,QAAA,eACAxH,OAAA,CAAC/B,SAAS;kBACTuK,OAAO,EAAC,UAAU;kBAClBjE,KAAK,EAAEV,gCAAgC,CAAC/C,YAAY,CAAE;kBAEtD4G,IAAI,EAAC,OAAO;kBACZH,SAAS,EAAC,qBAAqB;kBAC/BK,QAAQ,EAAER,YAAa;kBACvBtE,KAAK,EAAEQ,UAAW;kBAClBmF,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBZ,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEa,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD;gBAAE;kBAAAxE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEF,CAAC,EACLhB,UAAU,iBACVtD,OAAA,CAAChC,UAAU;YACV0G,KAAK,EAAE;cACNR,QAAQ,EAAE,MAAM;cAChB+E,KAAK,EAAE,SAAS;cAChBC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACPC,YAAY,EAAE,KAAK;cACnBP,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE;YACb,CAAE;YAAAvB,QAAA,gBAEFxH,OAAA;cAAM0E,KAAK,EAAE;gBAAEoE,OAAO,EAAE,MAAM;gBAAE5E,QAAQ,EAAE,MAAM;gBAAE6E,UAAU,EAAE,QAAQ;gBAAEO,WAAW,EAAC;cAAM,CAAE;cAC3F9E,uBAAuB,EAAE;gBAAEC,MAAM,EAAE5E;cAAQ;YAAE;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,EACD9D,SAAS,CAAC,mCAAmC,CAAC;UAAA;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CACZ,eAGDtE,OAAA,CAACjC,GAAG;YAACwJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCxH,OAAA;cAAKuH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEhH,SAAS,CAAC,SAAS;YAAC;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjEtE,OAAA;cAAAwH,QAAA,eACAxH,OAAA,CAAC/B,SAAS;gBACTuK,OAAO,EAAC,UAAU;gBAClBjE,KAAK,EAAEV,gCAAgC,CAACxC,cAAc,CAAE;gBAExDqG,IAAI,EAAC,OAAO;gBACZH,SAAS,EAAC,qBAAqB;gBAC/BK,QAAQ,EAAGhD,CAAC,IAAK;kBAChB;kBACA,MAAML,KAAK,GAAGK,CAAC,CAACE,MAAM,CAACP,KAAK;kBAC5B,IAAI,CAAC,SAAS,CAAC8C,IAAI,CAAC9C,KAAK,CAAC,EAAE;oBAC3B;kBACD;kBACA,MAAM+C,UAAU,GAAG1D,QAAQ,CAACW,KAAK,CAAC,IAAI,CAAC;kBACvC;kBACA,IAAI+C,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;oBACtCrE,eAAe,CAAC,IAAI,CAAC;kBACtB,CAAC,MAAM;oBACNA,eAAe,CAAC,KAAK,CAAC;kBACvB;kBACAjC,iBAAiB,CAAC,GAAGsG,UAAU,IAAI,CAAC;gBACrC,CAAE;gBACFmB,UAAU,EAAE;kBACXC,YAAY,EAAE,IAAI;kBAClBZ,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM;kBAE5B;gBACD,CAAE;gBACF7F,KAAK,EAAEE;cAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLtB,YAAY,iBACbhD,OAAA,CAAChC,UAAU;YACX0G,KAAK,EAAE;cACNR,QAAQ,EAAE,MAAM;cAChB+E,KAAK,EAAE,SAAS;cAChBC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACPC,YAAY,EAAE,KAAK;cACnBP,OAAO,EAAE;YACV,CAAE;YAAAtB,QAAA,gBACAxH,OAAA;cAAM0E,KAAK,EAAE;gBAAEoE,OAAO,EAAE,MAAM;gBAAE5E,QAAQ,EAAE,MAAM;gBAAE6E,UAAU,EAAE,QAAQ;gBAAEO,WAAW,EAAC;cAAM,CAAE;cAE9F9E,uBAAuB,EAAE;gBAAEC,MAAM,EAAE5E;cAAQ;YAAE;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,EACE9D,SAAS,CAAC,qCAAqC,CAAC;UAAA;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CACZ,eAEDtE,OAAA,CAACjC,GAAG;YAACwJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCxH,OAAA;cAAKuH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEhH,SAAS,CAAC,eAAe;YAAC;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEtE,OAAA;cAAAwH,QAAA,eACAxH,OAAA,CAAC/B,SAAS;gBACTuK,OAAO,EAAC,UAAU;gBAClBjE,KAAK,EAAEV,gCAAgC,CAACvC,mBAAmB,CAAE;gBAE7DoG,IAAI,EAAC,OAAO;gBACZH,SAAS,EAAC,qBAAqB;gBAC/BK,QAAQ,EAAGhD,CAAC,IAAK;kBAChB;kBACA,MAAML,KAAK,GAAGK,CAAC,CAACE,MAAM,CAACP,KAAK;kBAC5B,IAAI,CAAC,SAAS,CAAC8C,IAAI,CAAC9C,KAAK,CAAC,EAAE;oBAC3B;kBACD;kBACA,MAAM+C,UAAU,GAAG1D,QAAQ,CAACW,KAAK,CAAC,IAAI,CAAC;kBACvC;kBACA,IAAI+C,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;oBACtCnE,oBAAoB,CAAC,IAAI,CAAC;kBAC3B,CAAC,MAAM;oBACNA,oBAAoB,CAAC,KAAK,CAAC;kBAC5B;kBACAlC,sBAAsB,CAAC,GAAGqG,UAAU,EAAE,CAAC;gBACxC,CAAE;gBACFmB,UAAU,EAAE;kBACXC,YAAY,EAAE,IAAI;kBAClBZ,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM;kBAE5B;gBACD,CAAE;gBACF7F,KAAK,EAAEI;cAAkB;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLpB,iBAAiB,iBAClBlD,OAAA,CAAChC,UAAU;YACX0G,KAAK,EAAE;cACNR,QAAQ,EAAE,MAAM;cAChB+E,KAAK,EAAE,SAAS;cAChBC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACPC,YAAY,EAAE,KAAK;cACnBP,OAAO,EAAE;YACV,CAAE;YAAAtB,QAAA,gBACAxH,OAAA;cAAM0E,KAAK,EAAE;gBAAEoE,OAAO,EAAE,MAAM;gBAAE5E,QAAQ,EAAE,MAAM;gBAAE6E,UAAU,EAAE,QAAQ;gBAAEO,WAAW,EAAC;cAAM,CAAE;cAE9F9E,uBAAuB,EAAE;gBAAEC,MAAM,EAAE5E;cAAQ;YAAE;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,EACE9D,SAAS,CAAC,qCAAqC,CAAC;UAAA;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CACZ,eAGPtE,OAAA,CAACjC,GAAG;YAACwJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC3BxH,OAAA;cAAKuH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEhH,SAAS,CAAC,aAAa;YAAC;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1EtE,OAAA;cAAAwH,QAAA,eACExH,OAAA,CAAC/B,SAAS;gBACRuK,OAAO,EAAC,UAAU;gBAClBjE,KAAK,EAAEV,gCAAgC,CAACtC,iBAAiB,CAAE;gBAC3DmG,IAAI,EAAC,OAAO;gBACZH,SAAS,EAAC,qBAAqB;gBAC/BK,QAAQ,EAAGhD,CAAC,IAAK;kBACf;kBACA,MAAML,KAAK,GAAGK,CAAC,CAACE,MAAM,CAACP,KAAK;kBAC5B,IAAI,CAAC,SAAS,CAAC8C,IAAI,CAAC9C,KAAK,CAAC,EAAE;oBAC1B;kBACF;kBACA,MAAM+C,UAAU,GAAG1D,QAAQ,CAACW,KAAK,CAAC,IAAI,CAAC;kBACvC;kBACA,IAAI+C,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;oBACpCjE,kBAAkB,CAAC,IAAI,CAAC;kBAC1B,CAAC,MAAM;oBACLA,kBAAkB,CAAC,KAAK,CAAC;kBAC3B;kBACAnC,oBAAoB,CAAC,GAAGoG,UAAU,IAAI,CAAC;gBACzC,CAAE;gBACFmB,UAAU,EAAE;kBACVC,YAAY,EAAE,IAAI;kBAClBZ,EAAE,EAAE;oBACF,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAE;sBAAEA,MAAM,EAAE;oBAAO;kBACjC;gBACF,CAAE;gBACF7F,KAAK,EAAEM;cAAgB;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLlB,eAAe,iBACdpD,OAAA,CAAChC,UAAU;YACT0G,KAAK,EAAE;cACLR,QAAQ,EAAE,MAAM;cAChB+E,KAAK,EAAE,SAAS;cAChBC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACPC,YAAY,EAAE,KAAK;cACnBP,OAAO,EAAE;YACX,CAAE;YAAAtB,QAAA,gBAEFxH,OAAA;cACE0E,KAAK,EAAE;gBAAEoE,OAAO,EAAE,MAAM;gBAAE5E,QAAQ,EAAE,MAAM;gBAAE6E,UAAU,EAAE,QAAQ;gBAAEO,WAAW,EAAE;cAAM,CAAE;cACvF9E,uBAAuB,EAAE;gBAAEC,MAAM,EAAE5E;cAAQ;YAAE;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,sCAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb,eA2FKtE,OAAA,CAACjC,GAAG;YAACwJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCxH,OAAA;cAAKuH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEhH,SAAS,CAAC,QAAQ;YAAC;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChEtE,OAAA;cAAAwH,QAAA,eACAxH,OAAA;gBACC2H,IAAI,EAAC,OAAO;gBACZpD,KAAK,EAAEf,eAAe,IAAI,SAAU;gBACpCoE,QAAQ,EAAEjC,uBAAwB;gBAClC4B,SAAS,EAAC;cAAmB;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtE,OAAA,CAACjC,GAAG;YAACwJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCxH,OAAA;cAAKuH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEhH,SAAS,CAAC,YAAY;YAAC;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpEtE,OAAA;cAAAwH,QAAA,eACAxH,OAAA;gBACC2H,IAAI,EAAC,OAAO;gBACZpD,KAAK,EAAE9C,sBAAuB;gBAC9BmG,QAAQ,EAAE/B,2BAA4B;gBACtC0B,SAAS,EAAC;cAAmB;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNtE,OAAA;QAAKuH,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACnCxH,OAAA,CAAC5B,MAAM;UACPoK,OAAO,EAAC,WAAW;UACnBf,OAAO,EAAEvB,kBAAmB;UAC5BqB,SAAS,EAAE,aAAajE,UAAU,IAAIN,YAAY,IAAIE,iBAAiB,IAAIE,eAAe,GAAG,UAAU,GAAG,EAAE,EAAG;UAC/GiF,QAAQ,EAAE/E,UAAU,IAAIN,YAAY,IAAIE,iBAAiB,IAAIE,eAAgB,CAAC;UAAA;UAAAoE,QAAA,EAE3EhH,SAAS,CAAC,OAAO;QAAC;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAER,CAAC;AAACjE,EAAA,CAt0BIJ,qBAAqB;EAAA,QACDH,cAAc,EAgCnCR,cAAc;AAAA;AAAAiK,EAAA,GAjCbtJ,qBAAqB;AAw0B3B,eAAeA,qBAAqB;AAAC,IAAAsJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}