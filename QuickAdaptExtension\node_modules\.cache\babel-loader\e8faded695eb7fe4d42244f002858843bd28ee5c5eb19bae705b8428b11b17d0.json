{"ast": null, "code": "import React,{useState}from'react';import'./EnableAIButton.css';import{useTranslation}from'react-i18next';import useDrawerStore from'../../store/drawerStore';import AgentAdditionalContextPopup from'./AgentAdditionalContextPopup';import{cancelTraining}from'../../services/ScrapingService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StopScrapingButton=_ref=>{let{onClick}=_ref;const{setIsAgentTraining,isAgentTraining}=useDrawerStore(state=>state);const{t:translate}=useTranslation();const[showAdditionalContext,setShowAdditionalContext]=useState(false);const handleClick=()=>{if(isAgentTraining){setShowAdditionalContext(true);}else{onClick();}};const handleCancel=async()=>{try{// Cancel the training process (stops scraping and clears data)\nawait cancelTraining();// Update the training state\nsetIsAgentTraining(false);// Close the popup\nsetShowAdditionalContext(false);}catch(error){console.error('Error canceling training:',error);// Still close the popup even if there's an error\nsetShowAdditionalContext(false);}};return/*#__PURE__*/_jsxs(\"div\",{className:\"stop-scraping-button-container\",id:\"stop-scraping-button\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"enable-ai-button stop-scraping-button\",onClick:handleClick,children:/*#__PURE__*/_jsx(\"span\",{className:\"enable-ai-text\",children:translate(\"Stop Training\")})}),showAdditionalContext&&isAgentTraining&&/*#__PURE__*/_jsx(AgentAdditionalContextPopup,{open:showAdditionalContext,onClose:()=>setShowAdditionalContext(false),onSaved:()=>{setShowAdditionalContext(false);onClick();},onCancel:handleCancel})]});};export default StopScrapingButton;", "map": {"version": 3, "names": ["React", "useState", "useTranslation", "useDrawerStore", "AgentAdditionalContextPopup", "cancelTraining", "jsx", "_jsx", "jsxs", "_jsxs", "StopScrapingButton", "_ref", "onClick", "setIsAgentTraining", "isAgentTraining", "state", "t", "translate", "showAdditionalContext", "setShowAdditionalContext", "handleClick", "handleCancel", "error", "console", "className", "id", "children", "open", "onClose", "onSaved", "onCancel"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/AI/StopScrapingButton.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport './EnableAIButton.css';\r\nimport { useTranslation } from 'react-i18next';\r\nimport useDrawerStore, { DrawerState } from '../../store/drawerStore';\r\nimport AgentAdditionalContextPopup from './AgentAdditionalContextPopup';\r\nimport { cancelTraining } from '../../services/ScrapingService';\r\n\r\ninterface StopScrapingButtonProps {\r\n  onClick: () => void;\r\n}\r\n\r\nconst StopScrapingButton: React.FC<StopScrapingButtonProps> = ({ onClick }) => {\r\n  const {\r\n    setIsAgentTraining,\r\n    isAgentTraining\r\n  } = useDrawerStore((state: DrawerState) => state);\r\n  const { t: translate } = useTranslation();\r\n  const [showAdditionalContext, setShowAdditionalContext] = useState(false);\r\n\r\n  const handleClick = () => {\r\n    if(isAgentTraining){\r\n    setShowAdditionalContext(true);\r\n    }\r\n    else\r\n    {\r\n      onClick();\r\n    }\r\n  };\r\n\r\n  const handleCancel = async () => {\r\n    try {\r\n      // Cancel the training process (stops scraping and clears data)\r\n      await cancelTraining();\r\n\r\n      // Update the training state\r\n      setIsAgentTraining(false);\r\n\r\n      // Close the popup\r\n      setShowAdditionalContext(false);\r\n    } catch (error) {\r\n      console.error('Error canceling training:', error);\r\n      // Still close the popup even if there's an error\r\n      setShowAdditionalContext(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className='stop-scraping-button-container' id='stop-scraping-button'>\r\n      <button className=\"enable-ai-button stop-scraping-button\" onClick={handleClick}>\r\n        <span className=\"enable-ai-text\">{translate(\"Stop Training\")}</span>\r\n      </button>\r\n      {showAdditionalContext &&  isAgentTraining &&  (\r\n        <AgentAdditionalContextPopup\r\n          open={showAdditionalContext}\r\n          onClose={() => setShowAdditionalContext(false)}\r\n          onSaved={() => {\r\n            setShowAdditionalContext(false);\r\n            onClick();\r\n          }}\r\n          onCancel={handleCancel}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StopScrapingButton;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,sBAAsB,CAC7B,OAASC,cAAc,KAAQ,eAAe,CAC9C,MAAO,CAAAC,cAAc,KAAuB,yBAAyB,CACrE,MAAO,CAAAC,2BAA2B,KAAM,+BAA+B,CACvE,OAASC,cAAc,KAAQ,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMhE,KAAM,CAAAC,kBAAqD,CAAGC,IAAA,EAAiB,IAAhB,CAAEC,OAAQ,CAAC,CAAAD,IAAA,CACxE,KAAM,CACJE,kBAAkB,CAClBC,eACF,CAAC,CAAGX,cAAc,CAAEY,KAAkB,EAAKA,KAAK,CAAC,CACjD,KAAM,CAAEC,CAAC,CAAEC,SAAU,CAAC,CAAGf,cAAc,CAAC,CAAC,CACzC,KAAM,CAACgB,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGlB,QAAQ,CAAC,KAAK,CAAC,CAEzE,KAAM,CAAAmB,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAGN,eAAe,CAAC,CACnBK,wBAAwB,CAAC,IAAI,CAAC,CAC9B,CAAC,IAED,CACEP,OAAO,CAAC,CAAC,CACX,CACF,CAAC,CAED,KAAM,CAAAS,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF;AACA,KAAM,CAAAhB,cAAc,CAAC,CAAC,CAEtB;AACAQ,kBAAkB,CAAC,KAAK,CAAC,CAEzB;AACAM,wBAAwB,CAAC,KAAK,CAAC,CACjC,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD;AACAH,wBAAwB,CAAC,KAAK,CAAC,CACjC,CACF,CAAC,CAED,mBACEV,KAAA,QAAKe,SAAS,CAAC,gCAAgC,CAACC,EAAE,CAAC,sBAAsB,CAAAC,QAAA,eACvEnB,IAAA,WAAQiB,SAAS,CAAC,uCAAuC,CAACZ,OAAO,CAAEQ,WAAY,CAAAM,QAAA,cAC7EnB,IAAA,SAAMiB,SAAS,CAAC,gBAAgB,CAAAE,QAAA,CAAET,SAAS,CAAC,eAAe,CAAC,CAAO,CAAC,CAC9D,CAAC,CACRC,qBAAqB,EAAKJ,eAAe,eACxCP,IAAA,CAACH,2BAA2B,EAC1BuB,IAAI,CAAET,qBAAsB,CAC5BU,OAAO,CAAEA,CAAA,GAAMT,wBAAwB,CAAC,KAAK,CAAE,CAC/CU,OAAO,CAAEA,CAAA,GAAM,CACbV,wBAAwB,CAAC,KAAK,CAAC,CAC/BP,OAAO,CAAC,CAAC,CACX,CAAE,CACFkB,QAAQ,CAAET,YAAa,CACxB,CACF,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAX,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}