{"ast": null, "code": "import { adminApiService } from \"./APIService\";\nexport const saveGuide = async (guideData, onSuccess, onError, setLoading) => {\n  try {\n    setLoading(true);\n    const response = await adminApiService.post(\"/Guide/Saveguide\", guideData);\n    if (response && response.status === 200 && response.data.Success) {\n      onSuccess(response);\n    } else {\n      var _response$data;\n      console.error(\"Unexpected response:\", response);\n      const err = \"Unexpected response status\";\n      const errorMessage = (response === null || response === void 0 ? void 0 : (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.ErrorMessage) || err;\n      onError(errorMessage);\n    }\n  } catch (error) {\n    console.error(\"Error saving guide:\", error);\n    onError(error);\n  } finally {\n    setLoading(false);\n  }\n};\nexport const updateGuide = async (guideData, onSuccess, onError, setLoading) => {\n  try {\n    setLoading(true);\n    const response = await adminApiService.post(\"/Guide/Updateguide\", guideData);\n    if (response && response.status === 200 && response.data.Success) {\n      onSuccess(response);\n    } else {\n      var _response$data2;\n      console.error(\"Unexpected response:\", response);\n      const err = \"Unexpected response status\";\n      const errorMessage = (response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.ErrorMessage) || err;\n      onError(errorMessage);\n    }\n  } catch (error) {\n    console.error(\"Error saving guide:\", error);\n    onError(error);\n  } finally {\n    setLoading(false);\n  }\n};", "map": {"version": 3, "names": ["adminApiService", "saveGuide", "guideData", "onSuccess", "onError", "setLoading", "response", "post", "status", "data", "Success", "_response$data", "console", "error", "err", "errorMessage", "ErrorMessage", "updateGuide", "_response$data2"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/services/SaveGuideService.tsx"], "sourcesContent": ["import { AxiosResponse } from \"axios\";\r\nimport { adminApiService } from \"./APIService\";\r\n\r\nexport interface SaveGuideRequest {\r\n\tGuideId: string;\r\n\tGuideType: string;\r\n\tName: string;\r\n\tContent: string;\r\n\tOrganizationId: string;\r\n\tCreatedDate: string;\r\n\tUpdatedDate: string;\r\n\tCreatedBy: string;\r\n\tUpdatedBy: string;\r\n\tTargetUrl: string;\r\n\tFrequency: string;\r\n\tSegment: string;\r\n\tAccountId: string;\r\n\tGuideStatus: string;\r\n\tGuideStep: Array<any>;\r\n}\r\n\r\nexport const saveGuide = async (\r\n\tguideData: SaveGuideRequest,\r\n\tonSuccess: (response?: AxiosResponse<any, any>) => void,\r\n\tonError: (error: any) => void,\r\n\tsetLoading: (loading: boolean) => void\r\n) => {\r\n\ttry {\r\n\t\tsetLoading(true);\r\n\t\tconst response = await adminApiService.post(\"/Guide/Saveguide\", guideData);\r\n\r\n\t\tif (response && response.status === 200 && response.data.Success) {\r\n\t\t\tonSuccess(response);\r\n\r\n\t\t} else {\r\n\t\t\tconsole.error(\"Unexpected response:\", response);\r\n\t\t\tconst err = \"Unexpected response status\";\r\n\t\t\tconst errorMessage = response?.data?.ErrorMessage || err;\r\n\t\t\tonError(errorMessage);\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error(\"Error saving guide:\", error);\r\n\t\tonError(error);\r\n\t} finally {\r\n\t\tsetLoading(false);\r\n\t}\r\n};\r\n\r\nexport const updateGuide = async (\r\n\tguideData: SaveGuideRequest,\r\n\tonSuccess: (response?: AxiosResponse<any, any>) => void,\r\n\tonError: (error: any) => void,\r\n\tsetLoading: (loading: boolean) => void\r\n) => {\r\n\ttry {\r\n\t\tsetLoading(true);\r\n\t\tconst response = await adminApiService.post(\"/Guide/Updateguide\", guideData);\r\n\r\n\t\tif (response && response.status === 200 && response.data.Success) {\r\n\t\t\tonSuccess(response);\r\n\t\t} else {\r\n\t\t\tconsole.error(\"Unexpected response:\", response);\r\n\t\t\tconst err = \"Unexpected response status\";\r\n\t\t\tconst errorMessage = response?.data?.ErrorMessage || err;\r\n\t\t\tonError(errorMessage);\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error(\"Error saving guide:\", error);\r\n\t\tonError(error);\r\n\t} finally {\r\n\t\tsetLoading(false);\r\n\t}\r\n};\r\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,cAAc;AAoB9C,OAAO,MAAMC,SAAS,GAAG,MAAAA,CACxBC,SAA2B,EAC3BC,SAAuD,EACvDC,OAA6B,EAC7BC,UAAsC,KAClC;EACJ,IAAI;IACHA,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMC,QAAQ,GAAG,MAAMN,eAAe,CAACO,IAAI,CAAC,kBAAkB,EAAEL,SAAS,CAAC;IAE1E,IAAII,QAAQ,IAAIA,QAAQ,CAACE,MAAM,KAAK,GAAG,IAAIF,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;MACjEP,SAAS,CAACG,QAAQ,CAAC;IAEpB,CAAC,MAAM;MAAA,IAAAK,cAAA;MACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEP,QAAQ,CAAC;MAC/C,MAAMQ,GAAG,GAAG,4BAA4B;MACxC,MAAMC,YAAY,GAAG,CAAAT,QAAQ,aAARA,QAAQ,wBAAAK,cAAA,GAARL,QAAQ,CAAEG,IAAI,cAAAE,cAAA,uBAAdA,cAAA,CAAgBK,YAAY,KAAIF,GAAG;MACxDV,OAAO,CAACW,YAAY,CAAC;IACtB;EACD,CAAC,CAAC,OAAOF,KAAK,EAAE;IACfD,OAAO,CAACC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC3CT,OAAO,CAACS,KAAK,CAAC;EACf,CAAC,SAAS;IACTR,UAAU,CAAC,KAAK,CAAC;EAClB;AACD,CAAC;AAED,OAAO,MAAMY,WAAW,GAAG,MAAAA,CAC1Bf,SAA2B,EAC3BC,SAAuD,EACvDC,OAA6B,EAC7BC,UAAsC,KAClC;EACJ,IAAI;IACHA,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMC,QAAQ,GAAG,MAAMN,eAAe,CAACO,IAAI,CAAC,oBAAoB,EAAEL,SAAS,CAAC;IAE5E,IAAII,QAAQ,IAAIA,QAAQ,CAACE,MAAM,KAAK,GAAG,IAAIF,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;MACjEP,SAAS,CAACG,QAAQ,CAAC;IACpB,CAAC,MAAM;MAAA,IAAAY,eAAA;MACNN,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEP,QAAQ,CAAC;MAC/C,MAAMQ,GAAG,GAAG,4BAA4B;MACxC,MAAMC,YAAY,GAAG,CAAAT,QAAQ,aAARA,QAAQ,wBAAAY,eAAA,GAARZ,QAAQ,CAAEG,IAAI,cAAAS,eAAA,uBAAdA,eAAA,CAAgBF,YAAY,KAAIF,GAAG;MACxDV,OAAO,CAACW,YAAY,CAAC;IACtB;EACD,CAAC,CAAC,OAAOF,KAAK,EAAE;IACfD,OAAO,CAACC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC3CT,OAAO,CAACS,KAAK,CAAC;EACf,CAAC,SAAS;IACTR,UAAU,CAAC,KAAK,CAAC;EAClB;AACD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}