{"ast": null, "code": "// ScrapingService.ts - Handles targeted click-based scraping functionality\nimport{NewAgentTraining}from'../services/AIService';import i18n from'../multilinguial/i18n';// Get the translation function directly from i18next\nconst t=i18n.t.bind(i18n);let _currentAgentData=undefined;// Global state to track if scraping is active\nlet _isScrapingActive=false;let _scrapedData=[];let _lastScrapedTimestamp='';let _elementMap=new Map();// Map to track elements by XPath\nlet _clickListener=null;let _highlightedElements=new Set();// Track highlighted elements\nlet _overlayElements=new Set();// Track overlay elements for click blocking\n// Interface for element data\n// Interface for scraped page data\n/**\r\n * Check if scraping is currently active\r\n */export const isScrapingActive=()=>{return _isScrapingActive;};/**\r\n * Set the scraping active state\r\n */export const setScrapingActive=active=>{_isScrapingActive=active;};/**\r\n * Generate XPath for an element\r\n */const generateXPath=element=>{const tagName=element.tagName.toLowerCase();// ✅ ejs-multiselect logic\nif(tagName==='ejs-multiselect'){if(element.id){return`//*[@id=\"${element.id}\"]`;}const input=element.querySelector('input');if(input&&input.id){return`//*[@id=\"${input.id}\"]/ancestor::ejs-multiselect[1]`;}const dataId=element.getAttribute('data-id');if(dataId){return`//ejs-multiselect[@data-id=\"${dataId}\"]`;}const className=element.className;if(className){const uniqueClasses=className.split(' ').filter(cls=>cls&&!cls.startsWith('e-')&&cls.length>2);if(uniqueClasses.length>0){return`//ejs-multiselect[contains(@class, \"${uniqueClasses[0]}\")]`;}}return generatePositionalXPath(element);}// ✅ ejs-dropdownlist logic (updated)\nconst dropdownContainer=tagName==='ejs-dropdownlist'?element:element.closest('ejs-dropdownlist');if(dropdownContainer instanceof HTMLElement){const dropdownId=dropdownContainer.id;const input=dropdownContainer.querySelector('input');if(dropdownId&&input){return`//ejs-dropdownlist[@id=\"${dropdownId}\"]/div/input`;}return generatePositionalXPath(dropdownContainer);}if(element.id){return`//*[@id=\"${element.id}\"]`;}return generatePositionalXPath(element);};const generatePositionalXPath=element=>{const path=[];let current=element;while(current&&current.nodeType===Node.ELEMENT_NODE){let selector=current.nodeName.toLowerCase();if(current.id){selector+=`[@id=\"${current.id}\"]`;path.unshift(selector);break;}else{let sibling=current.previousElementSibling;let position=1;while(sibling){if(sibling.nodeName.toLowerCase()===selector){position++;}sibling=sibling.previousElementSibling;}if(position>1){selector+=`[${position}]`;}path.unshift(selector);}current=current.parentElement;}return'//'+path.join('/');};/**\r\n * Generate CSS selector for an element\r\n */const generateCssSelector=el=>{if(!el)return'';const path=[];while(el&&el.nodeType===Node.ELEMENT_NODE){let selector=el.tagName.toLowerCase();if(el.id){// Check if ID starts with a number or contains special characters\nconst id=el.id;if(/^[0-9]/.test(id)||/[^a-zA-Z0-9_-]/.test(id)){// Use attribute selector for IDs that start with numbers or have special chars\nselector+=`[id=\"${id}\"]`;}else{// Use standard ID selector for valid IDs\nselector+=`#${id}`;}path.unshift(selector);break;}else{// Safely handle className - it might be a string or SVGAnimatedString\nconst className=getElementClassName(el);if(className){selector+='.'+className.trim().replace(/\\s+/g,'.');}path.unshift(selector);el=el.parentElement;}}return path.join(' > ');};/**\r\n * Safely get className from an element (handles both HTML and SVG elements)\r\n */const getElementClassName=el=>{if(!el)return'';// For HTML elements, className is usually a string\nif(typeof el.className==='string'){return el.className;}// For SVG elements, className might be an SVGAnimatedString\nif(el.className&&typeof el.className==='object'&&'baseVal'in el.className){return el.className.baseVal||'';}// Fallback: try to get class attribute directly\nreturn el.getAttribute('class')||'';};/**\r\n * Generate a labelName for an element that can be used for fallback identification\r\n */const generateLabelName=element=>{// Try to find a meaningful label or name for the element\n// 1. Check for explicit label element\nif(element.id){var _label$textContent;const label=document.querySelector(`label[for=\"${element.id}\"]`);if(label&&(_label$textContent=label.textContent)!==null&&_label$textContent!==void 0&&_label$textContent.trim()){return label.textContent.trim();}}// 2. Special handling for dropdown elements - prioritize text content over generic aria-labels\nconst tagName=element.tagName.toLowerCase();const isDropdown=tagName==='ejs-dropdownlist'||element.closest('ejs-dropdownlist')||element.getAttribute('role')==='combobox'||element.className.includes('dropdown')||element.className.includes('select');if(isDropdown){var _element$textContent;// For dropdowns, prioritize visible text content over generic aria-labels\nconst textContent=(_element$textContent=element.textContent)===null||_element$textContent===void 0?void 0:_element$textContent.trim();if(textContent&&textContent.length>0){// Filter out generic dropdown text like \"dropdownlist\", \"combobox\", etc.\nconst genericTerms=['dropdownlist','combobox','select','dropdown'];const isGeneric=genericTerms.some(term=>textContent.toLowerCase()===term.toLowerCase());if(!isGeneric){return textContent.length>50?textContent.substring(0,50)+'...':textContent;}}}// 3. Check for aria-label (but not for dropdowns with generic values)\nconst ariaLabel=element.getAttribute('aria-label');if(ariaLabel!==null&&ariaLabel!==void 0&&ariaLabel.trim()){// Skip generic aria-labels for dropdowns\nif(isDropdown){const genericTerms=['dropdownlist','combobox','select','dropdown'];const isGeneric=genericTerms.some(term=>ariaLabel.toLowerCase()===term.toLowerCase());if(!isGeneric){return ariaLabel.trim();}}else{return ariaLabel.trim();}}// 4. Check for title attribute\nconst title=element.getAttribute('title');if(title!==null&&title!==void 0&&title.trim()){return title.trim();}// 5. Check for placeholder\nconst placeholder=element.getAttribute('placeholder');if(placeholder!==null&&placeholder!==void 0&&placeholder.trim()){return placeholder.trim();}// 6. Check for name attribute\nconst name=element.getAttribute('name');if(name!==null&&name!==void 0&&name.trim()){return name.trim();}// 7. Check for data-label or similar data attributes\nconst dataLabel=element.getAttribute('data-label')||element.getAttribute('data-name');if(dataLabel!==null&&dataLabel!==void 0&&dataLabel.trim()){return dataLabel.trim();}// 8. Check for text content (for non-dropdown elements or as fallback)\nif(!isDropdown){var _element$textContent2;const textContent=(_element$textContent2=element.textContent)===null||_element$textContent2===void 0?void 0:_element$textContent2.trim();if(textContent&&textContent.length>0){return textContent.length>50?textContent.substring(0,50)+'...':textContent;}}// 9. Check for value attribute (for inputs)\nconst value=element.getAttribute('value');if(value!==null&&value!==void 0&&value.trim()){return value.trim();}// 10. Fallback to element type and id/class\nconst id=element.id;const className=getElementClassName(element);if(id){return`${tagName}#${id}`;}else if(className){const firstClass=className.split(' ')[0];return`${tagName}.${firstClass}`;}// 11. Final fallback\nreturn tagName;};/**\r\n * Check if element should be ignored for highlighting\r\n */const shouldIgnoreHighlight=element=>{return element.classList.contains('AgentAdditionalContextPopup')||element.classList.contains(\"mdc-tooltip__surface\")||element.classList.contains(\"mdc-tooltip__surface-animation\")||element.classList.contains(\"mdc-tooltip\")||element.classList.contains(\"mdc-tooltip--shown\")||element.classList.contains(\"mdc-tooltip--showing\")||element.classList.contains(\"mdc-tooltip--hiding\")||element.getAttribute(\"role\")===\"tooltip\"||!!element.closest(\"#Tooltip-unique\")||!!element.closest(\"#my-react-drawer\")||!!element.closest(\"#tooltip-section-popover\")||!!element.closest(\"#btn-setting-toolbar\")||!!element.closest(\"#button-toolbar\")||!!element.closest(\"#color-picker\")||!!element.closest(\".qadpt-ext-banner\")||!!element.closest(\"#leftDrawer\")||!!element.closest(\"#image-popover\")||!!element.closest(\"#toggle-fit\")||!!element.closest(\"#color-popover\")||!!element.closest(\"#rte-popover\")||!!element.closest(\"#rte-alignment\")||!!element.closest(\"#rte-alignment-menu\")||!!element.closest(\"#rte-font\")||!!element.closest(\"#rte-bold\")||!!element.closest(\"#rte-italic\")||!!element.closest(\"#rte-underline\")||!!element.closest(\"#rte-strke-through\")||!!element.closest(\"#rte-alignment-menu-items\")||!!element.closest(\"#rte-more\")||!!element.closest(\"#rte-text-color\")||!!element.closest(\"#rte-text-color-popover\")||!!element.closest(\"#rte-text-highlight\")||!!element.closest(\"#rte-text-highlight-pop\")||!!element.closest(\"#rte-text-heading\")||!!element.closest(\"#rte-text-heading-menu-items\")||!!element.closest(\"#rte-text-format\")||!!element.closest(\"#rte-text-ul\")||!!element.closest(\"#rte-text-hyperlink\")||!!element.closest(\"#rte-video\")||!!element.closest(\"#rte-clear-formatting\")||!!element.closest(\"#rte-hyperlink-popover\")||!!element.closest(\"#rte-box\")||!!element.closest(element.id.startsWith(\"rt-editor\")?`#${element.id}`:\"nope\")||!!element.closest(\"#rte-placeholder\")||!!element.closest(\"#qadpt-designpopup\")||!!element.closest(\"#image-properties\")||!!element.closest(\"#rte-toolbar\")||!!element.closest(\"#tooltipdialog\")||!!element.closest(\"#rte-toolbar-paper\")||!!element.closest(\"#stop-scraping-button-container\")||!!element.closest(\"#rte-alignment-menu\")||!!element.closest(\"#rte-alignment-menu-items\")||!!element.closest(\"#quickadapt-scraping-instructions\")||// Ignore our own instruction banner\n!!element.closest(\"#AgentAdditionalContextPopup\")||// Ignore our own instruction banner\n!!element.closest(\"#textareaadditionalcontextpopup\");};/**\r\n * Check if element should be ignored for events\r\n */const shouldIgnoreEvents=element=>{return element.classList.contains(\"mdc-tooltip__surface\")||element.classList.contains(\"mdc-tooltip__surface-animation\")||element.classList.contains(\"mdc-tooltip\")||element.classList.contains(\"mdc-tooltip--shown\")||element.classList.contains(\"mdc-tooltip--showing\")||element.classList.contains(\"mdc-tooltip--hiding\")||element.getAttribute(\"role\")===\"tooltip\"||!!element.closest(\"#Tooltip-unique\")||!!element.closest(\"#tooltip-section-popover\")||!!element.closest(\"#btn-setting-toolbar\")||!!element.closest(\"#button-toolbar\")||!!element.closest(\"#color-picker\")||!!element.closest(\".qadpt-ext-banner\")||!!element.closest(\"#leftDrawer\")||!!element.closest(\"#rte-popover\")||!!element.closest(\"#stop-scraping-button-container\")||!!element.closest(element.id.startsWith(\"rt-editor\")?`#${element.id}`:\"nope\")||!!element.closest(\"#rte-box\")||!!element.closest(\"#rte-placeholder\")||!!element.closest(\"#rte-alignment-menu-items\")||!!element.closest(\"#qadpt-designpopup\")||!!element.closest(\"#quickadapt-scraping-instructions\")||// Ignore our own instruction banner\n!!element.closest(\"#AgentAdditionalContextPopup\")||// Ignore our own instruction banner\n!!element.closest(\"#textareaadditionalcontextpopup\");};/**\r\n * Add persistent red border to element WITHOUT blocking clicks\r\n */const addPersistentHighlightWithoutBlocking=element=>{if(shouldIgnoreHighlight(element))return;// Add persistent red border\nelement.style.outline='3px solid #ff0000 !important';element.style.outlineOffset='2px';element.setAttribute('data-quickadapt-highlighted','true');_highlightedElements.add(element);// No overlay creation - allow clicks to pass through\n};/**\r\n * Add persistent red border to element and create click-blocking overlay (legacy function)\r\n */const addPersistentHighlight=element=>{var _element$offsetParent;if(shouldIgnoreHighlight(element))return;// Add persistent red border\nelement.style.outline='3px solid #ff0000 !important';element.style.outlineOffset='2px';element.setAttribute('data-quickadapt-highlighted','true');_highlightedElements.add(element);// Create click-blocking overlay\nconst overlay=document.createElement('div');overlay.style.cssText=`\n    position: absolute;\n    top: ${element.offsetTop}px;\n    left: ${element.offsetLeft}px;\n    width: ${element.offsetWidth}px;\n    height: ${element.offsetHeight}px;\n    background: transparent;\n    z-index: 999999;\n    pointer-events: auto;\n    cursor: not-allowed;\n  `;overlay.setAttribute('data-quickadapt-overlay','true');const tooltipText=t('Element already scraped click blocked');overlay.title=tooltipText;// Position overlay relative to the element's parent\nconst rect=element.getBoundingClientRect();const parentRect=((_element$offsetParent=element.offsetParent)===null||_element$offsetParent===void 0?void 0:_element$offsetParent.getBoundingClientRect())||{top:0,left:0};overlay.style.top=`${rect.top-parentRect.top+window.scrollY}px`;overlay.style.left=`${rect.left-parentRect.left+window.scrollX}px`;// Add overlay to the element's parent or body\nconst parent=element.offsetParent||document.body;parent.appendChild(overlay);_overlayElements.add(overlay);// Block clicks on the overlay\noverlay.addEventListener('click',e=>{e.preventDefault();e.stopPropagation();},true);};/**\r\n * Remove all highlights and overlays\r\n */const removeAllHighlights=()=>{// Remove highlights\n_highlightedElements.forEach(element=>{if(element&&element.style){element.style.outline='';element.style.outlineOffset='';element.removeAttribute('data-quickadapt-highlighted');}});_highlightedElements.clear();// Remove overlays\n_overlayElements.forEach(overlay=>{if(overlay&&overlay.parentNode){overlay.parentNode.removeChild(overlay);}});_overlayElements.clear();};/**\r\n * Show brief visual feedback when an element is clicked and scraped\r\n */const showClickFeedback=element=>{try{// Create a temporary feedback indicator\nconst feedback=document.createElement('div');feedback.style.cssText=`\n      position: absolute;\n      background: #4CAF50;\n      color: white;\n      padding: 4px 8px;\n      border-radius: 4px;\n      font-size: 12px;\n      font-weight: bold;\n      z-index: 10001;\n      pointer-events: none;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.3);\n      opacity: 0;\n      transition: opacity 0.2s ease;\n    `;const scrapedText=`✓ ${t('Scraped')}`;feedback.textContent=scrapedText;// Position the feedback near the clicked element\nconst rect=element.getBoundingClientRect();feedback.style.left=`${rect.left+window.scrollX}px`;feedback.style.top=`${rect.top+window.scrollY-30}px`;document.body.appendChild(feedback);// Animate in\nsetTimeout(()=>{feedback.style.opacity='1';},10);// Remove after 2 seconds\nsetTimeout(()=>{feedback.style.opacity='0';setTimeout(()=>{if(feedback.parentNode){feedback.parentNode.removeChild(feedback);}},200);},2000);}catch(error){}};/**\r\n * Extract data from a single element (optimized for click-based scraping)\r\n */const extractElementData=element=>{var _element$textContent3;const rect=element.getBoundingClientRect();const xpath=generateXPath(element);const type=getElementType(element);const cssSelector=generateCssSelector(element);// Helper to get value from input/select/textarea or custom dropdowns\nfunction getFieldValue(el){// Standard HTML form elements\nif(el instanceof HTMLInputElement||el instanceof HTMLTextAreaElement){return el.value;}if(el instanceof HTMLSelectElement){var _el$options$el$select;return((_el$options$el$select=el.options[el.selectedIndex])===null||_el$options$el$select===void 0?void 0:_el$options$el$select.text)||el.value;}// Syncfusion/other custom dropdowns (e.g., ejs-dropdownlist)\n// Try to find an input or selected item inside\nconst input=el.querySelector('input');if(input&&input instanceof HTMLInputElement){return input.value;}// Try to find selected item span (common in custom dropdowns)\nconst selectedSpan=el.querySelector('.e-selected-item, .e-list-item.e-active, .e-ddl .e-input, .e-dropdownlist .e-input');if(selectedSpan){var _selectedSpan$textCon;return(_selectedSpan$textCon=selectedSpan.textContent)===null||_selectedSpan$textCon===void 0?void 0:_selectedSpan$textCon.trim();}// Try data-value attribute\nif(el.hasAttribute('data-value')){return el.getAttribute('data-value');}return undefined;}// Try to get value from the element itself\nlet value=getFieldValue(element);// If not found, try to get from a child input/select/textarea\nif(value===undefined){const childField=element.querySelector('input, select, textarea');if(childField){value=getFieldValue(childField);}}return{tagName:element.tagName,id:element.id||'',className:getElementClassName(element),text:((_element$textContent3=element.textContent)===null||_element$textContent3===void 0?void 0:_element$textContent3.trim())||element.id,//Added for agent training\nlabelName:generateLabelName(element),// Add labelName for fallback identification\nattributes:Array.from(element.attributes).reduce((acc,attr)=>{acc[attr.name]=attr.value;return acc;},{}),xpath,cssSelector,selector:xpath||cssSelector,// Primary selector with fallback\nrect:{top:rect.top,left:rect.left,width:rect.width,height:rect.height},children:[],// We don't need children for click-based scraping\nisVisible:rect.width>0&&rect.height>0,timestamp:new Date().toISOString(),url:window.location.href,// Add URL to each element\ntype,...(value!==undefined?{value}:{})};};const getElementType=element=>{const tagName=element.tagName.toLowerCase();// Check for ejs-radiobutton first\nif(tagName==='ejs-radiobutton'||element.closest('ejs-radiobutton')){return'radio';}if(tagName==='input'){const inputElement=element;const type=inputElement.type||'text';// Special handling for date inputs\nif(type==='text'&&(element.id.toLowerCase().includes('date')||element.className.toLowerCase().includes('date')||(element.getAttribute('placeholder')||'').toLowerCase().includes('date'))){// Check if it's a date range input or single date input\nconst value=inputElement.value||'';const placeholder=element.getAttribute('placeholder')||'';const id=element.id.toLowerCase();const className=element.className.toLowerCase();const name=(element.getAttribute('name')||'').toLowerCase();// Check if the value contains date range patterns (e.g., \"01/01/2023 - 01/31/2023\")\nconst dateRangePattern=/\\d{1,2}[\\/\\-\\.]\\d{1,2}[\\/\\-\\.]\\d{2,4}\\s*[-–—]\\s*\\d{1,2}[\\/\\-\\.]\\d{1,2}[\\/\\-\\.]\\d{2,4}/;const hasDateRangeValue=dateRangePattern.test(value);// Check for explicit date range terms (more specific)\nconst explicitRangeTerms=['daterange','date-range','date_range','period','duration','span','dates'];const hasExplicitRangeClass=explicitRangeTerms.some(term=>id.includes(term)||className.includes(term)||name.includes(term));// Check for range indicators in a more specific way\nconst rangeIndicators=['fromdate','todate','startdate','enddate','date_from','date_to','date-from','date-to','start_date','end_date','start-date','end-date'];const hasSpecificRangeIndicator=rangeIndicators.some(indicator=>id.includes(indicator)||className.includes(indicator)||name.includes(indicator));// Check for range indicators in placeholder (more specific)\nconst placeholderRangeTerms=['from','to','between','range','-','–','—'];const hasPlaceholderRangeIndicator=placeholderRangeTerms.some(term=>placeholder.toLowerCase().includes(term));// Return daterange only if we have strong indicators, otherwise dateinput\nif(hasDateRangeValue||hasExplicitRangeClass||hasSpecificRangeIndicator||hasPlaceholderRangeIndicator){return'daterange';}else{return'dateinput';}}// Inside a dropdown component\nif(element.closest('ejs-dropdownlist, ejs-combobox, ejs-multiselect, [role=\"combobox\"]')){return'dropdown';}return type;}// ejs-dropdownlist\nif(tagName==='ejs-dropdownlist'||element.closest('ejs-dropdownlist')){return'dropdown';}// select\nif(tagName==='select'){const selectElement=element;return selectElement.multiple?'multiselect':'dropdown';}// textarea\nif(tagName==='textarea'){return'textarea';}// contenteditable\nif(element.contentEditable==='true'){return'contenteditable';}// dropdown-like custom components\nif(element.getAttribute('role')==='combobox'||element.getAttribute('role')==='listbox'||element.className.includes('dropdown')||element.className.includes('select')){return'dropdown';}// ✅ Fallback for clickable, non-input UI elements\nreturn'click';};/**\r\n * Handle click events for element scraping\r\n */const handleElementClick=async(event,agentData)=>{try{// IMPORTANT: Don't prevent default or stop propagation\n// This allows the original click functionality to work normally\n// (navigation, form submission, button clicks, etc.)\nconst target=event.target;if(!target||!target.nodeType||target.nodeType!==Node.ELEMENT_NODE){return;}if(shouldIgnoreEvents(target)){return;}if(target.hasAttribute('data-quickadapt-highlighted')){return;}// Extract data from clicked element ONLY\nconst clickedElementData=extractElementData(target);// Store only the clicked element data (no parent element)\nconst elementsToStore=[clickedElementData];// Add to scraped data\nconsole.log(`📝 Attempting to scrape element: ${target.tagName} with XPath: ${clickedElementData.xpath}`);setScrapedData({elements:elementsToStore},true);// Save to local storage immediately after each element is scraped\nawait saveScrapedDataToStorage(agentData);console.log(`💾 Element data saved to local storage immediately`);// Add persistent red border WITHOUT blocking clicks (only to clicked element)\naddPersistentHighlightWithoutBlocking(target);// // Show brief success feedback\n// showClickFeedback(target);\n}catch(error){console.error('Error in handleElementClick:',error);}};export const setScrapedData=function(data){let append=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;const timestamp=new Date().toISOString();_lastScrapedTimestamp=timestamp;if(!append){// Clear existing data if not appending\n_scrapedData=[];_elementMap.clear();}// Process each element in the data\nif(data&&data.elements&&Array.isArray(data.elements)){data.elements.forEach(element=>{// Add timestamp to the element\nelement.timestamp=timestamp;// Use XPath as a unique identifier for the element\nif(element.xpath){// If element already exists in the map, don't add it again (prevent duplicates)\nif(_elementMap.has(element.xpath)){console.log(`⚠️ Skipping duplicate element with XPath: ${element.xpath}`);return;// Skip this element\n}else{// New element, add to map and data array\nconsole.log(`✅ Adding new element with XPath: ${element.xpath}`);_elementMap.set(element.xpath,element);_scrapedData.push(element);}}else{// No XPath, check for duplicates by other means (tagName + id + className)\nconst isDuplicate=_scrapedData.some(existing=>existing.tagName===element.tagName&&existing.id===element.id&&existing.className===element.className);if(!isDuplicate){_scrapedData.push(element);}else{}}});}};/**\r\n * Get the currently scraped data\r\n */export const getScrapedData=()=>{return _scrapedData;};/**\r\n * Get element by XPath\r\n */export const getElementByXPath=xpath=>{return _elementMap.get(xpath);};/**\r\n * Find DOM element using fallback mechanisms\r\n * Priority: xpath -> labelName -> cssSelector\r\n */// export const findElementWithFallback = (elementData: {\n//   xpath?: string;\n//   labelName?: string;\n//   cssSelector?: string;\n//   id?: string;\n// }): HTMLElement | null => {\n//   // Primary: Try xpath first\n//   if (elementData.xpath) {\n//     try {\n//       const result = document.evaluate(\n//         elementData.xpath,\n//         document,\n//         null,\n//         XPathResult.FIRST_ORDERED_NODE_TYPE,\n//         null\n//       );\n//       const element = result.singleNodeValue as HTMLElement;\n//       if (element) {\n//         console.log(`✅ Found element using xpath: ${elementData.xpath}`);\n//         return element;\n//       }\n//     } catch (error) {\n//       console.warn(`❌ XPath failed: ${elementData.xpath}`, error);\n//     }\n//   }\n//   // Fallback 1: Try to find by labelName\n//   if (elementData.labelName) {\n//     console.log(`🔄 Falling back to labelName: ${elementData.labelName}`);\n//     // Try different approaches to find by labelName\n//     const labelSelectors = [\n//       `[aria-label=\"${elementData.labelName}\"]`,\n//       `[title=\"${elementData.labelName}\"]`,\n//       `[placeholder=\"${elementData.labelName}\"]`,\n//       `[name=\"${elementData.labelName}\"]`,\n//       `[data-label=\"${elementData.labelName}\"]`,\n//       `[data-name=\"${elementData.labelName}\"]`\n//     ];\n//     for (const selector of labelSelectors) {\n//       try {\n//         const element = document.querySelector(selector) as HTMLElement;\n//         if (element) {\n//           console.log(`✅ Found element using labelName selector: ${selector}`);\n//           return element;\n//         }\n//       } catch (error) {\n//         console.warn(`❌ LabelName selector failed: ${selector}`, error);\n//       }\n//     }\n//     // Try to find by text content containing the labelName\n//     try {\n//       const xpath = `//*[contains(text(), \"${elementData.labelName}\")]`;\n//       const result = document.evaluate(\n//         xpath,\n//         document,\n//         null,\n//         XPathResult.FIRST_ORDERED_NODE_TYPE,\n//         null\n//       );\n//       const element = result.singleNodeValue as HTMLElement;\n//       if (element) {\n//         console.log(`✅ Found element using text content: ${elementData.labelName}`);\n//         return element;\n//       }\n//     } catch (error) {\n//       console.warn(`❌ Text content search failed for: ${elementData.labelName}`, error);\n//     }\n//     // Try to find label element and get its associated input\n//     try {\n//       const labelElement = Array.from(document.querySelectorAll('label')).find(\n//         label => label.textContent?.trim() === elementData.labelName\n//       );\n//       if (labelElement) {\n//         const forAttribute = labelElement.getAttribute('for');\n//         if (forAttribute) {\n//           const associatedElement = document.getElementById(forAttribute) as HTMLElement;\n//           if (associatedElement) {\n//             console.log(`✅ Found element using label association: ${elementData.labelName}`);\n//             return associatedElement;\n//           }\n//         }\n//       }\n//     } catch (error) {\n//       console.warn(`❌ Label association search failed for: ${elementData.labelName}`, error);\n//     }\n//   }\n//   // Fallback 2: Try cssSelector\n//   if (elementData.cssSelector) {\n//     console.log(`🔄 Falling back to cssSelector: ${elementData.cssSelector}`);\n//     try {\n//       const element = document.querySelector(elementData.cssSelector) as HTMLElement;\n//       if (element) {\n//         console.log(`✅ Found element using cssSelector: ${elementData.cssSelector}`);\n//         return element;\n//       }\n//     } catch (error) {\n//       console.warn(`❌ CSS selector failed: ${elementData.cssSelector}`, error);\n//       // Try to fix common CSS selector issues\n//       if (elementData.cssSelector.includes('#')) {\n//         try {\n//           // Extract ID and try attribute selector\n//           const idMatch = elementData.cssSelector.match(/#([^.\\s>+~]+)/);\n//           if (idMatch) {\n//             const id = idMatch[1];\n//             const tagMatch = elementData.cssSelector.match(/^([a-zA-Z]+)/);\n//             const tag = tagMatch ? tagMatch[1] : '';\n//             const attributeSelector = tag ? `${tag}[id=\"${id}\"]` : `[id=\"${id}\"]`;\n//             console.log(`🔄 Trying attribute selector fallback: ${attributeSelector}`);\n//             const element = document.querySelector(attributeSelector) as HTMLElement;\n//             if (element) {\n//               console.log(`✅ Found element using attribute selector: ${attributeSelector}`);\n//               return element;\n//             }\n//           }\n//         } catch (attributeError) {\n//           console.warn(`❌ Attribute selector fallback also failed`, attributeError);\n//         }\n//       }\n//     }\n//   }\n//   // Final fallback: Try by ID if available\n//   if (elementData.id) {\n//     console.log(`🔄 Final fallback to ID: ${elementData.id}`);\n//     try {\n//       const element = document.getElementById(elementData.id) as HTMLElement;\n//       if (element) {\n//         console.log(`✅ Found element using ID: ${elementData.id}`);\n//         return element;\n//       }\n//     } catch (error) {\n//       console.warn(`❌ ID selector failed: ${elementData.id}`, error);\n//     }\n//   }\n//   console.error(`❌ All fallback methods failed for element:`, elementData);\n//   return null;\n// };\n/**\r\n * Test function to demonstrate element finding with fallback mechanisms\r\n * This can be called from browser console for testing\r\n */// export const testElementFinding = (elementData: {\n//   xpath?: string;\n//   labelName?: string;\n//   cssSelector?: string;\n//   id?: string;\n// }): void => {\n//   console.log('🧪 Testing element finding with fallback mechanisms...');\n//   console.log('Input data:', elementData);\n//   const foundElement = findElementWithFallback(elementData);\n//   if (foundElement) {\n//     console.log('✅ Successfully found element:', foundElement);\n//     console.log('Element details:', {\n//       tagName: foundElement.tagName,\n//       id: foundElement.id,\n//       className: foundElement.className,\n//       textContent: foundElement.textContent?.substring(0, 100)\n//     });\n//   } else {\n//     console.log('❌ Could not find element with any fallback method');\n//   }\n// };\n/**\r\n * Get all xpath data from scraped elements\r\n */export const getXPathData=()=>{return _scrapedData.map(element=>({xpath:element.xpath,tagName:element.tagName,id:element.id,className:element.className,text:element.text,timestamp:element.timestamp,url:element.url}));};/**\r\n * Manually send current scraped data to backend API (can be called independently)\r\n */export const exportScrapedDataToFile=async accountId=>{try{if(_scrapedData.length===0){// Try to load from storage if no current data\nconst storedData=await loadScrapedDataFromStorage();if(!storedData||!storedData.scrapedData||storedData.scrapedData.length===0){// alert('No scraped data available to send to API. Please scrape some elements first.');\nreturn;}// Use stored data for API call\nawait saveScrapedDataToFile(accountId);}else{// Save current data to storage first, then send to API\nawait saveScrapedDataToStorage();await saveScrapedDataToFile(accountId);}}catch(error){// alert('Error sending scraped data to backend API. Check console for details.');\n}};/**\r\n * Get scraped data count\r\n */export const getScrapedDataCount=()=>{return _scrapedData.length;};/**\r\n * Check if there's existing scraped data in storage\r\n */export const hasScrapedDataInStorage=async()=>{try{const storedData=await loadScrapedDataFromStorage();return storedData&&storedData.scrapedData&&Array.isArray(storedData.scrapedData)&&storedData.scrapedData.length>0;}catch(error){return false;}};/**\r\n * Clear all scraped data (both in-memory and storage) - use when starting completely fresh\r\n */export const clearAllScrapedData=async()=>{console.log('🧹 Clearing all scraped data (memory + storage)');clearScrapedData();// Clear in-memory data\nawait clearScrapedDataFromStorage();// Clear storage data\nremoveAllHighlights();// Clear visual highlights\n};/**\r\n * Get current scraping status for debugging\r\n */export const getScrapingStatus=()=>{return{isActive:_isScrapingActive,elementCount:_scrapedData.length,elementMapSize:_elementMap.size,lastTimestamp:_lastScrapedTimestamp,highlightedElementsCount:_highlightedElements.size};};/**\r\n * Get the timestamp of the last scrape\r\n */export const getLastScrapedTimestamp=()=>{return _lastScrapedTimestamp;};/**\r\n * Clear scraped data\r\n */export const clearScrapedData=()=>{console.log(`🧹 Clearing scraped data - had ${_scrapedData.length} elements and ${_elementMap.size} in element map`);_scrapedData=[];_lastScrapedTimestamp='';_elementMap.clear();// Clear the element map to allow re-scraping of same elements\n};/**\r\n * Clear scraped data from Chrome storage (for debugging/reset purposes)\r\n */export const clearScrapedDataFromStorage=async()=>{try{localStorage.removeItem('quickadapt-scraped-data');}catch(error){console.error('Error clearing scraped data from storage:',error);}};/**\r\n * Save scraped data to Chrome storage\r\n */export const saveScrapedDataToStorage=async agentData=>{try{const storageData={scrapedData:_scrapedData,timestamp:_lastScrapedTimestamp,url:window.location.href,title:document.title,elementCount:_scrapedData.length,xpathData:_scrapedData.map(element=>({xpath:element.xpath,tagName:element.tagName,id:element.id,className:element.className,text:element.text,labelName:element.labelName,cssSelector:element.cssSelector,selector:element.selector,attributes:element.attributes,timestamp:element.timestamp,url:element.url})),// Add agent data if provided\n...(agentData&&{agentData:{AccountId:agentData.accountId,Description:agentData.agentDescription,Name:agentData.agentName,url:agentData.agentUrl}})};localStorage.setItem('quickadapt-scraped-data',JSON.stringify(storageData));storageData.xpathData.forEach((item,index)=>{console.log(`${index+1}. ${item.tagName} - ${item.xpath}`);});}catch(error){}};/**\r\n * Load scraped data from Chrome storage\r\n */export const loadScrapedDataFromStorage=async()=>{try{const data=localStorage.getItem('quickadapt-scraped-data');return data?JSON.parse(data):null;}catch(error){return null;}};/**\r\n * Send scraped data from Chrome storage to backend API\r\n */export const saveScrapedDataToFile=async accountId=>{try{const storedData=await loadScrapedDataFromStorage();if(!storedData){return;}const apiData={metadata:{url:storedData.url||window.location.href,title:storedData.title||document.title,timestamp:storedData.timestamp||new Date().toISOString(),elementCount:storedData.elementCount||0,exportedAt:new Date().toISOString()},elements:storedData.scrapedData||[],xpathData:storedData.xpathData||[]};// Send data to backend API\nawait uploadXPathsFile(apiData,accountId);}catch(error){}};/**\r\n * Upload XPath data to backend API using existing FileService\r\n */export const uploadXPathsFile=async(data,accountId)=>{try{// Convert JSON data to FormData as expected by the existing API\nconst formData=new FormData();// Create a JSON file blob\nconst jsonBlob=new Blob([JSON.stringify(data,null,2)],{type:'application/json'});// Generate filename with timestamp\nconst timestamp=new Date().toISOString().replace(/[:.]/g,'-');const pageTitle=(data.metadata.title||'scraped-data').replace(/[^a-zA-Z0-9]/g,'-');const filename=`quickadapt-xpath-data-${pageTitle}-${timestamp}.json`;// Add the file to FormData\nformData.append('aifiles',jsonBlob,filename);// ✅ Correct key name\n// Add metadata as form fields if needed\nformData.append('elementCount',data.metadata.elementCount.toString());formData.append('url',data.metadata.url);formData.append('timestamp',data.metadata.timestamp);// Import and use the existing uploadXpathsFile function\nconst{uploadXpathsFile}=await import('./FileService');if(!accountId){throw new Error('Account ID is required to upload XPath data');}const response=await uploadXpathsFile(accountId,formData);}catch(error){// Show error message to user\nconst errorMessage=error instanceof Error?error.message:'Unknown error occurred';throw error;// Re-throw to be caught by calling function\n}};/**\r\n * Start click-based scraping process\r\n */export const startAgentScraping=async(agentName,agentDescription,accountId,agentUrl)=>{if(_isScrapingActive)return;_isScrapingActive=true;console.log('🎯 Starting scraping session - checking storage consistency');// Check if Chrome storage has scraped data\nconst storedData=await loadScrapedDataFromStorage();if(!storedData||!storedData.scrapedData||!Array.isArray(storedData.scrapedData)||storedData.scrapedData.length===0){console.log('📊 No valid data in Chrome storage - clearing in-memory data');clearScrapedData();// Clear in-memory data if storage is empty\nawait saveScrapedDataToStorage({accountId,agentDescription,agentName,agentUrl});}else{console.log(`📊 Storage validation passed - ${storedData.scrapedData.length} elements in storage, ${_scrapedData.length} in memory`);}console.log(`📊 Current scraped elements count: ${_scrapedData.length}`);_currentAgentData={accountId,agentDescription,agentName,agentUrl};// Re-highlight existing scraped elements instead of clearing all highlights\n_scrapedData.forEach(element=>{if(element.xpath){try{const elementNode=document.evaluate(element.xpath,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue;if(elementNode){addPersistentHighlightWithoutBlocking(elementNode);}}catch(error){// Element might not exist anymore, that's okay\n}}});// Add click event listener to capture element clicks\nif(!_clickListener){_clickListener=event=>{// Call the async function without awaiting to avoid blocking the event handler\nhandleElementClick(event,_currentAgentData).catch(error=>{console.error('Error in click handler:',error);});};document.addEventListener('click',_clickListener,true);// Use capture phase\n}// Send message to content script to enable click-based scraping\nif(typeof chrome!=='undefined'&&chrome.runtime){try{chrome.runtime.sendMessage({action:'startClickScraping'});}catch(error){// Fallback: try to communicate through window events\nwindow.dispatchEvent(new CustomEvent('quickadapt-start-click-scraping'));}}// Show user instruction with translation support\nshowScrapingInstructions();};export const startScraping=()=>{if(_isScrapingActive)return;_isScrapingActive=true;clearScrapedData();// Add click event listener to capture element clicks\nif(!_clickListener){_clickListener=handleElementClick;document.addEventListener('click',_clickListener,true);// Use capture phase\n}// Send message to content script to enable click-based scraping\nif(typeof chrome!=='undefined'&&chrome.runtime){try{chrome.runtime.sendMessage({action:'startClickScraping'});}catch(error){// Fallback: try to communicate through window events\nwindow.dispatchEvent(new CustomEvent('quickadapt-start-click-scraping'));}}// Show user instruction\nshowScrapingInstructions();};/**\r\n * Stop click-based scraping process\r\n */export const stopScraping=async(isAgentTraining,accountId,agentName,agentDescription,agentUrl)=>{if(!_isScrapingActive)return;_isScrapingActive=false;// Remove click event listener\nif(_clickListener){document.removeEventListener('click',_clickListener,true);_clickListener=null;}// Process scraped data before clearing from storage\nif(_scrapedData.length>0){// Save to storage one final time to ensure we have the latest data, including agent data\nawait saveScrapedDataToStorage({accountId,agentDescription,agentName,agentUrl});// Get data from Chrome storage and save to file\n!isAgentTraining&&(await saveScrapedDataToFile(accountId));const filteredData=await getFilteredScrapedData();console.log(filteredData,\"filteredData\");const agent={AccountId:accountId,Description:agentDescription,Name:agentName,TrainingFields:filteredData,url:window.location.href};if(isAgentTraining&&agentName&&agentDescription){await NewAgentTraining(agent);}}await clearScrapedDataFromStorage();// Remove all highlights and overlays\nremoveAllHighlights();// Send message to background script to stop scraping\nif(typeof chrome!=='undefined'&&chrome.runtime){try{chrome.runtime.sendMessage({action:'stopClickScraping'});}catch(error){// Fallback: try to communicate through window events\nwindow.dispatchEvent(new CustomEvent('quickadapt-stop-click-scraping'));}}// Hide instructions\nhideScrapingInstructions();};/**\r\n * Cancel training process - stops scraping and clears data without processing\r\n */export const cancelTraining=async()=>{if(!_isScrapingActive)return;_isScrapingActive=false;// Remove click event listener\nif(_clickListener){document.removeEventListener('click',_clickListener,true);_clickListener=null;}// Clear scraped data from storage immediately without processing\nawait clearScrapedDataFromStorage();console.log('🧹 Cleared scraped data from storage after cancel training');// Clear in-memory data\nclearScrapedData();// Remove all highlights and overlays\nremoveAllHighlights();// Send message to background script to stop scraping\nif(typeof chrome!=='undefined'&&chrome.runtime){try{chrome.runtime.sendMessage({action:'stopClickScraping'});}catch(error){// Fallback: try to communicate through window events\nwindow.dispatchEvent(new CustomEvent('quickadapt-stop-click-scraping'));}}// Hide instructions\nhideScrapingInstructions();};export const getFilteredScrapedData=async()=>{const storedData=await loadScrapedDataFromStorage();if(!storedData||!Array.isArray(storedData.scrapedData))return[];return storedData.scrapedData.map(item=>{// Implement fallback logic for selector identification\nlet primarySelector=item.xpath||'';let fallbackSelector=item.cssSelector||'';// If no xpath, use labelName as fallback identifier\nif(!primarySelector&&item.labelName){primarySelector=`[aria-label=\"${item.labelName}\"]`;// Try aria-label first\n// Additional fallback selectors based on labelName\nif(!primarySelector){primarySelector=`[title=\"${item.labelName}\"]`;// Try title attribute\n}if(!primarySelector){primarySelector=`[placeholder=\"${item.labelName}\"]`;// Try placeholder\n}if(!primarySelector){primarySelector=`[name=\"${item.labelName}\"]`;// Try name attribute\n}}// If still no selector, use cssSelector as final fallback\nif(!primarySelector){primarySelector=fallbackSelector;}return{Name:item.text||'',xpath:item.xpath||'',labelName:item.labelName||'',selector:primarySelector,cssSelector:item.cssSelector||'',value:item.value||'',type:item.type||''};});};/**\r\n * Show scraping instructions to user\r\n */const showScrapingInstructions=()=>{// Remove existing instruction if any\nhideScrapingInstructions();const instructionDiv=document.createElement('div');instructionDiv.id='quickadapt-scraping-instructions';instructionDiv.style.cssText=`\n    position: fixed;\n    top: 20px;\n    right: 20px;\n    background: #4CAF50;\n    color: white;\n    padding: 15px 20px;\n    border-radius: 8px;\n    font-family: Arial, sans-serif;\n    font-size: 14px;\n    font-weight: bold;\n    z-index: 10000;\n    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\n    max-width: 320px;\n    text-align: center;\n  `;// Use translations with i18n instance directly\nconst mainTitle=`🎯 ${t('Click Scraping Active')}`;const clickElement=`• ${t('Click any element to scrape its XPath data')}`;const onlyClicked=`• ${t('Only the clicked element is scraped no duplicates')}`;const originalClick=`• ${t('Original click functionality still works')}`;const redBorders=`• ${t('Red borders show scraped elements')}`;const dataSaved=`• ${t('Data is saved to Chrome storage')}`;instructionDiv.innerHTML=`\n  \n    ${mainTitle}<br>\n    <small style=\"font-weight: normal; opacity: 0.9; display: block; margin-top: 8px;\">\n      ${clickElement}<br>\n      ${onlyClicked}<br>\n      ${originalClick}<br>\n      ${redBorders}<br>\n      ${dataSaved}\n    </small>\n  `;document.body.appendChild(instructionDiv);// Auto-hide after 8 seconds\nsetTimeout(()=>{if(instructionDiv.parentNode){instructionDiv.style.opacity='0.7';}},8000);};/**\r\n * Hide scraping instructions\r\n */const hideScrapingInstructions=()=>{const existingInstruction=document.getElementById('quickadapt-scraping-instructions');if(existingInstruction){existingInstruction.remove();}};/**\r\n * Initialize click-based scraping service\r\n * This should be called when the extension is loaded\r\n */export const initScrapingService=async()=>{_isScrapingActive=false;_scrapedData=[];_elementMap.clear();_lastScrapedTimestamp='';_clickListener=null;// Try to restore scraped data from storage (in case of page refresh)\ntry{const storedData=await loadScrapedDataFromStorage();if(storedData&&storedData.scrapedData&&Array.isArray(storedData.scrapedData)){_scrapedData=storedData.scrapedData;_lastScrapedTimestamp=storedData.timestamp||'';// Rebuild the element map for duplicate detection\n_scrapedData.forEach(element=>{if(element.xpath){_elementMap.set(element.xpath,element);}});console.log(`🔄 Restored ${_scrapedData.length} scraped elements from storage after page refresh`);// Re-highlight the previously scraped elements\n_scrapedData.forEach(element=>{if(element.xpath){try{const elementNode=document.evaluate(element.xpath,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue;if(elementNode){addPersistentHighlightWithoutBlocking(elementNode);}}catch(error){// Element might not exist anymore, that's okay\n}}});}}catch(error){console.log('No previous scraped data found or error loading from storage');}// Check if we're in a Chrome extension environment\nif(typeof chrome!=='undefined'&&chrome.runtime&&chrome.runtime.onMessage){// Listen for messages from background script\nchrome.runtime.onMessage.addListener((message,_sender,sendResponse)=>{if(message.action==='updateScrapingState'){_isScrapingActive=message.isActive;sendResponse({success:true});return true;}if(message.action==='getScrapingState'){sendResponse({isActive:_isScrapingActive,lastTimestamp:_lastScrapedTimestamp,elementCount:_scrapedData.length});return true;}if(message.action==='getScrapedData'){sendResponse({data:_scrapedData,timestamp:_lastScrapedTimestamp});return true;}if(message.action==='clearScrapedData'){clearScrapedData();sendResponse({success:true});return true;}});}else{}};// Initialize the service\ninitScrapingService().catch(error=>{console.error('Error initializing scraping service:',error);});/**\r\n * Utility to extract only essential data from a scraped element object\r\n * Includes fallback identification mechanisms\r\n */export function extractMinimalScrapeData(element){return{xpath:element.xpath||'',labelName:element.labelName||element.text||'',cssSelector:element.cssSelector||'',selector:element.selector||element.xpath||element.cssSelector||'',value:element.value,text:element.text||'',id:element.id||''};}", "map": {"version": 3, "names": ["NewAgentTraining", "i18n", "t", "bind", "_currentAgentData", "undefined", "_isScrapingActive", "_scrapedData", "_lastScrapedTimestamp", "_elementMap", "Map", "_clickListener", "_highlightedElements", "Set", "_overlayElements", "isScrapingActive", "setScrapingActive", "active", "generateXPath", "element", "tagName", "toLowerCase", "id", "input", "querySelector", "dataId", "getAttribute", "className", "uniqueClasses", "split", "filter", "cls", "startsWith", "length", "generatePositionalXPath", "dropdownContainer", "closest", "HTMLElement", "dropdownId", "path", "current", "nodeType", "Node", "ELEMENT_NODE", "selector", "nodeName", "unshift", "sibling", "previousElementSibling", "position", "parentElement", "join", "generateCssSelector", "el", "test", "getElementClassName", "trim", "replace", "baseVal", "generateLabelName", "_label$textContent", "label", "document", "textContent", "isDropdown", "includes", "_element$textContent", "genericTerms", "isGeneric", "some", "term", "substring", "aria<PERSON><PERSON><PERSON>", "title", "placeholder", "name", "dataLabel", "_element$textContent2", "value", "firstClass", "shouldIgnoreHighlight", "classList", "contains", "shouldIgnoreEvents", "addPersistentHighlightWithoutBlocking", "style", "outline", "outlineOffset", "setAttribute", "add", "addPersistentHighlight", "_element$offsetParent", "overlay", "createElement", "cssText", "offsetTop", "offsetLeft", "offsetWidth", "offsetHeight", "tooltipText", "rect", "getBoundingClientRect", "parentRect", "offsetParent", "top", "left", "window", "scrollY", "scrollX", "parent", "body", "append<PERSON><PERSON><PERSON>", "addEventListener", "e", "preventDefault", "stopPropagation", "removeAllHighlights", "for<PERSON>ach", "removeAttribute", "clear", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showClickFeedback", "feedback", "scrapedText", "setTimeout", "opacity", "error", "extractElementData", "_element$textContent3", "xpath", "type", "getElementType", "cssSelector", "getFieldValue", "HTMLInputElement", "HTMLTextAreaElement", "HTMLSelectElement", "_el$options$el$select", "options", "selectedIndex", "text", "selectedSpan", "_selectedSpan$textCon", "hasAttribute", "childField", "labelName", "attributes", "Array", "from", "reduce", "acc", "attr", "width", "height", "children", "isVisible", "timestamp", "Date", "toISOString", "url", "location", "href", "inputElement", "dateRangePattern", "hasDateRangeValue", "explicitRangeTerms", "hasExplicitRangeClass", "rangeIndicators", "hasSpecificRangeIndicator", "indicator", "placeholder<PERSON><PERSON><PERSON>Terms", "hasPlaceholderRangeIndicator", "selectElement", "multiple", "contentEditable", "handleElementClick", "event", "agentData", "target", "clickedElementData", "elementsToStore", "console", "log", "setScrapedData", "elements", "saveScrapedDataToStorage", "data", "append", "arguments", "isArray", "has", "set", "push", "isDuplicate", "existing", "getScrapedData", "getElementByXPath", "get", "getXPathData", "map", "exportScrapedDataToFile", "accountId", "storedData", "loadScrapedDataFromStorage", "scrapedData", "saveScrapedDataToFile", "getScrapedDataCount", "hasScrapedDataInStorage", "clearAllScrapedData", "clearScrapedData", "clearScrapedDataFromStorage", "getScrapingStatus", "isActive", "elementCount", "elementMapSize", "size", "lastTimestamp", "highlightedElementsCount", "getLastScrapedTimestamp", "localStorage", "removeItem", "storageData", "xpathData", "AccountId", "Description", "agentDescription", "Name", "<PERSON><PERSON><PERSON>", "agentUrl", "setItem", "JSON", "stringify", "item", "index", "getItem", "parse", "apiData", "metadata", "exportedAt", "uploadXPathsFile", "formData", "FormData", "jsonBlob", "Blob", "pageTitle", "filename", "toString", "uploadXpathsFile", "Error", "response", "errorMessage", "message", "startAgentScraping", "elementNode", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "singleNodeValue", "catch", "chrome", "runtime", "sendMessage", "action", "dispatchEvent", "CustomEvent", "showScrapingInstructions", "startScraping", "stopScraping", "isAgentTraining", "removeEventListener", "filteredData", "getFilteredScrapedData", "agent", "TrainingFields", "hideScrapingInstructions", "cancelTraining", "primarySelector", "fallbackSelector", "instructionDiv", "mainTitle", "clickElement", "onlyClicked", "originalClick", "redBorders", "dataSaved", "innerHTML", "existingInstruction", "getElementById", "remove", "initScrapingService", "onMessage", "addListener", "_sender", "sendResponse", "success", "extractMinimalScrapeData"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/services/ScrapingService.ts"], "sourcesContent": ["// ScrapingService.ts - <PERSON>les targeted click-based scraping functionality\r\n\r\nimport useDrawerStore, { DrawerState } from '../store/drawerStore';\r\nimport {NewAgentTraining} from '../services/AIService';\r\nimport { constants } from 'node:crypto';\r\n\r\nimport i18n from '../multilinguial/i18n';\r\n\r\n// Get the translation function directly from i18next\r\nconst t = i18n.t.bind(i18n);\r\n\r\n\r\nlet _currentAgentData: {\r\n  accountId?: string;\r\n  agentDescription?: string;\r\n  agentName?: string;\r\n  agentUrl?: string;\r\n} | undefined = undefined;\r\n\r\n// Global state to track if scraping is active\r\nlet _isScrapingActive = false;\r\nlet _scrapedData: any[] = [];\r\nlet _lastScrapedTimestamp: string = '';\r\nlet _elementMap: Map<string, ElementData> = new Map(); // Map to track elements by XPath\r\nlet _clickListener: ((event: MouseEvent) => void) | null = null;\r\nlet _highlightedElements: Set<HTMLElement> = new Set(); // Track highlighted elements\r\nlet _overlayElements: Set<HTMLElement> = new Set(); // Track overlay elements for click blocking\r\n// Interface for element data\r\nexport interface ElementData {\r\n  tagName: string;\r\n  id: string;\r\n  className: string;\r\n  text: string;\r\n  labelName: string; // Add labelName for fallback identification\r\n  attributes: Record<string, string>;\r\n  xpath: string;\r\n  cssSelector: string;\r\n  selector: string; // Primary selector (xpath with cssSelector fallback)\r\n  rect: {\r\n    top: number;\r\n    left: number;\r\n    width: number;\r\n    height: number;\r\n  };\r\n  children: ElementData[];\r\n  isVisible: boolean;\r\n  timestamp: string;\r\n  url: string; // Add URL field to track which page the element is from\r\n}\r\n\r\n// Interface for scraped page data\r\nexport interface ScrapedPageData {\r\n  url: string;\r\n  title: string;\r\n  timestamp: string;\r\n  elements: ElementData[];\r\n}\r\nexport type ScrapedElement = {\r\n  labelName: string;\r\n  id: string;\r\n  selector: string;\r\n  xpath: string;\r\n  cssSelector: string;\r\n  value: string;\r\n  type: string;\r\n};\r\n\r\n/**\r\n * Check if scraping is currently active\r\n */\r\nexport const isScrapingActive = (): boolean => {\r\n  return _isScrapingActive;\r\n};\r\n\r\n/**\r\n * Set the scraping active state\r\n */\r\nexport const setScrapingActive = (active: boolean): void => {\r\n  _isScrapingActive = active;\r\n};\r\n\r\n/**\r\n * Generate XPath for an element\r\n */\r\n\r\nconst generateXPath = (element: HTMLElement): string => {\r\n  const tagName = element.tagName.toLowerCase();\r\n\r\n  // ✅ ejs-multiselect logic\r\n  if (tagName === 'ejs-multiselect') {\r\n    if (element.id) {\r\n      return `//*[@id=\"${element.id}\"]`;\r\n    }\r\n\r\n    const input = element.querySelector('input');\r\n    if (input && input.id) {\r\n      return `//*[@id=\"${input.id}\"]/ancestor::ejs-multiselect[1]`;\r\n    }\r\n\r\n    const dataId = element.getAttribute('data-id');\r\n    if (dataId) {\r\n      return `//ejs-multiselect[@data-id=\"${dataId}\"]`;\r\n    }\r\n\r\n    const className = element.className;\r\n    if (className) {\r\n      const uniqueClasses = className\r\n        .split(' ')\r\n        .filter(cls => cls && !cls.startsWith('e-') && cls.length > 2);\r\n      if (uniqueClasses.length > 0) {\r\n        return `//ejs-multiselect[contains(@class, \"${uniqueClasses[0]}\")]`;\r\n      }\r\n    }\r\n\r\n    return generatePositionalXPath(element);\r\n  }\r\n\r\n  // ✅ ejs-dropdownlist logic (updated)\r\n  const dropdownContainer = tagName === 'ejs-dropdownlist'\r\n    ? element\r\n    : element.closest('ejs-dropdownlist');\r\n\r\n  if (dropdownContainer instanceof HTMLElement) {\r\n    const dropdownId = dropdownContainer.id;\r\n    const input = dropdownContainer.querySelector('input');\r\n\r\n    if (dropdownId && input) {\r\n      return `//ejs-dropdownlist[@id=\"${dropdownId}\"]/div/input`;\r\n    }\r\n\r\n    return generatePositionalXPath(dropdownContainer);\r\n  }\r\n\r\n  if (element.id) {\r\n    return `//*[@id=\"${element.id}\"]`;\r\n  }\r\n\r\n  return generatePositionalXPath(element);\r\n};\r\n\r\nconst generatePositionalXPath = (element: HTMLElement): string => {\r\n  const path: string[] = [];\r\n  let current: Element | null = element;\r\n\r\n  while (current && current.nodeType === Node.ELEMENT_NODE) {\r\n    let selector = current.nodeName.toLowerCase();\r\n\r\n    if (current.id) {\r\n      selector += `[@id=\"${current.id}\"]`;\r\n      path.unshift(selector);\r\n      break;\r\n    } else {\r\n      let sibling = current.previousElementSibling;\r\n      let position = 1;\r\n      while (sibling) {\r\n        if (sibling.nodeName.toLowerCase() === selector) {\r\n          position++;\r\n        }\r\n        sibling = sibling.previousElementSibling;\r\n      }\r\n\r\n      if (position > 1) {\r\n        selector += `[${position}]`;\r\n      }\r\n\r\n      path.unshift(selector);\r\n    }\r\n\r\n    current = current.parentElement;\r\n  }\r\n\r\n  return '//' + path.join('/');\r\n};\r\n\r\n\r\n\r\n\r\n\r\n/**\r\n * Generate CSS selector for an element\r\n */\r\n\r\n\r\nconst generateCssSelector = (el: HTMLElement): string => {\r\n  if (!el) return '';\r\n  const path = [];\r\n  while (el && el.nodeType === Node.ELEMENT_NODE) {\r\n    let selector = el.tagName.toLowerCase();\r\n    if (el.id) {\r\n      // Check if ID starts with a number or contains special characters\r\n      const id = el.id;\r\n      if (/^[0-9]/.test(id) || /[^a-zA-Z0-9_-]/.test(id)) {\r\n        // Use attribute selector for IDs that start with numbers or have special chars\r\n        selector += `[id=\"${id}\"]`;\r\n      } else {\r\n        // Use standard ID selector for valid IDs\r\n        selector += `#${id}`;\r\n      }\r\n      path.unshift(selector);\r\n      break;\r\n    } else {\r\n      // Safely handle className - it might be a string or SVGAnimatedString\r\n      const className = getElementClassName(el);\r\n      if (className) {\r\n        selector += '.' + className.trim().replace(/\\s+/g, '.');\r\n      }\r\n      path.unshift(selector);\r\n      el = el.parentElement!;\r\n    }\r\n  }\r\n  return path.join(' > ');\r\n};\r\n\r\n/**\r\n * Safely get className from an element (handles both HTML and SVG elements)\r\n */\r\nconst getElementClassName = (el: Element): string => {\r\n  if (!el) return '';\r\n\r\n  // For HTML elements, className is usually a string\r\n  if (typeof el.className === 'string') {\r\n    return el.className;\r\n  }\r\n\r\n  // For SVG elements, className might be an SVGAnimatedString\r\n  if (el.className && typeof el.className === 'object' && 'baseVal' in el.className) {\r\n    return (el.className as any).baseVal || '';\r\n  }\r\n\r\n  // Fallback: try to get class attribute directly\r\n  return el.getAttribute('class') || '';\r\n};\r\n\r\n/**\r\n * Generate a labelName for an element that can be used for fallback identification\r\n */\r\nconst generateLabelName = (element: HTMLElement): string => {\r\n  // Try to find a meaningful label or name for the element\r\n\r\n  // 1. Check for explicit label element\r\n  if (element.id) {\r\n    const label = document.querySelector(`label[for=\"${element.id}\"]`);\r\n    if (label && label.textContent?.trim()) {\r\n      return label.textContent.trim();\r\n    }\r\n  }\r\n\r\n  // 2. Special handling for dropdown elements - prioritize text content over generic aria-labels\r\n  const tagName = element.tagName.toLowerCase();\r\n  const isDropdown = tagName === 'ejs-dropdownlist' ||\r\n                     element.closest('ejs-dropdownlist') ||\r\n                     element.getAttribute('role') === 'combobox' ||\r\n                     element.className.includes('dropdown') ||\r\n                     element.className.includes('select');\r\n\r\n  if (isDropdown) {\r\n    // For dropdowns, prioritize visible text content over generic aria-labels\r\n    const textContent = element.textContent?.trim();\r\n    if (textContent && textContent.length > 0) {\r\n      // Filter out generic dropdown text like \"dropdownlist\", \"combobox\", etc.\r\n      const genericTerms = ['dropdownlist', 'combobox', 'select', 'dropdown'];\r\n      const isGeneric = genericTerms.some(term =>\r\n        textContent.toLowerCase() === term.toLowerCase()\r\n      );\r\n\r\n      if (!isGeneric) {\r\n        return textContent.length > 50 ? textContent.substring(0, 50) + '...' : textContent;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 3. Check for aria-label (but not for dropdowns with generic values)\r\n  const ariaLabel = element.getAttribute('aria-label');\r\n  if (ariaLabel?.trim()) {\r\n    // Skip generic aria-labels for dropdowns\r\n    if (isDropdown) {\r\n      const genericTerms = ['dropdownlist', 'combobox', 'select', 'dropdown'];\r\n      const isGeneric = genericTerms.some(term =>\r\n        ariaLabel.toLowerCase() === term.toLowerCase()\r\n      );\r\n      if (!isGeneric) {\r\n        return ariaLabel.trim();\r\n      }\r\n    } else {\r\n      return ariaLabel.trim();\r\n    }\r\n  }\r\n\r\n  // 4. Check for title attribute\r\n  const title = element.getAttribute('title');\r\n  if (title?.trim()) {\r\n    return title.trim();\r\n  }\r\n\r\n  // 5. Check for placeholder\r\n  const placeholder = element.getAttribute('placeholder');\r\n  if (placeholder?.trim()) {\r\n    return placeholder.trim();\r\n  }\r\n\r\n  // 6. Check for name attribute\r\n  const name = element.getAttribute('name');\r\n  if (name?.trim()) {\r\n    return name.trim();\r\n  }\r\n\r\n  // 7. Check for data-label or similar data attributes\r\n  const dataLabel = element.getAttribute('data-label') || element.getAttribute('data-name');\r\n  if (dataLabel?.trim()) {\r\n    return dataLabel.trim();\r\n  }\r\n\r\n  // 8. Check for text content (for non-dropdown elements or as fallback)\r\n  if (!isDropdown) {\r\n    const textContent = element.textContent?.trim();\r\n    if (textContent && textContent.length > 0) {\r\n      return textContent.length > 50 ? textContent.substring(0, 50) + '...' : textContent;\r\n    }\r\n  }\r\n\r\n  // 9. Check for value attribute (for inputs)\r\n  const value = element.getAttribute('value');\r\n  if (value?.trim()) {\r\n    return value.trim();\r\n  }\r\n\r\n  // 10. Fallback to element type and id/class\r\n  const id = element.id;\r\n  const className = getElementClassName(element);\r\n\r\n  if (id) {\r\n    return `${tagName}#${id}`;\r\n  } else if (className) {\r\n    const firstClass = className.split(' ')[0];\r\n    return `${tagName}.${firstClass}`;\r\n  }\r\n\r\n  // 11. Final fallback\r\n  return tagName;\r\n};\r\n\r\n/**\r\n * Check if element should be ignored for highlighting\r\n */\r\nconst shouldIgnoreHighlight = (element: HTMLElement): boolean => {\r\n  return (\r\n    element.classList.contains('AgentAdditionalContextPopup') ||\r\n    element.classList.contains(\"mdc-tooltip__surface\") ||\r\n    element.classList.contains(\"mdc-tooltip__surface-animation\") ||\r\n    element.classList.contains(\"mdc-tooltip\") ||\r\n    element.classList.contains(\"mdc-tooltip--shown\") ||\r\n    element.classList.contains(\"mdc-tooltip--showing\") ||\r\n    element.classList.contains(\"mdc-tooltip--hiding\") ||\r\n    element.getAttribute(\"role\") === \"tooltip\" ||\r\n    !!element.closest(\"#Tooltip-unique\") ||\r\n    !!element.closest(\"#my-react-drawer\") ||\r\n    !!element.closest(\"#tooltip-section-popover\") ||\r\n    !!element.closest(\"#btn-setting-toolbar\") ||\r\n    !!element.closest(\"#button-toolbar\") ||\r\n    !!element.closest(\"#color-picker\") ||\r\n    !!element.closest(\".qadpt-ext-banner\") ||\r\n    !!element.closest(\"#leftDrawer\") ||\r\n    !!element.closest(\"#image-popover\") ||\r\n    !!element.closest(\"#toggle-fit\") ||\r\n    !!element.closest(\"#color-popover\") ||\r\n    !!element.closest(\"#rte-popover\") ||\r\n    !!element.closest(\"#rte-alignment\") ||\r\n    !!element.closest(\"#rte-alignment-menu\") ||\r\n    !!element.closest(\"#rte-font\") ||\r\n    !!element.closest(\"#rte-bold\") ||\r\n    !!element.closest(\"#rte-italic\") ||\r\n    !!element.closest(\"#rte-underline\") ||\r\n    !!element.closest(\"#rte-strke-through\") ||\r\n    !!element.closest(\"#rte-alignment-menu-items\") ||\r\n    !!element.closest(\"#rte-more\") ||\r\n    !!element.closest(\"#rte-text-color\") ||\r\n    !!element.closest(\"#rte-text-color-popover\") ||\r\n    !!element.closest(\"#rte-text-highlight\") ||\r\n    !!element.closest(\"#rte-text-highlight-pop\") ||\r\n    !!element.closest(\"#rte-text-heading\") ||\r\n    !!element.closest(\"#rte-text-heading-menu-items\") ||\r\n    !!element.closest(\"#rte-text-format\") ||\r\n    !!element.closest(\"#rte-text-ul\") ||\r\n    !!element.closest(\"#rte-text-hyperlink\") ||\r\n    !!element.closest(\"#rte-video\") ||\r\n    !!element.closest(\"#rte-clear-formatting\") ||\r\n    !!element.closest(\"#rte-hyperlink-popover\") ||\r\n    !!element.closest(\"#rte-box\") ||\r\n    !!element.closest(element.id.startsWith(\"rt-editor\") ? `#${element.id}` : \"nope\") ||\r\n    !!element.closest(\"#rte-placeholder\") ||\r\n    !!element.closest(\"#qadpt-designpopup\") ||\r\n    !!element.closest(\"#image-properties\") ||\r\n    !!element.closest(\"#rte-toolbar\") ||\r\n    !!element.closest(\"#tooltipdialog\") ||\r\n    !!element.closest(\"#rte-toolbar-paper\") ||\r\n    !!element.closest(\"#stop-scraping-button-container\") ||\r\n    !!element.closest(\"#rte-alignment-menu\") ||\r\n    !!element.closest(\"#rte-alignment-menu-items\") ||\r\n    !!element.closest(\"#quickadapt-scraping-instructions\") || // Ignore our own instruction banner\r\n    !!element.closest(\"#AgentAdditionalContextPopup\") || // Ignore our own instruction banner\r\n    !!element.closest(\"#textareaadditionalcontextpopup\")\r\n\r\n  );\r\n};\r\n\r\n/**\r\n * Check if element should be ignored for events\r\n */\r\nconst shouldIgnoreEvents = (element: HTMLElement): boolean => {\r\n  return (\r\n    element.classList.contains(\"mdc-tooltip__surface\") ||\r\n    element.classList.contains(\"mdc-tooltip__surface-animation\") ||\r\n    element.classList.contains(\"mdc-tooltip\") ||\r\n    element.classList.contains(\"mdc-tooltip--shown\") ||\r\n    element.classList.contains(\"mdc-tooltip--showing\") ||\r\n    element.classList.contains(\"mdc-tooltip--hiding\") ||\r\n    element.getAttribute(\"role\") === \"tooltip\" ||\r\n    !!element.closest(\"#Tooltip-unique\") ||\r\n    !!element.closest(\"#tooltip-section-popover\") ||\r\n    !!element.closest(\"#btn-setting-toolbar\") ||\r\n    !!element.closest(\"#button-toolbar\") ||\r\n    !!element.closest(\"#color-picker\") ||\r\n    !!element.closest(\".qadpt-ext-banner\") ||\r\n    !!element.closest(\"#leftDrawer\") ||\r\n    !!element.closest(\"#rte-popover\") ||\r\n    !!element.closest(\"#stop-scraping-button-container\") ||\r\n    !!element.closest(element.id.startsWith(\"rt-editor\") ? `#${element.id}` : \"nope\") ||\r\n    !!element.closest(\"#rte-box\") ||\r\n    !!element.closest(\"#rte-placeholder\") ||\r\n    !!element.closest(\"#rte-alignment-menu-items\") ||\r\n    !!element.closest(\"#qadpt-designpopup\") ||\r\n    !!element.closest(\"#quickadapt-scraping-instructions\")||\r\n     // Ignore our own instruction banner\r\n     !!element.closest(\"#AgentAdditionalContextPopup\")|| // Ignore our own instruction banner\r\n    !!element.closest(\"#textareaadditionalcontextpopup\")\r\n  );\r\n};\r\n\r\n/**\r\n * Add persistent red border to element WITHOUT blocking clicks\r\n */\r\nconst addPersistentHighlightWithoutBlocking = (element: HTMLElement): void => {\r\n  if (shouldIgnoreHighlight(element)) return;\r\n\r\n  // Add persistent red border\r\n  element.style.outline = '3px solid #ff0000 !important';\r\n  element.style.outlineOffset = '2px';\r\n  element.setAttribute('data-quickadapt-highlighted', 'true');\r\n  _highlightedElements.add(element);\r\n\r\n  // No overlay creation - allow clicks to pass through\r\n};\r\n\r\n/**\r\n * Add persistent red border to element and create click-blocking overlay (legacy function)\r\n */\r\nconst addPersistentHighlight = (element: HTMLElement): void => {\r\n  if (shouldIgnoreHighlight(element)) return;\r\n\r\n  // Add persistent red border\r\n  element.style.outline = '3px solid #ff0000 !important';\r\n  element.style.outlineOffset = '2px';\r\n  element.setAttribute('data-quickadapt-highlighted', 'true');\r\n  _highlightedElements.add(element);\r\n\r\n  // Create click-blocking overlay\r\n  const overlay = document.createElement('div');\r\n  overlay.style.cssText = `\r\n    position: absolute;\r\n    top: ${element.offsetTop}px;\r\n    left: ${element.offsetLeft}px;\r\n    width: ${element.offsetWidth}px;\r\n    height: ${element.offsetHeight}px;\r\n    background: transparent;\r\n    z-index: 999999;\r\n    pointer-events: auto;\r\n    cursor: not-allowed;\r\n  `;\r\n  overlay.setAttribute('data-quickadapt-overlay', 'true');\r\n \r\n  \r\n  const tooltipText = t('Element already scraped click blocked');\r\n  overlay.title = tooltipText;\r\n\r\n  // Position overlay relative to the element's parent\r\n  const rect = element.getBoundingClientRect();\r\n  const parentRect = element.offsetParent?.getBoundingClientRect() || { top: 0, left: 0 };\r\n\r\n  overlay.style.top = `${rect.top - parentRect.top + window.scrollY}px`;\r\n  overlay.style.left = `${rect.left - parentRect.left + window.scrollX}px`;\r\n\r\n  // Add overlay to the element's parent or body\r\n  const parent = element.offsetParent || document.body;\r\n  parent.appendChild(overlay);\r\n  _overlayElements.add(overlay);\r\n\r\n  // Block clicks on the overlay\r\n  overlay.addEventListener('click', (e) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n  }, true);\r\n};\r\n\r\n/**\r\n * Remove all highlights and overlays\r\n */\r\nconst removeAllHighlights = (): void => {\r\n  // Remove highlights\r\n  _highlightedElements.forEach(element => {\r\n    if (element && element.style) {\r\n      element.style.outline = '';\r\n      element.style.outlineOffset = '';\r\n      element.removeAttribute('data-quickadapt-highlighted');\r\n    }\r\n  });\r\n  _highlightedElements.clear();\r\n\r\n  // Remove overlays\r\n  _overlayElements.forEach(overlay => {\r\n    if (overlay && overlay.parentNode) {\r\n      overlay.parentNode.removeChild(overlay);\r\n    }\r\n  });\r\n  _overlayElements.clear();\r\n};\r\n\r\n/**\r\n * Show brief visual feedback when an element is clicked and scraped\r\n */\r\nconst showClickFeedback = (element: HTMLElement): void => {\r\n  try {\r\n    // Create a temporary feedback indicator\r\n    const feedback = document.createElement('div');\r\n    feedback.style.cssText = `\r\n      position: absolute;\r\n      background: #4CAF50;\r\n      color: white;\r\n      padding: 4px 8px;\r\n      border-radius: 4px;\r\n      font-size: 12px;\r\n      font-weight: bold;\r\n      z-index: 10001;\r\n      pointer-events: none;\r\n      box-shadow: 0 2px 8px rgba(0,0,0,0.3);\r\n      opacity: 0;\r\n      transition: opacity 0.2s ease;\r\n    `;\r\n  \r\n    \r\n    const scrapedText = `✓ ${t('Scraped')}`;\r\n    feedback.textContent = scrapedText;\r\n\r\n    // Position the feedback near the clicked element\r\n    const rect = element.getBoundingClientRect();\r\n    feedback.style.left = `${rect.left + window.scrollX}px`;\r\n    feedback.style.top = `${rect.top + window.scrollY - 30}px`;\r\n\r\n    document.body.appendChild(feedback);\r\n\r\n    // Animate in\r\n    setTimeout(() => {\r\n      feedback.style.opacity = '1';\r\n    }, 10);\r\n\r\n    // Remove after 2 seconds\r\n    setTimeout(() => {\r\n      feedback.style.opacity = '0';\r\n      setTimeout(() => {\r\n        if (feedback.parentNode) {\r\n          feedback.parentNode.removeChild(feedback);\r\n        }\r\n      }, 200);\r\n    }, 2000);\r\n  } catch (error) {\r\n  }\r\n};\r\n\r\n/**\r\n * Extract data from a single element (optimized for click-based scraping)\r\n */\r\nconst extractElementData = (element: HTMLElement): ElementData & { value?: any ,type?:any } => {\r\n  const rect = element.getBoundingClientRect();\r\n  const xpath = generateXPath(element);\r\n  const type =   getElementType(element);\r\n  const cssSelector = generateCssSelector(element);\r\n\r\n  // Helper to get value from input/select/textarea or custom dropdowns\r\n  function getFieldValue(el: HTMLElement): any {\r\n    // Standard HTML form elements\r\n    if (el instanceof HTMLInputElement || el instanceof HTMLTextAreaElement) {\r\n      return el.value;\r\n    }\r\n    if (el instanceof HTMLSelectElement) {\r\n      return el.options[el.selectedIndex]?.text || el.value;\r\n    }\r\n    // Syncfusion/other custom dropdowns (e.g., ejs-dropdownlist)\r\n    // Try to find an input or selected item inside\r\n    const input = el.querySelector('input');\r\n    if (input && input instanceof HTMLInputElement) {\r\n      return input.value;\r\n    }\r\n    // Try to find selected item span (common in custom dropdowns)\r\n    const selectedSpan = el.querySelector('.e-selected-item, .e-list-item.e-active, .e-ddl .e-input, .e-dropdownlist .e-input');\r\n    if (selectedSpan) {\r\n      return selectedSpan.textContent?.trim();\r\n    }\r\n    // Try data-value attribute\r\n    if (el.hasAttribute('data-value')) {\r\n      return el.getAttribute('data-value');\r\n    }\r\n    return undefined;\r\n  }\r\n\r\n  // Try to get value from the element itself\r\n  let value = getFieldValue(element);\r\n  // If not found, try to get from a child input/select/textarea\r\n  if (value === undefined) {\r\n    const childField = element.querySelector('input, select, textarea');\r\n    if (childField) {\r\n      value = getFieldValue(childField as HTMLElement);\r\n    }\r\n  }\r\n\r\n  return {\r\n    tagName: element.tagName,\r\n    id: element.id || '',\r\n    className: getElementClassName(element),\r\n    text: element.textContent?.trim() || element.id,  //Added for agent training\r\n    labelName: generateLabelName(element), // Add labelName for fallback identification\r\n    attributes: Array.from(element.attributes).reduce((acc, attr) => {\r\n      acc[attr.name] = attr.value;\r\n      return acc;\r\n    }, {} as Record<string, string>),\r\n    xpath,\r\n    cssSelector,\r\n    selector: xpath || cssSelector, // Primary selector with fallback\r\n    rect: {\r\n      top: rect.top,\r\n      left: rect.left,\r\n      width: rect.width,\r\n      height: rect.height\r\n    },\r\n    children: [], // We don't need children for click-based scraping\r\n    isVisible: rect.width > 0 && rect.height > 0,\r\n    timestamp: new Date().toISOString(),\r\n    url: window.location.href, // Add URL to each element\r\n    type,\r\n    ...(value !== undefined ? { value } : {})\r\n  };\r\n};\r\n\r\n\r\nconst getElementType = (element: HTMLElement): any => {\r\n  const tagName = element.tagName.toLowerCase();\r\n\r\n  // Check for ejs-radiobutton first\r\n  if (tagName === 'ejs-radiobutton' || element.closest('ejs-radiobutton')) {\r\n    return 'radio';\r\n  }\r\n\r\n  if (tagName === 'input') {\r\n    const inputElement = element as HTMLInputElement;\r\n    const type = inputElement.type || 'text';\r\n\r\n    // Special handling for date inputs\r\n    if (\r\n      type === 'text' &&\r\n      (\r\n        element.id.toLowerCase().includes('date') ||\r\n        element.className.toLowerCase().includes('date') ||\r\n        (element.getAttribute('placeholder') || '').toLowerCase().includes('date')\r\n      )\r\n    ) {\r\n      // Check if it's a date range input or single date input\r\n      const value = inputElement.value || '';\r\n      const placeholder = element.getAttribute('placeholder') || '';\r\n      const id = element.id.toLowerCase();\r\n      const className = element.className.toLowerCase();\r\n      const name = (element.getAttribute('name') || '').toLowerCase();\r\n\r\n      // Check if the value contains date range patterns (e.g., \"01/01/2023 - 01/31/2023\")\r\n      const dateRangePattern = /\\d{1,2}[\\/\\-\\.]\\d{1,2}[\\/\\-\\.]\\d{2,4}\\s*[-–—]\\s*\\d{1,2}[\\/\\-\\.]\\d{1,2}[\\/\\-\\.]\\d{2,4}/;\r\n      const hasDateRangeValue = dateRangePattern.test(value);\r\n\r\n      // Check for explicit date range terms (more specific)\r\n      const explicitRangeTerms = [\r\n        'daterange', 'date-range', 'date_range',\r\n        'period', 'duration', 'span','dates'\r\n      ];\r\n      const hasExplicitRangeClass = explicitRangeTerms.some(term =>\r\n        id.includes(term) || className.includes(term) || name.includes(term)\r\n      );\r\n\r\n      // Check for range indicators in a more specific way\r\n      const rangeIndicators = [\r\n        'fromdate', 'todate', 'startdate', 'enddate',\r\n        'date_from', 'date_to', 'date-from', 'date-to',\r\n        'start_date', 'end_date', 'start-date', 'end-date'\r\n      ];\r\n\r\n      const hasSpecificRangeIndicator = rangeIndicators.some(indicator =>\r\n        id.includes(indicator) || className.includes(indicator) || name.includes(indicator)\r\n      );\r\n\r\n      // Check for range indicators in placeholder (more specific)\r\n      const placeholderRangeTerms = ['from', 'to', 'between', 'range', '-', '–', '—'];\r\n      const hasPlaceholderRangeIndicator = placeholderRangeTerms.some(term =>\r\n        placeholder.toLowerCase().includes(term)\r\n      );\r\n\r\n      // Return daterange only if we have strong indicators, otherwise dateinput\r\n      if (hasDateRangeValue || hasExplicitRangeClass || hasSpecificRangeIndicator || hasPlaceholderRangeIndicator) {\r\n        return 'daterange';\r\n      } else {\r\n        return 'dateinput';\r\n      }\r\n    }\r\n\r\n    // Inside a dropdown component\r\n    if (element.closest('ejs-dropdownlist, ejs-combobox, ejs-multiselect, [role=\"combobox\"]')) {\r\n      return 'dropdown';\r\n    }\r\n\r\n    return type;\r\n  }\r\n\r\n  // ejs-dropdownlist\r\n  if (tagName === 'ejs-dropdownlist' || element.closest('ejs-dropdownlist')) {\r\n    return 'dropdown';\r\n  }\r\n\r\n  // select\r\n  if (tagName === 'select') {\r\n    const selectElement = element as HTMLSelectElement;\r\n    return selectElement.multiple ? 'multiselect' : 'dropdown';\r\n  }\r\n\r\n  // textarea\r\n  if (tagName === 'textarea') {\r\n    return 'textarea';\r\n  }\r\n\r\n  // contenteditable\r\n  if (element.contentEditable === 'true') {\r\n    return 'contenteditable';\r\n  }\r\n\r\n  // dropdown-like custom components\r\n  if (\r\n    element.getAttribute('role') === 'combobox' ||\r\n    element.getAttribute('role') === 'listbox' ||\r\n    element.className.includes('dropdown') ||\r\n    element.className.includes('select')\r\n  ) {\r\n    return 'dropdown';\r\n  }\r\n\r\n  // ✅ Fallback for clickable, non-input UI elements\r\n  return 'click';\r\n};\r\n\r\n\r\n\r\n\r\n\r\n/**\r\n * Handle click events for element scraping\r\n */\r\n\r\nconst handleElementClick = async ( event: MouseEvent,agentData?: {\r\n  accountId?: string;\r\n  agentDescription?: string;\r\n  agentName?: string;\r\n  agentUrl?: string;\r\n}): Promise<void> => {\r\n  try {\r\n    // IMPORTANT: Don't prevent default or stop propagation\r\n    // This allows the original click functionality to work normally\r\n    // (navigation, form submission, button clicks, etc.)\r\n\r\n    const target = event.target as HTMLElement;\r\n    if (!target || !target.nodeType || target.nodeType !== Node.ELEMENT_NODE) {\r\n      return;\r\n    }\r\n\r\n    if (shouldIgnoreEvents(target)) {\r\n      return;\r\n    }\r\n\r\n    if (target.hasAttribute('data-quickadapt-highlighted')) {\r\n      return;\r\n    }\r\n\r\n\r\n    // Extract data from clicked element ONLY\r\n    const clickedElementData = extractElementData(target);\r\n\r\n    // Store only the clicked element data (no parent element)\r\n    const elementsToStore = [clickedElementData];\r\n\r\n    // Add to scraped data\r\n    console.log(`📝 Attempting to scrape element: ${target.tagName} with XPath: ${clickedElementData.xpath}`);\r\n    setScrapedData({ elements: elementsToStore }, true);\r\n\r\n    // Save to local storage immediately after each element is scraped\r\n\r\n    await saveScrapedDataToStorage(agentData);\r\n    console.log(`💾 Element data saved to local storage immediately`);\r\n\r\n    // Add persistent red border WITHOUT blocking clicks (only to clicked element)\r\n    addPersistentHighlightWithoutBlocking(target);\r\n\r\n\r\n    // // Show brief success feedback\r\n    // showClickFeedback(target);\r\n\r\n\r\n  } catch (error) {\r\n    console.error('Error in handleElementClick:', error);\r\n  }\r\n};\r\n\r\nexport const setScrapedData = (data: any, append: boolean = false): void => {\r\n  const timestamp = new Date().toISOString();\r\n  _lastScrapedTimestamp = timestamp;\r\n\r\n  if (!append) {\r\n    // Clear existing data if not appending\r\n    _scrapedData = [];\r\n    _elementMap.clear();\r\n  }\r\n\r\n  // Process each element in the data\r\n  if (data && data.elements && Array.isArray(data.elements)) {\r\n    data.elements.forEach((element: ElementData) => {\r\n      // Add timestamp to the element\r\n      element.timestamp = timestamp;\r\n\r\n      // Use XPath as a unique identifier for the element\r\n      if (element.xpath) {\r\n        // If element already exists in the map, don't add it again (prevent duplicates)\r\n        if (_elementMap.has(element.xpath)) {\r\n          console.log(`⚠️ Skipping duplicate element with XPath: ${element.xpath}`);\r\n          return; // Skip this element\r\n        } else {\r\n          // New element, add to map and data array\r\n          console.log(`✅ Adding new element with XPath: ${element.xpath}`);\r\n          _elementMap.set(element.xpath, element);\r\n          _scrapedData.push(element);\r\n        \r\n        }\r\n      } else {\r\n        // No XPath, check for duplicates by other means (tagName + id + className)\r\n        const isDuplicate = _scrapedData.some(existing =>\r\n          existing.tagName === element.tagName &&\r\n          existing.id === element.id &&\r\n          existing.className === element.className\r\n        );\r\n\r\n        if (!isDuplicate) {\r\n          _scrapedData.push(element);\r\n\r\n        } else {\r\n\r\n        }\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\n/**\r\n * Get the currently scraped data\r\n */\r\nexport const getScrapedData = (): any[] => {\r\n  return _scrapedData;\r\n};\r\n\r\n/**\r\n * Get element by XPath\r\n */\r\nexport const getElementByXPath = (xpath: string): ElementData | undefined => {\r\n  return _elementMap.get(xpath);\r\n};\r\n\r\n/**\r\n * Find DOM element using fallback mechanisms\r\n * Priority: xpath -> labelName -> cssSelector\r\n */\r\n// export const findElementWithFallback = (elementData: {\r\n//   xpath?: string;\r\n//   labelName?: string;\r\n//   cssSelector?: string;\r\n//   id?: string;\r\n// }): HTMLElement | null => {\r\n//   // Primary: Try xpath first\r\n//   if (elementData.xpath) {\r\n//     try {\r\n//       const result = document.evaluate(\r\n//         elementData.xpath,\r\n//         document,\r\n//         null,\r\n//         XPathResult.FIRST_ORDERED_NODE_TYPE,\r\n//         null\r\n//       );\r\n//       const element = result.singleNodeValue as HTMLElement;\r\n//       if (element) {\r\n//         console.log(`✅ Found element using xpath: ${elementData.xpath}`);\r\n//         return element;\r\n//       }\r\n//     } catch (error) {\r\n//       console.warn(`❌ XPath failed: ${elementData.xpath}`, error);\r\n//     }\r\n//   }\r\n\r\n//   // Fallback 1: Try to find by labelName\r\n//   if (elementData.labelName) {\r\n//     console.log(`🔄 Falling back to labelName: ${elementData.labelName}`);\r\n\r\n//     // Try different approaches to find by labelName\r\n//     const labelSelectors = [\r\n//       `[aria-label=\"${elementData.labelName}\"]`,\r\n//       `[title=\"${elementData.labelName}\"]`,\r\n//       `[placeholder=\"${elementData.labelName}\"]`,\r\n//       `[name=\"${elementData.labelName}\"]`,\r\n//       `[data-label=\"${elementData.labelName}\"]`,\r\n//       `[data-name=\"${elementData.labelName}\"]`\r\n//     ];\r\n\r\n//     for (const selector of labelSelectors) {\r\n//       try {\r\n//         const element = document.querySelector(selector) as HTMLElement;\r\n//         if (element) {\r\n//           console.log(`✅ Found element using labelName selector: ${selector}`);\r\n//           return element;\r\n//         }\r\n//       } catch (error) {\r\n//         console.warn(`❌ LabelName selector failed: ${selector}`, error);\r\n//       }\r\n//     }\r\n\r\n//     // Try to find by text content containing the labelName\r\n//     try {\r\n//       const xpath = `//*[contains(text(), \"${elementData.labelName}\")]`;\r\n//       const result = document.evaluate(\r\n//         xpath,\r\n//         document,\r\n//         null,\r\n//         XPathResult.FIRST_ORDERED_NODE_TYPE,\r\n//         null\r\n//       );\r\n//       const element = result.singleNodeValue as HTMLElement;\r\n//       if (element) {\r\n//         console.log(`✅ Found element using text content: ${elementData.labelName}`);\r\n//         return element;\r\n//       }\r\n//     } catch (error) {\r\n//       console.warn(`❌ Text content search failed for: ${elementData.labelName}`, error);\r\n//     }\r\n\r\n//     // Try to find label element and get its associated input\r\n//     try {\r\n//       const labelElement = Array.from(document.querySelectorAll('label')).find(\r\n//         label => label.textContent?.trim() === elementData.labelName\r\n//       );\r\n//       if (labelElement) {\r\n//         const forAttribute = labelElement.getAttribute('for');\r\n//         if (forAttribute) {\r\n//           const associatedElement = document.getElementById(forAttribute) as HTMLElement;\r\n//           if (associatedElement) {\r\n//             console.log(`✅ Found element using label association: ${elementData.labelName}`);\r\n//             return associatedElement;\r\n//           }\r\n//         }\r\n//       }\r\n//     } catch (error) {\r\n//       console.warn(`❌ Label association search failed for: ${elementData.labelName}`, error);\r\n//     }\r\n//   }\r\n\r\n//   // Fallback 2: Try cssSelector\r\n//   if (elementData.cssSelector) {\r\n//     console.log(`🔄 Falling back to cssSelector: ${elementData.cssSelector}`);\r\n//     try {\r\n//       const element = document.querySelector(elementData.cssSelector) as HTMLElement;\r\n//       if (element) {\r\n//         console.log(`✅ Found element using cssSelector: ${elementData.cssSelector}`);\r\n//         return element;\r\n//       }\r\n//     } catch (error) {\r\n//       console.warn(`❌ CSS selector failed: ${elementData.cssSelector}`, error);\r\n\r\n//       // Try to fix common CSS selector issues\r\n//       if (elementData.cssSelector.includes('#')) {\r\n//         try {\r\n//           // Extract ID and try attribute selector\r\n//           const idMatch = elementData.cssSelector.match(/#([^.\\s>+~]+)/);\r\n//           if (idMatch) {\r\n//             const id = idMatch[1];\r\n//             const tagMatch = elementData.cssSelector.match(/^([a-zA-Z]+)/);\r\n//             const tag = tagMatch ? tagMatch[1] : '';\r\n//             const attributeSelector = tag ? `${tag}[id=\"${id}\"]` : `[id=\"${id}\"]`;\r\n\r\n//             console.log(`🔄 Trying attribute selector fallback: ${attributeSelector}`);\r\n//             const element = document.querySelector(attributeSelector) as HTMLElement;\r\n//             if (element) {\r\n//               console.log(`✅ Found element using attribute selector: ${attributeSelector}`);\r\n//               return element;\r\n//             }\r\n//           }\r\n//         } catch (attributeError) {\r\n//           console.warn(`❌ Attribute selector fallback also failed`, attributeError);\r\n//         }\r\n//       }\r\n//     }\r\n//   }\r\n\r\n//   // Final fallback: Try by ID if available\r\n//   if (elementData.id) {\r\n//     console.log(`🔄 Final fallback to ID: ${elementData.id}`);\r\n//     try {\r\n//       const element = document.getElementById(elementData.id) as HTMLElement;\r\n//       if (element) {\r\n//         console.log(`✅ Found element using ID: ${elementData.id}`);\r\n//         return element;\r\n//       }\r\n//     } catch (error) {\r\n//       console.warn(`❌ ID selector failed: ${elementData.id}`, error);\r\n//     }\r\n//   }\r\n\r\n//   console.error(`❌ All fallback methods failed for element:`, elementData);\r\n//   return null;\r\n// };\r\n\r\n/**\r\n * Test function to demonstrate element finding with fallback mechanisms\r\n * This can be called from browser console for testing\r\n */\r\n// export const testElementFinding = (elementData: {\r\n//   xpath?: string;\r\n//   labelName?: string;\r\n//   cssSelector?: string;\r\n//   id?: string;\r\n// }): void => {\r\n//   console.log('🧪 Testing element finding with fallback mechanisms...');\r\n//   console.log('Input data:', elementData);\r\n\r\n//   const foundElement = findElementWithFallback(elementData);\r\n\r\n//   if (foundElement) {\r\n//     console.log('✅ Successfully found element:', foundElement);\r\n//     console.log('Element details:', {\r\n//       tagName: foundElement.tagName,\r\n//       id: foundElement.id,\r\n//       className: foundElement.className,\r\n//       textContent: foundElement.textContent?.substring(0, 100)\r\n//     });\r\n//   } else {\r\n//     console.log('❌ Could not find element with any fallback method');\r\n//   }\r\n// };\r\n\r\n\r\n\r\n/**\r\n * Get all xpath data from scraped elements\r\n */\r\nexport const getXPathData = (): Array<{xpath: string, tagName: string, id: string, className: string, text: string, timestamp: string, url: string}> => {\r\n  return _scrapedData.map(element => ({\r\n    xpath: element.xpath,\r\n    tagName: element.tagName,\r\n    id: element.id,\r\n    className: element.className,\r\n    text: element.text,\r\n    timestamp: element.timestamp,\r\n    url: element.url\r\n  }));\r\n};\r\n\r\n/**\r\n * Manually send current scraped data to backend API (can be called independently)\r\n */\r\nexport const exportScrapedDataToFile = async (accountId?: string): Promise<void> => {\r\n  try {\r\n    if (_scrapedData.length === 0) {\r\n      // Try to load from storage if no current data\r\n      const storedData = await loadScrapedDataFromStorage();\r\n      if (!storedData || !storedData.scrapedData || storedData.scrapedData.length === 0) {\r\n        // alert('No scraped data available to send to API. Please scrape some elements first.');\r\n        return;\r\n      }\r\n      // Use stored data for API call\r\n      await saveScrapedDataToFile(accountId);\r\n    } else {\r\n      // Save current data to storage first, then send to API\r\n      await saveScrapedDataToStorage();\r\n      await saveScrapedDataToFile(accountId);\r\n    }\r\n  } catch (error) {\r\n    // alert('Error sending scraped data to backend API. Check console for details.');\r\n  }\r\n};\r\n\r\n/**\r\n * Get scraped data count\r\n */\r\nexport const getScrapedDataCount = (): number => {\r\n  return _scrapedData.length;\r\n};\r\n\r\n/**\r\n * Check if there's existing scraped data in storage\r\n */\r\nexport const hasScrapedDataInStorage = async (): Promise<boolean> => {\r\n  try {\r\n    const storedData = await loadScrapedDataFromStorage();\r\n    return storedData && storedData.scrapedData && Array.isArray(storedData.scrapedData) && storedData.scrapedData.length > 0;\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Clear all scraped data (both in-memory and storage) - use when starting completely fresh\r\n */\r\nexport const clearAllScrapedData = async (): Promise<void> => {\r\n  console.log('🧹 Clearing all scraped data (memory + storage)');\r\n  clearScrapedData(); // Clear in-memory data\r\n  await clearScrapedDataFromStorage(); // Clear storage data\r\n  removeAllHighlights(); // Clear visual highlights\r\n};\r\n\r\n/**\r\n * Get current scraping status for debugging\r\n */\r\nexport const getScrapingStatus = (): {\r\n  isActive: boolean;\r\n  elementCount: number;\r\n  elementMapSize: number;\r\n  lastTimestamp: string;\r\n  highlightedElementsCount: number;\r\n} => {\r\n  return {\r\n    isActive: _isScrapingActive,\r\n    elementCount: _scrapedData.length,\r\n    elementMapSize: _elementMap.size,\r\n    lastTimestamp: _lastScrapedTimestamp,\r\n    highlightedElementsCount: _highlightedElements.size\r\n  };\r\n};\r\n\r\n/**\r\n * Get the timestamp of the last scrape\r\n */\r\nexport const getLastScrapedTimestamp = (): string => {\r\n  return _lastScrapedTimestamp;\r\n};\r\n\r\n/**\r\n * Clear scraped data\r\n */\r\nexport const clearScrapedData = (): void => {\r\n  console.log(`🧹 Clearing scraped data - had ${_scrapedData.length} elements and ${_elementMap.size} in element map`);\r\n  _scrapedData = [];\r\n  _lastScrapedTimestamp = '';\r\n  _elementMap.clear(); // Clear the element map to allow re-scraping of same elements\r\n};\r\n\r\n/**\r\n * Clear scraped data from Chrome storage (for debugging/reset purposes)\r\n */\r\nexport const clearScrapedDataFromStorage = async (): Promise<void> => {\r\n \r\n  try {\r\n          localStorage.removeItem('quickadapt-scraped-data');\r\n\r\n  } catch (error) {\r\n    console.error('Error clearing scraped data from storage:', error);\r\n  }\r\n};\r\n\r\n/**\r\n * Save scraped data to Chrome storage\r\n */\r\n\r\nexport const saveScrapedDataToStorage = async (agentData?: {\r\n  accountId?: string;\r\n  agentDescription?: string;\r\n  agentName?: string;\r\n  agentUrl?: string;\r\n}): Promise<void> => {\r\n  try {\r\n      const storageData = {\r\n        scrapedData: _scrapedData,\r\n        timestamp: _lastScrapedTimestamp,\r\n        url: window.location.href,\r\n        title: document.title,\r\n        elementCount: _scrapedData.length,\r\n        xpathData: _scrapedData.map(element => ({\r\n          xpath: element.xpath,\r\n          tagName: element.tagName,\r\n          id: element.id,\r\n          className: element.className,\r\n          text: element.text,\r\n          labelName: element.labelName,\r\n          cssSelector: element.cssSelector,\r\n          selector: element.selector,\r\n          attributes: element.attributes,\r\n          timestamp: element.timestamp,\r\n          url: element.url\r\n\r\n        })),\r\n        // Add agent data if provided\r\n        ...(agentData && {\r\n          agentData: {\r\n            AccountId: agentData.accountId,\r\n            Description: agentData.agentDescription,\r\n            Name: agentData.agentName,\r\n            url: agentData.agentUrl\r\n          }\r\n        })\r\n      };\r\n    localStorage.setItem('quickadapt-scraped-data', JSON.stringify(storageData));\r\n   storageData.xpathData.forEach((item, index) => {\r\n        console.log(`${index + 1}. ${item.tagName} - ${item.xpath}`);\r\n      });\r\n\r\n  } catch (error) {\r\n  }\r\n};\r\n\r\n/**\r\n * Load scraped data from Chrome storage\r\n */\r\nexport const loadScrapedDataFromStorage = async (): Promise<any> => {\r\n  try {\r\n    \r\n      const data = localStorage.getItem('quickadapt-scraped-data');\r\n      return data ? JSON.parse(data) : null;\r\n    \r\n  } catch (error) {\r\n    return null;\r\n  }\r\n};\r\n\r\n\r\n\r\n/**\r\n * Send scraped data from Chrome storage to backend API\r\n */\r\nexport const saveScrapedDataToFile = async (accountId?: string): Promise<void> => {\r\n  try {\r\n\r\n    const storedData = await loadScrapedDataFromStorage();\r\n\r\n    if (!storedData) {\r\n      return;\r\n    }\r\n\r\n    const apiData = {\r\n      metadata: {\r\n        url: storedData.url || window.location.href,\r\n        title: storedData.title || document.title,\r\n        timestamp: storedData.timestamp || new Date().toISOString(),\r\n        elementCount: storedData.elementCount || 0,\r\n        exportedAt: new Date().toISOString()\r\n      },\r\n      elements: storedData.scrapedData || [],\r\n      xpathData: storedData.xpathData || []\r\n    };\r\n\r\n\r\n    // Send data to backend API\r\n    await uploadXPathsFile(apiData, accountId);\r\n\r\n  } catch (error) {\r\n  }\r\n};\r\n\r\n/**\r\n * Upload XPath data to backend API using existing FileService\r\n */\r\nexport const uploadXPathsFile = async (data: any, accountId?: string): Promise<void> => {\r\n  try {\r\n\r\n    // Convert JSON data to FormData as expected by the existing API\r\n    const formData = new FormData();\r\n\r\n    // Create a JSON file blob\r\n    const jsonBlob = new Blob([JSON.stringify(data, null, 2)], {\r\n      type: 'application/json'\r\n    });\r\n\r\n    // Generate filename with timestamp\r\n    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\r\n    const pageTitle = (data.metadata.title || 'scraped-data').replace(/[^a-zA-Z0-9]/g, '-');\r\n    const filename = `quickadapt-xpath-data-${pageTitle}-${timestamp}.json`;\r\n\r\n    // Add the file to FormData\r\n    formData.append('aifiles', jsonBlob, filename); // ✅ Correct key name\r\n\r\n    // Add metadata as form fields if needed\r\n    formData.append('elementCount', data.metadata.elementCount.toString());\r\n    formData.append('url', data.metadata.url);\r\n    formData.append('timestamp', data.metadata.timestamp);\r\n\r\n   \r\n\r\n    // Import and use the existing uploadXpathsFile function\r\n    const { uploadXpathsFile } = await import('./FileService');\r\n\r\n    if (!accountId) {\r\n      throw new Error('Account ID is required to upload XPath data');\r\n    }\r\n\r\n    const response = await uploadXpathsFile(accountId, formData);\r\n\r\n  } catch (error) {\r\n\r\n    // Show error message to user\r\n    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\r\n\r\n    throw error; // Re-throw to be caught by calling function\r\n  }\r\n};\r\n\r\n/**\r\n * Start click-based scraping process\r\n */\r\n\r\nexport const startAgentScraping = async (agentName: string, agentDescription: string, accountId: string, agentUrl: string): Promise<void> => {\r\n  if (_isScrapingActive) return;\r\n\r\n  _isScrapingActive = true;\r\n  console.log('🎯 Starting scraping session - checking storage consistency');\r\n\r\n  // Check if Chrome storage has scraped data\r\n  const storedData = await loadScrapedDataFromStorage();\r\n  if (!storedData || !storedData.scrapedData || !Array.isArray(storedData.scrapedData) || storedData.scrapedData.length === 0) {\r\n    console.log('📊 No valid data in Chrome storage - clearing in-memory data');\r\n    clearScrapedData(); // Clear in-memory data if storage is empty\r\n    await saveScrapedDataToStorage({\r\n      accountId,\r\n      agentDescription,\r\n      agentName,\r\n      agentUrl\r\n    });\r\n  } else {\r\n    \r\n    console.log(`📊 Storage validation passed - ${storedData.scrapedData.length} elements in storage, ${_scrapedData.length} in memory`);\r\n  }\r\n\r\n  console.log(`📊 Current scraped elements count: ${_scrapedData.length}`);\r\n _currentAgentData = {\r\n    accountId,\r\n    agentDescription,\r\n    agentName,\r\n    agentUrl\r\n  };\r\n  // Re-highlight existing scraped elements instead of clearing all highlights\r\n  _scrapedData.forEach(element => {\r\n    if (element.xpath) {\r\n      try {\r\n        const elementNode = document.evaluate(\r\n          element.xpath,\r\n          document,\r\n          null,\r\n          XPathResult.FIRST_ORDERED_NODE_TYPE,\r\n          null\r\n        ).singleNodeValue as HTMLElement;\r\n\r\n        if (elementNode) {\r\n          addPersistentHighlightWithoutBlocking(elementNode);\r\n        }\r\n      } catch (error) {\r\n        // Element might not exist anymore, that's okay\r\n      }\r\n    }\r\n  });\r\n\r\n  // Add click event listener to capture element clicks\r\n  if (!_clickListener) {\r\n    _clickListener = (event: MouseEvent) => {\r\n      // Call the async function without awaiting to avoid blocking the event handler\r\n\r\n      handleElementClick(event,_currentAgentData).catch(error => {\r\n        console.error('Error in click handler:', error);\r\n      });\r\n    };\r\n    document.addEventListener('click', _clickListener, true); // Use capture phase\r\n  }\r\n\r\n  // Send message to content script to enable click-based scraping\r\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\r\n    try {\r\n      chrome.runtime.sendMessage({\r\n        action: 'startClickScraping'\r\n      });\r\n    } catch (error) {\r\n      // Fallback: try to communicate through window events\r\n      window.dispatchEvent(new CustomEvent('quickadapt-start-click-scraping'));\r\n    }\r\n  }\r\n\r\n \r\n  // Show user instruction with translation support\r\n  showScrapingInstructions();\r\n};\r\n\r\n \r\nexport const startScraping = (): void => {\r\n  if (_isScrapingActive) return;\r\n\r\n  _isScrapingActive = true;\r\n  clearScrapedData();\r\n\r\n\r\n  // Add click event listener to capture element clicks\r\n  if (!_clickListener) {\r\n    _clickListener = handleElementClick;\r\n    document.addEventListener('click', _clickListener, true); // Use capture phase\r\n  }\r\n\r\n  // Send message to content script to enable click-based scraping\r\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\r\n    try {\r\n      chrome.runtime.sendMessage({\r\n        action: 'startClickScraping'\r\n      });\r\n    } catch (error) {\r\n      // Fallback: try to communicate through window events\r\n      window.dispatchEvent(new CustomEvent('quickadapt-start-click-scraping'));\r\n    }\r\n  }\r\n\r\n  // Show user instruction\r\n  showScrapingInstructions();\r\n};\r\n\r\n\r\n \r\n\r\n\r\n\r\n/**\r\n * Stop click-based scraping process\r\n */\r\nexport const stopScraping = async (\r\n  isAgentTraining: boolean,\r\n  accountId: string,\r\n  agentName?: string,\r\n  agentDescription?: string,\r\n  agentUrl?: string,\r\n\r\n): Promise<void> => {\r\n  if (!_isScrapingActive) return;\r\n  _isScrapingActive = false;\r\n\r\n\r\n  // Remove click event listener\r\n  if (_clickListener) {\r\n    document.removeEventListener('click', _clickListener, true);\r\n    _clickListener = null;\r\n  }\r\n\r\n  // Process scraped data before clearing from storage\r\n  if (_scrapedData.length > 0) {\r\n\r\n\r\n    // Save to storage one final time to ensure we have the latest data, including agent data\r\n     await saveScrapedDataToStorage({\r\n      accountId,\r\n      agentDescription,\r\n       agentName,\r\n      agentUrl\r\n    });\r\n\r\n\r\n\r\n    // Get data from Chrome storage and save to file\r\n    !isAgentTraining && await saveScrapedDataToFile(accountId);\r\n   const filteredData = await getFilteredScrapedData();\r\n   console.log(filteredData,\"filteredData\");\r\n\r\n    const agent = {\r\n      AccountId:accountId,\r\n      Description:agentDescription,\r\n      Name:agentName,\r\n      TrainingFields:filteredData,\r\n      url: window.location.href\r\n    };\r\n    if (isAgentTraining && agentName && agentDescription) {\r\n      await NewAgentTraining(agent);\r\n    }\r\n  }\r\n\r\n     await clearScrapedDataFromStorage();\r\n\r\n\r\n  // Remove all highlights and overlays\r\n  removeAllHighlights();\r\n\r\n  // Send message to background script to stop scraping\r\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\r\n    try {\r\n      chrome.runtime.sendMessage({\r\n        action: 'stopClickScraping'\r\n      });\r\n    } catch (error) {\r\n      // Fallback: try to communicate through window events\r\n      window.dispatchEvent(new CustomEvent('quickadapt-stop-click-scraping'));\r\n    }\r\n  }\r\n\r\n  // Hide instructions\r\n  hideScrapingInstructions();\r\n};\r\n\r\n/**\r\n * Cancel training process - stops scraping and clears data without processing\r\n */\r\nexport const cancelTraining = async (): Promise<void> => {\r\n  if (!_isScrapingActive) return;\r\n  _isScrapingActive = false;\r\n\r\n  // Remove click event listener\r\n  if (_clickListener) {\r\n    document.removeEventListener('click', _clickListener, true);\r\n    _clickListener = null;\r\n  }\r\n\r\n  // Clear scraped data from storage immediately without processing\r\n  await clearScrapedDataFromStorage();\r\n  console.log('🧹 Cleared scraped data from storage after cancel training');\r\n\r\n  // Clear in-memory data\r\n  clearScrapedData();\r\n\r\n  // Remove all highlights and overlays\r\n  removeAllHighlights();\r\n\r\n  // Send message to background script to stop scraping\r\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\r\n    try {\r\n      chrome.runtime.sendMessage({\r\n        action: 'stopClickScraping'\r\n      });\r\n    } catch (error) {\r\n      // Fallback: try to communicate through window events\r\n      window.dispatchEvent(new CustomEvent('quickadapt-stop-click-scraping'));\r\n    }\r\n  }\r\n\r\n  // Hide instructions\r\n  hideScrapingInstructions();\r\n};\r\n\r\nexport const getFilteredScrapedData = async (): Promise<ScrapedElement[]> => {\r\n  const storedData = await loadScrapedDataFromStorage();\r\n  if (!storedData || !Array.isArray(storedData.scrapedData)) return [];\r\n\r\n  return storedData.scrapedData.map((item: any) => {\r\n    // Implement fallback logic for selector identification\r\n    let primarySelector = item.xpath || '';\r\n    let fallbackSelector = item.cssSelector || '';\r\n\r\n    // If no xpath, use labelName as fallback identifier\r\n    if (!primarySelector && item.labelName) {\r\n      primarySelector = `[aria-label=\"${item.labelName}\"]`; // Try aria-label first\r\n      // Additional fallback selectors based on labelName\r\n      if (!primarySelector) {\r\n        primarySelector = `[title=\"${item.labelName}\"]`; // Try title attribute\r\n      }\r\n      if (!primarySelector) {\r\n        primarySelector = `[placeholder=\"${item.labelName}\"]`; // Try placeholder\r\n      }\r\n      if (!primarySelector) {\r\n        primarySelector = `[name=\"${item.labelName}\"]`; // Try name attribute\r\n      }\r\n    }\r\n\r\n    // If still no selector, use cssSelector as final fallback\r\n    if (!primarySelector) {\r\n      primarySelector = fallbackSelector;\r\n    }\r\n\r\n    return {\r\n      Name:item.text || '',\r\n      xpath: item.xpath || '',\r\n      labelName: item.labelName  || '',\r\n      selector: primarySelector,\r\n      cssSelector: item.cssSelector || '',\r\n      value: item.value || '',\r\n      type: item.type || '',\r\n    };\r\n  });\r\n};\r\n\r\n/**\r\n * Show scraping instructions to user\r\n */\r\nconst showScrapingInstructions = (): void => {\r\n  // Remove existing instruction if any\r\n  hideScrapingInstructions();\r\n\r\n  const instructionDiv = document.createElement('div');\r\n  instructionDiv.id = 'quickadapt-scraping-instructions';\r\n  instructionDiv.style.cssText = `\r\n    position: fixed;\r\n    top: 20px;\r\n    right: 20px;\r\n    background: #4CAF50;\r\n    color: white;\r\n    padding: 15px 20px;\r\n    border-radius: 8px;\r\n    font-family: Arial, sans-serif;\r\n    font-size: 14px;\r\n    font-weight: bold;\r\n    z-index: 10000;\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\r\n    max-width: 320px;\r\n    text-align: center;\r\n  `;\r\n  \r\n  // Use translations with i18n instance directly\r\n  const mainTitle = `🎯 ${t('Click Scraping Active')}`;\r\n  const clickElement = `• ${t('Click any element to scrape its XPath data')}`;\r\n  const onlyClicked = `• ${t('Only the clicked element is scraped no duplicates')}`;\r\n  const originalClick = `• ${t('Original click functionality still works')}`;\r\n  const redBorders = `• ${t('Red borders show scraped elements')}`;\r\n  const dataSaved = `• ${t('Data is saved to Chrome storage')}`;\r\n  \r\n  instructionDiv.innerHTML = `\r\n  \r\n    ${mainTitle}<br>\r\n    <small style=\"font-weight: normal; opacity: 0.9; display: block; margin-top: 8px;\">\r\n      ${clickElement}<br>\r\n      ${onlyClicked}<br>\r\n      ${originalClick}<br>\r\n      ${redBorders}<br>\r\n      ${dataSaved}\r\n    </small>\r\n  `;\r\n\r\n  document.body.appendChild(instructionDiv);\r\n\r\n  // Auto-hide after 8 seconds\r\n  setTimeout(() => {\r\n    if (instructionDiv.parentNode) {\r\n      instructionDiv.style.opacity = '0.7';\r\n    }\r\n  }, 8000);\r\n};\r\n\r\n/**\r\n * Hide scraping instructions\r\n */\r\nconst hideScrapingInstructions = (): void => {\r\n  const existingInstruction = document.getElementById('quickadapt-scraping-instructions');\r\n  if (existingInstruction) {\r\n    existingInstruction.remove();\r\n  }\r\n};\r\n\r\n\r\n/**\r\n * Initialize click-based scraping service\r\n * This should be called when the extension is loaded\r\n */\r\nexport const initScrapingService = async (): Promise<void> => {\r\n  _isScrapingActive = false;\r\n  _scrapedData = [];\r\n  _elementMap.clear();\r\n  _lastScrapedTimestamp = '';\r\n  _clickListener = null;\r\n\r\n  // Try to restore scraped data from storage (in case of page refresh)\r\n  try {\r\n    const storedData = await loadScrapedDataFromStorage();\r\n    if (storedData && storedData.scrapedData && Array.isArray(storedData.scrapedData)) {\r\n      _scrapedData = storedData.scrapedData;\r\n      _lastScrapedTimestamp = storedData.timestamp || '';\r\n\r\n      // Rebuild the element map for duplicate detection\r\n      _scrapedData.forEach(element => {\r\n        if (element.xpath) {\r\n          _elementMap.set(element.xpath, element);\r\n        }\r\n      });\r\n\r\n      console.log(`🔄 Restored ${_scrapedData.length} scraped elements from storage after page refresh`);\r\n\r\n      // Re-highlight the previously scraped elements\r\n      _scrapedData.forEach(element => {\r\n        if (element.xpath) {\r\n          try {\r\n            const elementNode = document.evaluate(\r\n              element.xpath,\r\n              document,\r\n              null,\r\n              XPathResult.FIRST_ORDERED_NODE_TYPE,\r\n              null\r\n            ).singleNodeValue as HTMLElement;\r\n\r\n            if (elementNode) {\r\n              addPersistentHighlightWithoutBlocking(elementNode);\r\n            }\r\n          } catch (error) {\r\n            // Element might not exist anymore, that's okay\r\n          }\r\n        }\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.log('No previous scraped data found or error loading from storage');\r\n  }\r\n\r\n  // Check if we're in a Chrome extension environment\r\n  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {\r\n    // Listen for messages from background script\r\n    chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {\r\n      if (message.action === 'updateScrapingState') {\r\n        _isScrapingActive = message.isActive;\r\n        sendResponse({ success: true });\r\n        return true;\r\n      }\r\n\r\n      if (message.action === 'getScrapingState') {\r\n        sendResponse({\r\n          isActive: _isScrapingActive,\r\n          lastTimestamp: _lastScrapedTimestamp,\r\n          elementCount: _scrapedData.length\r\n        });\r\n        return true;\r\n      }\r\n\r\n      if (message.action === 'getScrapedData') {\r\n        sendResponse({\r\n          data: _scrapedData,\r\n          timestamp: _lastScrapedTimestamp\r\n        });\r\n        return true;\r\n      }\r\n\r\n      if (message.action === 'clearScrapedData') {\r\n        clearScrapedData();\r\n        sendResponse({ success: true });\r\n        return true;\r\n      }\r\n    });\r\n  } else {\r\n  }\r\n};\r\n\r\n\r\n// Initialize the service\r\ninitScrapingService().catch(error => {\r\n  console.error('Error initializing scraping service:', error);\r\n});\r\n\r\n/**\r\n * Utility to extract only essential data from a scraped element object\r\n * Includes fallback identification mechanisms\r\n */\r\nexport function extractMinimalScrapeData(element: any): {\r\n  xpath: string,\r\n  labelName: string,\r\n  cssSelector: string,\r\n  selector: string,\r\n  value: any,\r\n  text: string,\r\n  id: string\r\n} {\r\n  return {\r\n    xpath: element.xpath || '',\r\n    labelName: element.labelName || element.text || '',\r\n    cssSelector: element.cssSelector || '',\r\n    selector: element.selector || element.xpath || element.cssSelector || '',\r\n    value: element.value,\r\n    text: element.text || '',\r\n    id: element.id || '',\r\n  };\r\n}\r\n"], "mappings": "AAAA;AAGA,OAAQA,gBAAgB,KAAO,uBAAuB,CAGtD,MAAO,CAAAC,IAAI,KAAM,uBAAuB,CAExC;AACA,KAAM,CAAAC,CAAC,CAAGD,IAAI,CAACC,CAAC,CAACC,IAAI,CAACF,IAAI,CAAC,CAG3B,GAAI,CAAAG,iBAKS,CAAGC,SAAS,CAEzB;AACA,GAAI,CAAAC,iBAAiB,CAAG,KAAK,CAC7B,GAAI,CAAAC,YAAmB,CAAG,EAAE,CAC5B,GAAI,CAAAC,qBAA6B,CAAG,EAAE,CACtC,GAAI,CAAAC,WAAqC,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAE;AACvD,GAAI,CAAAC,cAAoD,CAAG,IAAI,CAC/D,GAAI,CAAAC,oBAAsC,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAE;AACxD,GAAI,CAAAC,gBAAkC,CAAG,GAAI,CAAAD,GAAG,CAAC,CAAC,CAAE;AACpD;AAuBA;AAiBA;AACA;AACA,GACA,MAAO,MAAM,CAAAE,gBAAgB,CAAGA,CAAA,GAAe,CAC7C,MAAO,CAAAT,iBAAiB,CAC1B,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAU,iBAAiB,CAAIC,MAAe,EAAW,CAC1DX,iBAAiB,CAAGW,MAAM,CAC5B,CAAC,CAED;AACA;AACA,GAEA,KAAM,CAAAC,aAAa,CAAIC,OAAoB,EAAa,CACtD,KAAM,CAAAC,OAAO,CAAGD,OAAO,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,CAE7C;AACA,GAAID,OAAO,GAAK,iBAAiB,CAAE,CACjC,GAAID,OAAO,CAACG,EAAE,CAAE,CACd,MAAO,YAAYH,OAAO,CAACG,EAAE,IAAI,CACnC,CAEA,KAAM,CAAAC,KAAK,CAAGJ,OAAO,CAACK,aAAa,CAAC,OAAO,CAAC,CAC5C,GAAID,KAAK,EAAIA,KAAK,CAACD,EAAE,CAAE,CACrB,MAAO,YAAYC,KAAK,CAACD,EAAE,iCAAiC,CAC9D,CAEA,KAAM,CAAAG,MAAM,CAAGN,OAAO,CAACO,YAAY,CAAC,SAAS,CAAC,CAC9C,GAAID,MAAM,CAAE,CACV,MAAO,+BAA+BA,MAAM,IAAI,CAClD,CAEA,KAAM,CAAAE,SAAS,CAAGR,OAAO,CAACQ,SAAS,CACnC,GAAIA,SAAS,CAAE,CACb,KAAM,CAAAC,aAAa,CAAGD,SAAS,CAC5BE,KAAK,CAAC,GAAG,CAAC,CACVC,MAAM,CAACC,GAAG,EAAIA,GAAG,EAAI,CAACA,GAAG,CAACC,UAAU,CAAC,IAAI,CAAC,EAAID,GAAG,CAACE,MAAM,CAAG,CAAC,CAAC,CAChE,GAAIL,aAAa,CAACK,MAAM,CAAG,CAAC,CAAE,CAC5B,MAAO,uCAAuCL,aAAa,CAAC,CAAC,CAAC,KAAK,CACrE,CACF,CAEA,MAAO,CAAAM,uBAAuB,CAACf,OAAO,CAAC,CACzC,CAEA;AACA,KAAM,CAAAgB,iBAAiB,CAAGf,OAAO,GAAK,kBAAkB,CACpDD,OAAO,CACPA,OAAO,CAACiB,OAAO,CAAC,kBAAkB,CAAC,CAEvC,GAAID,iBAAiB,WAAY,CAAAE,WAAW,CAAE,CAC5C,KAAM,CAAAC,UAAU,CAAGH,iBAAiB,CAACb,EAAE,CACvC,KAAM,CAAAC,KAAK,CAAGY,iBAAiB,CAACX,aAAa,CAAC,OAAO,CAAC,CAEtD,GAAIc,UAAU,EAAIf,KAAK,CAAE,CACvB,MAAO,2BAA2Be,UAAU,cAAc,CAC5D,CAEA,MAAO,CAAAJ,uBAAuB,CAACC,iBAAiB,CAAC,CACnD,CAEA,GAAIhB,OAAO,CAACG,EAAE,CAAE,CACd,MAAO,YAAYH,OAAO,CAACG,EAAE,IAAI,CACnC,CAEA,MAAO,CAAAY,uBAAuB,CAACf,OAAO,CAAC,CACzC,CAAC,CAED,KAAM,CAAAe,uBAAuB,CAAIf,OAAoB,EAAa,CAChE,KAAM,CAAAoB,IAAc,CAAG,EAAE,CACzB,GAAI,CAAAC,OAAuB,CAAGrB,OAAO,CAErC,MAAOqB,OAAO,EAAIA,OAAO,CAACC,QAAQ,GAAKC,IAAI,CAACC,YAAY,CAAE,CACxD,GAAI,CAAAC,QAAQ,CAAGJ,OAAO,CAACK,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAE7C,GAAImB,OAAO,CAAClB,EAAE,CAAE,CACdsB,QAAQ,EAAI,SAASJ,OAAO,CAAClB,EAAE,IAAI,CACnCiB,IAAI,CAACO,OAAO,CAACF,QAAQ,CAAC,CACtB,MACF,CAAC,IAAM,CACL,GAAI,CAAAG,OAAO,CAAGP,OAAO,CAACQ,sBAAsB,CAC5C,GAAI,CAAAC,QAAQ,CAAG,CAAC,CAChB,MAAOF,OAAO,CAAE,CACd,GAAIA,OAAO,CAACF,QAAQ,CAACxB,WAAW,CAAC,CAAC,GAAKuB,QAAQ,CAAE,CAC/CK,QAAQ,EAAE,CACZ,CACAF,OAAO,CAAGA,OAAO,CAACC,sBAAsB,CAC1C,CAEA,GAAIC,QAAQ,CAAG,CAAC,CAAE,CAChBL,QAAQ,EAAI,IAAIK,QAAQ,GAAG,CAC7B,CAEAV,IAAI,CAACO,OAAO,CAACF,QAAQ,CAAC,CACxB,CAEAJ,OAAO,CAAGA,OAAO,CAACU,aAAa,CACjC,CAEA,MAAO,IAAI,CAAGX,IAAI,CAACY,IAAI,CAAC,GAAG,CAAC,CAC9B,CAAC,CAMD;AACA;AACA,GAGA,KAAM,CAAAC,mBAAmB,CAAIC,EAAe,EAAa,CACvD,GAAI,CAACA,EAAE,CAAE,MAAO,EAAE,CAClB,KAAM,CAAAd,IAAI,CAAG,EAAE,CACf,MAAOc,EAAE,EAAIA,EAAE,CAACZ,QAAQ,GAAKC,IAAI,CAACC,YAAY,CAAE,CAC9C,GAAI,CAAAC,QAAQ,CAAGS,EAAE,CAACjC,OAAO,CAACC,WAAW,CAAC,CAAC,CACvC,GAAIgC,EAAE,CAAC/B,EAAE,CAAE,CACT;AACA,KAAM,CAAAA,EAAE,CAAG+B,EAAE,CAAC/B,EAAE,CAChB,GAAI,QAAQ,CAACgC,IAAI,CAAChC,EAAE,CAAC,EAAI,gBAAgB,CAACgC,IAAI,CAAChC,EAAE,CAAC,CAAE,CAClD;AACAsB,QAAQ,EAAI,QAAQtB,EAAE,IAAI,CAC5B,CAAC,IAAM,CACL;AACAsB,QAAQ,EAAI,IAAItB,EAAE,EAAE,CACtB,CACAiB,IAAI,CAACO,OAAO,CAACF,QAAQ,CAAC,CACtB,MACF,CAAC,IAAM,CACL;AACA,KAAM,CAAAjB,SAAS,CAAG4B,mBAAmB,CAACF,EAAE,CAAC,CACzC,GAAI1B,SAAS,CAAE,CACbiB,QAAQ,EAAI,GAAG,CAAGjB,SAAS,CAAC6B,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,CAAE,GAAG,CAAC,CACzD,CACAlB,IAAI,CAACO,OAAO,CAACF,QAAQ,CAAC,CACtBS,EAAE,CAAGA,EAAE,CAACH,aAAc,CACxB,CACF,CACA,MAAO,CAAAX,IAAI,CAACY,IAAI,CAAC,KAAK,CAAC,CACzB,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAI,mBAAmB,CAAIF,EAAW,EAAa,CACnD,GAAI,CAACA,EAAE,CAAE,MAAO,EAAE,CAElB;AACA,GAAI,MAAO,CAAAA,EAAE,CAAC1B,SAAS,GAAK,QAAQ,CAAE,CACpC,MAAO,CAAA0B,EAAE,CAAC1B,SAAS,CACrB,CAEA;AACA,GAAI0B,EAAE,CAAC1B,SAAS,EAAI,MAAO,CAAA0B,EAAE,CAAC1B,SAAS,GAAK,QAAQ,EAAI,SAAS,EAAI,CAAA0B,EAAE,CAAC1B,SAAS,CAAE,CACjF,MAAQ,CAAA0B,EAAE,CAAC1B,SAAS,CAAS+B,OAAO,EAAI,EAAE,CAC5C,CAEA;AACA,MAAO,CAAAL,EAAE,CAAC3B,YAAY,CAAC,OAAO,CAAC,EAAI,EAAE,CACvC,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAiC,iBAAiB,CAAIxC,OAAoB,EAAa,CAC1D;AAEA;AACA,GAAIA,OAAO,CAACG,EAAE,CAAE,KAAAsC,kBAAA,CACd,KAAM,CAAAC,KAAK,CAAGC,QAAQ,CAACtC,aAAa,CAAC,cAAcL,OAAO,CAACG,EAAE,IAAI,CAAC,CAClE,GAAIuC,KAAK,GAAAD,kBAAA,CAAIC,KAAK,CAACE,WAAW,UAAAH,kBAAA,WAAjBA,kBAAA,CAAmBJ,IAAI,CAAC,CAAC,CAAE,CACtC,MAAO,CAAAK,KAAK,CAACE,WAAW,CAACP,IAAI,CAAC,CAAC,CACjC,CACF,CAEA;AACA,KAAM,CAAApC,OAAO,CAAGD,OAAO,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,CAC7C,KAAM,CAAA2C,UAAU,CAAG5C,OAAO,GAAK,kBAAkB,EAC9BD,OAAO,CAACiB,OAAO,CAAC,kBAAkB,CAAC,EACnCjB,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC,GAAK,UAAU,EAC3CP,OAAO,CAACQ,SAAS,CAACsC,QAAQ,CAAC,UAAU,CAAC,EACtC9C,OAAO,CAACQ,SAAS,CAACsC,QAAQ,CAAC,QAAQ,CAAC,CAEvD,GAAID,UAAU,CAAE,KAAAE,oBAAA,CACd;AACA,KAAM,CAAAH,WAAW,EAAAG,oBAAA,CAAG/C,OAAO,CAAC4C,WAAW,UAAAG,oBAAA,iBAAnBA,oBAAA,CAAqBV,IAAI,CAAC,CAAC,CAC/C,GAAIO,WAAW,EAAIA,WAAW,CAAC9B,MAAM,CAAG,CAAC,CAAE,CACzC;AACA,KAAM,CAAAkC,YAAY,CAAG,CAAC,cAAc,CAAE,UAAU,CAAE,QAAQ,CAAE,UAAU,CAAC,CACvE,KAAM,CAAAC,SAAS,CAAGD,YAAY,CAACE,IAAI,CAACC,IAAI,EACtCP,WAAW,CAAC1C,WAAW,CAAC,CAAC,GAAKiD,IAAI,CAACjD,WAAW,CAAC,CACjD,CAAC,CAED,GAAI,CAAC+C,SAAS,CAAE,CACd,MAAO,CAAAL,WAAW,CAAC9B,MAAM,CAAG,EAAE,CAAG8B,WAAW,CAACQ,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,KAAK,CAAGR,WAAW,CACrF,CACF,CACF,CAEA;AACA,KAAM,CAAAS,SAAS,CAAGrD,OAAO,CAACO,YAAY,CAAC,YAAY,CAAC,CACpD,GAAI8C,SAAS,SAATA,SAAS,WAATA,SAAS,CAAEhB,IAAI,CAAC,CAAC,CAAE,CACrB;AACA,GAAIQ,UAAU,CAAE,CACd,KAAM,CAAAG,YAAY,CAAG,CAAC,cAAc,CAAE,UAAU,CAAE,QAAQ,CAAE,UAAU,CAAC,CACvE,KAAM,CAAAC,SAAS,CAAGD,YAAY,CAACE,IAAI,CAACC,IAAI,EACtCE,SAAS,CAACnD,WAAW,CAAC,CAAC,GAAKiD,IAAI,CAACjD,WAAW,CAAC,CAC/C,CAAC,CACD,GAAI,CAAC+C,SAAS,CAAE,CACd,MAAO,CAAAI,SAAS,CAAChB,IAAI,CAAC,CAAC,CACzB,CACF,CAAC,IAAM,CACL,MAAO,CAAAgB,SAAS,CAAChB,IAAI,CAAC,CAAC,CACzB,CACF,CAEA;AACA,KAAM,CAAAiB,KAAK,CAAGtD,OAAO,CAACO,YAAY,CAAC,OAAO,CAAC,CAC3C,GAAI+C,KAAK,SAALA,KAAK,WAALA,KAAK,CAAEjB,IAAI,CAAC,CAAC,CAAE,CACjB,MAAO,CAAAiB,KAAK,CAACjB,IAAI,CAAC,CAAC,CACrB,CAEA;AACA,KAAM,CAAAkB,WAAW,CAAGvD,OAAO,CAACO,YAAY,CAAC,aAAa,CAAC,CACvD,GAAIgD,WAAW,SAAXA,WAAW,WAAXA,WAAW,CAAElB,IAAI,CAAC,CAAC,CAAE,CACvB,MAAO,CAAAkB,WAAW,CAAClB,IAAI,CAAC,CAAC,CAC3B,CAEA;AACA,KAAM,CAAAmB,IAAI,CAAGxD,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC,CACzC,GAAIiD,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEnB,IAAI,CAAC,CAAC,CAAE,CAChB,MAAO,CAAAmB,IAAI,CAACnB,IAAI,CAAC,CAAC,CACpB,CAEA;AACA,KAAM,CAAAoB,SAAS,CAAGzD,OAAO,CAACO,YAAY,CAAC,YAAY,CAAC,EAAIP,OAAO,CAACO,YAAY,CAAC,WAAW,CAAC,CACzF,GAAIkD,SAAS,SAATA,SAAS,WAATA,SAAS,CAAEpB,IAAI,CAAC,CAAC,CAAE,CACrB,MAAO,CAAAoB,SAAS,CAACpB,IAAI,CAAC,CAAC,CACzB,CAEA;AACA,GAAI,CAACQ,UAAU,CAAE,KAAAa,qBAAA,CACf,KAAM,CAAAd,WAAW,EAAAc,qBAAA,CAAG1D,OAAO,CAAC4C,WAAW,UAAAc,qBAAA,iBAAnBA,qBAAA,CAAqBrB,IAAI,CAAC,CAAC,CAC/C,GAAIO,WAAW,EAAIA,WAAW,CAAC9B,MAAM,CAAG,CAAC,CAAE,CACzC,MAAO,CAAA8B,WAAW,CAAC9B,MAAM,CAAG,EAAE,CAAG8B,WAAW,CAACQ,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,KAAK,CAAGR,WAAW,CACrF,CACF,CAEA;AACA,KAAM,CAAAe,KAAK,CAAG3D,OAAO,CAACO,YAAY,CAAC,OAAO,CAAC,CAC3C,GAAIoD,KAAK,SAALA,KAAK,WAALA,KAAK,CAAEtB,IAAI,CAAC,CAAC,CAAE,CACjB,MAAO,CAAAsB,KAAK,CAACtB,IAAI,CAAC,CAAC,CACrB,CAEA;AACA,KAAM,CAAAlC,EAAE,CAAGH,OAAO,CAACG,EAAE,CACrB,KAAM,CAAAK,SAAS,CAAG4B,mBAAmB,CAACpC,OAAO,CAAC,CAE9C,GAAIG,EAAE,CAAE,CACN,MAAO,GAAGF,OAAO,IAAIE,EAAE,EAAE,CAC3B,CAAC,IAAM,IAAIK,SAAS,CAAE,CACpB,KAAM,CAAAoD,UAAU,CAAGpD,SAAS,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC1C,MAAO,GAAGT,OAAO,IAAI2D,UAAU,EAAE,CACnC,CAEA;AACA,MAAO,CAAA3D,OAAO,CAChB,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAA4D,qBAAqB,CAAI7D,OAAoB,EAAc,CAC/D,MACE,CAAAA,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,6BAA6B,CAAC,EACzD/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAClD/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,gCAAgC,CAAC,EAC5D/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,EACzC/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,oBAAoB,CAAC,EAChD/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAClD/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,EACjD/D,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC,GAAK,SAAS,EAC1C,CAAC,CAACP,OAAO,CAACiB,OAAO,CAAC,iBAAiB,CAAC,EACpC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,kBAAkB,CAAC,EACrC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,0BAA0B,CAAC,EAC7C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,sBAAsB,CAAC,EACzC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,iBAAiB,CAAC,EACpC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,eAAe,CAAC,EAClC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,mBAAmB,CAAC,EACtC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,aAAa,CAAC,EAChC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,gBAAgB,CAAC,EACnC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,aAAa,CAAC,EAChC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,gBAAgB,CAAC,EACnC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,cAAc,CAAC,EACjC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,gBAAgB,CAAC,EACnC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,qBAAqB,CAAC,EACxC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,WAAW,CAAC,EAC9B,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,WAAW,CAAC,EAC9B,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,aAAa,CAAC,EAChC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,gBAAgB,CAAC,EACnC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,oBAAoB,CAAC,EACvC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,2BAA2B,CAAC,EAC9C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,WAAW,CAAC,EAC9B,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,iBAAiB,CAAC,EACpC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,yBAAyB,CAAC,EAC5C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,qBAAqB,CAAC,EACxC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,yBAAyB,CAAC,EAC5C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,mBAAmB,CAAC,EACtC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,8BAA8B,CAAC,EACjD,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,kBAAkB,CAAC,EACrC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,cAAc,CAAC,EACjC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,qBAAqB,CAAC,EACxC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,YAAY,CAAC,EAC/B,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,uBAAuB,CAAC,EAC1C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,wBAAwB,CAAC,EAC3C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,UAAU,CAAC,EAC7B,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAACjB,OAAO,CAACG,EAAE,CAACU,UAAU,CAAC,WAAW,CAAC,CAAG,IAAIb,OAAO,CAACG,EAAE,EAAE,CAAG,MAAM,CAAC,EACjF,CAAC,CAACH,OAAO,CAACiB,OAAO,CAAC,kBAAkB,CAAC,EACrC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,oBAAoB,CAAC,EACvC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,mBAAmB,CAAC,EACtC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,cAAc,CAAC,EACjC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,gBAAgB,CAAC,EACnC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,oBAAoB,CAAC,EACvC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,iCAAiC,CAAC,EACpD,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,qBAAqB,CAAC,EACxC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,2BAA2B,CAAC,EAC9C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,mCAAmC,CAAC,EAAI;AAC1D,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,8BAA8B,CAAC,EAAI;AACrD,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,iCAAiC,CAAC,CAGxD,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAA+C,kBAAkB,CAAIhE,OAAoB,EAAc,CAC5D,MACE,CAAAA,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAClD/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,gCAAgC,CAAC,EAC5D/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,EACzC/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,oBAAoB,CAAC,EAChD/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAClD/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,EACjD/D,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC,GAAK,SAAS,EAC1C,CAAC,CAACP,OAAO,CAACiB,OAAO,CAAC,iBAAiB,CAAC,EACpC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,0BAA0B,CAAC,EAC7C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,sBAAsB,CAAC,EACzC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,iBAAiB,CAAC,EACpC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,eAAe,CAAC,EAClC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,mBAAmB,CAAC,EACtC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,aAAa,CAAC,EAChC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,cAAc,CAAC,EACjC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,iCAAiC,CAAC,EACpD,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAACjB,OAAO,CAACG,EAAE,CAACU,UAAU,CAAC,WAAW,CAAC,CAAG,IAAIb,OAAO,CAACG,EAAE,EAAE,CAAG,MAAM,CAAC,EACjF,CAAC,CAACH,OAAO,CAACiB,OAAO,CAAC,UAAU,CAAC,EAC7B,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,kBAAkB,CAAC,EACrC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,2BAA2B,CAAC,EAC9C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,oBAAoB,CAAC,EACvC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,mCAAmC,CAAC,EACrD;AACA,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,8BAA8B,CAAC,EAAG;AACrD,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,iCAAiC,CAAC,CAExD,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAgD,qCAAqC,CAAIjE,OAAoB,EAAW,CAC5E,GAAI6D,qBAAqB,CAAC7D,OAAO,CAAC,CAAE,OAEpC;AACAA,OAAO,CAACkE,KAAK,CAACC,OAAO,CAAG,8BAA8B,CACtDnE,OAAO,CAACkE,KAAK,CAACE,aAAa,CAAG,KAAK,CACnCpE,OAAO,CAACqE,YAAY,CAAC,6BAA6B,CAAE,MAAM,CAAC,CAC3D5E,oBAAoB,CAAC6E,GAAG,CAACtE,OAAO,CAAC,CAEjC;AACF,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAuE,sBAAsB,CAAIvE,OAAoB,EAAW,KAAAwE,qBAAA,CAC7D,GAAIX,qBAAqB,CAAC7D,OAAO,CAAC,CAAE,OAEpC;AACAA,OAAO,CAACkE,KAAK,CAACC,OAAO,CAAG,8BAA8B,CACtDnE,OAAO,CAACkE,KAAK,CAACE,aAAa,CAAG,KAAK,CACnCpE,OAAO,CAACqE,YAAY,CAAC,6BAA6B,CAAE,MAAM,CAAC,CAC3D5E,oBAAoB,CAAC6E,GAAG,CAACtE,OAAO,CAAC,CAEjC;AACA,KAAM,CAAAyE,OAAO,CAAG9B,QAAQ,CAAC+B,aAAa,CAAC,KAAK,CAAC,CAC7CD,OAAO,CAACP,KAAK,CAACS,OAAO,CAAG;AAC1B;AACA,WAAW3E,OAAO,CAAC4E,SAAS;AAC5B,YAAY5E,OAAO,CAAC6E,UAAU;AAC9B,aAAa7E,OAAO,CAAC8E,WAAW;AAChC,cAAc9E,OAAO,CAAC+E,YAAY;AAClC;AACA;AACA;AACA;AACA,GAAG,CACDN,OAAO,CAACJ,YAAY,CAAC,yBAAyB,CAAE,MAAM,CAAC,CAGvD,KAAM,CAAAW,WAAW,CAAGjG,CAAC,CAAC,uCAAuC,CAAC,CAC9D0F,OAAO,CAACnB,KAAK,CAAG0B,WAAW,CAE3B;AACA,KAAM,CAAAC,IAAI,CAAGjF,OAAO,CAACkF,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAAC,UAAU,CAAG,EAAAX,qBAAA,CAAAxE,OAAO,CAACoF,YAAY,UAAAZ,qBAAA,iBAApBA,qBAAA,CAAsBU,qBAAqB,CAAC,CAAC,GAAI,CAAEG,GAAG,CAAE,CAAC,CAAEC,IAAI,CAAE,CAAE,CAAC,CAEvFb,OAAO,CAACP,KAAK,CAACmB,GAAG,CAAG,GAAGJ,IAAI,CAACI,GAAG,CAAGF,UAAU,CAACE,GAAG,CAAGE,MAAM,CAACC,OAAO,IAAI,CACrEf,OAAO,CAACP,KAAK,CAACoB,IAAI,CAAG,GAAGL,IAAI,CAACK,IAAI,CAAGH,UAAU,CAACG,IAAI,CAAGC,MAAM,CAACE,OAAO,IAAI,CAExE;AACA,KAAM,CAAAC,MAAM,CAAG1F,OAAO,CAACoF,YAAY,EAAIzC,QAAQ,CAACgD,IAAI,CACpDD,MAAM,CAACE,WAAW,CAACnB,OAAO,CAAC,CAC3B9E,gBAAgB,CAAC2E,GAAG,CAACG,OAAO,CAAC,CAE7B;AACAA,OAAO,CAACoB,gBAAgB,CAAC,OAAO,CAAGC,CAAC,EAAK,CACvCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBD,CAAC,CAACE,eAAe,CAAC,CAAC,CACrB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAY,CACtC;AACAxG,oBAAoB,CAACyG,OAAO,CAAClG,OAAO,EAAI,CACtC,GAAIA,OAAO,EAAIA,OAAO,CAACkE,KAAK,CAAE,CAC5BlE,OAAO,CAACkE,KAAK,CAACC,OAAO,CAAG,EAAE,CAC1BnE,OAAO,CAACkE,KAAK,CAACE,aAAa,CAAG,EAAE,CAChCpE,OAAO,CAACmG,eAAe,CAAC,6BAA6B,CAAC,CACxD,CACF,CAAC,CAAC,CACF1G,oBAAoB,CAAC2G,KAAK,CAAC,CAAC,CAE5B;AACAzG,gBAAgB,CAACuG,OAAO,CAACzB,OAAO,EAAI,CAClC,GAAIA,OAAO,EAAIA,OAAO,CAAC4B,UAAU,CAAE,CACjC5B,OAAO,CAAC4B,UAAU,CAACC,WAAW,CAAC7B,OAAO,CAAC,CACzC,CACF,CAAC,CAAC,CACF9E,gBAAgB,CAACyG,KAAK,CAAC,CAAC,CAC1B,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAG,iBAAiB,CAAIvG,OAAoB,EAAW,CACxD,GAAI,CACF;AACA,KAAM,CAAAwG,QAAQ,CAAG7D,QAAQ,CAAC+B,aAAa,CAAC,KAAK,CAAC,CAC9C8B,QAAQ,CAACtC,KAAK,CAACS,OAAO,CAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAGD,KAAM,CAAA8B,WAAW,CAAG,KAAK1H,CAAC,CAAC,SAAS,CAAC,EAAE,CACvCyH,QAAQ,CAAC5D,WAAW,CAAG6D,WAAW,CAElC;AACA,KAAM,CAAAxB,IAAI,CAAGjF,OAAO,CAACkF,qBAAqB,CAAC,CAAC,CAC5CsB,QAAQ,CAACtC,KAAK,CAACoB,IAAI,CAAG,GAAGL,IAAI,CAACK,IAAI,CAAGC,MAAM,CAACE,OAAO,IAAI,CACvDe,QAAQ,CAACtC,KAAK,CAACmB,GAAG,CAAG,GAAGJ,IAAI,CAACI,GAAG,CAAGE,MAAM,CAACC,OAAO,CAAG,EAAE,IAAI,CAE1D7C,QAAQ,CAACgD,IAAI,CAACC,WAAW,CAACY,QAAQ,CAAC,CAEnC;AACAE,UAAU,CAAC,IAAM,CACfF,QAAQ,CAACtC,KAAK,CAACyC,OAAO,CAAG,GAAG,CAC9B,CAAC,CAAE,EAAE,CAAC,CAEN;AACAD,UAAU,CAAC,IAAM,CACfF,QAAQ,CAACtC,KAAK,CAACyC,OAAO,CAAG,GAAG,CAC5BD,UAAU,CAAC,IAAM,CACf,GAAIF,QAAQ,CAACH,UAAU,CAAE,CACvBG,QAAQ,CAACH,UAAU,CAACC,WAAW,CAACE,QAAQ,CAAC,CAC3C,CACF,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAOI,KAAK,CAAE,CAChB,CACF,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAC,kBAAkB,CAAI7G,OAAoB,EAA+C,KAAA8G,qBAAA,CAC7F,KAAM,CAAA7B,IAAI,CAAGjF,OAAO,CAACkF,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAA6B,KAAK,CAAGhH,aAAa,CAACC,OAAO,CAAC,CACpC,KAAM,CAAAgH,IAAI,CAAKC,cAAc,CAACjH,OAAO,CAAC,CACtC,KAAM,CAAAkH,WAAW,CAAGjF,mBAAmB,CAACjC,OAAO,CAAC,CAEhD;AACA,QAAS,CAAAmH,aAAaA,CAACjF,EAAe,CAAO,CAC3C;AACA,GAAIA,EAAE,WAAY,CAAAkF,gBAAgB,EAAIlF,EAAE,WAAY,CAAAmF,mBAAmB,CAAE,CACvE,MAAO,CAAAnF,EAAE,CAACyB,KAAK,CACjB,CACA,GAAIzB,EAAE,WAAY,CAAAoF,iBAAiB,CAAE,KAAAC,qBAAA,CACnC,MAAO,EAAAA,qBAAA,CAAArF,EAAE,CAACsF,OAAO,CAACtF,EAAE,CAACuF,aAAa,CAAC,UAAAF,qBAAA,iBAA5BA,qBAAA,CAA8BG,IAAI,GAAIxF,EAAE,CAACyB,KAAK,CACvD,CACA;AACA;AACA,KAAM,CAAAvD,KAAK,CAAG8B,EAAE,CAAC7B,aAAa,CAAC,OAAO,CAAC,CACvC,GAAID,KAAK,EAAIA,KAAK,WAAY,CAAAgH,gBAAgB,CAAE,CAC9C,MAAO,CAAAhH,KAAK,CAACuD,KAAK,CACpB,CACA;AACA,KAAM,CAAAgE,YAAY,CAAGzF,EAAE,CAAC7B,aAAa,CAAC,oFAAoF,CAAC,CAC3H,GAAIsH,YAAY,CAAE,KAAAC,qBAAA,CAChB,OAAAA,qBAAA,CAAOD,YAAY,CAAC/E,WAAW,UAAAgF,qBAAA,iBAAxBA,qBAAA,CAA0BvF,IAAI,CAAC,CAAC,CACzC,CACA;AACA,GAAIH,EAAE,CAAC2F,YAAY,CAAC,YAAY,CAAC,CAAE,CACjC,MAAO,CAAA3F,EAAE,CAAC3B,YAAY,CAAC,YAAY,CAAC,CACtC,CACA,MAAO,CAAArB,SAAS,CAClB,CAEA;AACA,GAAI,CAAAyE,KAAK,CAAGwD,aAAa,CAACnH,OAAO,CAAC,CAClC;AACA,GAAI2D,KAAK,GAAKzE,SAAS,CAAE,CACvB,KAAM,CAAA4I,UAAU,CAAG9H,OAAO,CAACK,aAAa,CAAC,yBAAyB,CAAC,CACnE,GAAIyH,UAAU,CAAE,CACdnE,KAAK,CAAGwD,aAAa,CAACW,UAAyB,CAAC,CAClD,CACF,CAEA,MAAO,CACL7H,OAAO,CAAED,OAAO,CAACC,OAAO,CACxBE,EAAE,CAAEH,OAAO,CAACG,EAAE,EAAI,EAAE,CACpBK,SAAS,CAAE4B,mBAAmB,CAACpC,OAAO,CAAC,CACvC0H,IAAI,CAAE,EAAAZ,qBAAA,CAAA9G,OAAO,CAAC4C,WAAW,UAAAkE,qBAAA,iBAAnBA,qBAAA,CAAqBzE,IAAI,CAAC,CAAC,GAAIrC,OAAO,CAACG,EAAE,CAAG;AAClD4H,SAAS,CAAEvF,iBAAiB,CAACxC,OAAO,CAAC,CAAE;AACvCgI,UAAU,CAAEC,KAAK,CAACC,IAAI,CAAClI,OAAO,CAACgI,UAAU,CAAC,CAACG,MAAM,CAAC,CAACC,GAAG,CAAEC,IAAI,GAAK,CAC/DD,GAAG,CAACC,IAAI,CAAC7E,IAAI,CAAC,CAAG6E,IAAI,CAAC1E,KAAK,CAC3B,MAAO,CAAAyE,GAAG,CACZ,CAAC,CAAE,CAAC,CAA2B,CAAC,CAChCrB,KAAK,CACLG,WAAW,CACXzF,QAAQ,CAAEsF,KAAK,EAAIG,WAAW,CAAE;AAChCjC,IAAI,CAAE,CACJI,GAAG,CAAEJ,IAAI,CAACI,GAAG,CACbC,IAAI,CAAEL,IAAI,CAACK,IAAI,CACfgD,KAAK,CAAErD,IAAI,CAACqD,KAAK,CACjBC,MAAM,CAAEtD,IAAI,CAACsD,MACf,CAAC,CACDC,QAAQ,CAAE,EAAE,CAAE;AACdC,SAAS,CAAExD,IAAI,CAACqD,KAAK,CAAG,CAAC,EAAIrD,IAAI,CAACsD,MAAM,CAAG,CAAC,CAC5CG,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnCC,GAAG,CAAEtD,MAAM,CAACuD,QAAQ,CAACC,IAAI,CAAE;AAC3B/B,IAAI,CACJ,IAAIrD,KAAK,GAAKzE,SAAS,CAAG,CAAEyE,KAAM,CAAC,CAAG,CAAC,CAAC,CAC1C,CAAC,CACH,CAAC,CAGD,KAAM,CAAAsD,cAAc,CAAIjH,OAAoB,EAAU,CACpD,KAAM,CAAAC,OAAO,CAAGD,OAAO,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,CAE7C;AACA,GAAID,OAAO,GAAK,iBAAiB,EAAID,OAAO,CAACiB,OAAO,CAAC,iBAAiB,CAAC,CAAE,CACvE,MAAO,OAAO,CAChB,CAEA,GAAIhB,OAAO,GAAK,OAAO,CAAE,CACvB,KAAM,CAAA+I,YAAY,CAAGhJ,OAA2B,CAChD,KAAM,CAAAgH,IAAI,CAAGgC,YAAY,CAAChC,IAAI,EAAI,MAAM,CAExC;AACA,GACEA,IAAI,GAAK,MAAM,GAEbhH,OAAO,CAACG,EAAE,CAACD,WAAW,CAAC,CAAC,CAAC4C,QAAQ,CAAC,MAAM,CAAC,EACzC9C,OAAO,CAACQ,SAAS,CAACN,WAAW,CAAC,CAAC,CAAC4C,QAAQ,CAAC,MAAM,CAAC,EAChD,CAAC9C,OAAO,CAACO,YAAY,CAAC,aAAa,CAAC,EAAI,EAAE,EAAEL,WAAW,CAAC,CAAC,CAAC4C,QAAQ,CAAC,MAAM,CAAC,CAC3E,CACD,CACA;AACA,KAAM,CAAAa,KAAK,CAAGqF,YAAY,CAACrF,KAAK,EAAI,EAAE,CACtC,KAAM,CAAAJ,WAAW,CAAGvD,OAAO,CAACO,YAAY,CAAC,aAAa,CAAC,EAAI,EAAE,CAC7D,KAAM,CAAAJ,EAAE,CAAGH,OAAO,CAACG,EAAE,CAACD,WAAW,CAAC,CAAC,CACnC,KAAM,CAAAM,SAAS,CAAGR,OAAO,CAACQ,SAAS,CAACN,WAAW,CAAC,CAAC,CACjD,KAAM,CAAAsD,IAAI,CAAG,CAACxD,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC,EAAI,EAAE,EAAEL,WAAW,CAAC,CAAC,CAE/D;AACA,KAAM,CAAA+I,gBAAgB,CAAG,uFAAuF,CAChH,KAAM,CAAAC,iBAAiB,CAAGD,gBAAgB,CAAC9G,IAAI,CAACwB,KAAK,CAAC,CAEtD;AACA,KAAM,CAAAwF,kBAAkB,CAAG,CACzB,WAAW,CAAE,YAAY,CAAE,YAAY,CACvC,QAAQ,CAAE,UAAU,CAAE,MAAM,CAAC,OAAO,CACrC,CACD,KAAM,CAAAC,qBAAqB,CAAGD,kBAAkB,CAACjG,IAAI,CAACC,IAAI,EACxDhD,EAAE,CAAC2C,QAAQ,CAACK,IAAI,CAAC,EAAI3C,SAAS,CAACsC,QAAQ,CAACK,IAAI,CAAC,EAAIK,IAAI,CAACV,QAAQ,CAACK,IAAI,CACrE,CAAC,CAED;AACA,KAAM,CAAAkG,eAAe,CAAG,CACtB,UAAU,CAAE,QAAQ,CAAE,WAAW,CAAE,SAAS,CAC5C,WAAW,CAAE,SAAS,CAAE,WAAW,CAAE,SAAS,CAC9C,YAAY,CAAE,UAAU,CAAE,YAAY,CAAE,UAAU,CACnD,CAED,KAAM,CAAAC,yBAAyB,CAAGD,eAAe,CAACnG,IAAI,CAACqG,SAAS,EAC9DpJ,EAAE,CAAC2C,QAAQ,CAACyG,SAAS,CAAC,EAAI/I,SAAS,CAACsC,QAAQ,CAACyG,SAAS,CAAC,EAAI/F,IAAI,CAACV,QAAQ,CAACyG,SAAS,CACpF,CAAC,CAED;AACA,KAAM,CAAAC,qBAAqB,CAAG,CAAC,MAAM,CAAE,IAAI,CAAE,SAAS,CAAE,OAAO,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC/E,KAAM,CAAAC,4BAA4B,CAAGD,qBAAqB,CAACtG,IAAI,CAACC,IAAI,EAClEI,WAAW,CAACrD,WAAW,CAAC,CAAC,CAAC4C,QAAQ,CAACK,IAAI,CACzC,CAAC,CAED;AACA,GAAI+F,iBAAiB,EAAIE,qBAAqB,EAAIE,yBAAyB,EAAIG,4BAA4B,CAAE,CAC3G,MAAO,WAAW,CACpB,CAAC,IAAM,CACL,MAAO,WAAW,CACpB,CACF,CAEA;AACA,GAAIzJ,OAAO,CAACiB,OAAO,CAAC,oEAAoE,CAAC,CAAE,CACzF,MAAO,UAAU,CACnB,CAEA,MAAO,CAAA+F,IAAI,CACb,CAEA;AACA,GAAI/G,OAAO,GAAK,kBAAkB,EAAID,OAAO,CAACiB,OAAO,CAAC,kBAAkB,CAAC,CAAE,CACzE,MAAO,UAAU,CACnB,CAEA;AACA,GAAIhB,OAAO,GAAK,QAAQ,CAAE,CACxB,KAAM,CAAAyJ,aAAa,CAAG1J,OAA4B,CAClD,MAAO,CAAA0J,aAAa,CAACC,QAAQ,CAAG,aAAa,CAAG,UAAU,CAC5D,CAEA;AACA,GAAI1J,OAAO,GAAK,UAAU,CAAE,CAC1B,MAAO,UAAU,CACnB,CAEA;AACA,GAAID,OAAO,CAAC4J,eAAe,GAAK,MAAM,CAAE,CACtC,MAAO,iBAAiB,CAC1B,CAEA;AACA,GACE5J,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC,GAAK,UAAU,EAC3CP,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC,GAAK,SAAS,EAC1CP,OAAO,CAACQ,SAAS,CAACsC,QAAQ,CAAC,UAAU,CAAC,EACtC9C,OAAO,CAACQ,SAAS,CAACsC,QAAQ,CAAC,QAAQ,CAAC,CACpC,CACA,MAAO,UAAU,CACnB,CAEA;AACA,MAAO,OAAO,CAChB,CAAC,CAMD;AACA;AACA,GAEA,KAAM,CAAA+G,kBAAkB,CAAG,KAAAA,CAAQC,KAAiB,CAACC,SAKpD,GAAoB,CACnB,GAAI,CACF;AACA;AACA;AAEA,KAAM,CAAAC,MAAM,CAAGF,KAAK,CAACE,MAAqB,CAC1C,GAAI,CAACA,MAAM,EAAI,CAACA,MAAM,CAAC1I,QAAQ,EAAI0I,MAAM,CAAC1I,QAAQ,GAAKC,IAAI,CAACC,YAAY,CAAE,CACxE,OACF,CAEA,GAAIwC,kBAAkB,CAACgG,MAAM,CAAC,CAAE,CAC9B,OACF,CAEA,GAAIA,MAAM,CAACnC,YAAY,CAAC,6BAA6B,CAAC,CAAE,CACtD,OACF,CAGA;AACA,KAAM,CAAAoC,kBAAkB,CAAGpD,kBAAkB,CAACmD,MAAM,CAAC,CAErD;AACA,KAAM,CAAAE,eAAe,CAAG,CAACD,kBAAkB,CAAC,CAE5C;AACAE,OAAO,CAACC,GAAG,CAAC,oCAAoCJ,MAAM,CAAC/J,OAAO,gBAAgBgK,kBAAkB,CAAClD,KAAK,EAAE,CAAC,CACzGsD,cAAc,CAAC,CAAEC,QAAQ,CAAEJ,eAAgB,CAAC,CAAE,IAAI,CAAC,CAEnD;AAEA,KAAM,CAAAK,wBAAwB,CAACR,SAAS,CAAC,CACzCI,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC,CAEjE;AACAnG,qCAAqC,CAAC+F,MAAM,CAAC,CAG7C;AACA;AAGF,CAAE,MAAOpD,KAAK,CAAE,CACduD,OAAO,CAACvD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CACF,CAAC,CAED,MAAO,MAAM,CAAAyD,cAAc,CAAG,QAAAA,CAACG,IAAS,CAAoC,IAAlC,CAAAC,MAAe,CAAAC,SAAA,CAAA5J,MAAA,IAAA4J,SAAA,MAAAxL,SAAA,CAAAwL,SAAA,IAAG,KAAK,CAC/D,KAAM,CAAAhC,SAAS,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC1CvJ,qBAAqB,CAAGqJ,SAAS,CAEjC,GAAI,CAAC+B,MAAM,CAAE,CACX;AACArL,YAAY,CAAG,EAAE,CACjBE,WAAW,CAAC8G,KAAK,CAAC,CAAC,CACrB,CAEA;AACA,GAAIoE,IAAI,EAAIA,IAAI,CAACF,QAAQ,EAAIrC,KAAK,CAAC0C,OAAO,CAACH,IAAI,CAACF,QAAQ,CAAC,CAAE,CACzDE,IAAI,CAACF,QAAQ,CAACpE,OAAO,CAAElG,OAAoB,EAAK,CAC9C;AACAA,OAAO,CAAC0I,SAAS,CAAGA,SAAS,CAE7B;AACA,GAAI1I,OAAO,CAAC+G,KAAK,CAAE,CACjB;AACA,GAAIzH,WAAW,CAACsL,GAAG,CAAC5K,OAAO,CAAC+G,KAAK,CAAC,CAAE,CAClCoD,OAAO,CAACC,GAAG,CAAC,6CAA6CpK,OAAO,CAAC+G,KAAK,EAAE,CAAC,CACzE,OAAQ;AACV,CAAC,IAAM,CACL;AACAoD,OAAO,CAACC,GAAG,CAAC,oCAAoCpK,OAAO,CAAC+G,KAAK,EAAE,CAAC,CAChEzH,WAAW,CAACuL,GAAG,CAAC7K,OAAO,CAAC+G,KAAK,CAAE/G,OAAO,CAAC,CACvCZ,YAAY,CAAC0L,IAAI,CAAC9K,OAAO,CAAC,CAE5B,CACF,CAAC,IAAM,CACL;AACA,KAAM,CAAA+K,WAAW,CAAG3L,YAAY,CAAC8D,IAAI,CAAC8H,QAAQ,EAC5CA,QAAQ,CAAC/K,OAAO,GAAKD,OAAO,CAACC,OAAO,EACpC+K,QAAQ,CAAC7K,EAAE,GAAKH,OAAO,CAACG,EAAE,EAC1B6K,QAAQ,CAACxK,SAAS,GAAKR,OAAO,CAACQ,SACjC,CAAC,CAED,GAAI,CAACuK,WAAW,CAAE,CAChB3L,YAAY,CAAC0L,IAAI,CAAC9K,OAAO,CAAC,CAE5B,CAAC,IAAM,CAEP,CACF,CACF,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAiL,cAAc,CAAGA,CAAA,GAAa,CACzC,MAAO,CAAA7L,YAAY,CACrB,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAA8L,iBAAiB,CAAInE,KAAa,EAA8B,CAC3E,MAAO,CAAAzH,WAAW,CAAC6L,GAAG,CAACpE,KAAK,CAAC,CAC/B,CAAC,CAED;AACA;AACA;AACA,GACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA,GACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA,GACA,MAAO,MAAM,CAAAqE,YAAY,CAAGA,CAAA,GAA4H,CACtJ,MAAO,CAAAhM,YAAY,CAACiM,GAAG,CAACrL,OAAO,GAAK,CAClC+G,KAAK,CAAE/G,OAAO,CAAC+G,KAAK,CACpB9G,OAAO,CAAED,OAAO,CAACC,OAAO,CACxBE,EAAE,CAAEH,OAAO,CAACG,EAAE,CACdK,SAAS,CAAER,OAAO,CAACQ,SAAS,CAC5BkH,IAAI,CAAE1H,OAAO,CAAC0H,IAAI,CAClBgB,SAAS,CAAE1I,OAAO,CAAC0I,SAAS,CAC5BG,GAAG,CAAE7I,OAAO,CAAC6I,GACf,CAAC,CAAC,CAAC,CACL,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAyC,uBAAuB,CAAG,KAAO,CAAAC,SAAkB,EAAoB,CAClF,GAAI,CACF,GAAInM,YAAY,CAAC0B,MAAM,GAAK,CAAC,CAAE,CAC7B;AACA,KAAM,CAAA0K,UAAU,CAAG,KAAM,CAAAC,0BAA0B,CAAC,CAAC,CACrD,GAAI,CAACD,UAAU,EAAI,CAACA,UAAU,CAACE,WAAW,EAAIF,UAAU,CAACE,WAAW,CAAC5K,MAAM,GAAK,CAAC,CAAE,CACjF;AACA,OACF,CACA;AACA,KAAM,CAAA6K,qBAAqB,CAACJ,SAAS,CAAC,CACxC,CAAC,IAAM,CACL;AACA,KAAM,CAAAhB,wBAAwB,CAAC,CAAC,CAChC,KAAM,CAAAoB,qBAAqB,CAACJ,SAAS,CAAC,CACxC,CACF,CAAE,MAAO3E,KAAK,CAAE,CACd;AAAA,CAEJ,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAgF,mBAAmB,CAAGA,CAAA,GAAc,CAC/C,MAAO,CAAAxM,YAAY,CAAC0B,MAAM,CAC5B,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAA+K,uBAAuB,CAAG,KAAAA,CAAA,GAA8B,CACnE,GAAI,CACF,KAAM,CAAAL,UAAU,CAAG,KAAM,CAAAC,0BAA0B,CAAC,CAAC,CACrD,MAAO,CAAAD,UAAU,EAAIA,UAAU,CAACE,WAAW,EAAIzD,KAAK,CAAC0C,OAAO,CAACa,UAAU,CAACE,WAAW,CAAC,EAAIF,UAAU,CAACE,WAAW,CAAC5K,MAAM,CAAG,CAAC,CAC3H,CAAE,MAAO8F,KAAK,CAAE,CACd,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAkF,mBAAmB,CAAG,KAAAA,CAAA,GAA2B,CAC5D3B,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC,CAC9D2B,gBAAgB,CAAC,CAAC,CAAE;AACpB,KAAM,CAAAC,2BAA2B,CAAC,CAAC,CAAE;AACrC/F,mBAAmB,CAAC,CAAC,CAAE;AACzB,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAgG,iBAAiB,CAAGA,CAAA,GAM5B,CACH,MAAO,CACLC,QAAQ,CAAE/M,iBAAiB,CAC3BgN,YAAY,CAAE/M,YAAY,CAAC0B,MAAM,CACjCsL,cAAc,CAAE9M,WAAW,CAAC+M,IAAI,CAChCC,aAAa,CAAEjN,qBAAqB,CACpCkN,wBAAwB,CAAE9M,oBAAoB,CAAC4M,IACjD,CAAC,CACH,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAG,uBAAuB,CAAGA,CAAA,GAAc,CACnD,MAAO,CAAAnN,qBAAqB,CAC9B,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAA0M,gBAAgB,CAAGA,CAAA,GAAY,CAC1C5B,OAAO,CAACC,GAAG,CAAC,kCAAkChL,YAAY,CAAC0B,MAAM,iBAAiBxB,WAAW,CAAC+M,IAAI,iBAAiB,CAAC,CACpHjN,YAAY,CAAG,EAAE,CACjBC,qBAAqB,CAAG,EAAE,CAC1BC,WAAW,CAAC8G,KAAK,CAAC,CAAC,CAAE;AACvB,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAA4F,2BAA2B,CAAG,KAAAA,CAAA,GAA2B,CAEpE,GAAI,CACIS,YAAY,CAACC,UAAU,CAAC,yBAAyB,CAAC,CAE1D,CAAE,MAAO9F,KAAK,CAAE,CACduD,OAAO,CAACvD,KAAK,CAAC,2CAA2C,CAAEA,KAAK,CAAC,CACnE,CACF,CAAC,CAED;AACA;AACA,GAEA,MAAO,MAAM,CAAA2D,wBAAwB,CAAG,KAAO,CAAAR,SAK9C,EAAoB,CACnB,GAAI,CACA,KAAM,CAAA4C,WAAW,CAAG,CAClBjB,WAAW,CAAEtM,YAAY,CACzBsJ,SAAS,CAAErJ,qBAAqB,CAChCwJ,GAAG,CAAEtD,MAAM,CAACuD,QAAQ,CAACC,IAAI,CACzBzF,KAAK,CAAEX,QAAQ,CAACW,KAAK,CACrB6I,YAAY,CAAE/M,YAAY,CAAC0B,MAAM,CACjC8L,SAAS,CAAExN,YAAY,CAACiM,GAAG,CAACrL,OAAO,GAAK,CACtC+G,KAAK,CAAE/G,OAAO,CAAC+G,KAAK,CACpB9G,OAAO,CAAED,OAAO,CAACC,OAAO,CACxBE,EAAE,CAAEH,OAAO,CAACG,EAAE,CACdK,SAAS,CAAER,OAAO,CAACQ,SAAS,CAC5BkH,IAAI,CAAE1H,OAAO,CAAC0H,IAAI,CAClBK,SAAS,CAAE/H,OAAO,CAAC+H,SAAS,CAC5Bb,WAAW,CAAElH,OAAO,CAACkH,WAAW,CAChCzF,QAAQ,CAAEzB,OAAO,CAACyB,QAAQ,CAC1BuG,UAAU,CAAEhI,OAAO,CAACgI,UAAU,CAC9BU,SAAS,CAAE1I,OAAO,CAAC0I,SAAS,CAC5BG,GAAG,CAAE7I,OAAO,CAAC6I,GAEf,CAAC,CAAC,CAAC,CACH;AACA,IAAIkB,SAAS,EAAI,CACfA,SAAS,CAAE,CACT8C,SAAS,CAAE9C,SAAS,CAACwB,SAAS,CAC9BuB,WAAW,CAAE/C,SAAS,CAACgD,gBAAgB,CACvCC,IAAI,CAAEjD,SAAS,CAACkD,SAAS,CACzBpE,GAAG,CAAEkB,SAAS,CAACmD,QACjB,CACF,CAAC,CACH,CAAC,CACHT,YAAY,CAACU,OAAO,CAAC,yBAAyB,CAAEC,IAAI,CAACC,SAAS,CAACV,WAAW,CAAC,CAAC,CAC7EA,WAAW,CAACC,SAAS,CAAC1G,OAAO,CAAC,CAACoH,IAAI,CAAEC,KAAK,GAAK,CAC1CpD,OAAO,CAACC,GAAG,CAAC,GAAGmD,KAAK,CAAG,CAAC,KAAKD,IAAI,CAACrN,OAAO,MAAMqN,IAAI,CAACvG,KAAK,EAAE,CAAC,CAC9D,CAAC,CAAC,CAEN,CAAE,MAAOH,KAAK,CAAE,CAChB,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAA6E,0BAA0B,CAAG,KAAAA,CAAA,GAA0B,CAClE,GAAI,CAEA,KAAM,CAAAjB,IAAI,CAAGiC,YAAY,CAACe,OAAO,CAAC,yBAAyB,CAAC,CAC5D,MAAO,CAAAhD,IAAI,CAAG4C,IAAI,CAACK,KAAK,CAACjD,IAAI,CAAC,CAAG,IAAI,CAEzC,CAAE,MAAO5D,KAAK,CAAE,CACd,MAAO,KAAI,CACb,CACF,CAAC,CAID;AACA;AACA,GACA,MAAO,MAAM,CAAA+E,qBAAqB,CAAG,KAAO,CAAAJ,SAAkB,EAAoB,CAChF,GAAI,CAEF,KAAM,CAAAC,UAAU,CAAG,KAAM,CAAAC,0BAA0B,CAAC,CAAC,CAErD,GAAI,CAACD,UAAU,CAAE,CACf,OACF,CAEA,KAAM,CAAAkC,OAAO,CAAG,CACdC,QAAQ,CAAE,CACR9E,GAAG,CAAE2C,UAAU,CAAC3C,GAAG,EAAItD,MAAM,CAACuD,QAAQ,CAACC,IAAI,CAC3CzF,KAAK,CAAEkI,UAAU,CAAClI,KAAK,EAAIX,QAAQ,CAACW,KAAK,CACzCoF,SAAS,CAAE8C,UAAU,CAAC9C,SAAS,EAAI,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC3DuD,YAAY,CAAEX,UAAU,CAACW,YAAY,EAAI,CAAC,CAC1CyB,UAAU,CAAE,GAAI,CAAAjF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACrC,CAAC,CACD0B,QAAQ,CAAEkB,UAAU,CAACE,WAAW,EAAI,EAAE,CACtCkB,SAAS,CAAEpB,UAAU,CAACoB,SAAS,EAAI,EACrC,CAAC,CAGD;AACA,KAAM,CAAAiB,gBAAgB,CAACH,OAAO,CAAEnC,SAAS,CAAC,CAE5C,CAAE,MAAO3E,KAAK,CAAE,CAChB,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAiH,gBAAgB,CAAG,KAAAA,CAAOrD,IAAS,CAAEe,SAAkB,GAAoB,CACtF,GAAI,CAEF;AACA,KAAM,CAAAuC,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAE/B;AACA,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACb,IAAI,CAACC,SAAS,CAAC7C,IAAI,CAAE,IAAI,CAAE,CAAC,CAAC,CAAC,CAAE,CACzDxD,IAAI,CAAE,kBACR,CAAC,CAAC,CAEF;AACA,KAAM,CAAA0B,SAAS,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACtG,OAAO,CAAC,OAAO,CAAE,GAAG,CAAC,CAChE,KAAM,CAAA4L,SAAS,CAAG,CAAC1D,IAAI,CAACmD,QAAQ,CAACrK,KAAK,EAAI,cAAc,EAAEhB,OAAO,CAAC,eAAe,CAAE,GAAG,CAAC,CACvF,KAAM,CAAA6L,QAAQ,CAAG,yBAAyBD,SAAS,IAAIxF,SAAS,OAAO,CAEvE;AACAoF,QAAQ,CAACrD,MAAM,CAAC,SAAS,CAAEuD,QAAQ,CAAEG,QAAQ,CAAC,CAAE;AAEhD;AACAL,QAAQ,CAACrD,MAAM,CAAC,cAAc,CAAED,IAAI,CAACmD,QAAQ,CAACxB,YAAY,CAACiC,QAAQ,CAAC,CAAC,CAAC,CACtEN,QAAQ,CAACrD,MAAM,CAAC,KAAK,CAAED,IAAI,CAACmD,QAAQ,CAAC9E,GAAG,CAAC,CACzCiF,QAAQ,CAACrD,MAAM,CAAC,WAAW,CAAED,IAAI,CAACmD,QAAQ,CAACjF,SAAS,CAAC,CAIrD;AACA,KAAM,CAAE2F,gBAAiB,CAAC,CAAG,KAAM,OAAM,CAAC,eAAe,CAAC,CAE1D,GAAI,CAAC9C,SAAS,CAAE,CACd,KAAM,IAAI,CAAA+C,KAAK,CAAC,6CAA6C,CAAC,CAChE,CAEA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAF,gBAAgB,CAAC9C,SAAS,CAAEuC,QAAQ,CAAC,CAE9D,CAAE,MAAOlH,KAAK,CAAE,CAEd;AACA,KAAM,CAAA4H,YAAY,CAAG5H,KAAK,WAAY,CAAA0H,KAAK,CAAG1H,KAAK,CAAC6H,OAAO,CAAG,wBAAwB,CAEtF,KAAM,CAAA7H,KAAK,CAAE;AACf,CACF,CAAC,CAED;AACA;AACA,GAEA,MAAO,MAAM,CAAA8H,kBAAkB,CAAG,KAAAA,CAAOzB,SAAiB,CAAEF,gBAAwB,CAAExB,SAAiB,CAAE2B,QAAgB,GAAoB,CAC3I,GAAI/N,iBAAiB,CAAE,OAEvBA,iBAAiB,CAAG,IAAI,CACxBgL,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC,CAE1E;AACA,KAAM,CAAAoB,UAAU,CAAG,KAAM,CAAAC,0BAA0B,CAAC,CAAC,CACrD,GAAI,CAACD,UAAU,EAAI,CAACA,UAAU,CAACE,WAAW,EAAI,CAACzD,KAAK,CAAC0C,OAAO,CAACa,UAAU,CAACE,WAAW,CAAC,EAAIF,UAAU,CAACE,WAAW,CAAC5K,MAAM,GAAK,CAAC,CAAE,CAC3HqJ,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC,CAC3E2B,gBAAgB,CAAC,CAAC,CAAE;AACpB,KAAM,CAAAxB,wBAAwB,CAAC,CAC7BgB,SAAS,CACTwB,gBAAgB,CAChBE,SAAS,CACTC,QACF,CAAC,CAAC,CACJ,CAAC,IAAM,CAEL/C,OAAO,CAACC,GAAG,CAAC,kCAAkCoB,UAAU,CAACE,WAAW,CAAC5K,MAAM,yBAAyB1B,YAAY,CAAC0B,MAAM,YAAY,CAAC,CACtI,CAEAqJ,OAAO,CAACC,GAAG,CAAC,sCAAsChL,YAAY,CAAC0B,MAAM,EAAE,CAAC,CACzE7B,iBAAiB,CAAG,CACjBsM,SAAS,CACTwB,gBAAgB,CAChBE,SAAS,CACTC,QACF,CAAC,CACD;AACA9N,YAAY,CAAC8G,OAAO,CAAClG,OAAO,EAAI,CAC9B,GAAIA,OAAO,CAAC+G,KAAK,CAAE,CACjB,GAAI,CACF,KAAM,CAAA4H,WAAW,CAAGhM,QAAQ,CAACiM,QAAQ,CACnC5O,OAAO,CAAC+G,KAAK,CACbpE,QAAQ,CACR,IAAI,CACJkM,WAAW,CAACC,uBAAuB,CACnC,IACF,CAAC,CAACC,eAA8B,CAEhC,GAAIJ,WAAW,CAAE,CACf1K,qCAAqC,CAAC0K,WAAW,CAAC,CACpD,CACF,CAAE,MAAO/H,KAAK,CAAE,CACd;AAAA,CAEJ,CACF,CAAC,CAAC,CAEF;AACA,GAAI,CAACpH,cAAc,CAAE,CACnBA,cAAc,CAAIsK,KAAiB,EAAK,CACtC;AAEAD,kBAAkB,CAACC,KAAK,CAAC7K,iBAAiB,CAAC,CAAC+P,KAAK,CAACpI,KAAK,EAAI,CACzDuD,OAAO,CAACvD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CAAC,CAAC,CACJ,CAAC,CACDjE,QAAQ,CAACkD,gBAAgB,CAAC,OAAO,CAAErG,cAAc,CAAE,IAAI,CAAC,CAAE;AAC5D,CAEA;AACA,GAAI,MAAO,CAAAyP,MAAM,GAAK,WAAW,EAAIA,MAAM,CAACC,OAAO,CAAE,CACnD,GAAI,CACFD,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC,CACzBC,MAAM,CAAE,oBACV,CAAC,CAAC,CACJ,CAAE,MAAOxI,KAAK,CAAE,CACd;AACArB,MAAM,CAAC8J,aAAa,CAAC,GAAI,CAAAC,WAAW,CAAC,iCAAiC,CAAC,CAAC,CAC1E,CACF,CAGA;AACAC,wBAAwB,CAAC,CAAC,CAC5B,CAAC,CAGD,MAAO,MAAM,CAAAC,aAAa,CAAGA,CAAA,GAAY,CACvC,GAAIrQ,iBAAiB,CAAE,OAEvBA,iBAAiB,CAAG,IAAI,CACxB4M,gBAAgB,CAAC,CAAC,CAGlB;AACA,GAAI,CAACvM,cAAc,CAAE,CACnBA,cAAc,CAAGqK,kBAAkB,CACnClH,QAAQ,CAACkD,gBAAgB,CAAC,OAAO,CAAErG,cAAc,CAAE,IAAI,CAAC,CAAE;AAC5D,CAEA;AACA,GAAI,MAAO,CAAAyP,MAAM,GAAK,WAAW,EAAIA,MAAM,CAACC,OAAO,CAAE,CACnD,GAAI,CACFD,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC,CACzBC,MAAM,CAAE,oBACV,CAAC,CAAC,CACJ,CAAE,MAAOxI,KAAK,CAAE,CACd;AACArB,MAAM,CAAC8J,aAAa,CAAC,GAAI,CAAAC,WAAW,CAAC,iCAAiC,CAAC,CAAC,CAC1E,CACF,CAEA;AACAC,wBAAwB,CAAC,CAAC,CAC5B,CAAC,CAOD;AACA;AACA,GACA,MAAO,MAAM,CAAAE,YAAY,CAAG,KAAAA,CAC1BC,eAAwB,CACxBnE,SAAiB,CACjB0B,SAAkB,CAClBF,gBAAyB,CACzBG,QAAiB,GAEC,CAClB,GAAI,CAAC/N,iBAAiB,CAAE,OACxBA,iBAAiB,CAAG,KAAK,CAGzB;AACA,GAAIK,cAAc,CAAE,CAClBmD,QAAQ,CAACgN,mBAAmB,CAAC,OAAO,CAAEnQ,cAAc,CAAE,IAAI,CAAC,CAC3DA,cAAc,CAAG,IAAI,CACvB,CAEA;AACA,GAAIJ,YAAY,CAAC0B,MAAM,CAAG,CAAC,CAAE,CAG3B;AACC,KAAM,CAAAyJ,wBAAwB,CAAC,CAC9BgB,SAAS,CACTwB,gBAAgB,CACfE,SAAS,CACVC,QACF,CAAC,CAAC,CAIF;AACA,CAACwC,eAAe,GAAI,KAAM,CAAA/D,qBAAqB,CAACJ,SAAS,CAAC,EAC3D,KAAM,CAAAqE,YAAY,CAAG,KAAM,CAAAC,sBAAsB,CAAC,CAAC,CACnD1F,OAAO,CAACC,GAAG,CAACwF,YAAY,CAAC,cAAc,CAAC,CAEvC,KAAM,CAAAE,KAAK,CAAG,CACZjD,SAAS,CAACtB,SAAS,CACnBuB,WAAW,CAACC,gBAAgB,CAC5BC,IAAI,CAACC,SAAS,CACd8C,cAAc,CAACH,YAAY,CAC3B/G,GAAG,CAAEtD,MAAM,CAACuD,QAAQ,CAACC,IACvB,CAAC,CACD,GAAI2G,eAAe,EAAIzC,SAAS,EAAIF,gBAAgB,CAAE,CACpD,KAAM,CAAAlO,gBAAgB,CAACiR,KAAK,CAAC,CAC/B,CACF,CAEG,KAAM,CAAA9D,2BAA2B,CAAC,CAAC,CAGtC;AACA/F,mBAAmB,CAAC,CAAC,CAErB;AACA,GAAI,MAAO,CAAAgJ,MAAM,GAAK,WAAW,EAAIA,MAAM,CAACC,OAAO,CAAE,CACnD,GAAI,CACFD,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC,CACzBC,MAAM,CAAE,mBACV,CAAC,CAAC,CACJ,CAAE,MAAOxI,KAAK,CAAE,CACd;AACArB,MAAM,CAAC8J,aAAa,CAAC,GAAI,CAAAC,WAAW,CAAC,gCAAgC,CAAC,CAAC,CACzE,CACF,CAEA;AACAU,wBAAwB,CAAC,CAAC,CAC5B,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAC,cAAc,CAAG,KAAAA,CAAA,GAA2B,CACvD,GAAI,CAAC9Q,iBAAiB,CAAE,OACxBA,iBAAiB,CAAG,KAAK,CAEzB;AACA,GAAIK,cAAc,CAAE,CAClBmD,QAAQ,CAACgN,mBAAmB,CAAC,OAAO,CAAEnQ,cAAc,CAAE,IAAI,CAAC,CAC3DA,cAAc,CAAG,IAAI,CACvB,CAEA;AACA,KAAM,CAAAwM,2BAA2B,CAAC,CAAC,CACnC7B,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC,CAEzE;AACA2B,gBAAgB,CAAC,CAAC,CAElB;AACA9F,mBAAmB,CAAC,CAAC,CAErB;AACA,GAAI,MAAO,CAAAgJ,MAAM,GAAK,WAAW,EAAIA,MAAM,CAACC,OAAO,CAAE,CACnD,GAAI,CACFD,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC,CACzBC,MAAM,CAAE,mBACV,CAAC,CAAC,CACJ,CAAE,MAAOxI,KAAK,CAAE,CACd;AACArB,MAAM,CAAC8J,aAAa,CAAC,GAAI,CAAAC,WAAW,CAAC,gCAAgC,CAAC,CAAC,CACzE,CACF,CAEA;AACAU,wBAAwB,CAAC,CAAC,CAC5B,CAAC,CAED,MAAO,MAAM,CAAAH,sBAAsB,CAAG,KAAAA,CAAA,GAAuC,CAC3E,KAAM,CAAArE,UAAU,CAAG,KAAM,CAAAC,0BAA0B,CAAC,CAAC,CACrD,GAAI,CAACD,UAAU,EAAI,CAACvD,KAAK,CAAC0C,OAAO,CAACa,UAAU,CAACE,WAAW,CAAC,CAAE,MAAO,EAAE,CAEpE,MAAO,CAAAF,UAAU,CAACE,WAAW,CAACL,GAAG,CAAEiC,IAAS,EAAK,CAC/C;AACA,GAAI,CAAA4C,eAAe,CAAG5C,IAAI,CAACvG,KAAK,EAAI,EAAE,CACtC,GAAI,CAAAoJ,gBAAgB,CAAG7C,IAAI,CAACpG,WAAW,EAAI,EAAE,CAE7C;AACA,GAAI,CAACgJ,eAAe,EAAI5C,IAAI,CAACvF,SAAS,CAAE,CACtCmI,eAAe,CAAG,gBAAgB5C,IAAI,CAACvF,SAAS,IAAI,CAAE;AACtD;AACA,GAAI,CAACmI,eAAe,CAAE,CACpBA,eAAe,CAAG,WAAW5C,IAAI,CAACvF,SAAS,IAAI,CAAE;AACnD,CACA,GAAI,CAACmI,eAAe,CAAE,CACpBA,eAAe,CAAG,iBAAiB5C,IAAI,CAACvF,SAAS,IAAI,CAAE;AACzD,CACA,GAAI,CAACmI,eAAe,CAAE,CACpBA,eAAe,CAAG,UAAU5C,IAAI,CAACvF,SAAS,IAAI,CAAE;AAClD,CACF,CAEA;AACA,GAAI,CAACmI,eAAe,CAAE,CACpBA,eAAe,CAAGC,gBAAgB,CACpC,CAEA,MAAO,CACLnD,IAAI,CAACM,IAAI,CAAC5F,IAAI,EAAI,EAAE,CACpBX,KAAK,CAAEuG,IAAI,CAACvG,KAAK,EAAI,EAAE,CACvBgB,SAAS,CAAEuF,IAAI,CAACvF,SAAS,EAAK,EAAE,CAChCtG,QAAQ,CAAEyO,eAAe,CACzBhJ,WAAW,CAAEoG,IAAI,CAACpG,WAAW,EAAI,EAAE,CACnCvD,KAAK,CAAE2J,IAAI,CAAC3J,KAAK,EAAI,EAAE,CACvBqD,IAAI,CAAEsG,IAAI,CAACtG,IAAI,EAAI,EACrB,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAuI,wBAAwB,CAAGA,CAAA,GAAY,CAC3C;AACAS,wBAAwB,CAAC,CAAC,CAE1B,KAAM,CAAAI,cAAc,CAAGzN,QAAQ,CAAC+B,aAAa,CAAC,KAAK,CAAC,CACpD0L,cAAc,CAACjQ,EAAE,CAAG,kCAAkC,CACtDiQ,cAAc,CAAClM,KAAK,CAACS,OAAO,CAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,CAED;AACA,KAAM,CAAA0L,SAAS,CAAG,MAAMtR,CAAC,CAAC,uBAAuB,CAAC,EAAE,CACpD,KAAM,CAAAuR,YAAY,CAAG,KAAKvR,CAAC,CAAC,4CAA4C,CAAC,EAAE,CAC3E,KAAM,CAAAwR,WAAW,CAAG,KAAKxR,CAAC,CAAC,mDAAmD,CAAC,EAAE,CACjF,KAAM,CAAAyR,aAAa,CAAG,KAAKzR,CAAC,CAAC,0CAA0C,CAAC,EAAE,CAC1E,KAAM,CAAA0R,UAAU,CAAG,KAAK1R,CAAC,CAAC,mCAAmC,CAAC,EAAE,CAChE,KAAM,CAAA2R,SAAS,CAAG,KAAK3R,CAAC,CAAC,iCAAiC,CAAC,EAAE,CAE7DqR,cAAc,CAACO,SAAS,CAAG;AAC7B;AACA,MAAMN,SAAS;AACf;AACA,QAAQC,YAAY;AACpB,QAAQC,WAAW;AACnB,QAAQC,aAAa;AACrB,QAAQC,UAAU;AAClB,QAAQC,SAAS;AACjB;AACA,GAAG,CAED/N,QAAQ,CAACgD,IAAI,CAACC,WAAW,CAACwK,cAAc,CAAC,CAEzC;AACA1J,UAAU,CAAC,IAAM,CACf,GAAI0J,cAAc,CAAC/J,UAAU,CAAE,CAC7B+J,cAAc,CAAClM,KAAK,CAACyC,OAAO,CAAG,KAAK,CACtC,CACF,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAqJ,wBAAwB,CAAGA,CAAA,GAAY,CAC3C,KAAM,CAAAY,mBAAmB,CAAGjO,QAAQ,CAACkO,cAAc,CAAC,kCAAkC,CAAC,CACvF,GAAID,mBAAmB,CAAE,CACvBA,mBAAmB,CAACE,MAAM,CAAC,CAAC,CAC9B,CACF,CAAC,CAGD;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,mBAAmB,CAAG,KAAAA,CAAA,GAA2B,CAC5D5R,iBAAiB,CAAG,KAAK,CACzBC,YAAY,CAAG,EAAE,CACjBE,WAAW,CAAC8G,KAAK,CAAC,CAAC,CACnB/G,qBAAqB,CAAG,EAAE,CAC1BG,cAAc,CAAG,IAAI,CAErB;AACA,GAAI,CACF,KAAM,CAAAgM,UAAU,CAAG,KAAM,CAAAC,0BAA0B,CAAC,CAAC,CACrD,GAAID,UAAU,EAAIA,UAAU,CAACE,WAAW,EAAIzD,KAAK,CAAC0C,OAAO,CAACa,UAAU,CAACE,WAAW,CAAC,CAAE,CACjFtM,YAAY,CAAGoM,UAAU,CAACE,WAAW,CACrCrM,qBAAqB,CAAGmM,UAAU,CAAC9C,SAAS,EAAI,EAAE,CAElD;AACAtJ,YAAY,CAAC8G,OAAO,CAAClG,OAAO,EAAI,CAC9B,GAAIA,OAAO,CAAC+G,KAAK,CAAE,CACjBzH,WAAW,CAACuL,GAAG,CAAC7K,OAAO,CAAC+G,KAAK,CAAE/G,OAAO,CAAC,CACzC,CACF,CAAC,CAAC,CAEFmK,OAAO,CAACC,GAAG,CAAC,eAAehL,YAAY,CAAC0B,MAAM,mDAAmD,CAAC,CAElG;AACA1B,YAAY,CAAC8G,OAAO,CAAClG,OAAO,EAAI,CAC9B,GAAIA,OAAO,CAAC+G,KAAK,CAAE,CACjB,GAAI,CACF,KAAM,CAAA4H,WAAW,CAAGhM,QAAQ,CAACiM,QAAQ,CACnC5O,OAAO,CAAC+G,KAAK,CACbpE,QAAQ,CACR,IAAI,CACJkM,WAAW,CAACC,uBAAuB,CACnC,IACF,CAAC,CAACC,eAA8B,CAEhC,GAAIJ,WAAW,CAAE,CACf1K,qCAAqC,CAAC0K,WAAW,CAAC,CACpD,CACF,CAAE,MAAO/H,KAAK,CAAE,CACd;AAAA,CAEJ,CACF,CAAC,CAAC,CACJ,CACF,CAAE,MAAOA,KAAK,CAAE,CACduD,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC,CAC7E,CAEA;AACA,GAAI,MAAO,CAAA6E,MAAM,GAAK,WAAW,EAAIA,MAAM,CAACC,OAAO,EAAID,MAAM,CAACC,OAAO,CAAC8B,SAAS,CAAE,CAC/E;AACA/B,MAAM,CAACC,OAAO,CAAC8B,SAAS,CAACC,WAAW,CAAC,CAACxC,OAAO,CAAEyC,OAAO,CAAEC,YAAY,GAAK,CACvE,GAAI1C,OAAO,CAACW,MAAM,GAAK,qBAAqB,CAAE,CAC5CjQ,iBAAiB,CAAGsP,OAAO,CAACvC,QAAQ,CACpCiF,YAAY,CAAC,CAAEC,OAAO,CAAE,IAAK,CAAC,CAAC,CAC/B,MAAO,KAAI,CACb,CAEA,GAAI3C,OAAO,CAACW,MAAM,GAAK,kBAAkB,CAAE,CACzC+B,YAAY,CAAC,CACXjF,QAAQ,CAAE/M,iBAAiB,CAC3BmN,aAAa,CAAEjN,qBAAqB,CACpC8M,YAAY,CAAE/M,YAAY,CAAC0B,MAC7B,CAAC,CAAC,CACF,MAAO,KAAI,CACb,CAEA,GAAI2N,OAAO,CAACW,MAAM,GAAK,gBAAgB,CAAE,CACvC+B,YAAY,CAAC,CACX3G,IAAI,CAAEpL,YAAY,CAClBsJ,SAAS,CAAErJ,qBACb,CAAC,CAAC,CACF,MAAO,KAAI,CACb,CAEA,GAAIoP,OAAO,CAACW,MAAM,GAAK,kBAAkB,CAAE,CACzCrD,gBAAgB,CAAC,CAAC,CAClBoF,YAAY,CAAC,CAAEC,OAAO,CAAE,IAAK,CAAC,CAAC,CAC/B,MAAO,KAAI,CACb,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACP,CACF,CAAC,CAGD;AACAL,mBAAmB,CAAC,CAAC,CAAC/B,KAAK,CAACpI,KAAK,EAAI,CACnCuD,OAAO,CAACvD,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAC,CAC9D,CAAC,CAAC,CAEF;AACA;AACA;AACA,GACA,MAAO,SAAS,CAAAyK,wBAAwBA,CAACrR,OAAY,CAQnD,CACA,MAAO,CACL+G,KAAK,CAAE/G,OAAO,CAAC+G,KAAK,EAAI,EAAE,CAC1BgB,SAAS,CAAE/H,OAAO,CAAC+H,SAAS,EAAI/H,OAAO,CAAC0H,IAAI,EAAI,EAAE,CAClDR,WAAW,CAAElH,OAAO,CAACkH,WAAW,EAAI,EAAE,CACtCzF,QAAQ,CAAEzB,OAAO,CAACyB,QAAQ,EAAIzB,OAAO,CAAC+G,KAAK,EAAI/G,OAAO,CAACkH,WAAW,EAAI,EAAE,CACxEvD,KAAK,CAAE3D,OAAO,CAAC2D,KAAK,CACpB+D,IAAI,CAAE1H,OAAO,CAAC0H,IAAI,EAAI,EAAE,CACxBvH,EAAE,CAAEH,OAAO,CAACG,EAAE,EAAI,EACpB,CAAC,CACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}