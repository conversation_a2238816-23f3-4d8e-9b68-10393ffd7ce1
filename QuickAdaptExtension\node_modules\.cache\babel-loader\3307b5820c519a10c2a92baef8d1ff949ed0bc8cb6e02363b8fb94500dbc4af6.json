{"ast": null, "code": "import React,{useEffect,useRef,useState,useMemo}from\"react\";import{Box,IconButton,Paper,Popover,Tooltip,Typography,LinearProgress,MobileStepper,Breadcrumbs}from\"@mui/material\";import AddIcon from\"@mui/icons-material/Add\";import{Image,TextFormat,Link}from\"@mui/icons-material\";import useDrawerStore from\"../../store/drawerStore\";import ButtonSection from\"./components/Buttons\";import ImageSection from\"./components/ImageSection\";import RTEsection from\"./components/RTE/RTESection\";import{createPortal}from\"react-dom\";import CloseIcon from\"@mui/icons-material/Close\";import JoditEditor from\"jodit-react\";import AlertPopup from\"../drawer/AlertPopup\";import PerfectScrollbar from'react-perfect-scrollbar';import\"react-perfect-scrollbar/dist/css/styles.css\";import{useTranslation}from\"react-i18next\";import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";// Maximum allowed sections of each type\nconst MAX_SECTIONS={image:3,button:3,rte:3};const TooltipBody=_ref=>{var _toolTipGuideMetaData4,_toolTipGuideMetaData5,_boxRef$current;let{isPopoverOpen,setIsPopoverOpen,popupPosition,isUnSavedChanges,openWarning,setopenWarning,handleLeave,updatedGuideData}=_ref;const{t:translate}=useTranslation();const{toolTipGuideMetaData,currentStep,handleTooltipRTEValue,handleRTEDeleteSection,handleRTECloneSection,tooltip,tooltipBackgroundcolor,openTooltip,selectedOption,selectedTemplate,selectedTemplateTour,setTooltipPositionByXpath,width,borderRadius,Annpadding,borderColor,tooltipborderradius,tooltipbordersize,tooltipBordercolor,tooltipPosition,tooltipWidth,tooltippadding,AnnborderSize,currentStepIndex,dismissData,steps,setCurrentHoveredElement,elementClick,dismiss,setDismiss,progress,setProgress,ProgressColor,setProgressColor}=useDrawerStore(state=>state);const[anchorEl,setAnchorEl]=useState(null);const boxRef=useRef(new Map());const[savedRange,setSaveRange]=useState(undefined);const[currentIndex,setCurrentIndex]=useState(0);const[currentRTEFocusedId,setCurrentRTEFocusedId]=useState(\"\");const[currentFocusedType,setCurrentFocusedType]=useState();const[isEditorFocused,setIsEditorFocused]=useState(false);// State to track if scrolling is needed\nconst[needsScrolling,setNeedsScrolling]=useState(false);const contentRef=useRef(null);const scrollbarRef=useRef(null);// Section count tracking for limits\nconst[sectionCounts,setSectionCounts]=useState({image:0,button:0,rte:0});// Helper function to check if a section type has reached its limit\nconst hasReachedLimit=type=>{// Map the section type to the corresponding key in sectionCounts\nconst countType=type===\"rte\"?\"rte\":type===\"button\"?\"button\":type===\"image\"?\"image\":null;// If the type is not supported, return false\nif(countType===null)return false;return sectionCounts[countType]>=MAX_SECTIONS[countType];};const handleAddIconClick=(event,idx)=>{if(hasReachedLimit(\"rte\")&&hasReachedLimit(\"button\")&&hasReachedLimit(\"image\")){return;}const currentTarget=event.currentTarget;setTimeout(()=>{setCurrentIndex(idx);setAnchorEl(currentTarget);},0);};const handlePopoverClose=()=>{setAnchorEl(null);};const handleFocus=id=>{setIsPopoverOpen(true);setCurrentRTEFocusedId(id);};const handleBlur=id=>{var _boxRef$current$get,_boxRef$current$get$c;if(boxRef!==null&&boxRef!==void 0&&(_boxRef$current$get=boxRef.current.get(id))!==null&&_boxRef$current$get!==void 0&&(_boxRef$current$get$c=_boxRef$current$get.current)!==null&&_boxRef$current$get$c!==void 0&&_boxRef$current$get$c.innerHTML){var _boxRef$current$get2,_boxRef$current$get2$;handleTooltipRTEValue(id,((_boxRef$current$get2=boxRef.current.get(id))===null||_boxRef$current$get2===void 0?void 0:(_boxRef$current$get2$=_boxRef$current$get2.current)===null||_boxRef$current$get2$===void 0?void 0:_boxRef$current$get2$.innerHTML.trim())||\"\");}};const handleDeleteSection=()=>{handleRTEDeleteSection(currentRTEFocusedId);};const handleCloneContainer=()=>{handleRTECloneSection(currentRTEFocusedId);};const[sections,setSections]=useState([{type:\"image\"},{type:\"text\"},{type:\"button\"}]);const[draggingIndex,setDraggingIndex]=useState(null);const[draggedIndex,setDraggedIndex]=useState(null);const handleDragStart=index=>{setDraggingIndex(index);};const handleDragEnter=index=>{if(draggingIndex!==null&&draggingIndex!==index){const reorderedSections=[...sections];const[removed]=reorderedSections.splice(draggingIndex,1);reorderedSections.splice(index,0,removed);setSections(reorderedSections);setDraggingIndex(index);}};const handleDragEnd=()=>{setDraggingIndex(null);};const addToBoxRef=id=>{if(!boxRef.current.has(id)){const newRef=/*#__PURE__*/React.createRef();boxRef.current.set(id,newRef);return newRef;}return boxRef.current.get(id);};// Update section counts when the component mounts or when containers change\nuseEffect(()=>{var _toolTipGuideMetaData;if(toolTipGuideMetaData&&(_toolTipGuideMetaData=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData!==void 0&&_toolTipGuideMetaData.containers){const containers=toolTipGuideMetaData[currentStep-1].containers;const counts={image:0,button:0,rte:0};// Count each type of section\ncontainers.forEach(container=>{if(container.type===\"image\")counts.image++;else if(container.type===\"button\")counts.button++;else if(container.type===\"rte\")counts.rte++;});setSectionCounts(counts);}},[toolTipGuideMetaData,currentStep]);// Check if content needs scrolling with improved detection\nuseEffect(()=>{const checkScrollNeeded=()=>{if(contentRef.current){// Force a reflow to get accurate measurements\ncontentRef.current.style.height='auto';const contentHeight=contentRef.current.scrollHeight;const containerHeight=320;// max-height value\nconst shouldScroll=contentHeight>containerHeight;setNeedsScrolling(shouldScroll);// Force update scrollbar\nif(scrollbarRef.current){// Try multiple methods to update the scrollbar\nif(scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}// Force re-initialization if needed\nsetTimeout(()=>{if(scrollbarRef.current&&scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}},10);}}};checkScrollNeeded();const timeouts=[setTimeout(checkScrollNeeded,50),setTimeout(checkScrollNeeded,100),setTimeout(checkScrollNeeded,200),setTimeout(checkScrollNeeded,500)];let resizeObserver=null;let mutationObserver=null;if(contentRef.current&&window.ResizeObserver){resizeObserver=new ResizeObserver(()=>{setTimeout(checkScrollNeeded,10);});resizeObserver.observe(contentRef.current);}if(contentRef.current&&window.MutationObserver){mutationObserver=new MutationObserver(()=>{setTimeout(checkScrollNeeded,10);});mutationObserver.observe(contentRef.current,{childList:true,subtree:true,attributes:true,attributeFilter:['style','class']});}return()=>{timeouts.forEach(clearTimeout);if(resizeObserver){resizeObserver.disconnect();}if(mutationObserver){mutationObserver.disconnect();}};},[toolTipGuideMetaData,currentStep]);useEffect(()=>{if(openTooltip){if(typeof window!==\"undefined\"){var _toolTipGuideMetaData2,_toolTipGuideMetaData3;const xpath=(_toolTipGuideMetaData2=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData2===void 0?void 0:(_toolTipGuideMetaData3=_toolTipGuideMetaData2.xpath)===null||_toolTipGuideMetaData3===void 0?void 0:_toolTipGuideMetaData3.value;// Check if XPath is valid and not empty\nif(xpath&&xpath.trim()!==\"\"){const result=window.document.evaluate(xpath,window.document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null);const element=result.singleNodeValue;if(element){var _element$textContent;setCurrentHoveredElement(element);const rect=element.getBoundingClientRect();const info={element,tagName:element.tagName,classes:element.className,id:element.id,position:{x:rect.x,y:rect.y,width:rect.width,height:rect.height},textContent:((_element$textContent=element.textContent)===null||_element$textContent===void 0?void 0:_element$textContent.trim())||\"\"};setTooltipPositionByXpath(info);}}}setTimeout(()=>{let popup=document.querySelector(\".MuiTooltip-popperInteractive\");if(popup instanceof HTMLElement&&popupPosition){//popup.style.top = `${popupPosition.top || \"20px\"}`;\n//popup.style.left = `${popupPosition.left || \"10px\"}`; // Example for setting the left position\n}},10);}},[openTooltip]);const canvasProperties=(_toolTipGuideMetaData4=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData4===void 0?void 0:_toolTipGuideMetaData4.canvas;return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{placeContent:\"end\",display:\"flex\"},children:!isPopoverOpen&&dismiss&&/*#__PURE__*/_jsx(IconButton,{className:\"qadpt-dismiss\",\"aria-label\":\"close\",children:/*#__PURE__*/_jsx(CloseIcon,{sx:{zoom:\"1\",color:\"#000\"}})})}),/*#__PURE__*/_jsx(PerfectScrollbar,{ref:scrollbarRef,style:{maxHeight:\"320px\"},options:{suppressScrollY:!needsScrolling,suppressScrollX:true,wheelPropagation:false,swipeEasing:true,minScrollbarLength:20,scrollingThreshold:1000,scrollYMarginOffset:0},children:/*#__PURE__*/_jsxs(\"div\",{ref:contentRef,style:{// backgroundColor: canvasProperties?.backgroundColor || \"white\",\n// padding: canvasProperties?.padding || \"4px\",\n// width: `${toolTipGuideMetaData[currentStep - 1]?.canvas.width} !important` || \"500px !important\",\n// borderRadius: canvasProperties?.borderRadius || \"8px\",\n// minWidth: TOOLTIP_MN_WIDTH,\n// maxWidth: TOOLTIP_MX_WIDTH,\nminHeight:60,// maxHeight: 320,\n// height: selectedTemplate === \"Tooltip\" ? \"auto\" : \"350px\",\noverflow:\"hidden\",position:\"relative\"},children:[(_toolTipGuideMetaData5=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData5===void 0?void 0:_toolTipGuideMetaData5.containers.map((item,index)=>{const id=`${item.type}|${item.id}`;const handleDragStart=index=>{if(draggedIndex===index)return;// Prevent redundant drag starts\nsetDraggedIndex(index);};const handleDragEnter=index=>{if(draggedIndex!==null&&draggedIndex!==index){const updatedContainers=[...toolTipGuideMetaData[currentStep-1].containers];const[draggedItem]=updatedContainers.splice(draggedIndex,1);updatedContainers.splice(index,0,draggedItem);// Update state only if the order has changed\nuseDrawerStore.setState({toolTipGuideMetaData:[{...toolTipGuideMetaData[currentStep-1],containers:updatedContainers}]});setDraggedIndex(index);}};const handleDragEnd=()=>{setDraggedIndex(null);};return/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(\"div\",{onMouseEnter:e=>{setCurrentFocusedType(e.currentTarget.id);},style:{position:\"relative\",padding:\"7px 0\",borderBottomWidth:currentFocusedType===id?\"1px\":\"0px\",borderBottomColor:currentFocusedType===id?\"var(--primarycolor)\":\"transparent\",borderBottomStyle:currentFocusedType===id?\"dotted\":\"none\",marginBottom:\"10px\"},id:id,draggable:item.type!==\"rte\",onDragStart:()=>handleDragStart(index),onDragEnter:()=>handleDragEnter(index),onDragEnd:handleDragEnd,children:item.type===\"button\"?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(ButtonSection,{items:item,updatedGuideData:updatedGuideData,isCloneDisabled:hasReachedLimit(\"button\")}),currentFocusedType===id&&/*#__PURE__*/_jsx(AddSectionComp,{handleAddIconClick:e=>handleAddIconClick(e,index)})]}):item.type===\"image\"?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(ImageSection,{items:item,isCloneDisabled:hasReachedLimit(\"image\")}),currentFocusedType===id&&/*#__PURE__*/_jsx(AddSectionComp,{handleAddIconClick:e=>handleAddIconClick(e,index)})]}):item.type===\"rte\"?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(RTEsection,{items:item//@ts-ignore\n,boxRef:addToBoxRef(item.id),handleFocus:handleFocus,handleeBlur:handleBlur,isPopoverOpen:isPopoverOpen,setIsPopoverOpen:setIsPopoverOpen,currentRTEFocusedId:currentRTEFocusedId,isCloneDisabled:hasReachedLimit(\"rte\")}),currentFocusedType===id&&/*#__PURE__*/_jsx(AddSectionComp,{handleAddIconClick:e=>handleAddIconClick(e,index)})]}):null})});}),/*#__PURE__*/_jsx(SectionPopOver,{anchorEl:anchorEl,handlePopoverClose:handlePopoverClose,currentIndex:currentIndex,hasReachedLimit:hasReachedLimit}),isUnSavedChanges&&openWarning&&/*#__PURE__*/_jsx(AlertPopup,{openWarning:openWarning,setopenWarning:setopenWarning,handleLeave:handleLeave}),isPopoverOpen&&currentRTEFocusedId&&(boxRef===null||boxRef===void 0?void 0:(_boxRef$current=boxRef.current)===null||_boxRef$current===void 0?void 0:_boxRef$current.get(currentRTEFocusedId))&&/*#__PURE__*/_jsx(RTEToolbar,{isPopoverOpen:isPopoverOpen,setIsPopoverOpen:setIsPopoverOpen,boxRef:boxRef.current.get(currentRTEFocusedId),currentRTEFocusedId:currentRTEFocusedId,handleDeleteSection:handleDeleteSection,handleCloneContainer:handleCloneContainer,savedRange:savedRange,setSaveRange:setSaveRange,tooltip:tooltip,popupPosition:popupPosition,handleTooltipRTEValue:handleTooltipRTEValue,iseditorfocused:isEditorFocused,setIsEditorFocused:setIsEditorFocused,canvasproperties:canvasProperties,isCloneDisabled:hasReachedLimit(\"rte\")})]})},`scrollbar-${needsScrolling}`),progress&&toolTipGuideMetaData.length>1&&(selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\"||selectedTemplateTour===\"Hotspot\")&&(selectedOption===1||selectedOption===\"\"?/*#__PURE__*/_jsx(DotsStepper,{activeStep:currentStep,steps:steps.length,ProgressColor:ProgressColor}):selectedOption===2?/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\"//sx={{paddingTop:\"15px\",padding:\"8px\"}}\n,value:currentStep/steps.length*100,sx:{height:\"6px\",borderRadius:\"20px\",margin:\"6px 10px\",'& .MuiLinearProgress-bar':{backgroundColor:ProgressColor// progress bar color\n}}})}):selectedOption===3?/*#__PURE__*/_jsx(\"div\",{style:{padding:\"8px\"},children:/*#__PURE__*/_jsx(BreadCrumpStepper,{activeStep:currentStep,steps:steps.length,ProgressColor:ProgressColor})}):selectedOption===4?/*#__PURE__*/_jsx(Breadcrumbs,{\"aria-label\":\"breadcrumb\",sx:{padding:\"8px\"},children:/*#__PURE__*/_jsxs(Typography,{sx:{color:ProgressColor},children:[\"Step \",currentStep,\" of \",steps.length]})}):null)]});};export default TooltipBody;const AddSectionComp=_ref2=>{let{handleAddIconClick}=_ref2;return/*#__PURE__*/_jsx(Box,{sx:{position:\"absolute\",transform:\"translate(-50%,-50%)\",top:\"99%\",left:\"50%\"},children:/*#__PURE__*/_jsx(IconButton,{onClick:handleAddIconClick,sx:{backgroundColor:\"#5F9EA0\",\"&:hover\":{backgroundColor:\"#70afaf\"},borderRadius:\"8px\",padding:\"5px !important\"},children:/*#__PURE__*/_jsx(AddIcon,{fontSize:\"small\",sx:{color:\"#fff\"}})})});};const SectionPopOver=_ref3=>{let{anchorEl,handlePopoverClose,currentIndex,hasReachedLimit}=_ref3;const{t:translate}=useTranslation();const{createTooltipSections,selectedOption,currentStep,steps,currentStepIndex}=useDrawerStore(state=>state);const handleAddSection=sectionType=>{// Don't add if limit is reached\nif(hasReachedLimit(sectionType)){return;}createTooltipSections(sectionType,currentIndex);handlePopoverClose();};return/*#__PURE__*/_jsx(_Fragment,{children:anchorEl&&/*#__PURE__*/_jsx(Popover,{open:Boolean(anchorEl),anchorEl:anchorEl,onClose:handlePopoverClose,anchorOrigin:{vertical:\"bottom\",horizontal:\"center\"},transformOrigin:{vertical:\"top\",horizontal:\"center\"},id:\"tooltip-section-popover\",slotProps:{paper:{sx:{padding:\"12px\",display:\"flex\",gap:\"16px\",width:\"auto\"}},root:{// instead of writing sx on popover write here it also target to root and more clear\nsx:{zIndex:theme=>theme.zIndex.tooltip+1000}}},disableEnforceFocus:true,children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"row\",gap:\"16px\",children:[/*#__PURE__*/_jsx(Tooltip,{title:hasReachedLimit(\"rte\")?translate(\"Maximum limit of 3 Rich Text sections reached\",{defaultValue:\"Maximum limit of 3 Rich Text sections reached\"}):\"\",PopperProps:{sx:{zIndex:9999}},children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"column\",alignItems:\"center\",sx:{cursor:hasReachedLimit(\"rte\")?\"not-allowed\":\"pointer\",opacity:hasReachedLimit(\"rte\")?0.5:1,svg:{fontSize:\"24px !important\"}},onClick:()=>!hasReachedLimit(\"rte\")&&handleAddSection(\"rte\"),children:[/*#__PURE__*/_jsx(TextFormat,{}),/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"11px !important\"},children:translate(\"Rich Text\",{defaultValue:\"Rich Text\"})})]})}),/*#__PURE__*/_jsx(Tooltip,{title:hasReachedLimit(\"button\")?translate(\"Maximum limit of 3 Button sections reached\",{defaultValue:\"Maximum limit of 3 Button sections reached\"}):\"\",PopperProps:{sx:{zIndex:9999}},children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"column\",alignItems:\"center\",sx:{cursor:hasReachedLimit(\"button\")?\"not-allowed\":\"pointer\",opacity:hasReachedLimit(\"button\")?0.5:1,svg:{fontSize:\"24px !important\"}},onClick:()=>!hasReachedLimit(\"button\")&&handleAddSection(\"button\"),children:[/*#__PURE__*/_jsx(Link,{}),/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"11px !important\"},children:translate(\"Button\",{defaultValue:\"Button\"})})]})}),/*#__PURE__*/_jsx(Tooltip,{title:hasReachedLimit(\"image\")?translate(\"Maximum limit of 3 Image sections reached\",{defaultValue:\"Maximum limit of 3 Image sections reached\"}):\"\",PopperProps:{sx:{zIndex:9999}},children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"column\",alignItems:\"center\",sx:{cursor:hasReachedLimit(\"image\")?\"not-allowed\":\"pointer\",opacity:hasReachedLimit(\"image\")?0.5:1,svg:{fontSize:\"24px !important\"}},onClick:()=>!hasReachedLimit(\"image\")&&handleAddSection(\"image\"),children:[/*#__PURE__*/_jsx(Image,{}),/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"11px !important\"},children:translate(\"Image\",{defaultValue:\"Image\"})})]})})]})})});};const RTEToolbar=_ref4=>{var _document$getElementB,_document$getElementB2,_document$getElementB3,_document$getElementB4,_document$getElementB5,_boxRef$current2;let{isPopoverOpen,currentRTEFocusedId,tooltip,setIsPopoverOpen,boxRef,savedRange,handleDeleteSection,handleCloneContainer,setSaveRange,popupPosition,handleTooltipRTEValue,iseditorfocused,setIsEditorFocused,canvasproperties,isCloneDisabled}=_ref4;// const handleRTEPopoverClose = (event: MouseEvent | TouchEvent) => {\n// \tconst target = event.target as HTMLElement;\n// \t// Check if the target is within specific editable or toolbar elements\n// \tconst isElement =\n// \t\ttarget.closest(\"[contenteditable]\") ||\n// \t\ttarget.closest(\"#rte-toolbar-paper\") ||\n// \t\ttarget.closest(\"#rte-alignment-menu\") ||\n// \t\ttarget.closest(\"#rte-alignment-menu-items\") ||\n// \t\ttarget.closest(target.id.startsWith(\"#rt-editor\") ? `${target.id}` : \"nope\");\n// \tif (target && isElement) {\n// \t\treturn;\n// \t}\n// \t// Check if the target is within Jodit Editor container\n// \t//const isJoditContainer = target.classList.contains(\"backdrop\");\n// \t//const isClickedoutside =  target.classList.contains(\"quickAdopt-selection\");\n// \t// Handle dynamic ID checks safely\n// \t// const isRTEditor = target.id.startsWith(\"rt-editor\") && target.closest(`#${target.id}`);\n// \t// if (isJoditContainer || isElement || isRTEditor) {\n// \t// \treturn;\n// \t// }\n// \t// Delay closing the popover slightly\n// \t\tsetIsPopoverOpen(false);\n// };\n// const handleBlur = () => {\n// \tsetIsEditorFocused(false);\n//     console.log(\"Editor lost focus\");\n// };\nconst handleFocus=()=>{setIsEditorFocused(true);console.log(\"Editor is focused\");};const[isRtlDirection,setIsRtlDirection]=useState(false);useEffect(()=>{const dir=document.body.getAttribute(\"dir\")||\"ltr\";setIsRtlDirection(dir.toLowerCase()===\"rtl\");},[]);const editorConfig=useMemo(()=>({direction:isRtlDirection?'rtl':'ltr',// Jodit uses 'direction' not just 'rtl'\nlanguage:'en',// Optional: change language as well\ntoolbarSticky:false,toolbarAdaptive:false,autofocus:true,// Enable auto-focus for immediate interaction\nbuttons:[\"bold\",\"italic\",\"underline\",\"brush\",\"font\",\"fontsize\",\"link\",{name:\"more\",iconURL:\"https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg\",list:[\"source\",\"strikethrough\",\"ul\",\"ol\",\"image\",\"video\",\"table\",\"align\",\"undo\",\"redo\",\"|\",\"hr\",\"eraser\",\"copyformat\",\"symbol\",\"fullsize\",\"print\",\"superscript\",\"subscript\",\"|\",\"outdent\",\"indent\",\"paragraph\"]}],events:{focus:handleFocus,//blur: handleBlur\nafterInit:editor=>{// Ensure the editor is focused immediately after initialization\nsetTimeout(()=>{editor.focus();},0);}},maxHeight:\"calc(100% - 90px)\"}),[isRtlDirection]);return/*#__PURE__*/_jsx(_Fragment,{children:isPopoverOpen&&/*#__PURE__*/createPortal(/*#__PURE__*/// <ClickAwayListener onClickAway={(event) => {\n// \tconst target = event.target as HTMLElement;\n// \t// const isJoditContainer = target.classList.contains(\"backdrop\");\n// \t// const isClickedoutside = target.classList.contains(\"quickAdopt-selection\");\n// \tconst isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\n// \tconst isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\n// \tconst isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\n// \tconst isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\n// \tconst isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\n// \t// Check if the target is inside a button, and if it is, check if it's inside the toolbar button container\n// \tconst isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\n// \t// Check if the target is the specific \"Insert\" button (or similar button you want)\n// \tconst isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\n// \tif (!isInsidePopup && !isInsideWorkplacePopup && !isSelectionMarker && !isLinkPopup && !isInsideToolbarButton && !isInsertButton) {\n// \t\thandleRTEPopoverClose(event);\n// \t}\n// }}>\n_jsx(Paper,{style:{position:\"absolute\",zIndex:99999,left:((_document$getElementB=(_document$getElementB2=document.getElementById(\"Tooltip-unique\"))===null||_document$getElementB2===void 0?void 0:(_document$getElementB3=_document$getElementB2.getBoundingClientRect())===null||_document$getElementB3===void 0?void 0:_document$getElementB3.x)!==null&&_document$getElementB!==void 0?_document$getElementB:150)+3,// @ts-ignore\ntop:((_document$getElementB4=document.getElementById(\"Tooltip-unique\"))===null||_document$getElementB4===void 0?void 0:(_document$getElementB5=_document$getElementB4.getBoundingClientRect())===null||_document$getElementB5===void 0?void 0:_document$getElementB5.y)-50||80,width:(canvasproperties===null||canvasproperties===void 0?void 0:canvasproperties.width)||\"300px\"},component:\"div\",id:\"rte-toolbar-paper\",role:\"presentation\",sx:{\"& .jodit-status-bar-link\":{display:\"none !important\"},\"& .jodit-ui-input__wrapper\":{pointerEvents:\"auto\"},\"& .jodit-toolbar-button button\":{minWidth:\"8px !important\"},\"& .jodit-workplace\":{maxHeight:\"calc(100vh - 290px) !important\",overflowX:\"auto !important\"}},children:/*#__PURE__*/_jsx(JoditEditor,{value:(boxRef===null||boxRef===void 0?void 0:(_boxRef$current2=boxRef.current)===null||_boxRef$current2===void 0?void 0:_boxRef$current2.innerHTML)||\"\",className:\"qadpt-jodit\",config:{...editorConfig,placeholder:\"Start typing here...\",controls:{font:{list:{\"Poppins, sans-serif\":\"Poppins\",\"Roboto, sans-serif\":\"Roboto\",\"Comic Sans MS, sans-serif\":\"Comic Sans MS\",\"Open Sans, sans-serif\":\"Open Sans\",\"Calibri, sans-serif\":\"Calibri\",\"Century Gothic, sans-serif\":\"Century Gothic\"}}}},onBlur:(newContent,event)=>{var _document$querySelect,_document$querySelect2,_document$querySelect3,_document$querySelect4;const isInsidePopup=(_document$querySelect=document.querySelector(\".jodit-popup\"))===null||_document$querySelect===void 0?void 0:_document$querySelect.contains(event.target);const isInsideJoditPopup=(_document$querySelect2=document.querySelector(\".jodit-wysiwyg\"))===null||_document$querySelect2===void 0?void 0:_document$querySelect2.contains(event.target);const isInsideWorkplacePopup=(_document$querySelect3=document.querySelector(\".jodit-dialog__panel\"))===null||_document$querySelect3===void 0?void 0:_document$querySelect3.contains(event.target);const isSelectionMarker=event.target.id.startsWith(\"jodit-selection_marker_\");const isLinkPopup=(_document$querySelect4=document.querySelector(\".jodit-ui-input__input\"))===null||_document$querySelect4===void 0?void 0:_document$querySelect4.contains(event.target);// Check if the target is inside a button, and if it is, check if it's inside the toolbar button container\nconst isInsideToolbarButton=event.target.closest(\".jodit-toolbar-button__button\")!==null;const isPasteEvent=event.type===\"paste\"||event.type===\"keydown\"&&event.ctrlKey&&event.key===\"v\";// Check if the target is the specific \"Insert\" button (or similar button you want)\nconst isInsertButton=event.target.closest(\"button[aria-pressed='false']\")!==null;const formElement=document.querySelector(\".jodit-ui-form\");if(boxRef!==null&&boxRef!==void 0&&boxRef.current&&!isInsidePopup&&!isSelectionMarker&&!isLinkPopup&&!isInsideToolbarButton&&!isInsertButton&&!formElement&&isInsideWorkplacePopup===undefined){boxRef.current.innerHTML=newContent;handleTooltipRTEValue(currentRTEFocusedId,newContent.trim());setIsPopoverOpen(false);}},onChange:newContent=>{if(boxRef!==null&&boxRef!==void 0&&boxRef.current){boxRef.current.innerHTML=newContent;}}})}),// </ClickAwayListener >\ndocument.body,\"rte-toolbar-portal\")});};const DotsStepper=_ref5=>{let{steps,activeStep,ProgressColor}=_ref5;return/*#__PURE__*/_jsx(MobileStepper,{variant:\"dots\",steps:steps,position:\"static\",activeStep:activeStep-1,sx:{flexGrow:1,display:\"flex\",justifyContent:\"center\",background:\"inherit\",\"& .MuiMobileStepper-dotActive\":{backgroundColor:ProgressColor// active dot color\n}},nextButton:/*#__PURE__*/_jsx(_Fragment,{}),backButton:/*#__PURE__*/_jsx(_Fragment,{})});};const BreadCrumpStepper=_ref6=>{let{steps,activeStep,ProgressColor}=_ref6;return/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',gap:\"4px\"// Adjust space between steps\n},children:Array.from({length:steps}).map((_,index)=>/*#__PURE__*/_jsx(\"div\",{style:{width:'14px',height:'4px',backgroundColor:index===activeStep-1?ProgressColor:'#e0e0e0',// Active color and inactive color\nborderRadius:'100px'}},index))});};", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useMemo", "Box", "IconButton", "Paper", "Popover", "<PERSON><PERSON><PERSON>", "Typography", "LinearProgress", "MobileStepper", "Breadcrumbs", "AddIcon", "Image", "TextFormat", "Link", "useDrawerStore", "ButtonSection", "ImageSection", "RTEsection", "createPortal", "CloseIcon", "JoditEditor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PerfectScrollbar", "useTranslation", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "MAX_SECTIONS", "image", "button", "rte", "TooltipBody", "_ref", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_boxRef$current", "isPopoverOpen", "setIsPopoverOpen", "popupPosition", "isUnSavedChanges", "openWarning", "setopenWarning", "handleLeave", "updatedGuideData", "t", "translate", "toolTipGuideMetaData", "currentStep", "handleTooltipRTEValue", "handleRTEDeleteSection", "handleRTECloneSection", "tooltip", "tooltipBackgroundcolor", "openTooltip", "selectedOption", "selectedTemplate", "selectedTemplateTour", "setTooltipPositionByXpath", "width", "borderRadius", "Annpadding", "borderColor", "tooltipborderradius", "tooltipbordersize", "tooltipBordercolor", "tooltipPosition", "tooltipWidth", "tooltippadding", "AnnborderSize", "currentStepIndex", "dismissData", "steps", "setCurrentHoveredElement", "elementClick", "dismiss", "<PERSON><PERSON><PERSON><PERSON>", "progress", "setProgress", "ProgressColor", "setProgressColor", "state", "anchorEl", "setAnchorEl", "boxRef", "Map", "savedRange", "setSaveRange", "undefined", "currentIndex", "setCurrentIndex", "currentRTEFocusedId", "setCurrentRTEFocusedId", "currentFocusedType", "setCurrentFocusedType", "isEditorFocused", "setIsEditorFocused", "needsScrolling", "setNeedsScrolling", "contentRef", "scrollbarRef", "sectionCounts", "setSectionCounts", "hasReachedLimit", "type", "countType", "handleAddIconClick", "event", "idx", "currentTarget", "setTimeout", "handlePopoverClose", "handleFocus", "id", "handleBlur", "_boxRef$current$get", "_boxRef$current$get$c", "current", "get", "innerHTML", "_boxRef$current$get2", "_boxRef$current$get2$", "trim", "handleDeleteSection", "handleCloneContainer", "sections", "setSections", "draggingIndex", "setDraggingIndex", "draggedIndex", "setDraggedIndex", "handleDragStart", "index", "handleDragEnter", "reorderedSections", "removed", "splice", "handleDragEnd", "addToBoxRef", "has", "newRef", "createRef", "set", "_toolTipGuideMetaData", "containers", "counts", "for<PERSON>ach", "container", "checkScrollNeeded", "style", "height", "contentHeight", "scrollHeight", "containerHeight", "shouldScroll", "updateScroll", "timeouts", "resizeObserver", "mutationObserver", "window", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "clearTimeout", "disconnect", "_toolTipGuideMetaData2", "_toolTipGuideMetaData3", "xpath", "value", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "element", "singleNodeValue", "_element$textContent", "rect", "getBoundingClientRect", "info", "tagName", "classes", "className", "position", "x", "y", "textContent", "popup", "querySelector", "HTMLElement", "canvasProperties", "canvas", "children", "place<PERSON><PERSON>nt", "display", "sx", "zoom", "color", "ref", "maxHeight", "options", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "minHeight", "overflow", "map", "item", "updatedContainers", "draggedItem", "setState", "onMouseEnter", "e", "padding", "borderBottomWidth", "borderBottomColor", "borderBottomStyle", "marginBottom", "draggable", "onDragStart", "onDragEnter", "onDragEnd", "items", "isCloneDisabled", "AddSectionComp", "handleeBlur", "SectionPopOver", "RTEToolbar", "iseditorfocused", "canvasproperties", "length", "DotsStepper", "activeStep", "variant", "margin", "backgroundColor", "BreadCrumpStepper", "_ref2", "transform", "top", "left", "onClick", "fontSize", "_ref3", "createTooltipSections", "handleAddSection", "sectionType", "open", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "slotProps", "paper", "gap", "root", "zIndex", "theme", "disableEnforceFocus", "flexDirection", "title", "defaultValue", "PopperProps", "alignItems", "cursor", "opacity", "svg", "_ref4", "_document$getElementB", "_document$getElementB2", "_document$getElementB3", "_document$getElementB4", "_document$getElementB5", "_boxRef$current2", "console", "log", "isRtlDirection", "setIsRtlDirection", "dir", "body", "getAttribute", "toLowerCase", "editorConfig", "direction", "language", "toolbarSticky", "toolbarAdaptive", "autofocus", "buttons", "name", "iconURL", "list", "events", "focus", "afterInit", "editor", "getElementById", "component", "role", "pointerEvents", "min<PERSON><PERSON><PERSON>", "overflowX", "config", "placeholder", "controls", "font", "onBlur", "newContent", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsidePopup", "contains", "target", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "startsWith", "isLinkPopup", "isInsideToolbarButton", "closest", "isPasteEvent", "ctrl<PERSON>ey", "key", "isInsertButton", "formElement", "onChange", "_ref5", "flexGrow", "justifyContent", "background", "nextButton", "backButton", "_ref6", "Array", "from", "_"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/Tooltips/TooltipBody.tsx"], "sourcesContent": ["import React, { RefObject, useEffect, useRef, useState, useMemo } from \"react\";\r\nimport {\r\n\tBox,\r\n\tClickAwayListener,\r\n\tIconButton,\r\n\tPaper,\r\n\tPopover,\r\n\tTextField,\r\n\tTooltip,\r\n\tTypography,\r\n\tLinearProgress,\r\n\tMobileStepper,\r\n\tBreadcrumbs,\r\n} from \"@mui/material\";\r\nimport { CustomWidthTooltip, EXTENSION_PART, TOOLTIP_HEIGHT, TOOLTIP_MN_WIDTH, TOOLTIP_MX_WIDTH } from \"./Tooltip\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport { Image, TextFormat, Code, VideoLibrary, GifBox, Link } from \"@mui/icons-material\";\r\nimport useDrawerStore, { TSectionType, TooltipState } from \"../../store/drawerStore\";\r\nimport ButtonSection from \"./components/Buttons\";\r\nimport ImageSection from \"./components/ImageSection\";\r\nimport RTEsection from \"./components/RTE/RTESection\";\r\nimport RTE from \"./components/RTE/RTE\";\r\nimport { createPortal } from \"react-dom\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport JoditEditor from \"jodit-react\";\r\nimport AlertPopup from \"../drawer/AlertPopup\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport \"react-perfect-scrollbar/dist/css/styles.css\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\ntype SectionType = { type: \"image\" | \"button\" | \"video\" | \"gif\" | \"html\" } | { type: \"text\" };\r\n\r\n// Maximum allowed sections of each type\r\nconst MAX_SECTIONS = {\r\n\timage: 3,\r\n\tbutton: 3,\r\n\trte: 3,\r\n};\r\nconst TooltipBody = ({\r\n\tisPopoverOpen,\r\n\tsetIsPopoverOpen,\r\n\tpopupPosition,\r\n\tisUnSavedChanges,\r\n\topenWarning,\r\n\tsetopenWarning,\r\n\thandleLeave,\r\n\tupdatedGuideData,\r\n}: {\r\n\tisPopoverOpen: boolean;\r\n\tsetIsPopoverOpen: (param: boolean) => void;\r\n\tpopupPosition: any;\r\n\tisUnSavedChanges: boolean;\r\n\topenWarning: boolean;\r\n\tsetopenWarning: (params: boolean) => void;\r\n\thandleLeave: () => void;\r\n\t\tupdatedGuideData: any;\r\n}) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\ttoolTipGuideMetaData,\r\n\t\tcurrentStep,\r\n\t\thandleTooltipRTEValue,\r\n\t\thandleRTEDeleteSection,\r\n\t\thandleRTECloneSection,\r\n\t\ttooltip,\r\n\t\ttooltipBackgroundcolor,\r\n\t\topenTooltip,\r\n\t\tselectedOption,\r\n\t\tselectedTemplate,\r\n\t\tselectedTemplateTour,\r\n\t\tsetTooltipPositionByXpath,\r\n\t\twidth,\r\n\t\tborderRadius,\r\n\t\tAnnpadding,\r\n\t\tborderColor,\r\n\t\ttooltipborderradius,\r\n\t\ttooltipbordersize,\r\n\t\ttooltipBordercolor,\r\n\t\ttooltipPosition,\r\n\t\ttooltipWidth,\r\n\t\ttooltippadding,\r\n\t\tAnnborderSize,\r\n\t\tcurrentStepIndex,\r\n\t\tdismissData,\r\n\t\tsteps,\r\n\t\tsetCurrentHoveredElement,\r\n\t\telementClick,\r\n\t\tdismiss,\r\n\t\tsetDismiss,\r\n\t\tprogress,\r\n\t\tsetProgress,\r\n\t\tProgressColor,\r\n\t\tsetProgressColor\r\n\t} = useDrawerStore((state) => state);\r\n\r\n\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst boxRef = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());\r\n\r\n\tconst [savedRange, setSaveRange] = useState<Range | undefined>(undefined);\r\n\tconst [currentIndex, setCurrentIndex] = useState<number>(0);\r\n\tconst [currentRTEFocusedId, setCurrentRTEFocusedId] = useState<string>(\"\");\r\n\tconst [currentFocusedType, setCurrentFocusedType] = useState<TSectionType>();\r\n\tconst [isEditorFocused, setIsEditorFocused] = useState(false);\r\n\r\n\t// State to track if scrolling is needed\r\n\tconst [needsScrolling, setNeedsScrolling] = useState(false);\r\n\tconst contentRef = useRef<HTMLDivElement>(null);\r\n\tconst scrollbarRef = useRef<any>(null);\r\n\r\n\t// Section count tracking for limits\r\n\tconst [sectionCounts, setSectionCounts] = useState({\r\n\t\timage: 0,\r\n\t\tbutton: 0,\r\n\t\trte: 0\r\n\t});\r\n\r\n\t// Helper function to check if a section type has reached its limit\r\n\tconst hasReachedLimit = (type: TSectionType): boolean => {\r\n\t\t// Map the section type to the corresponding key in sectionCounts\r\n\t\tconst countType = type === \"rte\" ? \"rte\" : type === \"button\" ? \"button\" : type === \"image\" ? \"image\" : null;\r\n\r\n\t\t// If the type is not supported, return false\r\n\t\tif (countType === null) return false;\r\n\r\n\t\treturn sectionCounts[countType] >= MAX_SECTIONS[countType];\r\n\t};\r\n\tconst handleAddIconClick = (event: React.MouseEvent<HTMLElement>, idx: number) => {\r\n\t\tif (\r\n\t\thasReachedLimit(\"rte\") &&\r\n\t\thasReachedLimit(\"button\") &&\r\n\t\thasReachedLimit(\"image\")\r\n\t\t) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tconst currentTarget = event.currentTarget;\r\n\t\tsetTimeout(() => {\r\n\t\t\tsetCurrentIndex(idx);\r\n\t\t\tsetAnchorEl(currentTarget);\r\n\t\t}, 0);\r\n\t};\r\n\r\n\tconst handlePopoverClose = () => {\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleFocus = (id: string) => {\r\n\t\tsetIsPopoverOpen(true);\r\n\t\tsetCurrentRTEFocusedId(id);\r\n\t};\r\n\r\n\tconst handleBlur = (id: string) => {\r\n\t\tif (boxRef?.current.get(id)?.current?.innerHTML) {\r\n\t\t\thandleTooltipRTEValue(id, boxRef.current.get(id)?.current?.innerHTML.trim() || \"\");\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleDeleteSection = () => {\r\n\t\thandleRTEDeleteSection(currentRTEFocusedId);\r\n\t};\r\n\tconst handleCloneContainer = () => {\r\n\t\thandleRTECloneSection(currentRTEFocusedId);\r\n\t};\r\n\tconst [sections, setSections] = useState<SectionType[]>([{ type: \"image\" }, { type: \"text\" }, { type: \"button\" }]);\r\n\tconst [draggingIndex, setDraggingIndex] = useState<number | null>(null);\r\n\tconst [draggedIndex, setDraggedIndex] = useState<number | null>(null);\r\n\tconst handleDragStart = (index: number) => {\r\n\t\tsetDraggingIndex(index);\r\n\t};\r\n\tconst handleDragEnter = (index: number) => {\r\n\t\tif (draggingIndex !== null && draggingIndex !== index) {\r\n\t\t\tconst reorderedSections = [...sections];\r\n\t\t\tconst [removed] = reorderedSections.splice(draggingIndex, 1);\r\n\t\t\treorderedSections.splice(index, 0, removed);\r\n\t\t\tsetSections(reorderedSections);\r\n\t\t\tsetDraggingIndex(index);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleDragEnd = () => {\r\n\t\tsetDraggingIndex(null);\r\n\t};\r\n\r\n\tconst addToBoxRef = (id: string) => {\r\n\t\tif (!boxRef.current.has(id)) {\r\n\t\t\tconst newRef = React.createRef<HTMLDivElement>();\r\n\t\t\tboxRef.current.set(id, newRef);\r\n\t\t\treturn newRef;\r\n\t\t}\r\n\t\treturn boxRef.current.get(id);\r\n\t};\r\n\r\n\t// Update section counts when the component mounts or when containers change\r\n\tuseEffect(() => {\r\n\t\tif (toolTipGuideMetaData && toolTipGuideMetaData[currentStep - 1]?.containers) {\r\n\t\t\tconst containers = toolTipGuideMetaData[currentStep - 1].containers;\r\n\t\t\tconst counts = {\r\n\t\t\t\timage: 0,\r\n\t\t\t\tbutton: 0,\r\n\t\t\t\trte: 0\r\n\t\t\t};\r\n\r\n\t\t\t// Count each type of section\r\n\t\t\tcontainers.forEach(container => {\r\n\t\t\t\tif (container.type === \"image\") counts.image++;\r\n\t\t\t\telse if (container.type === \"button\") counts.button++;\r\n\t\t\t\telse if (container.type === \"rte\") counts.rte++;\r\n\t\t\t});\r\n\r\n\t\t\tsetSectionCounts(counts);\r\n\t\t}\r\n\t}, [toolTipGuideMetaData, currentStep]);\r\n\r\n\t// Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [toolTipGuideMetaData, currentStep]);\r\n\r\n\tuseEffect(() => {\r\n\t\tif (openTooltip) {\r\n\t\t\tif (typeof window !== \"undefined\") {\r\n\t\t\t\tconst xpath = toolTipGuideMetaData[currentStep - 1]?.xpath?.value;\r\n\r\n\t\t\t\t// Check if XPath is valid and not empty\r\n\t\t\t\tif (xpath && xpath.trim() !== \"\") {\r\n\t\t\t\t\tconst result = window.document.evaluate(\r\n\t\t\t\t\t\txpath,\r\n\t\t\t\t\t\twindow.document,\r\n\t\t\t\t\t\tnull,\r\n\t\t\t\t\t\tXPathResult.FIRST_ORDERED_NODE_TYPE,\r\n\t\t\t\t\t\tnull\r\n\t\t\t\t\t);\r\n\r\n\t\t\t\t\tconst element = result.singleNodeValue as HTMLElement | null;\r\n\t\t\t\t\tif (element) {\r\n\t\t\t\t\t\tsetCurrentHoveredElement(element);\r\n\t\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\t\tconst info = {\r\n\t\t\t\t\t\t\telement,\r\n\t\t\t\t\t\t\ttagName: element.tagName,\r\n\t\t\t\t\t\t\tclasses: element.className,\r\n\t\t\t\t\t\t\tid: element.id,\r\n\t\t\t\t\t\t\tposition: {\r\n\t\t\t\t\t\t\t\tx: rect.x,\r\n\t\t\t\t\t\t\t\ty: rect.y,\r\n\t\t\t\t\t\t\t\twidth: rect.width,\r\n\t\t\t\t\t\t\t\theight: rect.height,\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\ttextContent: element.textContent?.trim() || \"\",\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tsetTooltipPositionByXpath(info);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tlet popup = document.querySelector(\".MuiTooltip-popperInteractive\");\r\n\t\t\t\tif (popup instanceof HTMLElement && popupPosition) {\r\n\t\t\t\t\t//popup.style.top = `${popupPosition.top || \"20px\"}`;\r\n\t\t\t\t\t//popup.style.left = `${popupPosition.left || \"10px\"}`; // Example for setting the left position\r\n\t\t\t\t}\r\n\t\t\t}, 10);\r\n\t\t}\r\n\t}, [openTooltip]);\r\n\r\n\tconst canvasProperties = toolTipGuideMetaData[currentStep - 1]?.canvas;\r\n\r\n\treturn (\r\n\t\t<div>\r\n\t\t<div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n\t\t\t\t{!isPopoverOpen && dismiss && (\r\n\t\t\t\t\t<IconButton className=\"qadpt-dismiss\"\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon sx={{ zoom: \"1\", color: \"#000\" }} />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t)}\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{ maxHeight: \"320px\" }}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t<div\r\n\t\t\tref={contentRef}\r\n\t\t\tstyle={{\r\n\t\t\t\t// backgroundColor: canvasProperties?.backgroundColor || \"white\",\r\n\t\t\t\t// padding: canvasProperties?.padding || \"4px\",\r\n\t\t\t\t// width: `${toolTipGuideMetaData[currentStep - 1]?.canvas.width} !important` || \"500px !important\",\r\n\t\t\t\t// borderRadius: canvasProperties?.borderRadius || \"8px\",\r\n\t\t\t\t// minWidth: TOOLTIP_MN_WIDTH,\r\n\t\t\t\t// maxWidth: TOOLTIP_MX_WIDTH,\r\n\t\t\t\tminHeight: 60,\r\n\t\t\t\t// maxHeight: 320,\r\n\t\t\t\t// height: selectedTemplate === \"Tooltip\" ? \"auto\" : \"350px\",\r\n\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\tposition: \"relative\",\r\n\t\t\t}}\r\n\t\t>\r\n\t\t\t\r\n\t\t\t{toolTipGuideMetaData[currentStep - 1]?.containers.map((item, index) => {\r\n\t\t\t\tconst id = `${item.type}|${item.id}`;\r\n\t\t\t\tconst handleDragStart = (index: number) => {\r\n\t\t\t\t\tif (draggedIndex === index) return; // Prevent redundant drag starts\r\n\t\t\t\t\tsetDraggedIndex(index);\r\n\t\t\t\t};\r\n\r\n\t\t\t\tconst handleDragEnter = (index: number) => {\r\n\t\t\t\t\tif (draggedIndex !== null && draggedIndex !== index) {\r\n\t\t\t\t\t\tconst updatedContainers = [...toolTipGuideMetaData[currentStep - 1].containers];\r\n\t\t\t\t\t\tconst [draggedItem] = updatedContainers.splice(draggedIndex, 1);\r\n\t\t\t\t\t\tupdatedContainers.splice(index, 0, draggedItem);\r\n\r\n\t\t\t\t\t\t// Update state only if the order has changed\r\n\t\t\t\t\t\tuseDrawerStore.setState({\r\n\t\t\t\t\t\t\ttoolTipGuideMetaData: [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t...toolTipGuideMetaData[currentStep - 1],\r\n\t\t\t\t\t\t\t\t\tcontainers: updatedContainers,\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\tsetDraggedIndex(index);\r\n\t\t\t\t\t}\r\n\t\t\t\t};\r\n\r\n\t\t\t\tconst handleDragEnd = () => {\r\n\t\t\t\t\tsetDraggedIndex(null);\r\n\t\t\t\t};\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tonMouseEnter={(e) => {\r\n\t\t\t\t\t\t\t\tsetCurrentFocusedType(e.currentTarget.id as TSectionType);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\t\tpadding: \"7px 0\",\r\n\t\t\t\t\t\t\t\tborderBottomWidth: currentFocusedType === id ? \"1px\" : \"0px\",\r\n\t\t\t\t\t\t\t\tborderBottomColor: currentFocusedType === id ? \"var(--primarycolor)\" : \"transparent\",\r\n\t\t\t\t\t\t\t\tborderBottomStyle: currentFocusedType === id ? \"dotted\" : \"none\",\r\n\t\t\t\t\t\t\t\tmarginBottom:\"10px\"\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tid={id}\r\n\t\t\t\t\t\t\tdraggable={item.type !== \"rte\"}\r\n\t\t\t\t\t\t\tonDragStart={() => handleDragStart(index)}\r\n\t\t\t\t\t\t\tonDragEnter={() => handleDragEnter(index)}\r\n\t\t\t\t\t\t\tonDragEnd={handleDragEnd}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{item.type === \"button\" ? (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<ButtonSection\r\n\t\t\t\t\t\t\t\t\t\titems={item}\r\n\t\t\t\t\t\t\t\t\t\tupdatedGuideData={updatedGuideData}\r\n\t\t\t\t\t\t\t\t\t\tisCloneDisabled={hasReachedLimit(\"button\")}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t{currentFocusedType === id && (\r\n\t\t\t\t\t\t\t\t\t\t<AddSectionComp handleAddIconClick={(e) => handleAddIconClick(e, index)} />\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t) : item.type === \"image\" ? (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<ImageSection\r\n\t\t\t\t\t\t\t\t\t\titems={item}\r\n\t\t\t\t\t\t\t\t\t\tisCloneDisabled={hasReachedLimit(\"image\")}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t{currentFocusedType === id && (\r\n\t\t\t\t\t\t\t\t\t\t<AddSectionComp handleAddIconClick={(e) => handleAddIconClick(e, index)} />\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t) : item.type === \"rte\" ? (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<RTEsection\r\n\t\t\t\t\t\t\t\t\t\titems={item}\r\n\t\t\t\t\t\t\t\t\t\t//@ts-ignore\r\n\t\t\t\t\t\t\t\t\t\tboxRef={addToBoxRef(item.id)}\r\n\t\t\t\t\t\t\t\t\t\thandleFocus={handleFocus}\r\n\t\t\t\t\t\t\t\t\t\thandleeBlur={handleBlur}\r\n\t\t\t\t\t\t\t\t\t\tisPopoverOpen={isPopoverOpen}\r\n\t\t\t\t\t\t\t\t\t\tsetIsPopoverOpen={setIsPopoverOpen}\r\n\t\t\t\t\t\t\t\t\t\tcurrentRTEFocusedId={currentRTEFocusedId}\r\n\t\t\t\t\t\t\t\t\t\tisCloneDisabled={hasReachedLimit(\"rte\")}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t{currentFocusedType === id && (\r\n\t\t\t\t\t\t\t\t\t\t<AddSectionComp handleAddIconClick={(e) => handleAddIconClick(e, index)} />\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</>\r\n\t\t\t\t);\r\n\t\t\t})}\r\n\r\n\t\t\t\r\n\r\n\t\t\t{/* {anchorEl ? ( */}\r\n\t\t\t<SectionPopOver\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\thandlePopoverClose={handlePopoverClose}\r\n\t\t\t\tcurrentIndex={currentIndex}\r\n\t\t\t\thasReachedLimit={hasReachedLimit}\r\n\t\t\t/>\r\n\t\t\t{/* ) : null} */}\r\n\t\t\t{isUnSavedChanges && openWarning && (\r\n\t\t\t\t<AlertPopup\r\n\t\t\t\t\topenWarning={openWarning}\r\n\t\t\t\t\tsetopenWarning={setopenWarning}\r\n\t\t\t\t\thandleLeave={handleLeave}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t\t{isPopoverOpen && currentRTEFocusedId && boxRef?.current?.get(currentRTEFocusedId) && (\r\n\t\t\t\t<RTEToolbar\r\n\t\t\t\t\tisPopoverOpen={isPopoverOpen}\r\n\t\t\t\t\tsetIsPopoverOpen={setIsPopoverOpen}\r\n\t\t\t\t\tboxRef={boxRef.current.get(currentRTEFocusedId)}\r\n\t\t\t\t\tcurrentRTEFocusedId={currentRTEFocusedId}\r\n\t\t\t\t\thandleDeleteSection={handleDeleteSection}\r\n\t\t\t\t\thandleCloneContainer={handleCloneContainer}\r\n\t\t\t\t\tsavedRange={savedRange}\r\n\t\t\t\t\tsetSaveRange={setSaveRange}\r\n\t\t\t\t\ttooltip={tooltip}\r\n\t\t\t\t\tpopupPosition={popupPosition}\r\n\t\t\t\t\thandleTooltipRTEValue={handleTooltipRTEValue}\r\n\t\t\t\t\tiseditorfocused={isEditorFocused}\r\n\t\t\t\t\tsetIsEditorFocused={setIsEditorFocused}\r\n\t\t\t\t\tcanvasproperties={canvasProperties}\r\n\t\t\t\t\t\t\tisCloneDisabled={hasReachedLimit(\"rte\")}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t\t\t</div>\r\n\t\t\t\t</PerfectScrollbar>\r\n\t\t{progress && toolTipGuideMetaData.length>1 &&\r\n\t\t\t\t(selectedTemplate === \"Tooltip\"  || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\") && \r\n\t\t\t\t(selectedOption === 1 || selectedOption === \"\" ? (\r\n\t\t\t\t\t<DotsStepper\r\n\t\t\t\t\t\tactiveStep={currentStep}\r\n\t\t\t\t\t\tsteps={steps.length}\r\n\t\t\t\t\t\tProgressColor = {ProgressColor}\r\n\t\t\t\t\t/>\r\n\t\t\t\t) : selectedOption === 2 ? (\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\t//sx={{paddingTop:\"15px\",padding:\"8px\"}}\r\n\t\t\t\t\t\t\tvalue={(currentStep / steps.length) * 100}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\t'& .MuiLinearProgress-bar': {\r\n                                backgroundColor: ProgressColor, // progress bar color\r\n                              },}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t) \r\n\t\t\t\t: selectedOption === 3 ? (\r\n\t\t\t\t\t<div style={{padding:\"8px\"}}>\r\n\t\t\t\t\t\t<BreadCrumpStepper\r\n\t\t\t\t\t\tactiveStep={currentStep}\r\n\t\t\t\t\t\tsteps={steps.length}\r\n\t\t\t\t\t\tProgressColor={ProgressColor}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t) : selectedOption === 4 ? (\r\n\t\t\t\t\t<Breadcrumbs\r\n\t\t\t\t\t\taria-label=\"breadcrumb\"\r\n\t\t\t\t\t\tsx={{ padding: \"8px\" }}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Typography sx={{color : ProgressColor}}>\r\n\t\t\t\t\t\t\tStep {currentStep} of {steps.length}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t</Breadcrumbs>\r\n\t\t\t\t) : null)}\r\n\t\t</div>\r\n\t);\r\n};\r\nexport default TooltipBody;\r\n\r\nconst AddSectionComp = ({\r\n\thandleAddIconClick,\r\n}: {\r\n\thandleAddIconClick: (event: React.MouseEvent<HTMLElement>) => void;\r\n}) => {\r\n\treturn (\r\n\t\t<Box sx={{ position: \"absolute\", transform: \"translate(-50%,-50%)\", top: \"99%\", left: \"50%\" }}>\r\n\t\t\t<IconButton\r\n\t\t\t\tonClick={handleAddIconClick}\r\n\t\t\t\tsx={{\r\n\t\t\t\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\tbackgroundColor: \"#70afaf\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<AddIcon\r\n\t\t\t\t\tfontSize=\"small\"\r\n\t\t\t\t\tsx={{ color: \"#fff\" }}\r\n\t\t\t\t/>\r\n\t\t\t</IconButton>\r\n\t\t</Box>\r\n\t);\r\n};\r\n\r\nconst SectionPopOver = ({\r\n\tanchorEl,\r\n\thandlePopoverClose,\r\n\tcurrentIndex,\r\n\thasReachedLimit\r\n}: {\r\n\tanchorEl: HTMLElement | null;\r\n\thandlePopoverClose: () => void;\r\n\tcurrentIndex: number;\r\n\thasReachedLimit: (type: TSectionType) => boolean;\r\n}) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst { createTooltipSections, selectedOption, currentStep, steps, currentStepIndex } = useDrawerStore(\r\n\t\t(state: any) => state\r\n\t);\r\n\tconst handleAddSection = (sectionType: TSectionType) => {\r\n\t\t// Don't add if limit is reached\r\n\t\tif (hasReachedLimit(sectionType)) {\r\n\t\t\t\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tcreateTooltipSections(sectionType, currentIndex);\r\n\t\thandlePopoverClose();\r\n\t};\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{anchorEl && (\r\n\t\t\t\t<Popover\r\n\t\t\t\t\topen={Boolean(anchorEl)}\r\n\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\tonClose={handlePopoverClose}\r\n\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tid=\"tooltip-section-popover\"\r\n\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\tpaper: {\r\n\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tpadding: \"12px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\tgap: \"16px\",\r\n\t\t\t\t\t\t\twidth: \"auto\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\troot: {\r\n\t\t\t\t\t\t\t// instead of writing sx on popover write here it also target to root and more clear\r\n\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1000,\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tdisableEnforceFocus={true}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\tflexDirection=\"row\"\r\n\t\t\t\t\t\tgap=\"16px\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\ttitle={hasReachedLimit(\"rte\") ? translate(\"Maximum limit of 3 Rich Text sections reached\", { defaultValue: \"Maximum limit of 3 Rich Text sections reached\" }) : \"\"}\r\n\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcursor: hasReachedLimit(\"rte\") ? \"not-allowed\" : \"pointer\",\r\n\t\t\t\t\t\t\t\t\topacity: hasReachedLimit(\"rte\") ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={() => !hasReachedLimit(\"rte\") && handleAddSection(\"rte\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<TextFormat />\r\n\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"11px !important\" }}>{translate(\"Rich Text\", { defaultValue: \"Rich Text\" })}</Typography>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\ttitle={hasReachedLimit(\"button\") ? translate(\"Maximum limit of 3 Button sections reached\", { defaultValue: \"Maximum limit of 3 Button sections reached\" }) : \"\"}\r\n\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcursor: hasReachedLimit(\"button\") ? \"not-allowed\" : \"pointer\",\r\n\t\t\t\t\t\t\t\t\topacity: hasReachedLimit(\"button\") ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={() => !hasReachedLimit(\"button\") && handleAddSection(\"button\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Link />\r\n\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"11px !important\" }}>{translate(\"Button\", { defaultValue: \"Button\" })}</Typography>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\ttitle={hasReachedLimit(\"image\") ? translate(\"Maximum limit of 3 Image sections reached\", { defaultValue: \"Maximum limit of 3 Image sections reached\" }) : \"\"}\r\n\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcursor: hasReachedLimit(\"image\") ? \"not-allowed\" : \"pointer\",\r\n\t\t\t\t\t\t\t\t\topacity: hasReachedLimit(\"image\") ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={() => !hasReachedLimit(\"image\") && handleAddSection(\"image\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Image />\r\n\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"11px !important\" }}>{translate(\"Image\", { defaultValue: \"Image\" })}</Typography>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t\t{/* <Tooltip\r\n\t\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<span>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\tsx={{ cursor: \"pointer\", opacity: 0.5 }}\r\n\t\t\t\t\t\t\t// onClick={() => handleAddSection(\"video\")}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<VideoLibrary />\r\n\t\t\t\t\t\t\t<Typography variant=\"caption\">Video</Typography>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</span>\r\n\t\t\t\t</Tooltip>\r\n\t\t\t\t<Box\r\n\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\tsx={{ cursor: \"pointer\", opacity: 0.5 }}\r\n\t\t\t\t\t// onClick={() => handleAddSection(\"gif\")}\r\n\t\t\t\t>\r\n\t\t\t\t\t<GifBox />\r\n\t\t\t\t\t<Typography variant=\"caption\">Gif</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t\t<Tooltip\r\n\t\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<span>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\tsx={{ cursor: \"pointer\", opacity: 0.5 }}\r\n\t\t\t\t\t\t\t// onClick={() => handleAddSection(\"html\")}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Code />\r\n\t\t\t\t\t\t\t<Typography variant=\"caption\">HTML</Typography>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</span>\r\n\t\t\t\t</Tooltip> */}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Popover>\r\n\t\t\t)}\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nconst RTEToolbar = ({\r\n\tisPopoverOpen,\r\n\tcurrentRTEFocusedId,\r\n\ttooltip,\r\n\tsetIsPopoverOpen,\r\n\tboxRef,\r\n\tsavedRange,\r\n\thandleDeleteSection,\r\n\thandleCloneContainer,\r\n\tsetSaveRange,\r\n\tpopupPosition,\r\n\thandleTooltipRTEValue,\r\n\tiseditorfocused,\r\n\tsetIsEditorFocused,\r\n\tcanvasproperties,\r\n\tisCloneDisabled,\r\n}: {\r\n\tisPopoverOpen: boolean;\r\n\tcurrentRTEFocusedId: string;\r\n\ttooltip: TooltipState;\r\n\tsetIsPopoverOpen: (params: boolean) => void;\r\n\tboxRef: React.RefObject<HTMLDivElement> | undefined;\r\n\tsavedRange: Range | undefined;\r\n\thandleDeleteSection: () => void;\r\n\thandleCloneContainer: () => void;\r\n\tsetSaveRange: (params: Range) => void;\r\n\tpopupPosition: any;\r\n\thandleTooltipRTEValue: (id: string, value: string) => void;\r\n\tiseditorfocused: boolean;\r\n\tsetIsEditorFocused: (params: boolean) => void;\r\n\tcanvasproperties: any;\r\n\t\tisCloneDisabled?: boolean;\r\n\t// open: boolean;\r\n}) => {\r\n\t// const handleRTEPopoverClose = (event: MouseEvent | TouchEvent) => {\r\n\t// \tconst target = event.target as HTMLElement;\r\n\r\n\t// \t// Check if the target is within specific editable or toolbar elements\r\n\t// \tconst isElement =\r\n\t// \t\ttarget.closest(\"[contenteditable]\") ||\r\n\t// \t\ttarget.closest(\"#rte-toolbar-paper\") ||\r\n\t// \t\ttarget.closest(\"#rte-alignment-menu\") ||\r\n\t// \t\ttarget.closest(\"#rte-alignment-menu-items\") ||\r\n\t// \t\ttarget.closest(target.id.startsWith(\"#rt-editor\") ? `${target.id}` : \"nope\");\r\n\t// \tif (target && isElement) {\r\n\t// \t\treturn;\r\n\t// \t}\r\n\r\n\t// \t// Check if the target is within Jodit Editor container\r\n\t// \t//const isJoditContainer = target.classList.contains(\"backdrop\");\r\n\t// \t//const isClickedoutside =  target.classList.contains(\"quickAdopt-selection\");\r\n\r\n\t// \t// Handle dynamic ID checks safely\r\n\t// \t// const isRTEditor = target.id.startsWith(\"rt-editor\") && target.closest(`#${target.id}`);\r\n\r\n\t// \t// if (isJoditContainer || isElement || isRTEditor) {\r\n\t// \t// \treturn;\r\n\t// \t// }\r\n\r\n\t// \t// Delay closing the popover slightly\r\n\r\n\t// \t\tsetIsPopoverOpen(false);\r\n\r\n\t// };\r\n\r\n\t// const handleBlur = () => {\r\n\t// \tsetIsEditorFocused(false);\r\n\t//     console.log(\"Editor lost focus\");\r\n\t// };\r\n\r\n\tconst handleFocus = () => {\r\n\t\tsetIsEditorFocused(true);\r\n\t\tconsole.log(\"Editor is focused\");\r\n\t};\r\n\tconst [isRtlDirection, setIsRtlDirection] = useState<boolean>(false);\r\n\tuseEffect(() => {\r\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\r\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\r\n}, []);\r\n\tconst editorConfig = useMemo(\r\n\t\t() => ({\r\n\t\t\tdirection: isRtlDirection ? 'rtl' as const : 'ltr' as const,\r\n            \r\n\t\t\t// Jodit uses 'direction' not just 'rtl'\r\n\t\t\t\t\tlanguage:  'en', // Optional: change language as well\r\n\t\t\ttoolbarSticky: false,\r\n\t\t\ttoolbarAdaptive: false,\r\n\t\t\tautofocus: true, // Enable auto-focus for immediate interaction\r\n\t\t\tbuttons: [\r\n\t\t\t\t\"bold\",\r\n\t\t\t\t\"italic\",\r\n\t\t\t\t\"underline\",\r\n\t\t\t\t\"brush\",\r\n\t\t\t\t\"font\",\r\n\t\t\t\t\"fontsize\",\r\n\t\t\t\t\"link\",\r\n\t\t\t\t{\r\n\t\t\t\t\tname: \"more\",\r\n\t\t\t\t\ticonURL: \"https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg\",\r\n\t\t\t\t\tlist: [\r\n\t\t\t\t\t\t\"source\",\r\n\t\t\t\t\t\t\"strikethrough\",\r\n\t\t\t\t\t\t\"ul\",\r\n\t\t\t\t\t\t\"ol\",\r\n\t\t\t\t\t\t\"image\",\r\n\t\t\t\t\t\t\"video\",\r\n\t\t\t\t\t\t\"table\",\r\n\t\t\t\t\t\t\"align\",\r\n\t\t\t\t\t\t\"undo\",\r\n\t\t\t\t\t\t\"redo\",\r\n\t\t\t\t\t\t\"|\",\r\n\t\t\t\t\t\t\"hr\",\r\n\t\t\t\t\t\t\"eraser\",\r\n\t\t\t\t\t\t\"copyformat\",\r\n\t\t\t\t\t\t\"symbol\",\r\n\t\t\t\t\t\t\"fullsize\",\r\n\t\t\t\t\t\t\"print\",\r\n\t\t\t\t\t\t\"superscript\",\r\n\t\t\t\t\t\t\"subscript\",\r\n\t\t\t\t\t\t\"|\",\r\n\t\t\t\t\t\t\"outdent\",\r\n\t\t\t\t\t\t\"indent\",\r\n\t\t\t\t\t\t\"paragraph\",\r\n\t\t\t\t\t],\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t\tevents: {\r\n\t\t\t\tfocus: handleFocus,\r\n\t\t\t\t//blur: handleBlur\r\n\t\t\t\tafterInit: (editor: any) => {\r\n\t\t\t\t\t// Ensure the editor is focused immediately after initialization\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\teditor.focus();\r\n\t\t\t\t\t}, 0);\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t\tmaxHeight: \"calc(100% - 90px)\",\r\n\t\t}),\r\n\t\t[isRtlDirection]\r\n\t);\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{isPopoverOpen &&\r\n\t\t\t\tcreatePortal(\r\n\t\t\t\t\t// <ClickAwayListener onClickAway={(event) => {\r\n\t\t\t\t\t// \tconst target = event.target as HTMLElement;\r\n\t\t\t\t\t// \t// const isJoditContainer = target.classList.contains(\"backdrop\");\r\n\t\t\t\t\t// \t// const isClickedoutside = target.classList.contains(\"quickAdopt-selection\");\r\n\t\t\t\t\t// \tconst isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n\t\t\t\t\t// \tconst isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n\t\t\t\t\t// \tconst isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n\t\t\t\t\t// \tconst isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n\t\t\t\t\t// \tconst isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n\t\t\t\t\t// \t// Check if the target is inside a button, and if it is, check if it's inside the toolbar button container\r\n\t\t\t\t\t// \tconst isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n\t\t\t\t\t// \t// Check if the target is the specific \"Insert\" button (or similar button you want)\r\n\t\t\t\t\t// \tconst isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\t\t\t\t\t// \tif (!isInsidePopup && !isInsideWorkplacePopup && !isSelectionMarker && !isLinkPopup && !isInsideToolbarButton && !isInsertButton) {\r\n\t\t\t\t\t// \t\thandleRTEPopoverClose(event);\r\n\t\t\t\t\t// \t}\r\n\t\t\t\t\t// }}>\r\n\t\t\t\t\t<Paper\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\tzIndex: 99999,\r\n\t\t\t\t\t\t\tleft: (document.getElementById(\"Tooltip-unique\")?.getBoundingClientRect()?.x ?? 150) + 3,\r\n\t\t\t\t\t\t\t// @ts-ignore\r\n\t\t\t\t\t\t\ttop: document.getElementById(\"Tooltip-unique\")?.getBoundingClientRect()?.y - 50 || 80,\r\n\t\t\t\t\t\t\twidth: canvasproperties?.width || \"300px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\tid=\"rte-toolbar-paper\"\r\n\t\t\t\t\t\trole=\"presentation\"\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\"& .jodit-status-bar-link\": {\r\n\t\t\t\t\t\t\t\tdisplay: \"none !important\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\"& .jodit-ui-input__wrapper\": {\r\n\t\t\t\t\t\t\t\tpointerEvents: \"auto\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\"& .jodit-toolbar-button button\": {\r\n\t\t\t\t\t\t\t\tminWidth: \"8px !important\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\"& .jodit-workplace\": {\r\n\t\t\t\t\t\t\t\tmaxHeight: \"calc(100vh - 290px) !important\",\r\n\t\t\t\t\t\t\t\toverflowX: \"auto !important\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<JoditEditor\r\n\t\t\t\t\t\t\tvalue={boxRef?.current?.innerHTML || \"\"}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-jodit\"\r\n\t\t\t\t\t\t\tconfig={{\r\n\t\t\t\t\t\t\t\t...editorConfig,\r\n\t\t\t\t\t\t\t\tplaceholder: \"Start typing here...\",\r\n\t\t\t\t\t\t\t\tcontrols: {\r\n\t\t\t\t\t\t\t\t\tfont: {\r\n\t\t\t\t\t\t\t\t\t\tlist: {\r\n\t\t\t\t\t\t\t\t\t\t\t\"Poppins, sans-serif\": \"Poppins\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"Roboto, sans-serif\": \"Roboto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"Open Sans, sans-serif\": \"Open Sans\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"Calibri, sans-serif\": \"Calibri\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"Century Gothic, sans-serif\": \"Century Gothic\",\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonBlur={(newContent, event) => {\r\n\t\t\t\t\t\t\t\tconst isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n\t\t\t\t\t\t\t\tconst isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n\t\t\t\t\t\t\t\tconst isInsideWorkplacePopup = document\r\n\t\t\t\t\t\t\t\t\t.querySelector(\".jodit-dialog__panel\")\r\n\t\t\t\t\t\t\t\t\t?.contains(event.target as Node);\r\n\t\t\t\t\t\t\t\tconst isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n\t\t\t\t\t\t\t\tconst isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n\t\t\t\t\t\t\t\t// Check if the target is inside a button, and if it is, check if it's inside the toolbar button container\r\n\t\t\t\t\t\t\t\tconst isInsideToolbarButton =\r\n\t\t\t\t\t\t\t\t\t(event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n\t\t\t\t\t\t\t\tconst isPasteEvent =\r\n\t\t\t\t\t\t\t\t\tevent.type === \"paste\" ||\r\n\t\t\t\t\t\t\t\t\t(event.type === \"keydown\" &&\r\n\t\t\t\t\t\t\t\t\t\t(event as unknown as KeyboardEvent).ctrlKey &&\r\n\t\t\t\t\t\t\t\t\t\t(event as unknown as KeyboardEvent).key === \"v\");\r\n\t\t\t\t\t\t\t\t// Check if the target is the specific \"Insert\" button (or similar button you want)\r\n\t\t\t\t\t\t\t\tconst isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\t\t\t\t\t\t\t\tconst formElement = document.querySelector(\".jodit-ui-form\");\r\n\r\n\t\t\t\t\t\t\t\tif (\r\n\t\t\t\t\t\t\t\t\tboxRef?.current &&\r\n\t\t\t\t\t\t\t\t\t!isInsidePopup &&\r\n\t\t\t\t\t\t\t\t\t!isSelectionMarker &&\r\n\t\t\t\t\t\t\t\t\t!isLinkPopup &&\r\n\t\t\t\t\t\t\t\t\t!isInsideToolbarButton &&\r\n\t\t\t\t\t\t\t\t\t!isInsertButton &&\r\n\t\t\t\t\t\t\t\t\t!formElement &&\r\n\t\t\t\t\t\t\t\t\tisInsideWorkplacePopup === undefined\r\n\t\t\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\t\t\tboxRef.current.innerHTML = newContent;\r\n\t\t\t\t\t\t\t\t\thandleTooltipRTEValue(currentRTEFocusedId, newContent.trim());\r\n\t\t\t\t\t\t\t\t\tsetIsPopoverOpen(false);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonChange={(newContent) => {\r\n\t\t\t\t\t\t\t\tif (boxRef?.current) {\r\n\t\t\t\t\t\t\t\t\tboxRef.current.innerHTML = newContent;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Paper>,\r\n\t\t\t\t\t// </ClickAwayListener >\r\n\t\t\t\t\tdocument.body,\r\n\t\t\t\t\t\"rte-toolbar-portal\"\r\n\t\t\t\t)}\r\n\t\t</>\r\n\t);\r\n};\r\nconst DotsStepper = ({ steps, activeStep, ProgressColor }: { steps: number; activeStep: number; ProgressColor:string }) => {\r\n\treturn (\r\n\t\t<MobileStepper\r\n\t\t\tvariant=\"dots\"\r\n\t\t\tsteps={steps}\r\n\t\t\tposition=\"static\"\r\n\t\t\tactiveStep={activeStep - 1}\r\n\t\t\tsx={{ flexGrow: 1, display: \"flex\", justifyContent: \"center\" ,background:\"inherit\", \"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\tbackgroundColor: ProgressColor, // active dot color\r\n\t\t\t  }}}\r\n\t\t\tnextButton={<></>}\r\n\t\t\tbackButton={<></>}\r\n\t\t/>\r\n\t);\r\n};\r\nconst BreadCrumpStepper = ({ steps, activeStep,ProgressColor }: { steps: number; activeStep: number; ProgressColor:any }) => {\r\n\treturn (\r\n\t\t<Box sx={{\r\n\t\t\tdisplay: 'flex',\r\n\t\t\tjustifyContent: 'center',\r\n\t\t\tgap: \"4px\", // Adjust space between steps\r\n\t\t\t\r\n\t\t  }}>\r\n\t\t  {/* Custom Step Indicators */}\r\n\t\t\r\n\t\t\t{Array.from({ length: steps }).map((_, index) => (\r\n\t\t\t  <div\r\n\t\t\t\tkey={index}\r\n\t\t\t\tstyle={{\r\n\t\t\t\t  width: '14px',\r\n\t\t\t\t  height: '4px',\r\n\t\t\t\t  backgroundColor: index === activeStep - 1 ? ProgressColor : '#e0e0e0', // Active color and inactive color\r\n\t\t\t\t  borderRadius: '100px',\r\n\t\t\t\t}}\r\n\t\t\t  />\r\n\t\t\t))}\r\n\t\t\r\n\t\t</Box>\r\n\t  );\r\n};\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAeC,SAAS,CAAEC,MAAM,CAAEC,QAAQ,CAAEC,OAAO,KAAQ,OAAO,CAC9E,OACCC,GAAG,CAEHC,UAAU,CACVC,KAAK,CACLC,OAAO,CAEPC,OAAO,CACPC,UAAU,CACVC,cAAc,CACdC,aAAa,CACbC,WAAW,KACL,eAAe,CAEtB,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,OAASC,KAAK,CAAEC,UAAU,CAA8BC,IAAI,KAAQ,qBAAqB,CACzF,MAAO,CAAAC,cAAc,KAAsC,yBAAyB,CACpF,MAAO,CAAAC,aAAa,KAAM,sBAAsB,CAChD,MAAO,CAAAC,YAAY,KAAM,2BAA2B,CACpD,MAAO,CAAAC,UAAU,KAAM,6BAA6B,CAEpD,OAASC,YAAY,KAAQ,WAAW,CACxC,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,WAAW,KAAM,aAAa,CACrC,MAAO,CAAAC,UAAU,KAAM,sBAAsB,CAC7C,MAAO,CAAAC,gBAAgB,KAAM,yBAAyB,CACtD,MAAO,6CAA6C,CACpD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAI/C;AACA,KAAM,CAAAC,YAAY,CAAG,CACpBC,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTC,GAAG,CAAE,CACN,CAAC,CACD,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAkBd,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,eAAA,IAlBe,CACpBC,aAAa,CACbC,gBAAgB,CAChBC,aAAa,CACbC,gBAAgB,CAChBC,WAAW,CACXC,cAAc,CACdC,WAAW,CACXC,gBAUD,CAAC,CAAAX,IAAA,CACA,KAAM,CAAEY,CAAC,CAAEC,SAAU,CAAC,CAAGzB,cAAc,CAAC,CAAC,CACzC,KAAM,CACL0B,oBAAoB,CACpBC,WAAW,CACXC,qBAAqB,CACrBC,sBAAsB,CACtBC,qBAAqB,CACrBC,OAAO,CACPC,sBAAsB,CACtBC,WAAW,CACXC,cAAc,CACdC,gBAAgB,CAChBC,oBAAoB,CACpBC,yBAAyB,CACzBC,KAAK,CACLC,YAAY,CACZC,UAAU,CACVC,WAAW,CACXC,mBAAmB,CACnBC,iBAAiB,CACjBC,kBAAkB,CAClBC,eAAe,CACfC,YAAY,CACZC,cAAc,CACdC,aAAa,CACbC,gBAAgB,CAChBC,WAAW,CACXC,KAAK,CACLC,wBAAwB,CACxBC,YAAY,CACZC,OAAO,CACPC,UAAU,CACVC,QAAQ,CACRC,WAAW,CACXC,aAAa,CACbC,gBACD,CAAC,CAAGpE,cAAc,CAAEqE,KAAK,EAAKA,KAAK,CAAC,CAEpC,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGtF,QAAQ,CAAqB,IAAI,CAAC,CAClE,KAAM,CAAAuF,MAAM,CAAGxF,MAAM,CAA+C,GAAI,CAAAyF,GAAG,CAAC,CAAC,CAAC,CAE9E,KAAM,CAACC,UAAU,CAAEC,YAAY,CAAC,CAAG1F,QAAQ,CAAoB2F,SAAS,CAAC,CACzE,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG7F,QAAQ,CAAS,CAAC,CAAC,CAC3D,KAAM,CAAC8F,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG/F,QAAQ,CAAS,EAAE,CAAC,CAC1E,KAAM,CAACgG,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjG,QAAQ,CAAe,CAAC,CAC5E,KAAM,CAACkG,eAAe,CAAEC,kBAAkB,CAAC,CAAGnG,QAAQ,CAAC,KAAK,CAAC,CAE7D;AACA,KAAM,CAACoG,cAAc,CAAEC,iBAAiB,CAAC,CAAGrG,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAAsG,UAAU,CAAGvG,MAAM,CAAiB,IAAI,CAAC,CAC/C,KAAM,CAAAwG,YAAY,CAAGxG,MAAM,CAAM,IAAI,CAAC,CAEtC;AACA,KAAM,CAACyG,aAAa,CAAEC,gBAAgB,CAAC,CAAGzG,QAAQ,CAAC,CAClDgC,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTC,GAAG,CAAE,CACN,CAAC,CAAC,CAEF;AACA,KAAM,CAAAwE,eAAe,CAAIC,IAAkB,EAAc,CACxD;AACA,KAAM,CAAAC,SAAS,CAAGD,IAAI,GAAK,KAAK,CAAG,KAAK,CAAGA,IAAI,GAAK,QAAQ,CAAG,QAAQ,CAAGA,IAAI,GAAK,OAAO,CAAG,OAAO,CAAG,IAAI,CAE3G;AACA,GAAIC,SAAS,GAAK,IAAI,CAAE,MAAO,MAAK,CAEpC,MAAO,CAAAJ,aAAa,CAACI,SAAS,CAAC,EAAI7E,YAAY,CAAC6E,SAAS,CAAC,CAC3D,CAAC,CACD,KAAM,CAAAC,kBAAkB,CAAGA,CAACC,KAAoC,CAAEC,GAAW,GAAK,CACjF,GACAL,eAAe,CAAC,KAAK,CAAC,EACtBA,eAAe,CAAC,QAAQ,CAAC,EACzBA,eAAe,CAAC,OAAO,CAAC,CACtB,CACD,OACD,CACA,KAAM,CAAAM,aAAa,CAAGF,KAAK,CAACE,aAAa,CACzCC,UAAU,CAAC,IAAM,CAChBpB,eAAe,CAACkB,GAAG,CAAC,CACpBzB,WAAW,CAAC0B,aAAa,CAAC,CAC3B,CAAC,CAAE,CAAC,CAAC,CACN,CAAC,CAED,KAAM,CAAAE,kBAAkB,CAAGA,CAAA,GAAM,CAChC5B,WAAW,CAAC,IAAI,CAAC,CAClB,CAAC,CAED,KAAM,CAAA6B,WAAW,CAAIC,EAAU,EAAK,CACnC3E,gBAAgB,CAAC,IAAI,CAAC,CACtBsD,sBAAsB,CAACqB,EAAE,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAC,UAAU,CAAID,EAAU,EAAK,KAAAE,mBAAA,CAAAC,qBAAA,CAClC,GAAIhC,MAAM,SAANA,MAAM,YAAA+B,mBAAA,CAAN/B,MAAM,CAAEiC,OAAO,CAACC,GAAG,CAACL,EAAE,CAAC,UAAAE,mBAAA,YAAAC,qBAAA,CAAvBD,mBAAA,CAAyBE,OAAO,UAAAD,qBAAA,WAAhCA,qBAAA,CAAkCG,SAAS,CAAE,KAAAC,oBAAA,CAAAC,qBAAA,CAChDxE,qBAAqB,CAACgE,EAAE,CAAE,EAAAO,oBAAA,CAAApC,MAAM,CAACiC,OAAO,CAACC,GAAG,CAACL,EAAE,CAAC,UAAAO,oBAAA,kBAAAC,qBAAA,CAAtBD,oBAAA,CAAwBH,OAAO,UAAAI,qBAAA,iBAA/BA,qBAAA,CAAiCF,SAAS,CAACG,IAAI,CAAC,CAAC,GAAI,EAAE,CAAC,CACnF,CACD,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,CACjCzE,sBAAsB,CAACyC,mBAAmB,CAAC,CAC5C,CAAC,CACD,KAAM,CAAAiC,oBAAoB,CAAGA,CAAA,GAAM,CAClCzE,qBAAqB,CAACwC,mBAAmB,CAAC,CAC3C,CAAC,CACD,KAAM,CAACkC,QAAQ,CAAEC,WAAW,CAAC,CAAGjI,QAAQ,CAAgB,CAAC,CAAE2G,IAAI,CAAE,OAAQ,CAAC,CAAE,CAAEA,IAAI,CAAE,MAAO,CAAC,CAAE,CAAEA,IAAI,CAAE,QAAS,CAAC,CAAC,CAAC,CAClH,KAAM,CAACuB,aAAa,CAAEC,gBAAgB,CAAC,CAAGnI,QAAQ,CAAgB,IAAI,CAAC,CACvE,KAAM,CAACoI,YAAY,CAAEC,eAAe,CAAC,CAAGrI,QAAQ,CAAgB,IAAI,CAAC,CACrE,KAAM,CAAAsI,eAAe,CAAIC,KAAa,EAAK,CAC1CJ,gBAAgB,CAACI,KAAK,CAAC,CACxB,CAAC,CACD,KAAM,CAAAC,eAAe,CAAID,KAAa,EAAK,CAC1C,GAAIL,aAAa,GAAK,IAAI,EAAIA,aAAa,GAAKK,KAAK,CAAE,CACtD,KAAM,CAAAE,iBAAiB,CAAG,CAAC,GAAGT,QAAQ,CAAC,CACvC,KAAM,CAACU,OAAO,CAAC,CAAGD,iBAAiB,CAACE,MAAM,CAACT,aAAa,CAAE,CAAC,CAAC,CAC5DO,iBAAiB,CAACE,MAAM,CAACJ,KAAK,CAAE,CAAC,CAAEG,OAAO,CAAC,CAC3CT,WAAW,CAACQ,iBAAiB,CAAC,CAC9BN,gBAAgB,CAACI,KAAK,CAAC,CACxB,CACD,CAAC,CAED,KAAM,CAAAK,aAAa,CAAGA,CAAA,GAAM,CAC3BT,gBAAgB,CAAC,IAAI,CAAC,CACvB,CAAC,CAED,KAAM,CAAAU,WAAW,CAAIzB,EAAU,EAAK,CACnC,GAAI,CAAC7B,MAAM,CAACiC,OAAO,CAACsB,GAAG,CAAC1B,EAAE,CAAC,CAAE,CAC5B,KAAM,CAAA2B,MAAM,cAAGlJ,KAAK,CAACmJ,SAAS,CAAiB,CAAC,CAChDzD,MAAM,CAACiC,OAAO,CAACyB,GAAG,CAAC7B,EAAE,CAAE2B,MAAM,CAAC,CAC9B,MAAO,CAAAA,MAAM,CACd,CACA,MAAO,CAAAxD,MAAM,CAACiC,OAAO,CAACC,GAAG,CAACL,EAAE,CAAC,CAC9B,CAAC,CAED;AACAtH,SAAS,CAAC,IAAM,KAAAoJ,qBAAA,CACf,GAAIhG,oBAAoB,GAAAgG,qBAAA,CAAIhG,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,UAAA+F,qBAAA,WAArCA,qBAAA,CAAuCC,UAAU,CAAE,CAC9E,KAAM,CAAAA,UAAU,CAAGjG,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,CAACgG,UAAU,CACnE,KAAM,CAAAC,MAAM,CAAG,CACdpH,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTC,GAAG,CAAE,CACN,CAAC,CAED;AACAiH,UAAU,CAACE,OAAO,CAACC,SAAS,EAAI,CAC/B,GAAIA,SAAS,CAAC3C,IAAI,GAAK,OAAO,CAAEyC,MAAM,CAACpH,KAAK,EAAE,CAAC,IAC1C,IAAIsH,SAAS,CAAC3C,IAAI,GAAK,QAAQ,CAAEyC,MAAM,CAACnH,MAAM,EAAE,CAAC,IACjD,IAAIqH,SAAS,CAAC3C,IAAI,GAAK,KAAK,CAAEyC,MAAM,CAAClH,GAAG,EAAE,CAChD,CAAC,CAAC,CAEFuE,gBAAgB,CAAC2C,MAAM,CAAC,CACzB,CACD,CAAC,CAAE,CAAClG,oBAAoB,CAAEC,WAAW,CAAC,CAAC,CAEvC;AACArD,SAAS,CAAC,IAAM,CACf,KAAM,CAAAyJ,iBAAiB,CAAGA,CAAA,GAAM,CAC/B,GAAIjD,UAAU,CAACkB,OAAO,CAAE,CACvB;AACAlB,UAAU,CAACkB,OAAO,CAACgC,KAAK,CAACC,MAAM,CAAG,MAAM,CACxC,KAAM,CAAAC,aAAa,CAAGpD,UAAU,CAACkB,OAAO,CAACmC,YAAY,CACrD,KAAM,CAAAC,eAAe,CAAG,GAAG,CAAE;AAC7B,KAAM,CAAAC,YAAY,CAAGH,aAAa,CAAGE,eAAe,CAGpDvD,iBAAiB,CAACwD,YAAY,CAAC,CAE/B;AACA,GAAItD,YAAY,CAACiB,OAAO,CAAE,CACzB;AACA,GAAIjB,YAAY,CAACiB,OAAO,CAACsC,YAAY,CAAE,CACtCvD,YAAY,CAACiB,OAAO,CAACsC,YAAY,CAAC,CAAC,CACpC,CACA;AACA7C,UAAU,CAAC,IAAM,CAChB,GAAIV,YAAY,CAACiB,OAAO,EAAIjB,YAAY,CAACiB,OAAO,CAACsC,YAAY,CAAE,CAC9DvD,YAAY,CAACiB,OAAO,CAACsC,YAAY,CAAC,CAAC,CACpC,CACD,CAAC,CAAE,EAAE,CAAC,CACP,CACD,CACD,CAAC,CAGDP,iBAAiB,CAAC,CAAC,CAGnB,KAAM,CAAAQ,QAAQ,CAAG,CAChB9C,UAAU,CAACsC,iBAAiB,CAAE,EAAE,CAAC,CACjCtC,UAAU,CAACsC,iBAAiB,CAAE,GAAG,CAAC,CAClCtC,UAAU,CAACsC,iBAAiB,CAAE,GAAG,CAAC,CAClCtC,UAAU,CAACsC,iBAAiB,CAAE,GAAG,CAAC,CAClC,CAGD,GAAI,CAAAS,cAAqC,CAAG,IAAI,CAChD,GAAI,CAAAC,gBAAyC,CAAG,IAAI,CAEpD,GAAI3D,UAAU,CAACkB,OAAO,EAAI0C,MAAM,CAACC,cAAc,CAAE,CAChDH,cAAc,CAAG,GAAI,CAAAG,cAAc,CAAC,IAAM,CACzClD,UAAU,CAACsC,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFS,cAAc,CAACI,OAAO,CAAC9D,UAAU,CAACkB,OAAO,CAAC,CAC3C,CAGA,GAAIlB,UAAU,CAACkB,OAAO,EAAI0C,MAAM,CAACG,gBAAgB,CAAE,CAClDJ,gBAAgB,CAAG,GAAI,CAAAI,gBAAgB,CAAC,IAAM,CAC7CpD,UAAU,CAACsC,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFU,gBAAgB,CAACG,OAAO,CAAC9D,UAAU,CAACkB,OAAO,CAAE,CAC5C8C,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IAAI,CACbC,UAAU,CAAE,IAAI,CAChBC,eAAe,CAAE,CAAC,OAAO,CAAE,OAAO,CACnC,CAAC,CAAC,CACH,CAEA,MAAO,IAAM,CACZV,QAAQ,CAACV,OAAO,CAACqB,YAAY,CAAC,CAC9B,GAAIV,cAAc,CAAE,CACnBA,cAAc,CAACW,UAAU,CAAC,CAAC,CAC5B,CACA,GAAIV,gBAAgB,CAAE,CACrBA,gBAAgB,CAACU,UAAU,CAAC,CAAC,CAC9B,CACD,CAAC,CACF,CAAC,CAAE,CAACzH,oBAAoB,CAAEC,WAAW,CAAC,CAAC,CAEvCrD,SAAS,CAAC,IAAM,CACf,GAAI2D,WAAW,CAAE,CAChB,GAAI,MAAO,CAAAyG,MAAM,GAAK,WAAW,CAAE,KAAAU,sBAAA,CAAAC,sBAAA,CAClC,KAAM,CAAAC,KAAK,EAAAF,sBAAA,CAAG1H,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,UAAAyH,sBAAA,kBAAAC,sBAAA,CAArCD,sBAAA,CAAuCE,KAAK,UAAAD,sBAAA,iBAA5CA,sBAAA,CAA8CE,KAAK,CAEjE;AACA,GAAID,KAAK,EAAIA,KAAK,CAACjD,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACjC,KAAM,CAAAmD,MAAM,CAAGd,MAAM,CAACe,QAAQ,CAACC,QAAQ,CACtCJ,KAAK,CACLZ,MAAM,CAACe,QAAQ,CACf,IAAI,CACJE,WAAW,CAACC,uBAAuB,CACnC,IACD,CAAC,CAED,KAAM,CAAAC,OAAO,CAAGL,MAAM,CAACM,eAAqC,CAC5D,GAAID,OAAO,CAAE,KAAAE,oBAAA,CACZ3G,wBAAwB,CAACyG,OAAO,CAAC,CACjC,KAAM,CAAAG,IAAI,CAAGH,OAAO,CAACI,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAAC,IAAI,CAAG,CACZL,OAAO,CACPM,OAAO,CAAEN,OAAO,CAACM,OAAO,CACxBC,OAAO,CAAEP,OAAO,CAACQ,SAAS,CAC1BzE,EAAE,CAAEiE,OAAO,CAACjE,EAAE,CACd0E,QAAQ,CAAE,CACTC,CAAC,CAAEP,IAAI,CAACO,CAAC,CACTC,CAAC,CAAER,IAAI,CAACQ,CAAC,CACTlI,KAAK,CAAE0H,IAAI,CAAC1H,KAAK,CACjB2F,MAAM,CAAE+B,IAAI,CAAC/B,MACd,CAAC,CACDwC,WAAW,CAAE,EAAAV,oBAAA,CAAAF,OAAO,CAACY,WAAW,UAAAV,oBAAA,iBAAnBA,oBAAA,CAAqB1D,IAAI,CAAC,CAAC,GAAI,EAC7C,CAAC,CACDhE,yBAAyB,CAAC6H,IAAI,CAAC,CAChC,CACD,CACD,CACAzE,UAAU,CAAC,IAAM,CAChB,GAAI,CAAAiF,KAAK,CAAGjB,QAAQ,CAACkB,aAAa,CAAC,+BAA+B,CAAC,CACnE,GAAID,KAAK,WAAY,CAAAE,WAAW,EAAI1J,aAAa,CAAE,CAClD;AACA;AAAA,CAEF,CAAC,CAAE,EAAE,CAAC,CACP,CACD,CAAC,CAAE,CAACe,WAAW,CAAC,CAAC,CAEjB,KAAM,CAAA4I,gBAAgB,EAAAhK,sBAAA,CAAGa,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,UAAAd,sBAAA,iBAArCA,sBAAA,CAAuCiK,MAAM,CAEtE,mBACCxK,KAAA,QAAAyK,QAAA,eACA7K,IAAA,QAAK8H,KAAK,CAAE,CAAEgD,YAAY,CAAE,KAAK,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAAF,QAAA,CAClD,CAAC/J,aAAa,EAAIsC,OAAO,eACzBpD,IAAA,CAACvB,UAAU,EAAC0L,SAAS,CAAC,eAAe,CAEpC,aAAW,OAAO,CAAAU,QAAA,cAElB7K,IAAA,CAACN,SAAS,EAACsL,EAAE,CAAE,CAAEC,IAAI,CAAE,GAAG,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CACpC,CACZ,CACG,CAAC,cAENlL,IAAA,CAACH,gBAAgB,EAEhBsL,GAAG,CAAEtG,YAAa,CAClBiD,KAAK,CAAE,CAAEsD,SAAS,CAAE,OAAQ,CAAE,CAC9BC,OAAO,CAAE,CACRC,eAAe,CAAE,CAAC5G,cAAc,CAChC6G,eAAe,CAAE,IAAI,CACrBC,gBAAgB,CAAE,KAAK,CACvBC,WAAW,CAAE,IAAI,CACjBC,kBAAkB,CAAE,EAAE,CACtBC,kBAAkB,CAAE,IAAI,CACxBC,mBAAmB,CAAE,CACtB,CAAE,CAAAf,QAAA,cAEJzK,KAAA,QACC+K,GAAG,CAAEvG,UAAW,CAChBkD,KAAK,CAAE,CACN;AACA;AACA;AACA;AACA;AACA;AACA+D,SAAS,CAAE,EAAE,CACb;AACA;AACAC,QAAQ,CAAE,QAAQ,CAClB1B,QAAQ,CAAE,UACX,CAAE,CAAAS,QAAA,GAAAjK,sBAAA,CAGDY,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,UAAAb,sBAAA,iBAArCA,sBAAA,CAAuC6G,UAAU,CAACsE,GAAG,CAAC,CAACC,IAAI,CAAEnF,KAAK,GAAK,CACvE,KAAM,CAAAnB,EAAE,CAAG,GAAGsG,IAAI,CAAC/G,IAAI,IAAI+G,IAAI,CAACtG,EAAE,EAAE,CACpC,KAAM,CAAAkB,eAAe,CAAIC,KAAa,EAAK,CAC1C,GAAIH,YAAY,GAAKG,KAAK,CAAE,OAAQ;AACpCF,eAAe,CAACE,KAAK,CAAC,CACvB,CAAC,CAED,KAAM,CAAAC,eAAe,CAAID,KAAa,EAAK,CAC1C,GAAIH,YAAY,GAAK,IAAI,EAAIA,YAAY,GAAKG,KAAK,CAAE,CACpD,KAAM,CAAAoF,iBAAiB,CAAG,CAAC,GAAGzK,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,CAACgG,UAAU,CAAC,CAC/E,KAAM,CAACyE,WAAW,CAAC,CAAGD,iBAAiB,CAAChF,MAAM,CAACP,YAAY,CAAE,CAAC,CAAC,CAC/DuF,iBAAiB,CAAChF,MAAM,CAACJ,KAAK,CAAE,CAAC,CAAEqF,WAAW,CAAC,CAE/C;AACA7M,cAAc,CAAC8M,QAAQ,CAAC,CACvB3K,oBAAoB,CAAE,CACrB,CACC,GAAGA,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,CACxCgG,UAAU,CAAEwE,iBACb,CAAC,CAEH,CAAC,CAAC,CAEFtF,eAAe,CAACE,KAAK,CAAC,CACvB,CACD,CAAC,CAED,KAAM,CAAAK,aAAa,CAAGA,CAAA,GAAM,CAC3BP,eAAe,CAAC,IAAI,CAAC,CACtB,CAAC,CACD,mBACC3G,IAAA,CAAAE,SAAA,EAAA2K,QAAA,cACC7K,IAAA,QACCoM,YAAY,CAAGC,CAAC,EAAK,CACpB9H,qBAAqB,CAAC8H,CAAC,CAAC/G,aAAa,CAACI,EAAkB,CAAC,CAC1D,CAAE,CACFoC,KAAK,CAAE,CACNsC,QAAQ,CAAE,UAAU,CACpBkC,OAAO,CAAE,OAAO,CAChBC,iBAAiB,CAAEjI,kBAAkB,GAAKoB,EAAE,CAAG,KAAK,CAAG,KAAK,CAC5D8G,iBAAiB,CAAElI,kBAAkB,GAAKoB,EAAE,CAAG,qBAAqB,CAAG,aAAa,CACpF+G,iBAAiB,CAAEnI,kBAAkB,GAAKoB,EAAE,CAAG,QAAQ,CAAG,MAAM,CAChEgH,YAAY,CAAC,MACd,CAAE,CACFhH,EAAE,CAAEA,EAAG,CACPiH,SAAS,CAAEX,IAAI,CAAC/G,IAAI,GAAK,KAAM,CAC/B2H,WAAW,CAAEA,CAAA,GAAMhG,eAAe,CAACC,KAAK,CAAE,CAC1CgG,WAAW,CAAEA,CAAA,GAAM/F,eAAe,CAACD,KAAK,CAAE,CAC1CiG,SAAS,CAAE5F,aAAc,CAAA2D,QAAA,CAExBmB,IAAI,CAAC/G,IAAI,GAAK,QAAQ,cACtB7E,KAAA,CAAAF,SAAA,EAAA2K,QAAA,eACC7K,IAAA,CAACV,aAAa,EACbyN,KAAK,CAAEf,IAAK,CACZ3K,gBAAgB,CAAEA,gBAAiB,CACnC2L,eAAe,CAAEhI,eAAe,CAAC,QAAQ,CAAE,CAC3C,CAAC,CACDV,kBAAkB,GAAKoB,EAAE,eACzB1F,IAAA,CAACiN,cAAc,EAAC9H,kBAAkB,CAAGkH,CAAC,EAAKlH,kBAAkB,CAACkH,CAAC,CAAExF,KAAK,CAAE,CAAE,CAC1E,EACA,CAAC,CACAmF,IAAI,CAAC/G,IAAI,GAAK,OAAO,cACxB7E,KAAA,CAAAF,SAAA,EAAA2K,QAAA,eACC7K,IAAA,CAACT,YAAY,EACZwN,KAAK,CAAEf,IAAK,CACZgB,eAAe,CAAEhI,eAAe,CAAC,OAAO,CAAE,CAC1C,CAAC,CACDV,kBAAkB,GAAKoB,EAAE,eACzB1F,IAAA,CAACiN,cAAc,EAAC9H,kBAAkB,CAAGkH,CAAC,EAAKlH,kBAAkB,CAACkH,CAAC,CAAExF,KAAK,CAAE,CAAE,CAC1E,EACA,CAAC,CACAmF,IAAI,CAAC/G,IAAI,GAAK,KAAK,cACtB7E,KAAA,CAAAF,SAAA,EAAA2K,QAAA,eACC7K,IAAA,CAACR,UAAU,EACVuN,KAAK,CAAEf,IACP;AAAA,CACAnI,MAAM,CAAEsD,WAAW,CAAC6E,IAAI,CAACtG,EAAE,CAAE,CAC7BD,WAAW,CAAEA,WAAY,CACzByH,WAAW,CAAEvH,UAAW,CACxB7E,aAAa,CAAEA,aAAc,CAC7BC,gBAAgB,CAAEA,gBAAiB,CACnCqD,mBAAmB,CAAEA,mBAAoB,CACzC4I,eAAe,CAAEhI,eAAe,CAAC,KAAK,CAAE,CACxC,CAAC,CACDV,kBAAkB,GAAKoB,EAAE,eACzB1F,IAAA,CAACiN,cAAc,EAAC9H,kBAAkB,CAAGkH,CAAC,EAAKlH,kBAAkB,CAACkH,CAAC,CAAExF,KAAK,CAAE,CAAE,CAC1E,EACA,CAAC,CACA,IAAI,CACJ,CAAC,CACL,CAAC,CAEL,CAAC,CAAC,cAKF7G,IAAA,CAACmN,cAAc,EACdxJ,QAAQ,CAAEA,QAAS,CACnB6B,kBAAkB,CAAEA,kBAAmB,CACvCtB,YAAY,CAAEA,YAAa,CAC3Bc,eAAe,CAAEA,eAAgB,CACjC,CAAC,CAED/D,gBAAgB,EAAIC,WAAW,eAC/BlB,IAAA,CAACJ,UAAU,EACVsB,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BC,WAAW,CAAEA,WAAY,CACzB,CACD,CACAN,aAAa,EAAIsD,mBAAmB,GAAIP,MAAM,SAANA,MAAM,kBAAAhD,eAAA,CAANgD,MAAM,CAAEiC,OAAO,UAAAjF,eAAA,iBAAfA,eAAA,CAAiBkF,GAAG,CAAC3B,mBAAmB,CAAC,gBACjFpE,IAAA,CAACoN,UAAU,EACVtM,aAAa,CAAEA,aAAc,CAC7BC,gBAAgB,CAAEA,gBAAiB,CACnC8C,MAAM,CAAEA,MAAM,CAACiC,OAAO,CAACC,GAAG,CAAC3B,mBAAmB,CAAE,CAChDA,mBAAmB,CAAEA,mBAAoB,CACzCgC,mBAAmB,CAAEA,mBAAoB,CACzCC,oBAAoB,CAAEA,oBAAqB,CAC3CtC,UAAU,CAAEA,UAAW,CACvBC,YAAY,CAAEA,YAAa,CAC3BnC,OAAO,CAAEA,OAAQ,CACjBb,aAAa,CAAEA,aAAc,CAC7BU,qBAAqB,CAAEA,qBAAsB,CAC7C2L,eAAe,CAAE7I,eAAgB,CACjCC,kBAAkB,CAAEA,kBAAmB,CACvC6I,gBAAgB,CAAE3C,gBAAiB,CACjCqC,eAAe,CAAEhI,eAAe,CAAC,KAAK,CAAE,CAC1C,CACD,EACK,CAAC,EAhKD,aAAaN,cAAc,EAiKd,CAAC,CACpBpB,QAAQ,EAAI9B,oBAAoB,CAAC+L,MAAM,CAAC,CAAC,GACvCtL,gBAAgB,GAAK,SAAS,EAAKC,oBAAoB,GAAK,SAAS,EAAIA,oBAAoB,GAAK,SAAS,CAAC,GAC5GF,cAAc,GAAK,CAAC,EAAIA,cAAc,GAAK,EAAE,cAC7ChC,IAAA,CAACwN,WAAW,EACXC,UAAU,CAAEhM,WAAY,CACxBwB,KAAK,CAAEA,KAAK,CAACsK,MAAO,CACpB/J,aAAa,CAAIA,aAAc,CAC/B,CAAC,CACCxB,cAAc,GAAK,CAAC,cACvBhC,IAAA,QAAA6K,QAAA,cACC7K,IAAA,CAAClB,cAAc,EACd4O,OAAO,CAAC,aACR;AAAA,CACArE,KAAK,CAAG5H,WAAW,CAAGwB,KAAK,CAACsK,MAAM,CAAI,GAAI,CAC1CvC,EAAE,CAAE,CACHjD,MAAM,CAAE,KAAK,CACb1F,YAAY,CAAE,MAAM,CACpBsL,MAAM,CAAE,UAAU,CAClB,0BAA0B,CAAE,CACJC,eAAe,CAAEpK,aAAe;AAClC,CAAE,CAAE,CAC3B,CAAC,CACE,CAAC,CAELxB,cAAc,GAAK,CAAC,cACrBhC,IAAA,QAAK8H,KAAK,CAAE,CAACwE,OAAO,CAAC,KAAK,CAAE,CAAAzB,QAAA,cAC3B7K,IAAA,CAAC6N,iBAAiB,EAClBJ,UAAU,CAAEhM,WAAY,CACxBwB,KAAK,CAAEA,KAAK,CAACsK,MAAO,CACpB/J,aAAa,CAAEA,aAAc,CAE7B,CAAC,CACG,CAAC,CACHxB,cAAc,GAAK,CAAC,cACvBhC,IAAA,CAAChB,WAAW,EACX,aAAW,YAAY,CACvBgM,EAAE,CAAE,CAAEsB,OAAO,CAAE,KAAM,CAAE,CAAAzB,QAAA,cAEvBzK,KAAA,CAACvB,UAAU,EAACmM,EAAE,CAAE,CAACE,KAAK,CAAG1H,aAAa,CAAE,CAAAqH,QAAA,EAAC,OACnC,CAACpJ,WAAW,CAAC,MAAI,CAACwB,KAAK,CAACsK,MAAM,EACxB,CAAC,CACD,CAAC,CACX,IAAI,CAAC,EACN,CAAC,CAER,CAAC,CACD,cAAe,CAAA9M,WAAW,CAE1B,KAAM,CAAAwM,cAAc,CAAGa,KAAA,EAIjB,IAJkB,CACvB3I,kBAGD,CAAC,CAAA2I,KAAA,CACA,mBACC9N,IAAA,CAACxB,GAAG,EAACwM,EAAE,CAAE,CAAEZ,QAAQ,CAAE,UAAU,CAAE2D,SAAS,CAAE,sBAAsB,CAAEC,GAAG,CAAE,KAAK,CAAEC,IAAI,CAAE,KAAM,CAAE,CAAApD,QAAA,cAC7F7K,IAAA,CAACvB,UAAU,EACVyP,OAAO,CAAE/I,kBAAmB,CAC5B6F,EAAE,CAAE,CACH4C,eAAe,CAAE,SAAS,CAC1B,SAAS,CAAE,CACVA,eAAe,CAAE,SAClB,CAAC,CACDvL,YAAY,CAAE,KAAK,CACnBiK,OAAO,CAAE,gBACV,CAAE,CAAAzB,QAAA,cAEF7K,IAAA,CAACf,OAAO,EACPkP,QAAQ,CAAC,OAAO,CAChBnD,EAAE,CAAE,CAAEE,KAAK,CAAE,MAAO,CAAE,CACtB,CAAC,CACS,CAAC,CACT,CAAC,CAER,CAAC,CAED,KAAM,CAAAiC,cAAc,CAAGiB,KAAA,EAUjB,IAVkB,CACvBzK,QAAQ,CACR6B,kBAAkB,CAClBtB,YAAY,CACZc,eAMD,CAAC,CAAAoJ,KAAA,CACA,KAAM,CAAE9M,CAAC,CAAEC,SAAU,CAAC,CAAGzB,cAAc,CAAC,CAAC,CACzC,KAAM,CAAEuO,qBAAqB,CAAErM,cAAc,CAAEP,WAAW,CAAEwB,KAAK,CAAEF,gBAAiB,CAAC,CAAG1D,cAAc,CACpGqE,KAAU,EAAKA,KACjB,CAAC,CACD,KAAM,CAAA4K,gBAAgB,CAAIC,WAAyB,EAAK,CACvD;AACA,GAAIvJ,eAAe,CAACuJ,WAAW,CAAC,CAAE,CAEjC,OACD,CAEAF,qBAAqB,CAACE,WAAW,CAAErK,YAAY,CAAC,CAChDsB,kBAAkB,CAAC,CAAC,CACrB,CAAC,CAED,mBACCxF,IAAA,CAAAE,SAAA,EAAA2K,QAAA,CACElH,QAAQ,eACR3D,IAAA,CAACrB,OAAO,EACP6P,IAAI,CAAEC,OAAO,CAAC9K,QAAQ,CAAE,CACxBA,QAAQ,CAAEA,QAAS,CACnB+K,OAAO,CAAElJ,kBAAmB,CAC5BmJ,YAAY,CAAE,CACbC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACb,CAAE,CACFC,eAAe,CAAE,CAChBF,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,QACb,CAAE,CACFnJ,EAAE,CAAC,yBAAyB,CAC5BqJ,SAAS,CAAE,CACVC,KAAK,CAAE,CACNhE,EAAE,CAAE,CACJsB,OAAO,CAAE,MAAM,CACfvB,OAAO,CAAE,MAAM,CACfkE,GAAG,CAAE,MAAM,CACX7M,KAAK,CAAE,MACR,CACA,CAAC,CACD8M,IAAI,CAAE,CACL;AACAlE,EAAE,CAAE,CACHmE,MAAM,CAAGC,KAAK,EAAKA,KAAK,CAACD,MAAM,CAACtN,OAAO,CAAG,IAC3C,CACD,CACD,CAAE,CACFwN,mBAAmB,CAAE,IAAK,CAAAxE,QAAA,cAE1BzK,KAAA,CAAC5B,GAAG,EACHuM,OAAO,CAAC,MAAM,CACduE,aAAa,CAAC,KAAK,CACnBL,GAAG,CAAC,MAAM,CAAApE,QAAA,eAEV7K,IAAA,CAACpB,OAAO,EACP2Q,KAAK,CAAEvK,eAAe,CAAC,KAAK,CAAC,CAAGzD,SAAS,CAAC,+CAA+C,CAAE,CAAEiO,YAAY,CAAE,+CAAgD,CAAC,CAAC,CAAG,EAAG,CACnKC,WAAW,CAAE,CACZzE,EAAE,CAAE,CACHmE,MAAM,CAAE,IACT,CACD,CAAE,CAAAtE,QAAA,cAEFzK,KAAA,CAAC5B,GAAG,EACHuM,OAAO,CAAC,MAAM,CACduE,aAAa,CAAC,QAAQ,CACtBI,UAAU,CAAC,QAAQ,CACnB1E,EAAE,CAAE,CACH2E,MAAM,CAAE3K,eAAe,CAAC,KAAK,CAAC,CAAG,aAAa,CAAG,SAAS,CAC1D4K,OAAO,CAAE5K,eAAe,CAAC,KAAK,CAAC,CAAG,GAAG,CAAG,CAAC,CACzC6K,GAAG,CAAE,CACJ1B,QAAQ,CAAE,iBACX,CACD,CAAE,CACFD,OAAO,CAAEA,CAAA,GAAM,CAAClJ,eAAe,CAAC,KAAK,CAAC,EAAIsJ,gBAAgB,CAAC,KAAK,CAAE,CAAAzD,QAAA,eAElE7K,IAAA,CAACb,UAAU,GAAE,CAAC,cACda,IAAA,CAACnB,UAAU,EAACmM,EAAE,CAAE,CAAEmD,QAAQ,CAAE,iBAAkB,CAAE,CAAAtD,QAAA,CAAEtJ,SAAS,CAAC,WAAW,CAAE,CAAEiO,YAAY,CAAE,WAAY,CAAC,CAAC,CAAa,CAAC,EACjH,CAAC,CACE,CAAC,cAEVxP,IAAA,CAACpB,OAAO,EACP2Q,KAAK,CAAEvK,eAAe,CAAC,QAAQ,CAAC,CAAGzD,SAAS,CAAC,4CAA4C,CAAE,CAAEiO,YAAY,CAAE,4CAA6C,CAAC,CAAC,CAAG,EAAG,CAChKC,WAAW,CAAE,CACZzE,EAAE,CAAE,CACHmE,MAAM,CAAE,IACT,CACD,CAAE,CAAAtE,QAAA,cAEFzK,KAAA,CAAC5B,GAAG,EACHuM,OAAO,CAAC,MAAM,CACduE,aAAa,CAAC,QAAQ,CACtBI,UAAU,CAAC,QAAQ,CACnB1E,EAAE,CAAE,CACH2E,MAAM,CAAE3K,eAAe,CAAC,QAAQ,CAAC,CAAG,aAAa,CAAG,SAAS,CAC7D4K,OAAO,CAAE5K,eAAe,CAAC,QAAQ,CAAC,CAAG,GAAG,CAAG,CAAC,CAC5C6K,GAAG,CAAE,CACJ1B,QAAQ,CAAE,iBACX,CACD,CAAE,CACFD,OAAO,CAAEA,CAAA,GAAM,CAAClJ,eAAe,CAAC,QAAQ,CAAC,EAAIsJ,gBAAgB,CAAC,QAAQ,CAAE,CAAAzD,QAAA,eAExE7K,IAAA,CAACZ,IAAI,GAAE,CAAC,cACRY,IAAA,CAACnB,UAAU,EAACmM,EAAE,CAAE,CAAEmD,QAAQ,CAAE,iBAAkB,CAAE,CAAAtD,QAAA,CAAEtJ,SAAS,CAAC,QAAQ,CAAE,CAAEiO,YAAY,CAAE,QAAS,CAAC,CAAC,CAAa,CAAC,EAC3G,CAAC,CACE,CAAC,cAEVxP,IAAA,CAACpB,OAAO,EACP2Q,KAAK,CAAEvK,eAAe,CAAC,OAAO,CAAC,CAAGzD,SAAS,CAAC,2CAA2C,CAAE,CAAEiO,YAAY,CAAE,2CAA4C,CAAC,CAAC,CAAG,EAAG,CAC7JC,WAAW,CAAE,CACZzE,EAAE,CAAE,CACHmE,MAAM,CAAE,IACT,CACD,CAAE,CAAAtE,QAAA,cAEFzK,KAAA,CAAC5B,GAAG,EACHuM,OAAO,CAAC,MAAM,CACduE,aAAa,CAAC,QAAQ,CACtBI,UAAU,CAAC,QAAQ,CACnB1E,EAAE,CAAE,CACH2E,MAAM,CAAE3K,eAAe,CAAC,OAAO,CAAC,CAAG,aAAa,CAAG,SAAS,CAC5D4K,OAAO,CAAE5K,eAAe,CAAC,OAAO,CAAC,CAAG,GAAG,CAAG,CAAC,CAC3C6K,GAAG,CAAE,CACJ1B,QAAQ,CAAE,iBACX,CACD,CAAE,CACFD,OAAO,CAAEA,CAAA,GAAM,CAAClJ,eAAe,CAAC,OAAO,CAAC,EAAIsJ,gBAAgB,CAAC,OAAO,CAAE,CAAAzD,QAAA,eAEtE7K,IAAA,CAACd,KAAK,GAAE,CAAC,cACTc,IAAA,CAACnB,UAAU,EAACmM,EAAE,CAAE,CAAEmD,QAAQ,CAAE,iBAAkB,CAAE,CAAAtD,QAAA,CAAEtJ,SAAS,CAAC,OAAO,CAAE,CAAEiO,YAAY,CAAE,OAAQ,CAAC,CAAC,CAAa,CAAC,EACzG,CAAC,CACE,CAAC,EAsDN,CAAC,CACE,CACT,CACA,CAAC,CAEL,CAAC,CAED,KAAM,CAAApC,UAAU,CAAG0C,KAAA,EAiCb,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,gBAAA,IAjCc,CACnBtP,aAAa,CACbsD,mBAAmB,CACnBvC,OAAO,CACPd,gBAAgB,CAChB8C,MAAM,CACNE,UAAU,CACVqC,mBAAmB,CACnBC,oBAAoB,CACpBrC,YAAY,CACZhD,aAAa,CACbU,qBAAqB,CACrB2L,eAAe,CACf5I,kBAAkB,CAClB6I,gBAAgB,CAChBN,eAkBD,CAAC,CAAA8C,KAAA,CACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AAEA,KAAM,CAAArK,WAAW,CAAGA,CAAA,GAAM,CACzBhB,kBAAkB,CAAC,IAAI,CAAC,CACxB4L,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC,CACjC,CAAC,CACD,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGlS,QAAQ,CAAU,KAAK,CAAC,CACpEF,SAAS,CAAC,IAAM,CACb,KAAM,CAAAqS,GAAG,CAAGlH,QAAQ,CAACmH,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC,EAAI,KAAK,CACtDH,iBAAiB,CAACC,GAAG,CAACG,WAAW,CAAC,CAAC,GAAK,KAAK,CAAC,CAClD,CAAC,CAAE,EAAE,CAAC,CACL,KAAM,CAAAC,YAAY,CAAGtS,OAAO,CAC3B,KAAO,CACNuS,SAAS,CAAEP,cAAc,CAAG,KAAK,CAAY,KAAc,CAE3D;AACEQ,QAAQ,CAAG,IAAI,CAAE;AACnBC,aAAa,CAAE,KAAK,CACpBC,eAAe,CAAE,KAAK,CACtBC,SAAS,CAAE,IAAI,CAAE;AACjBC,OAAO,CAAE,CACR,MAAM,CACN,QAAQ,CACR,WAAW,CACX,OAAO,CACP,MAAM,CACN,UAAU,CACV,MAAM,CACN,CACCC,IAAI,CAAE,MAAM,CACZC,OAAO,CAAE,+DAA+D,CACxEC,IAAI,CAAE,CACL,QAAQ,CACR,eAAe,CACf,IAAI,CACJ,IAAI,CACJ,OAAO,CACP,OAAO,CACP,OAAO,CACP,OAAO,CACP,MAAM,CACN,MAAM,CACN,GAAG,CACH,IAAI,CACJ,QAAQ,CACR,YAAY,CACZ,QAAQ,CACR,UAAU,CACV,OAAO,CACP,aAAa,CACb,WAAW,CACX,GAAG,CACH,SAAS,CACT,QAAQ,CACR,WAAW,CAEb,CAAC,CACD,CACDC,MAAM,CAAE,CACPC,KAAK,CAAE/L,WAAW,CAClB;AACAgM,SAAS,CAAGC,MAAW,EAAK,CAC3B;AACAnM,UAAU,CAAC,IAAM,CAChBmM,MAAM,CAACF,KAAK,CAAC,CAAC,CACf,CAAC,CAAE,CAAC,CAAC,CACN,CACD,CAAC,CACDpG,SAAS,CAAE,mBACZ,CAAC,CAAC,CACF,CAACmF,cAAc,CAChB,CAAC,CAED,mBACCvQ,IAAA,CAAAE,SAAA,EAAA2K,QAAA,CACE/J,aAAa,eACbrB,YAAY,cACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAO,IAAA,CAACtB,KAAK,EACLoJ,KAAK,CAAE,CACNsC,QAAQ,CAAE,UAAU,CACpB+E,MAAM,CAAE,KAAK,CACblB,IAAI,CAAE,EAAA8B,qBAAA,EAAAC,sBAAA,CAACzG,QAAQ,CAACoI,cAAc,CAAC,gBAAgB,CAAC,UAAA3B,sBAAA,kBAAAC,sBAAA,CAAzCD,sBAAA,CAA2CjG,qBAAqB,CAAC,CAAC,UAAAkG,sBAAA,iBAAlEA,sBAAA,CAAoE5F,CAAC,UAAA0F,qBAAA,UAAAA,qBAAA,CAAI,GAAG,EAAI,CAAC,CACxF;AACA/B,GAAG,CAAE,EAAAkC,sBAAA,CAAA3G,QAAQ,CAACoI,cAAc,CAAC,gBAAgB,CAAC,UAAAzB,sBAAA,kBAAAC,sBAAA,CAAzCD,sBAAA,CAA2CnG,qBAAqB,CAAC,CAAC,UAAAoG,sBAAA,iBAAlEA,sBAAA,CAAoE7F,CAAC,EAAG,EAAE,EAAI,EAAE,CACrFlI,KAAK,CAAE,CAAAkL,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAElL,KAAK,GAAI,OACnC,CAAE,CACFwP,SAAS,CAAE,KAAM,CACjBlM,EAAE,CAAC,mBAAmB,CACtBmM,IAAI,CAAC,cAAc,CACnB7G,EAAE,CAAE,CACH,0BAA0B,CAAE,CAC3BD,OAAO,CAAE,iBACV,CAAC,CACD,4BAA4B,CAAE,CAC7B+G,aAAa,CAAE,MAChB,CAAC,CACD,gCAAgC,CAAE,CACjCC,QAAQ,CAAE,gBACX,CAAC,CACD,oBAAoB,CAAE,CACrB3G,SAAS,CAAE,gCAAgC,CAC3C4G,SAAS,CAAE,iBACZ,CACD,CAAE,CAAAnH,QAAA,cAEF7K,IAAA,CAACL,WAAW,EACX0J,KAAK,CAAE,CAAAxF,MAAM,SAANA,MAAM,kBAAAuM,gBAAA,CAANvM,MAAM,CAAEiC,OAAO,UAAAsK,gBAAA,iBAAfA,gBAAA,CAAiBpK,SAAS,GAAI,EAAG,CACxCmE,SAAS,CAAC,aAAa,CACvB8H,MAAM,CAAE,CACP,GAAGpB,YAAY,CACfqB,WAAW,CAAE,sBAAsB,CACnCC,QAAQ,CAAE,CACTC,IAAI,CAAE,CACLd,IAAI,CAAE,CACL,qBAAqB,CAAE,SAAS,CAChC,oBAAoB,CAAE,QAAQ,CAC9B,2BAA2B,CAAE,eAAe,CAC5C,uBAAuB,CAAE,WAAW,CACpC,qBAAqB,CAAE,SAAS,CAChC,4BAA4B,CAAE,gBAC/B,CACD,CACD,CACD,CAAE,CACFe,MAAM,CAAEA,CAACC,UAAU,CAAElN,KAAK,GAAK,KAAAmN,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAC9B,KAAM,CAAAC,aAAa,EAAAJ,qBAAA,CAAGhJ,QAAQ,CAACkB,aAAa,CAAC,cAAc,CAAC,UAAA8H,qBAAA,iBAAtCA,qBAAA,CAAwCK,QAAQ,CAACxN,KAAK,CAACyN,MAAc,CAAC,CAC5F,KAAM,CAAAC,kBAAkB,EAAAN,sBAAA,CAAGjJ,QAAQ,CAACkB,aAAa,CAAC,gBAAgB,CAAC,UAAA+H,sBAAA,iBAAxCA,sBAAA,CAA0CI,QAAQ,CAACxN,KAAK,CAACyN,MAAc,CAAC,CACnG,KAAM,CAAAE,sBAAsB,EAAAN,sBAAA,CAAGlJ,QAAQ,CACrCkB,aAAa,CAAC,sBAAsB,CAAC,UAAAgI,sBAAA,iBADRA,sBAAA,CAE5BG,QAAQ,CAACxN,KAAK,CAACyN,MAAc,CAAC,CACjC,KAAM,CAAAG,iBAAiB,CAAI5N,KAAK,CAACyN,MAAM,CAAiBnN,EAAE,CAACuN,UAAU,CAAC,yBAAyB,CAAC,CAChG,KAAM,CAAAC,WAAW,EAAAR,sBAAA,CAAGnJ,QAAQ,CAACkB,aAAa,CAAC,wBAAwB,CAAC,UAAAiI,sBAAA,iBAAhDA,sBAAA,CAAkDE,QAAQ,CAACxN,KAAK,CAACyN,MAAc,CAAC,CACpG;AACA,KAAM,CAAAM,qBAAqB,CACzB/N,KAAK,CAACyN,MAAM,CAAiBO,OAAO,CAAC,+BAA+B,CAAC,GAAK,IAAI,CAChF,KAAM,CAAAC,YAAY,CACjBjO,KAAK,CAACH,IAAI,GAAK,OAAO,EACrBG,KAAK,CAACH,IAAI,GAAK,SAAS,EACvBG,KAAK,CAA8BkO,OAAO,EAC1ClO,KAAK,CAA8BmO,GAAG,GAAK,GAAI,CAClD;AACA,KAAM,CAAAC,cAAc,CAAIpO,KAAK,CAACyN,MAAM,CAAiBO,OAAO,CAAC,8BAA8B,CAAC,GAAK,IAAI,CACrG,KAAM,CAAAK,WAAW,CAAGlK,QAAQ,CAACkB,aAAa,CAAC,gBAAgB,CAAC,CAE5D,GACC5G,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEiC,OAAO,EACf,CAAC6M,aAAa,EACd,CAACK,iBAAiB,EAClB,CAACE,WAAW,EACZ,CAACC,qBAAqB,EACtB,CAACK,cAAc,EACf,CAACC,WAAW,EACZV,sBAAsB,GAAK9O,SAAS,CACnC,CACDJ,MAAM,CAACiC,OAAO,CAACE,SAAS,CAAGsM,UAAU,CACrC5Q,qBAAqB,CAAC0C,mBAAmB,CAAEkO,UAAU,CAACnM,IAAI,CAAC,CAAC,CAAC,CAC7DpF,gBAAgB,CAAC,KAAK,CAAC,CACxB,CACD,CAAE,CACF2S,QAAQ,CAAGpB,UAAU,EAAK,CACzB,GAAIzO,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEiC,OAAO,CAAE,CACpBjC,MAAM,CAACiC,OAAO,CAACE,SAAS,CAAGsM,UAAU,CACtC,CACD,CAAE,CACF,CAAC,CACI,CAAC,CACR;AACA/I,QAAQ,CAACmH,IAAI,CACb,oBACD,CAAC,CACD,CAAC,CAEL,CAAC,CACD,KAAM,CAAAlD,WAAW,CAAGmG,KAAA,EAAuG,IAAtG,CAAE1Q,KAAK,CAAEwK,UAAU,CAAEjK,aAA2E,CAAC,CAAAmQ,KAAA,CACrH,mBACC3T,IAAA,CAACjB,aAAa,EACb2O,OAAO,CAAC,MAAM,CACdzK,KAAK,CAAEA,KAAM,CACbmH,QAAQ,CAAC,QAAQ,CACjBqD,UAAU,CAAEA,UAAU,CAAG,CAAE,CAC3BzC,EAAE,CAAE,CAAE4I,QAAQ,CAAE,CAAC,CAAE7I,OAAO,CAAE,MAAM,CAAE8I,cAAc,CAAE,QAAQ,CAAEC,UAAU,CAAC,SAAS,CAAE,+BAA+B,CAAE,CACpHlG,eAAe,CAAEpK,aAAe;AAC/B,CAAC,CAAE,CACLuQ,UAAU,cAAE/T,IAAA,CAAAE,SAAA,GAAI,CAAE,CAClB8T,UAAU,cAAEhU,IAAA,CAAAE,SAAA,GAAI,CAAE,CAClB,CAAC,CAEJ,CAAC,CACD,KAAM,CAAA2N,iBAAiB,CAAGoG,KAAA,EAAmG,IAAlG,CAAEhR,KAAK,CAAEwK,UAAU,CAACjK,aAAwE,CAAC,CAAAyQ,KAAA,CACvH,mBACCjU,IAAA,CAACxB,GAAG,EAACwM,EAAE,CAAE,CACRD,OAAO,CAAE,MAAM,CACf8I,cAAc,CAAE,QAAQ,CACxB5E,GAAG,CAAE,KAAO;AAEX,CAAE,CAAApE,QAAA,CAGFqJ,KAAK,CAACC,IAAI,CAAC,CAAE5G,MAAM,CAAEtK,KAAM,CAAC,CAAC,CAAC8I,GAAG,CAAC,CAACqI,CAAC,CAAEvN,KAAK,gBAC1C7G,IAAA,QAED8H,KAAK,CAAE,CACL1F,KAAK,CAAE,MAAM,CACb2F,MAAM,CAAE,KAAK,CACb6F,eAAe,CAAE/G,KAAK,GAAK4G,UAAU,CAAG,CAAC,CAAGjK,aAAa,CAAG,SAAS,CAAE;AACvEnB,YAAY,CAAE,OAChB,CAAE,EANGwE,KAOH,CACF,CAAC,CAEE,CAAC,CAER,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}