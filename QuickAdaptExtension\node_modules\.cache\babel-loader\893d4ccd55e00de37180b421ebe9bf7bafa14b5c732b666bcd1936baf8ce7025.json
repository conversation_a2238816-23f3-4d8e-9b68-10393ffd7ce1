{"ast": null, "code": "import React,{useContext,useEffect,useRef,useState}from'react';import PopupList from'../guideList/PopupList';import StopScrapingButton from\"../../AI/StopScrapingButton\";import{ProductToursicon,Tooltipsicon,Bannersicon,Checklisticon,Hotspoticon,Surveyicon,Announcementsicon}from'../../../assets/icons/icons';import'./GuideMenuOptions.css';import useDrawerStore from'../../../store/drawerStore';import{isScrapingActive,stopScraping,startScraping,getScrapedDataCount,loadScrapedDataFromStorage,exportScrapedDataToFile}from'../../../services/ScrapingService';import{Tooltip}from'@mui/material';import PerfectScrollbar from'react-perfect-scrollbar';import'react-perfect-scrollbar/dist/css/styles.css';import ModernChatWindow from'../../AIAgent/ModernChatWindow';import CreateWithAIButton from'../../AIAgent/CreateWithAIButton';import userSession from'../../../store/userSession';import{AccountContext}from'../../login/AccountContext';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Guidemenu=_ref=>{let{isAIGuidePersisted,setIsAIGuidePersisted,setStepData,onGridItemClick,isCollapsed,toggleDrawer,activeMenu,setActiveMenu,searchText,setSearchText,setisShowIcon,setIsPopupOpen,setIsLoggedIn,setIsTourPopupOpen,setIsDrawerClosed,setShowBannerenduser,currentAccountId}=_ref;const{t:translate}=useTranslation();const{accountId}=useContext(AccountContext);const{setSelectedTemplate,setSelectedTemplateTour,setSteps,steps,setTooltipCount,tooltipCount,SetGuideName,setIsTooltipPopup,HotspotGuideDetails,setBannerPopup,setElementSelected,TooltipGuideDetails,HotspotGuideDetailsNew,setSelectedStepTypeHotspot,selectedTemplate,selectedTemplateTour,activeMenu:drawerActiveMenu,searchText:drawerSearchText,setActiveMenu:setDrawerActiveMenu,setSearchText:setDrawerSearchText,isExtensionClosed,setIsExtensionClosed,isAgentTraining,agentName,agentDescription,agentUrl}=useDrawerStore(state=>state);useEffect(()=>{if(isExtensionClosed){setIsExtensionClosed(false);}},[]);// State for AI chat window visibility\nconst[isAIChatOpen,setIsAIChatOpen]=useState(false);// Get the setCurrentGuideId function from userSession\nconst{setCurrentGuideId}=userSession(state=>state);const{hasAnnouncementOpened,setHasAnnouncementOpened}=userSession();const hasTriggered=useRef(false);useEffect(()=>{if(hasAnnouncementOpened&&!hasTriggered.current){setDrawerActiveMenu(\"announcements\");setDrawerSearchText(\"Announcement\");hasTriggered.current=true;// optionally reset the flag if you want it to trigger again next login\nsetHasAnnouncementOpened(false);}},[hasAnnouncementOpened]);// Add this effect to handle reopening\nuseEffect(()=>{// When extension is reopened (isExtensionClosed becomes false)\nif(!isExtensionClosed&&activeMenu){// Reopen the popup with the previously selected menu\nsetDrawerActiveMenu(activeMenu);setDrawerSearchText(searchText);setIsPopupOpen(true);}},[isExtensionClosed,activeMenu,searchText]);const menuItems=[{id:\"announcements\",name:\"Announcement\",// Use English for logic\ndescription:translate(\"Helps to communicate important updates, notifications, or messages.\",{defaultValue:\"Helps to communicate important updates, notifications, or messages.\"}),icon:/*#__PURE__*/_jsx(\"span\",{className:`${isCollapsed?\"qadpt-colsvg\":\"\"}`,dangerouslySetInnerHTML:{__html:Announcementsicon}})},{id:\"banners\",name:\"Banner\",description:translate(\"Displays notifications at the top or bottom of the screen.\",{defaultValue:\"Displays notifications at the top or bottom of the screen.\"}),icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:Bannersicon}})},{id:\"tooltips\",name:\"Tooltip\",description:translate(\"Provide quick explanations, tips, or instructions of the tools,\",{defaultValue:\"Provide quick explanations, tips, or instructions of the tools,\"}),icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:Tooltipsicon}}),disabled:false},{id:\"hotspot\",name:\"Hotspot\",description:translate(\"Interactive areas to draw attention to important features, actions, or guidance.\",{defaultValue:\"Interactive areas to draw attention to important features, actions, or guidance.\"}),icon:/*#__PURE__*/_jsx(\"span\",{className:`${isCollapsed?\"qadpt-colsvg\":\"\"}`,dangerouslySetInnerHTML:{__html:Hotspoticon}}),disabled:false},{id:\"tours\",name:\"Tour\",description:translate(\"Step-by-step guides to navigate and understand key features.\",{defaultValue:\"Step-by-step guides to navigate and understand key features.\"}),icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:ProductToursicon}}),disabled:false},{id:\"checklists\",name:\"Checklist\",description:translate(\"Task lists that guide users through a series of steps or actions\",{defaultValue:\"Task lists that guide users through a series of steps or actions\"}),icon:/*#__PURE__*/_jsx(\"span\",{className:`${isCollapsed?\"qadpt-colsvg\":\"\"}`,dangerouslySetInnerHTML:{__html:Checklisticon}}),disabled:false},{id:\"survey\",name:\"Survey\",description:translate(\"Interactive forms or questionnaires designed to collect feedback, insights, or opinions\",{defaultValue:\"Interactive forms or questionnaires designed to collect feedback, insights, or opinions\"}),icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:Surveyicon}}),disabled:true}];// State for AI scraping\nconst[showScrapingButton,setShowScrapingButton]=useState(false);const[scrapedCount,setScrapedCount]=useState(0);// Check scraping status on component mount and periodically\nuseEffect(()=>{// Initial check\nsetShowScrapingButton(isScrapingActive());setScrapedCount(getScrapedDataCount());// Set up periodic check\nconst checkInterval=setInterval(()=>{setShowScrapingButton(isScrapingActive());setScrapedCount(getScrapedDataCount());},1000);// Clean up interval on unmount\nreturn()=>clearInterval(checkInterval);},[]);const handleEnableAI=async()=>{// Check if scraping is already active\nif(isScrapingActive()){alert('Scraping is already in progress');return;}try{// Start scraping directly without file upload (now async)\nawait startScraping();setShowScrapingButton(true);}catch(error){console.error('Error starting scraping:',error);alert('Error starting scraping');}};const handleStopScraping=async()=>{try{// Use the service to stop scraping - don't clear storage for \"Stop Training\" (save)\nawait stopScraping(isAgentTraining,accountId,agentName,agentDescription,agentUrl);setShowScrapingButton(false);// Show scraped data in console\nif(scrapedCount>0){}else{alert('No elements were scraped.');}}catch(error){console.error('Error stopping scraping:',error);alert('Error stopping scraping');}};const handleViewScrapedData=async()=>{try{// Also try to load and display stored data\nconst storedData=await loadScrapedDataFromStorage();if(storedData){console.group('🗄️ Stored Scraped Data');console.log('Stored data:',storedData);console.groupEnd();}// alert(`${scrapedCount} elements have been scraped. Check the console for current and stored data.`);\n}catch(error){console.error('Error viewing scraped data:',error);// alert(`${scrapedCount} elements have been scraped. Check the console for details.`);\n}};const handleExportToFile=async()=>{try{await exportScrapedDataToFile();}catch(error){console.error('Error sending to API:',error);alert('Error sending data to backend API. Check console for details.');}};const handleMenuClick=async(menuId,menuName)=>{var _menuItems$find;if(isCollapsed===true){toggleDrawer(false);}if((_menuItems$find=menuItems.find(item=>item.id===menuId))!==null&&_menuItems$find!==void 0&&_menuItems$find.disabled)return;// Handle AI chat window separately\nif(menuId===\"createwithai\"){setIsAIChatOpen(true);return;}setDrawerActiveMenu(menuId);setDrawerSearchText(menuName);};const handleClosePopup=()=>{setDrawerActiveMenu(null);setDrawerSearchText(\"\");};const handleCloseAIChat=()=>{setIsAIChatOpen(false);};const handleAddClick=function(searchText){var _menuItems$find2;let isEditing=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;let guideDetails=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;setisShowIcon(true);onGridItemClick(searchText,isEditing,guideDetails);setDrawerActiveMenu(((_menuItems$find2=menuItems.find(item=>item.name===searchText))===null||_menuItems$find2===void 0?void 0:_menuItems$find2.id)||null);if(isCollapsed){toggleDrawer();}};return/*#__PURE__*/_jsxs(\"div\",{style:{top:isCollapsed?\"20px\":\"0px\",position:\"relative\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"side-menu\",style:{height:\"calc(100vh - 65px)\",overflow:\"hidden\"},children:/*#__PURE__*/_jsxs(PerfectScrollbar,{children:[/*#__PURE__*/_jsx(CreateWithAIButton,{onClick:()=>handleMenuClick(\"createwithai\",translate(\"Create With AI\"))}),/*#__PURE__*/_jsx(\"ul\",{className:\"menu-list\",children:menuItems.map(item=>/*#__PURE__*/_jsx(Tooltip,{title:item.disabled?translate(\"Coming Soon\"):translate(item.name),PopperProps:{sx:{zIndex:9999}},children:/*#__PURE__*/_jsx(\"span\",{style:{cursor:item.disabled?\"not-allowed\":\"pointer\"},children:/*#__PURE__*/_jsx(\"li\",{\"data-id\":item.id,className:`menu-item ${drawerActiveMenu===item.id?\"active\":\"\"} ${item.disabled?\"disabled\":\"\"}`,onClick:()=>handleMenuClick(item.id,item.name),style:{opacity:item.disabled?0.5:1},children:/*#__PURE__*/_jsxs(\"div\",{className:\"menu-content\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"icons\",children:/*#__PURE__*/_jsx(\"div\",{children:item.icon})}),!isCollapsed&&/*#__PURE__*/_jsxs(\"div\",{className:\"menu-text\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"menu-title\",children:item.id===\"createwithai\"?translate(item.name):translate(`${item.name}`,{defaultValue:`${item.name}s`})}),/*#__PURE__*/_jsx(\"div\",{className:\"menu-description\",children:item.description})]})]})})})},item.id))}),!isCollapsed&&!isAIChatOpen&&menuItems.map(item=>/*#__PURE__*/_jsx(PopupList,{title:item.name,Open:drawerActiveMenu===item.id,onClose:handleClosePopup,searchText:drawerSearchText,onAddClick:handleAddClick,isAIGuidePersisted:isAIGuidePersisted,setIsAIGuidePersisted:setIsAIGuidePersisted},item.id))]})}),isAIChatOpen&&/*#__PURE__*/_jsx(ModernChatWindow,{onClose:handleCloseAIChat,setStepData:setStepData,setIsAIChatOpen:setIsAIChatOpen,setCurrentGuideId:setCurrentGuideId,setIsPopupOpen:setIsPopupOpen,setIsLoggedIn:setIsLoggedIn,setIsTourPopupOpen:setIsTourPopupOpen,setIsDrawerClosed:setIsDrawerClosed,setShowBannerenduser:setShowBannerenduser}),showScrapingButton&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'8px'},children:[/*#__PURE__*/_jsx(StopScrapingButton,{onClick:handleStopScraping}),scrapedCount>0&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#4CAF50',textAlign:'center',cursor:'pointer',padding:'4px 8px',backgroundColor:'rgba(76, 175, 80, 0.1)',borderRadius:'4px',border:'1px solid rgba(76, 175, 80, 0.3)'},onClick:handleViewScrapedData,children:[\"\\uD83D\\uDCCA \",scrapedCount,\" elements scraped (click to view)\"]}),/*#__PURE__*/_jsx(\"button\",{style:{fontSize:'12px',color:'#2196F3',backgroundColor:'rgba(33, 150, 243, 0.1)',border:'1px solid rgba(33, 150, 243, 0.3)',borderRadius:'4px',padding:'6px 12px',cursor:'pointer',fontWeight:'bold'},onClick:handleExportToFile,children:\"\\uD83D\\uDE80 Send to API\"})]})]})]});};export default Guidemenu;", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useRef", "useState", "PopupList", "StopScrapingButton", "ProductToursicon", "Tooltipsicon", "Bannersicon", "<PERSON><PERSON><PERSON>", "Hotspoticon", "Surveyicon", "Announcementsicon", "useDrawerStore", "isScrapingActive", "stopScraping", "startScraping", "getScrapedDataCount", "loadScrapedDataFromStorage", "exportScrapedDataToFile", "<PERSON><PERSON><PERSON>", "PerfectScrollbar", "ModernChatWindow", "CreateWithAIButton", "userSession", "AccountContext", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Guidemenu", "_ref", "isAIGuidePersisted", "setIsAIGuidePersisted", "setStepData", "onGridItemClick", "isCollapsed", "toggle<PERSON>rawer", "activeMenu", "setActiveMenu", "searchText", "setSearchText", "setisShowIcon", "setIsPopupOpen", "setIsLoggedIn", "setIsTourPopupOpen", "setIsDrawerClosed", "setShowBannerenduser", "currentAccountId", "t", "translate", "accountId", "setSelectedTemplate", "setSelectedTemplateTour", "setSteps", "steps", "setTooltipCount", "tooltipCount", "SetGuideName", "setIsTooltipPopup", "HotspotGuideDetails", "setBannerPopup", "setElementSelected", "TooltipGuideDetails", "HotspotGuideDetailsNew", "setSelectedStepTypeHotspot", "selectedTemplate", "selectedTemplateTour", "drawerActiveMenu", "drawerSearchText", "setDrawerActiveMenu", "setDrawerSearchText", "isExtensionClosed", "setIsExtensionClosed", "isAgentTraining", "<PERSON><PERSON><PERSON>", "agentDescription", "agentUrl", "state", "isAIChatOpen", "setIsAIChatOpen", "setCurrentGuideId", "hasAnnouncementOpened", "setHasAnnouncementOpened", "hasTriggered", "current", "menuItems", "id", "name", "description", "defaultValue", "icon", "className", "dangerouslySetInnerHTML", "__html", "disabled", "showScrapingButton", "setShowScrapingButton", "scrapedCount", "setScrapedCount", "checkInterval", "setInterval", "clearInterval", "handleEnableAI", "alert", "error", "console", "handleStopScraping", "handleViewScrapedData", "storedData", "group", "log", "groupEnd", "handleExportToFile", "handleMenuClick", "menuId", "menuName", "_menuItems$find", "find", "item", "handleClosePopup", "handleCloseAIChat", "handleAddClick", "_menuItems$find2", "isEditing", "arguments", "length", "undefined", "guideDetails", "style", "top", "position", "children", "height", "overflow", "onClick", "map", "title", "PopperProps", "sx", "zIndex", "cursor", "opacity", "Open", "onClose", "onAddClick", "display", "flexDirection", "gap", "fontSize", "color", "textAlign", "padding", "backgroundColor", "borderRadius", "border", "fontWeight"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideSetting/guideList/GuideMenuOptions.tsx"], "sourcesContent": ["import React, { useContext, useEffect, useRef, useState } from 'react';\r\nimport PopupList from '../guideList/PopupList';\r\nimport StopScrapingButton from \"../../AI/StopScrapingButton\";\r\nimport {\r\n    ProductToursicon, Tooltipsicon,\r\n    announcementicon, Bannersicon,\r\n    Checklisticon, Hotspoticon,\r\n    Surveyicon, Announcementsicon,\r\n    ai\r\n} from '../../../assets/icons/icons';\r\nimport './GuideMenuOptions.css';\r\nimport EnableAIButton from '../../AI/EnableAI';\r\nimport useDrawerStore, { DrawerState } from '../../../store/drawerStore';\r\nimport { isScrapingActive, stopScraping, startScraping, getScrapedDataCount, loadScrapedDataFromStorage, exportScrapedDataToFile } from '../../../services/ScrapingService';\r\n\r\nimport { Tooltip } from '@mui/material';\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\nimport ModernChatWindow from '../../AIAgent/ModernChatWindow';\r\nimport CreateWithAIButton from '../../AIAgent/CreateWithAIButton';\r\n\r\nimport userSession from '../../../store/userSession';\r\nimport { AccountContext } from '../../login/AccountContext';\r\nimport { IsOpenAIKeyEnabledForAccount } from '../../../services/GuideListServices';\r\n\r\nimport { useTranslation } from 'react-i18next';\r\n\r\n\r\n\r\nconst Guidemenu = ({\r\n\tisAIGuidePersisted,\r\n\tsetIsAIGuidePersisted,\r\n\tsetStepData,\r\n\tonGridItemClick,\r\n\tisCollapsed,\r\n\ttoggleDrawer,\r\n\tactiveMenu,\r\n\tsetActiveMenu,\r\n\tsearchText,\r\n\tsetSearchText,\r\n\tsetisShowIcon,\r\n\tsetIsPopupOpen,\r\n\tsetIsLoggedIn,\r\n\tsetIsTourPopupOpen,\r\n\tsetIsDrawerClosed,\r\n\tsetShowBannerenduser,\r\n\tcurrentAccountId\r\n}: any) => {\r\n\tconst { t: translate } = useTranslation();\r\nconst{accountId}=useContext(AccountContext);\r\n\tconst {\r\n\t\tsetSelectedTemplate,\r\n\t\tsetSelectedTemplateTour,\r\n\t\tsetSteps,\r\n\t\tsteps,\r\n\t\tsetTooltipCount,\r\n\t\ttooltipCount,\r\n\t\tSetGuideName,\r\n\t\tsetIsTooltipPopup,\r\n\t\tHotspotGuideDetails,\r\n\t\tsetBannerPopup,\r\n\t\tsetElementSelected,\r\n\t\tTooltipGuideDetails,\r\n\t\tHotspotGuideDetailsNew,\r\n\t\tsetSelectedStepTypeHotspot,\r\n\t\tselectedTemplate,\r\n\t\tselectedTemplateTour,\r\n\t\tactiveMenu: drawerActiveMenu,\r\n\t\tsearchText: drawerSearchText,\r\n\t\tsetActiveMenu: setDrawerActiveMenu,\r\n\t\tsetSearchText: setDrawerSearchText,\r\n\t\tisExtensionClosed,\r\n\t\tsetIsExtensionClosed,\r\n\t\tisAgentTraining,\r\n\t\tagentName, \r\n\t\tagentDescription,\r\n\t\tagentUrl\r\n\t\t\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n\tuseEffect(() => {\r\n\t\tif (isExtensionClosed) {\r\n\t\t\tsetIsExtensionClosed(false);\r\n\t\t}\r\n\t}, []);\r\n\t// State for AI chat window visibility\r\n\tconst [isAIChatOpen, setIsAIChatOpen] = useState(false);\r\n\t// Get the setCurrentGuideId function from userSession\r\n\tconst { setCurrentGuideId } = userSession((state: any) => state);\r\n\r\n\r\n\tconst { hasAnnouncementOpened, setHasAnnouncementOpened } = userSession();\r\n\tconst hasTriggered = useRef(false);\r\n\r\n\tuseEffect(() => {\r\n\t\tif (hasAnnouncementOpened && !hasTriggered.current) {\r\n\t\t\tsetDrawerActiveMenu(\"announcements\");\r\n\t\t\tsetDrawerSearchText(\"Announcement\");\r\n\t\t\thasTriggered.current = true;\r\n\t\t\t// optionally reset the flag if you want it to trigger again next login\r\n\t\t\tsetHasAnnouncementOpened(false);\r\n\t\t}\r\n\t}, [hasAnnouncementOpened]);\r\n\r\n\t// Add this effect to handle reopening\r\n\tuseEffect(() => {\r\n\t\t// When extension is reopened (isExtensionClosed becomes false)\r\n\t\tif (!isExtensionClosed && activeMenu) {\r\n\t\t\t// Reopen the popup with the previously selected menu\r\n\t\t\tsetDrawerActiveMenu(activeMenu);\r\n\t\t\tsetDrawerSearchText(searchText);\r\n\t\t\tsetIsPopupOpen(true);\r\n\t\t}\r\n\t}, [isExtensionClosed, activeMenu, searchText]);\r\n\tconst menuItems = [\r\n\t\t{\r\n\t\t\tid: \"announcements\",\r\n\t\t\tname: \"Announcement\", // Use English for logic\r\n\t\t\tdescription: translate(\"Helps to communicate important updates, notifications, or messages.\", { defaultValue: \"Helps to communicate important updates, notifications, or messages.\" }),\r\n\t\t\ticon: <span className={`${isCollapsed ? \"qadpt-colsvg\" : \"\"}`}\r\n\t\t\tdangerouslySetInnerHTML={{ __html: Announcementsicon }} />,\r\n\t\t},\r\n\t\t{\r\n\t\t\tid: \"banners\",\r\n\t\t\tname: \"Banner\",\r\n\t\t\tdescription: translate(\"Displays notifications at the top or bottom of the screen.\", { defaultValue: \"Displays notifications at the top or bottom of the screen.\" }),\r\n\t\t\ticon: <span dangerouslySetInnerHTML={{ __html: Bannersicon }} />,\r\n\t\t},\r\n\t\t{\r\n\t\t\tid: \"tooltips\",\r\n\t\t\tname: \"Tooltip\",\r\n\t\t\tdescription: translate(\"Provide quick explanations, tips, or instructions of the tools,\", { defaultValue: \"Provide quick explanations, tips, or instructions of the tools,\" }),\r\n\t\t\ticon: <span dangerouslySetInnerHTML={{ __html: Tooltipsicon }} />,\r\n\t\t\tdisabled: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tid: \"hotspot\",\r\n\t\t\tname: \"Hotspot\",\r\n\t\t\tdescription: translate(\"Interactive areas to draw attention to important features, actions, or guidance.\", { defaultValue: \"Interactive areas to draw attention to important features, actions, or guidance.\" }),\r\n\t\t\ticon: (\r\n\t\t\t\t<span\r\n\t\t\t\tclassName={`${isCollapsed ? \"qadpt-colsvg\" : \"\"}`}\r\n\t\t\t\tdangerouslySetInnerHTML={{ __html: Hotspoticon }}\r\n\t\t\t\t/>\r\n\t\t\t),\t\t\tdisabled: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tid: \"tours\",\r\n\t\t\tname: \"Tour\",\r\n\t\t\tdescription: translate(\"Step-by-step guides to navigate and understand key features.\", { defaultValue: \"Step-by-step guides to navigate and understand key features.\" }),\r\n\t\t\ticon: <span dangerouslySetInnerHTML={{ __html: ProductToursicon }} />,\r\n\t\t\tdisabled: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tid: \"checklists\",\r\n\t\t\tname: \"Checklist\",\r\n\t\t\tdescription: translate(\"Task lists that guide users through a series of steps or actions\", { defaultValue: \"Task lists that guide users through a series of steps or actions\" }),\r\n\t\t\ticon: (\r\n\t\t\t\t<span\r\n\t\t\t\t\tclassName={`${isCollapsed ? \"qadpt-colsvg\" : \"\"}`}\r\n\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Checklisticon }}\r\n\t\t\t\t/>\r\n\t\t\t),\t\t\tdisabled: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tid: \"survey\",\r\n\t\t\tname: \"Survey\",\r\n\t\t\tdescription: translate(\"Interactive forms or questionnaires designed to collect feedback, insights, or opinions\", { defaultValue: \"Interactive forms or questionnaires designed to collect feedback, insights, or opinions\" }),\r\n\t\t\ticon: <span dangerouslySetInnerHTML={{ __html: Surveyicon }} />,\r\n\t\t\tdisabled: true,\r\n\t\t},\r\n\t];\r\n\t// State for AI scraping\r\n    const [showScrapingButton, setShowScrapingButton] = useState(false);\r\n    const [scrapedCount, setScrapedCount] = useState(0);\r\n\r\n    // Check scraping status on component mount and periodically\r\n    useEffect(() => {\r\n        // Initial check\r\n        setShowScrapingButton(isScrapingActive());\r\n        setScrapedCount(getScrapedDataCount());\r\n\r\n        // Set up periodic check\r\n        const checkInterval = setInterval(() => {\r\n            setShowScrapingButton(isScrapingActive());\r\n            setScrapedCount(getScrapedDataCount());\r\n        }, 1000);\r\n\r\n        // Clean up interval on unmount\r\n        return () => clearInterval(checkInterval);\r\n    }, []);\r\n\r\n\tconst handleEnableAI = async () => {\r\n        // Check if scraping is already active\r\n        if (isScrapingActive()) {\r\n            alert('Scraping is already in progress');\r\n            return;\r\n        }\r\n\r\n        try {\r\n            // Start scraping directly without file upload (now async)\r\n            await startScraping();\r\n            setShowScrapingButton(true);\r\n        } catch (error) {\r\n            console.error('Error starting scraping:', error);\r\n            alert('Error starting scraping');\r\n        }\r\n    };\r\n\r\n    const handleStopScraping = async () => {\r\n        try {\r\n            // Use the service to stop scraping - don't clear storage for \"Stop Training\" (save)\r\n            await stopScraping(isAgentTraining, accountId, agentName, agentDescription, agentUrl);\r\n            setShowScrapingButton(false);\r\n\r\n            // Show scraped data in console\r\n            if (scrapedCount > 0) {\r\n            } else {\r\n                alert('No elements were scraped.');\r\n            }\r\n        } catch (error) {\r\n            console.error('Error stopping scraping:', error);\r\n            alert('Error stopping scraping');\r\n        }\r\n    };\r\n\r\n    const handleViewScrapedData = async () => {\r\n        try {\r\n            \r\n\r\n            // Also try to load and display stored data\r\n            const storedData = await loadScrapedDataFromStorage();\r\n            if (storedData) {\r\n                console.group('🗄️ Stored Scraped Data');\r\n                console.log('Stored data:', storedData);\r\n                console.groupEnd();\r\n            }\r\n\r\n           // alert(`${scrapedCount} elements have been scraped. Check the console for current and stored data.`);\r\n        } catch (error) {\r\n            console.error('Error viewing scraped data:', error);\r\n           // alert(`${scrapedCount} elements have been scraped. Check the console for details.`);\r\n        }\r\n    };\r\n\r\n    const handleExportToFile = async () => {\r\n        try {\r\n            await exportScrapedDataToFile();\r\n        } catch (error) {\r\n            console.error('Error sending to API:', error);\r\n            alert('Error sending data to backend API. Check console for details.');\r\n        }\r\n    };\r\n\r\n\tconst handleMenuClick = async (menuId: string, menuName: string) => {\r\n\t\tif (isCollapsed === true) {\r\n\t\t\ttoggleDrawer(false);\r\n\t\t}\r\n\t\tif (menuItems.find((item) => item.id === menuId)?.disabled) return;\r\n\r\n\t\t// Handle AI chat window separately\r\n\t\tif (menuId === \"createwithai\") {\r\n\t\t\tsetIsAIChatOpen(true);\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tsetDrawerActiveMenu(menuId);\r\n\t\tsetDrawerSearchText(menuName);\r\n\t};\r\n\r\n\tconst handleClosePopup = () => {\r\n\t\tsetDrawerActiveMenu(null);\r\n\t\tsetDrawerSearchText(\"\");\r\n\t};\r\n\r\n\tconst handleCloseAIChat = () => {\r\n\t\tsetIsAIChatOpen(false);\r\n\t};\r\n\r\n\tconst handleAddClick = (searchText: string, isEditing: boolean = false, guideDetails: any = null) => {\r\n\t\tsetisShowIcon(true);\r\n\t\tonGridItemClick(searchText, isEditing, guideDetails);\r\n\t\tsetDrawerActiveMenu(menuItems.find((item) => item.name === searchText)?.id || null);\r\n\t\tif (isCollapsed) {\r\n\t\t\ttoggleDrawer();\r\n\t\t}\r\n\t};\r\n\r\n\treturn (\r\n\t\t<div style={{ top: isCollapsed ? \"20px\" : \"0px\", position: \"relative\" }}>\r\n\r\n\t\t\t<div className=\"side-menu\" style={{ height: \"calc(100vh - 65px)\", overflow: \"hidden\" }}>\r\n\t\t\t<PerfectScrollbar>\r\n\t\t\t\t\t<CreateWithAIButton onClick={() => handleMenuClick(\"createwithai\", translate(\"Create With AI\"))} />\r\n\t\t\t\t<ul className=\"menu-list\">\r\n\t\t\t\t\t{menuItems.map((item) => (\r\n\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\tkey={item.id}\r\n\t\t\t\t\t\t\ttitle={item.disabled ? translate(\"Coming Soon\") : translate(item.name)}\r\n\t\t\t\t\t\t\tPopperProps={{ sx: { zIndex: 9999 } }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span style={{ cursor: item.disabled ? \"not-allowed\" : \"pointer\" }}>\r\n\t\t\t\t\t\t\t\t<li\r\n\t\t\t\t\t\t\t\t\tdata-id={item.id}\r\n\t\t\t\t\t\t\t\t\tclassName={`menu-item ${drawerActiveMenu === item.id ? \"active\" : \"\"} ${item.disabled ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleMenuClick(item.id, item.name)}\r\n\t\t\t\t\t\t\t\t\tstyle={{ opacity: item.disabled ? 0.5 : 1 }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"menu-content\">\r\n\t\t\t\t\t\t\t\t\t\t<span className=\"icons\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div>{item.icon}</div>\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t{!isCollapsed && (\r\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"menu-text\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"menu-title\">{item.id === \"createwithai\" ? translate(item.name) : translate(`${item.name}`, { defaultValue: `${item.name}s` })}</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"menu-description\">{item.description}</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t))}\r\n\t\t\t\t</ul>\r\n\t\t\t\t{/* Render popups for each menu item */}\r\n\t\t\t\t{!isCollapsed && !isAIChatOpen && menuItems.map((item) => (\r\n\t\t\t\t\t<PopupList\r\n\t\t\t\t\t\tkey={item.id}\r\n\t\t\t\t\t\ttitle={item.name}\r\n\t\t\t\t\t\tOpen={drawerActiveMenu === item.id}\r\n\t\t\t\t\t\tonClose={handleClosePopup}\r\n\t\t\t\t\t\tsearchText={drawerSearchText}\r\n\t\t\t\t\t\tonAddClick={handleAddClick}\r\n\t\t\t\t\t\tisAIGuidePersisted={isAIGuidePersisted}\r\n\t\t\t\t\t\tsetIsAIGuidePersisted={setIsAIGuidePersisted}\r\n\t\t\t\t\t/>\r\n\t\t\t\t))}\r\n\t\t\t</PerfectScrollbar>\r\n\t\t</div>\r\n\t\t{/* AI Chat Window */}\r\n\t\t{isAIChatOpen && (\r\n\t\t\t<ModernChatWindow\r\n\t\t\t\tonClose={handleCloseAIChat}\r\n\t\t\t\tsetStepData={setStepData}\r\n\t\t\t\tsetIsAIChatOpen={setIsAIChatOpen}\r\n\t\t\t\tsetCurrentGuideId={setCurrentGuideId}\r\n\t\t\t\tsetIsPopupOpen={setIsPopupOpen}\r\n\t\t\t\tsetIsLoggedIn={setIsLoggedIn}\r\n\t\t\t\tsetIsTourPopupOpen={setIsTourPopupOpen}\r\n\t\t\t\tsetIsDrawerClosed={setIsDrawerClosed}\r\n\t\t\t\tsetShowBannerenduser={setShowBannerenduser}\r\n\t\t\t/>\r\n\t\t)}\r\n\t\t{/* <EnableAIButton onClick={handleEnableAI} /> */}\r\n\t\t{showScrapingButton && (\r\n\t\t\t<div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\r\n\t\t\t\t<StopScrapingButton onClick={handleStopScraping} />\r\n\t\t\t\t{scrapedCount > 0 && (\r\n\t\t\t\t\t<>\r\n\t\t\t\t\t\t<div style={{\r\n\t\t\t\t\t\t\tfontSize: '12px',\r\n\t\t\t\t\t\t\tcolor: '#4CAF50',\r\n\t\t\t\t\t\t\ttextAlign: 'center',\r\n\t\t\t\t\t\t\tcursor: 'pointer',\r\n\t\t\t\t\t\t\tpadding: '4px 8px',\r\n\t\t\t\t\t\t\tbackgroundColor: 'rgba(76, 175, 80, 0.1)',\r\n\t\t\t\t\t\t\tborderRadius: '4px',\r\n\t\t\t\t\t\t\tborder: '1px solid rgba(76, 175, 80, 0.3)'\r\n\t\t\t\t\t\t}} onClick={handleViewScrapedData}>\r\n\t\t\t\t\t\t\t📊 {scrapedCount} elements scraped (click to view)\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<button style={{\r\n\t\t\t\t\t\t\tfontSize: '12px',\r\n\t\t\t\t\t\t\tcolor: '#2196F3',\r\n\t\t\t\t\t\t\tbackgroundColor: 'rgba(33, 150, 243, 0.1)',\r\n\t\t\t\t\t\t\tborder: '1px solid rgba(33, 150, 243, 0.3)',\r\n\t\t\t\t\t\t\tborderRadius: '4px',\r\n\t\t\t\t\t\t\tpadding: '6px 12px',\r\n\t\t\t\t\t\t\tcursor: 'pointer',\r\n\t\t\t\t\t\t\tfontWeight: 'bold'\r\n\t\t\t\t\t\t}} onClick={handleExportToFile}>\r\n\t\t\t\t\t\t\t🚀 Send to API\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</>\r\n\t\t\t\t)}\r\n\t\t\t</div>\r\n\t\t)}\r\n\t</div>\r\n);\r\n}\r\n\r\nexport default Guidemenu;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,UAAU,CAAEC,SAAS,CAAEC,MAAM,CAAEC,QAAQ,KAAQ,OAAO,CACtE,MAAO,CAAAC,SAAS,KAAM,wBAAwB,CAC9C,MAAO,CAAAC,kBAAkB,KAAM,6BAA6B,CAC5D,OACIC,gBAAgB,CAAEC,YAAY,CACZC,WAAW,CAC7BC,aAAa,CAAEC,WAAW,CAC1BC,UAAU,CAAEC,iBAAiB,KAE1B,6BAA6B,CACpC,MAAO,wBAAwB,CAE/B,MAAO,CAAAC,cAAc,KAAuB,4BAA4B,CACxE,OAASC,gBAAgB,CAAEC,YAAY,CAAEC,aAAa,CAAEC,mBAAmB,CAAEC,0BAA0B,CAAEC,uBAAuB,KAAQ,mCAAmC,CAE3K,OAASC,OAAO,KAAQ,eAAe,CACvC,MAAO,CAAAC,gBAAgB,KAAM,yBAAyB,CACtD,MAAO,6CAA6C,CAEpD,MAAO,CAAAC,gBAAgB,KAAM,gCAAgC,CAC7D,MAAO,CAAAC,kBAAkB,KAAM,kCAAkC,CAEjE,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CACpD,OAASC,cAAc,KAAQ,4BAA4B,CAG3D,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAI/C,KAAM,CAAAC,SAAS,CAAGC,IAAA,EAkBP,IAlBQ,CAClBC,kBAAkB,CAClBC,qBAAqB,CACrBC,WAAW,CACXC,eAAe,CACfC,WAAW,CACXC,YAAY,CACZC,UAAU,CACVC,aAAa,CACbC,UAAU,CACVC,aAAa,CACbC,aAAa,CACbC,cAAc,CACdC,aAAa,CACbC,kBAAkB,CAClBC,iBAAiB,CACjBC,oBAAoB,CACpBC,gBACI,CAAC,CAAAjB,IAAA,CACL,KAAM,CAAEkB,CAAC,CAAEC,SAAU,CAAC,CAAG3B,cAAc,CAAC,CAAC,CAC1C,KAAK,CAAC4B,SAAS,CAAC,CAACtD,UAAU,CAACyB,cAAc,CAAC,CAC1C,KAAM,CACL8B,mBAAmB,CACnBC,uBAAuB,CACvBC,QAAQ,CACRC,KAAK,CACLC,eAAe,CACfC,YAAY,CACZC,YAAY,CACZC,iBAAiB,CACjBC,mBAAmB,CACnBC,cAAc,CACdC,kBAAkB,CAClBC,mBAAmB,CACnBC,sBAAsB,CACtBC,0BAA0B,CAC1BC,gBAAgB,CAChBC,oBAAoB,CACpB7B,UAAU,CAAE8B,gBAAgB,CAC5B5B,UAAU,CAAE6B,gBAAgB,CAC5B9B,aAAa,CAAE+B,mBAAmB,CAClC7B,aAAa,CAAE8B,mBAAmB,CAClCC,iBAAiB,CACjBC,oBAAoB,CACpBC,eAAe,CACfC,SAAS,CACTC,gBAAgB,CAChBC,QAED,CAAC,CAAGnE,cAAc,CAAEoE,KAAkB,EAAKA,KAAK,CAAC,CACjDhF,SAAS,CAAC,IAAM,CACf,GAAI0E,iBAAiB,CAAE,CACtBC,oBAAoB,CAAC,KAAK,CAAC,CAC5B,CACD,CAAC,CAAE,EAAE,CAAC,CACN;AACA,KAAM,CAACM,YAAY,CAAEC,eAAe,CAAC,CAAGhF,QAAQ,CAAC,KAAK,CAAC,CACvD;AACA,KAAM,CAAEiF,iBAAkB,CAAC,CAAG5D,WAAW,CAAEyD,KAAU,EAAKA,KAAK,CAAC,CAGhE,KAAM,CAAEI,qBAAqB,CAAEC,wBAAyB,CAAC,CAAG9D,WAAW,CAAC,CAAC,CACzE,KAAM,CAAA+D,YAAY,CAAGrF,MAAM,CAAC,KAAK,CAAC,CAElCD,SAAS,CAAC,IAAM,CACf,GAAIoF,qBAAqB,EAAI,CAACE,YAAY,CAACC,OAAO,CAAE,CACnDf,mBAAmB,CAAC,eAAe,CAAC,CACpCC,mBAAmB,CAAC,cAAc,CAAC,CACnCa,YAAY,CAACC,OAAO,CAAG,IAAI,CAC3B;AACAF,wBAAwB,CAAC,KAAK,CAAC,CAChC,CACD,CAAC,CAAE,CAACD,qBAAqB,CAAC,CAAC,CAE3B;AACApF,SAAS,CAAC,IAAM,CACf;AACA,GAAI,CAAC0E,iBAAiB,EAAIlC,UAAU,CAAE,CACrC;AACAgC,mBAAmB,CAAChC,UAAU,CAAC,CAC/BiC,mBAAmB,CAAC/B,UAAU,CAAC,CAC/BG,cAAc,CAAC,IAAI,CAAC,CACrB,CACD,CAAC,CAAE,CAAC6B,iBAAiB,CAAElC,UAAU,CAAEE,UAAU,CAAC,CAAC,CAC/C,KAAM,CAAA8C,SAAS,CAAG,CACjB,CACCC,EAAE,CAAE,eAAe,CACnBC,IAAI,CAAE,cAAc,CAAE;AACtBC,WAAW,CAAEvC,SAAS,CAAC,qEAAqE,CAAE,CAAEwC,YAAY,CAAE,qEAAsE,CAAC,CAAC,CACtLC,IAAI,cAAElE,IAAA,SAAMmE,SAAS,CAAE,GAAGxD,WAAW,CAAG,cAAc,CAAG,EAAE,EAAG,CAC9DyD,uBAAuB,CAAE,CAAEC,MAAM,CAAErF,iBAAkB,CAAE,CAAE,CAC1D,CAAC,CACD,CACC8E,EAAE,CAAE,SAAS,CACbC,IAAI,CAAE,QAAQ,CACdC,WAAW,CAAEvC,SAAS,CAAC,4DAA4D,CAAE,CAAEwC,YAAY,CAAE,4DAA6D,CAAC,CAAC,CACpKC,IAAI,cAAElE,IAAA,SAAMoE,uBAAuB,CAAE,CAAEC,MAAM,CAAEzF,WAAY,CAAE,CAAE,CAChE,CAAC,CACD,CACCkF,EAAE,CAAE,UAAU,CACdC,IAAI,CAAE,SAAS,CACfC,WAAW,CAAEvC,SAAS,CAAC,iEAAiE,CAAE,CAAEwC,YAAY,CAAE,iEAAkE,CAAC,CAAC,CAC9KC,IAAI,cAAElE,IAAA,SAAMoE,uBAAuB,CAAE,CAAEC,MAAM,CAAE1F,YAAa,CAAE,CAAE,CAAC,CACjE2F,QAAQ,CAAE,KACX,CAAC,CACD,CACCR,EAAE,CAAE,SAAS,CACbC,IAAI,CAAE,SAAS,CACfC,WAAW,CAAEvC,SAAS,CAAC,kFAAkF,CAAE,CAAEwC,YAAY,CAAE,kFAAmF,CAAC,CAAC,CAChNC,IAAI,cACHlE,IAAA,SACAmE,SAAS,CAAE,GAAGxD,WAAW,CAAG,cAAc,CAAG,EAAE,EAAG,CAClDyD,uBAAuB,CAAE,CAAEC,MAAM,CAAEvF,WAAY,CAAE,CAChD,CACD,CAAIwF,QAAQ,CAAE,KAChB,CAAC,CACD,CACCR,EAAE,CAAE,OAAO,CACXC,IAAI,CAAE,MAAM,CACZC,WAAW,CAAEvC,SAAS,CAAC,8DAA8D,CAAE,CAAEwC,YAAY,CAAE,8DAA+D,CAAC,CAAC,CACxKC,IAAI,cAAElE,IAAA,SAAMoE,uBAAuB,CAAE,CAAEC,MAAM,CAAE3F,gBAAiB,CAAE,CAAE,CAAC,CACrE4F,QAAQ,CAAE,KACX,CAAC,CACD,CACCR,EAAE,CAAE,YAAY,CAChBC,IAAI,CAAE,WAAW,CACjBC,WAAW,CAAEvC,SAAS,CAAC,kEAAkE,CAAE,CAAEwC,YAAY,CAAE,kEAAmE,CAAC,CAAC,CAChLC,IAAI,cACHlE,IAAA,SACCmE,SAAS,CAAE,GAAGxD,WAAW,CAAG,cAAc,CAAG,EAAE,EAAG,CAClDyD,uBAAuB,CAAE,CAAEC,MAAM,CAAExF,aAAc,CAAE,CACnD,CACD,CAAIyF,QAAQ,CAAE,KAChB,CAAC,CACD,CACCR,EAAE,CAAE,QAAQ,CACZC,IAAI,CAAE,QAAQ,CACdC,WAAW,CAAEvC,SAAS,CAAC,yFAAyF,CAAE,CAAEwC,YAAY,CAAE,yFAA0F,CAAC,CAAC,CAC9NC,IAAI,cAAElE,IAAA,SAAMoE,uBAAuB,CAAE,CAAEC,MAAM,CAAEtF,UAAW,CAAE,CAAE,CAAC,CAC/DuF,QAAQ,CAAE,IACX,CAAC,CACD,CACD;AACG,KAAM,CAACC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjG,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACkG,YAAY,CAAEC,eAAe,CAAC,CAAGnG,QAAQ,CAAC,CAAC,CAAC,CAEnD;AACAF,SAAS,CAAC,IAAM,CACZ;AACAmG,qBAAqB,CAACtF,gBAAgB,CAAC,CAAC,CAAC,CACzCwF,eAAe,CAACrF,mBAAmB,CAAC,CAAC,CAAC,CAEtC;AACA,KAAM,CAAAsF,aAAa,CAAGC,WAAW,CAAC,IAAM,CACpCJ,qBAAqB,CAACtF,gBAAgB,CAAC,CAAC,CAAC,CACzCwF,eAAe,CAACrF,mBAAmB,CAAC,CAAC,CAAC,CAC1C,CAAC,CAAE,IAAI,CAAC,CAER;AACA,MAAO,IAAMwF,aAAa,CAACF,aAAa,CAAC,CAC7C,CAAC,CAAE,EAAE,CAAC,CAET,KAAM,CAAAG,cAAc,CAAG,KAAAA,CAAA,GAAY,CAC5B;AACA,GAAI5F,gBAAgB,CAAC,CAAC,CAAE,CACpB6F,KAAK,CAAC,iCAAiC,CAAC,CACxC,OACJ,CAEA,GAAI,CACA;AACA,KAAM,CAAA3F,aAAa,CAAC,CAAC,CACrBoF,qBAAqB,CAAC,IAAI,CAAC,CAC/B,CAAE,MAAOQ,KAAK,CAAE,CACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDD,KAAK,CAAC,yBAAyB,CAAC,CACpC,CACJ,CAAC,CAED,KAAM,CAAAG,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACA;AACA,KAAM,CAAA/F,YAAY,CAAC8D,eAAe,CAAEvB,SAAS,CAAEwB,SAAS,CAAEC,gBAAgB,CAAEC,QAAQ,CAAC,CACrFoB,qBAAqB,CAAC,KAAK,CAAC,CAE5B;AACA,GAAIC,YAAY,CAAG,CAAC,CAAE,CACtB,CAAC,IAAM,CACHM,KAAK,CAAC,2BAA2B,CAAC,CACtC,CACJ,CAAE,MAAOC,KAAK,CAAE,CACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDD,KAAK,CAAC,yBAAyB,CAAC,CACpC,CACJ,CAAC,CAED,KAAM,CAAAI,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CAGA;AACA,KAAM,CAAAC,UAAU,CAAG,KAAM,CAAA9F,0BAA0B,CAAC,CAAC,CACrD,GAAI8F,UAAU,CAAE,CACZH,OAAO,CAACI,KAAK,CAAC,yBAAyB,CAAC,CACxCJ,OAAO,CAACK,GAAG,CAAC,cAAc,CAAEF,UAAU,CAAC,CACvCH,OAAO,CAACM,QAAQ,CAAC,CAAC,CACtB,CAED;AACH,CAAE,MAAOP,KAAK,CAAE,CACZC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACpD;AACH,CACJ,CAAC,CAED,KAAM,CAAAQ,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACA,KAAM,CAAAjG,uBAAuB,CAAC,CAAC,CACnC,CAAE,MAAOyF,KAAK,CAAE,CACZC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CD,KAAK,CAAC,+DAA+D,CAAC,CAC1E,CACJ,CAAC,CAEJ,KAAM,CAAAU,eAAe,CAAG,KAAAA,CAAOC,MAAc,CAAEC,QAAgB,GAAK,KAAAC,eAAA,CACnE,GAAIjF,WAAW,GAAK,IAAI,CAAE,CACzBC,YAAY,CAAC,KAAK,CAAC,CACpB,CACA,IAAAgF,eAAA,CAAI/B,SAAS,CAACgC,IAAI,CAAEC,IAAI,EAAKA,IAAI,CAAChC,EAAE,GAAK4B,MAAM,CAAC,UAAAE,eAAA,WAA5CA,eAAA,CAA8CtB,QAAQ,CAAE,OAE5D;AACA,GAAIoB,MAAM,GAAK,cAAc,CAAE,CAC9BnC,eAAe,CAAC,IAAI,CAAC,CACrB,OACD,CACAV,mBAAmB,CAAC6C,MAAM,CAAC,CAC3B5C,mBAAmB,CAAC6C,QAAQ,CAAC,CAC9B,CAAC,CAED,KAAM,CAAAI,gBAAgB,CAAGA,CAAA,GAAM,CAC9BlD,mBAAmB,CAAC,IAAI,CAAC,CACzBC,mBAAmB,CAAC,EAAE,CAAC,CACxB,CAAC,CAED,KAAM,CAAAkD,iBAAiB,CAAGA,CAAA,GAAM,CAC/BzC,eAAe,CAAC,KAAK,CAAC,CACvB,CAAC,CAED,KAAM,CAAA0C,cAAc,CAAG,QAAAA,CAAClF,UAAkB,CAA2D,KAAAmF,gBAAA,IAAzD,CAAAC,SAAkB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,IAAE,CAAAG,YAAiB,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAC/FnF,aAAa,CAAC,IAAI,CAAC,CACnBP,eAAe,CAACK,UAAU,CAAEoF,SAAS,CAAEI,YAAY,CAAC,CACpD1D,mBAAmB,CAAC,EAAAqD,gBAAA,CAAArC,SAAS,CAACgC,IAAI,CAAEC,IAAI,EAAKA,IAAI,CAAC/B,IAAI,GAAKhD,UAAU,CAAC,UAAAmF,gBAAA,iBAAlDA,gBAAA,CAAoDpC,EAAE,GAAI,IAAI,CAAC,CACnF,GAAInD,WAAW,CAAE,CAChBC,YAAY,CAAC,CAAC,CACf,CACD,CAAC,CAED,mBACCV,KAAA,QAAKsG,KAAK,CAAE,CAAEC,GAAG,CAAE9F,WAAW,CAAG,MAAM,CAAG,KAAK,CAAE+F,QAAQ,CAAE,UAAW,CAAE,CAAAC,QAAA,eAEvE3G,IAAA,QAAKmE,SAAS,CAAC,WAAW,CAACqC,KAAK,CAAE,CAAEI,MAAM,CAAE,oBAAoB,CAAEC,QAAQ,CAAE,QAAS,CAAE,CAAAF,QAAA,cACvFzG,KAAA,CAACT,gBAAgB,EAAAkH,QAAA,eACf3G,IAAA,CAACL,kBAAkB,EAACmH,OAAO,CAAEA,CAAA,GAAMrB,eAAe,CAAC,cAAc,CAAEhE,SAAS,CAAC,gBAAgB,CAAC,CAAE,CAAE,CAAC,cACpGzB,IAAA,OAAImE,SAAS,CAAC,WAAW,CAAAwC,QAAA,CACvB9C,SAAS,CAACkD,GAAG,CAAEjB,IAAI,eACnB9F,IAAA,CAACR,OAAO,EAEPwH,KAAK,CAAElB,IAAI,CAACxB,QAAQ,CAAG7C,SAAS,CAAC,aAAa,CAAC,CAAGA,SAAS,CAACqE,IAAI,CAAC/B,IAAI,CAAE,CACvEkD,WAAW,CAAE,CAAEC,EAAE,CAAE,CAAEC,MAAM,CAAE,IAAK,CAAE,CAAE,CAAAR,QAAA,cAEtC3G,IAAA,SAAMwG,KAAK,CAAE,CAAEY,MAAM,CAAEtB,IAAI,CAACxB,QAAQ,CAAG,aAAa,CAAG,SAAU,CAAE,CAAAqC,QAAA,cAClE3G,IAAA,OACC,UAAS8F,IAAI,CAAChC,EAAG,CACjBK,SAAS,CAAE,aAAaxB,gBAAgB,GAAKmD,IAAI,CAAChC,EAAE,CAAG,QAAQ,CAAG,EAAE,IAAIgC,IAAI,CAACxB,QAAQ,CAAG,UAAU,CAAG,EAAE,EAAG,CAC1GwC,OAAO,CAAEA,CAAA,GAAMrB,eAAe,CAACK,IAAI,CAAChC,EAAE,CAAEgC,IAAI,CAAC/B,IAAI,CAAE,CACnDyC,KAAK,CAAE,CAAEa,OAAO,CAAEvB,IAAI,CAACxB,QAAQ,CAAG,GAAG,CAAG,CAAE,CAAE,CAAAqC,QAAA,cAE5CzG,KAAA,QAAKiE,SAAS,CAAC,cAAc,CAAAwC,QAAA,eAC5B3G,IAAA,SAAMmE,SAAS,CAAC,OAAO,CAAAwC,QAAA,cACtB3G,IAAA,QAAA2G,QAAA,CAAMb,IAAI,CAAC5B,IAAI,CAAM,CAAC,CACjB,CAAC,CACN,CAACvD,WAAW,eACZT,KAAA,QAAKiE,SAAS,CAAC,WAAW,CAAAwC,QAAA,eACzB3G,IAAA,QAAKmE,SAAS,CAAC,YAAY,CAAAwC,QAAA,CAAEb,IAAI,CAAChC,EAAE,GAAK,cAAc,CAAGrC,SAAS,CAACqE,IAAI,CAAC/B,IAAI,CAAC,CAAGtC,SAAS,CAAC,GAAGqE,IAAI,CAAC/B,IAAI,EAAE,CAAE,CAAEE,YAAY,CAAE,GAAG6B,IAAI,CAAC/B,IAAI,GAAI,CAAC,CAAC,CAAM,CAAC,cACpJ/D,IAAA,QAAKmE,SAAS,CAAC,kBAAkB,CAAAwC,QAAA,CAAEb,IAAI,CAAC9B,WAAW,CAAM,CAAC,EACtD,CACL,EACG,CAAC,CACH,CAAC,CACA,CAAC,EAvBF8B,IAAI,CAAChC,EAwBF,CACT,CAAC,CACC,CAAC,CAEJ,CAACnD,WAAW,EAAI,CAAC2C,YAAY,EAAIO,SAAS,CAACkD,GAAG,CAAEjB,IAAI,eACpD9F,IAAA,CAACxB,SAAS,EAETwI,KAAK,CAAElB,IAAI,CAAC/B,IAAK,CACjBuD,IAAI,CAAE3E,gBAAgB,GAAKmD,IAAI,CAAChC,EAAG,CACnCyD,OAAO,CAAExB,gBAAiB,CAC1BhF,UAAU,CAAE6B,gBAAiB,CAC7B4E,UAAU,CAAEvB,cAAe,CAC3B1F,kBAAkB,CAAEA,kBAAmB,CACvCC,qBAAqB,CAAEA,qBAAsB,EAPxCsF,IAAI,CAAChC,EAQV,CACD,CAAC,EACe,CAAC,CACf,CAAC,CAELR,YAAY,eACZtD,IAAA,CAACN,gBAAgB,EAChB6H,OAAO,CAAEvB,iBAAkB,CAC3BvF,WAAW,CAAEA,WAAY,CACzB8C,eAAe,CAAEA,eAAgB,CACjCC,iBAAiB,CAAEA,iBAAkB,CACrCtC,cAAc,CAAEA,cAAe,CAC/BC,aAAa,CAAEA,aAAc,CAC7BC,kBAAkB,CAAEA,kBAAmB,CACvCC,iBAAiB,CAAEA,iBAAkB,CACrCC,oBAAoB,CAAEA,oBAAqB,CAC3C,CACD,CAEAiD,kBAAkB,eAClBrE,KAAA,QAAKsG,KAAK,CAAE,CAAEiB,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEC,GAAG,CAAE,KAAM,CAAE,CAAAhB,QAAA,eACpE3G,IAAA,CAACvB,kBAAkB,EAACqI,OAAO,CAAE5B,kBAAmB,CAAE,CAAC,CAClDT,YAAY,CAAG,CAAC,eAChBvE,KAAA,CAAAE,SAAA,EAAAuG,QAAA,eACCzG,KAAA,QAAKsG,KAAK,CAAE,CACXoB,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,QAAQ,CACnBV,MAAM,CAAE,SAAS,CACjBW,OAAO,CAAE,SAAS,CAClBC,eAAe,CAAE,wBAAwB,CACzCC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,kCACT,CAAE,CAACpB,OAAO,CAAE3B,qBAAsB,CAAAwB,QAAA,EAAC,eAC/B,CAAClC,YAAY,CAAC,mCAClB,EAAK,CAAC,cACNzE,IAAA,WAAQwG,KAAK,CAAE,CACdoB,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,SAAS,CAChBG,eAAe,CAAE,yBAAyB,CAC1CE,MAAM,CAAE,mCAAmC,CAC3CD,YAAY,CAAE,KAAK,CACnBF,OAAO,CAAE,UAAU,CACnBX,MAAM,CAAE,SAAS,CACjBe,UAAU,CAAE,MACb,CAAE,CAACrB,OAAO,CAAEtB,kBAAmB,CAAAmB,QAAA,CAAC,0BAEhC,CAAQ,CAAC,EACR,CACF,EACG,CACL,EACG,CAAC,CAEP,CAAC,CAED,cAAe,CAAAtG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}