{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\GuidesPreview\\\\HotspotPreview.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, Typography } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\n// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\nimport PerfectScrollbar from 'react-perfect-scrollbar';\nimport 'react-perfect-scrollbar/dist/css/styles.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst HotspotPreview = ({\n  anchorEl,\n  guideStep,\n  title,\n  text,\n  imageUrl,\n  onClose,\n  onPrevious,\n  onContinue,\n  videoUrl,\n  currentStep,\n  totalSteps,\n  onDontShowAgain,\n  progress,\n  textFieldProperties,\n  imageProperties,\n  customButton,\n  modalProperties,\n  canvasProperties,\n  htmlSnippet,\n  previousButtonStyles,\n  continueButtonStyles,\n  OverlayValue,\n  savedGuideData,\n  hotspotProperties,\n  handleHotspotHover,\n  handleHotspotClick,\n  isHotspotPopupOpen,\n  showHotspotenduser\n}) => {\n  _s();\n  var _savedGuideData$Guide, _savedGuideData$Guide2, _textFieldProperties$, _textFieldProperties$2, _textFieldProperties$3, _imageProperties, _imageProperties$Cust, _imageProperties$Cust2, _savedGuideData$Guide31, _savedGuideData$Guide32, _savedGuideData$Guide33;\n  const {\n    setCurrentStep,\n    selectedTemplate,\n    toolTipGuideMetaData,\n    elementSelected,\n    axisData,\n    tooltipXaxis,\n    tooltipYaxis,\n    setOpenTooltip,\n    openTooltip,\n    pulseAnimationsH,\n    hotspotGuideMetaData,\n    selectedTemplateTour,\n    selectedOption,\n    ProgressColor\n  } = useDrawerStore(state => state);\n  const [targetElement, setTargetElement] = useState(null);\n  // State to track if the popover should be shown\n  // State for popup visibility is managed through openTooltip\n  const [popupPosition, setPopupPosition] = useState(null);\n  const [dynamicWidth, setDynamicWidth] = useState(null);\n  const [hotspotSize, setHotspotSize] = useState(30); // Track hotspot size for dynamic popup positioning\n  const contentRef = useRef(null);\n  const buttonContainerRef = useRef(null);\n  let hotspot;\n  const getElementByXPath = xpath => {\n    const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\n    const node = result.singleNodeValue;\n    if (node instanceof HTMLElement) {\n      return node;\n    } else if (node !== null && node !== void 0 && node.parentElement) {\n      return node.parentElement; // Return parent if it's a text node\n    } else {\n      return null;\n    }\n  };\n  let xpath;\n  if (savedGuideData) xpath = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide = savedGuideData.GuideStep) === null || _savedGuideData$Guide === void 0 ? void 0 : (_savedGuideData$Guide2 = _savedGuideData$Guide[0]) === null || _savedGuideData$Guide2 === void 0 ? void 0 : _savedGuideData$Guide2.ElementPath;\n  const getElementPosition = xpath => {\n    const element = getElementByXPath(xpath || \"\");\n    if (element) {\n      const rect = element.getBoundingClientRect();\n      return {\n        top: rect.top,\n        //+ window.scrollY + yOffset, // Adjust for vertical scroll\n        left: rect.left // + window.scrollX + xOffset, // Adjust for horizontal scroll\n      };\n    }\n    return null;\n  };\n  // State to track if scrolling is needed\n  const [needsScrolling, setNeedsScrolling] = useState(false);\n  const scrollbarRef = useRef(null);\n  // Function to get estimated popup dimensions\n  const getEstimatedPopupDimensions = () => {\n    // Try to get actual dimensions from content if available\n    if (contentRef.current) {\n      const contentRect = contentRef.current.getBoundingClientRect();\n      return {\n        width: Math.max(contentRect.width, 250),\n        // Minimum width\n        height: Math.max(contentRect.height + 100, 150) // Add padding for buttons/progress\n      };\n    }\n\n    // Fallback to estimated dimensions\n    const estimatedWidth = dynamicWidth ? parseInt(dynamicWidth) : canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.Width ? parseInt(canvasProperties.Width) : 300;\n    const estimatedHeight = hasOnlyButtons() ? 80 : hasOnlyText() ? 120 : 250;\n    return {\n      width: estimatedWidth,\n      height: estimatedHeight\n    };\n  };\n\n  // Function to calculate smart popup position that stays within viewport\n  const calculatePopupPosition = (elementRect, hotspotSize, xOffset, yOffset) => {\n    const hotspotLeft = elementRect.x + xOffset;\n    const hotspotTop = elementRect.y + yOffset;\n\n    // Get viewport dimensions\n    const viewportWidth = window.innerWidth;\n    const viewportHeight = window.innerHeight;\n\n    // Get estimated popup dimensions\n    const {\n      width: estimatedPopupWidth,\n      height: estimatedPopupHeight\n    } = getEstimatedPopupDimensions();\n\n    // Default positioning (right and below)\n    let dynamicOffsetX = hotspotSize + 10;\n    let dynamicOffsetY = hotspotSize + 10;\n\n    // Check if popup would go outside viewport on the right\n    const wouldOverflowRight = hotspotLeft + dynamicOffsetX + estimatedPopupWidth > viewportWidth - 50; // 20px margin\n\n    // Check if popup would go outside viewport at the bottom\n    const wouldOverflowBottom = hotspotTop + dynamicOffsetY + estimatedPopupHeight > viewportHeight - 50; // 20px margin\n\n    // Check if popup would go outside viewport on the left (when positioning to the left)\n    const wouldOverflowLeft = hotspotLeft - estimatedPopupWidth - 10 < 20; // 20px margin\n\n    // Check if popup would go outside viewport at the top (when positioning above)\n    const wouldOverflowTop = hotspotTop - estimatedPopupHeight - 10 < 20; // 20px margin\n\n    // Smart positioning logic\n    if (wouldOverflowRight && !wouldOverflowLeft) {\n      // Position to the left of the hotspot\n      dynamicOffsetX = -(estimatedPopupWidth - 160);\n    } else if (wouldOverflowRight && wouldOverflowLeft) {\n      // Center horizontally if both sides would overflow\n      dynamicOffsetX = -(estimatedPopupWidth / 2);\n    }\n    if (wouldOverflowBottom && !wouldOverflowTop) {\n      // Position above the hotspot\n      dynamicOffsetY = -(estimatedPopupHeight - 160);\n    } else if (wouldOverflowBottom && wouldOverflowTop) {\n      // Center vertically if both top and bottom would overflow\n      dynamicOffsetY = -(estimatedPopupHeight / 2);\n    }\n\n    // Debug logging (can be removed in production)\n    if (process.env.NODE_ENV === 'development') {\n      console.log('Smart positioning:', {\n        hotspotPosition: {\n          left: hotspotLeft,\n          top: hotspotTop\n        },\n        popupDimensions: {\n          width: estimatedPopupWidth,\n          height: estimatedPopupHeight\n        },\n        viewport: {\n          width: viewportWidth,\n          height: viewportHeight\n        },\n        overflows: {\n          right: wouldOverflowRight,\n          bottom: wouldOverflowBottom,\n          left: wouldOverflowLeft,\n          top: wouldOverflowTop\n        },\n        finalOffset: {\n          x: dynamicOffsetX,\n          y: dynamicOffsetY\n        }\n      });\n    }\n    return {\n      top: hotspotTop + window.scrollY + dynamicOffsetY,\n      left: hotspotLeft + window.scrollX + dynamicOffsetX\n    };\n  };\n  useEffect(() => {\n    const element = getElementByXPath(xpath);\n    if (element) {\n      const rect = element.getBoundingClientRect();\n      setPopupPosition({\n        top: rect.top + window.scrollY,\n        // Account for scrolling\n        left: rect.left + window.scrollX\n      });\n    }\n  }, [xpath]);\n  useEffect(() => {\n    if (typeof window !== undefined) {\n      const position = getElementPosition(xpath || \"\");\n      if (position) {\n        setPopupPosition(position);\n      }\n    }\n  }, [xpath]);\n  useEffect(() => {\n    const element = getElementByXPath(xpath);\n    // setTargetElement(element);\n    if (element) {}\n  }, [savedGuideData]);\n  useEffect(() => {\n    var _guideStep;\n    const element = getElementByXPath(guideStep === null || guideStep === void 0 ? void 0 : (_guideStep = guideStep[currentStep - 1]) === null || _guideStep === void 0 ? void 0 : _guideStep.ElementPath);\n    setTargetElement(element);\n    if (element) {\n      element.style.backgroundColor = \"red !important\";\n\n      // Update popup position when target element changes\n      const rect = element.getBoundingClientRect();\n      setPopupPosition({\n        top: rect.top + window.scrollY,\n        left: rect.left + window.scrollX\n      });\n    }\n  }, [guideStep, currentStep]);\n\n  // Hotspot styles are applied directly in the applyHotspotStyles function\n  // State for overlay value\n  const [, setOverlayValue] = useState(false);\n  const handleContinue = () => {\n    if (selectedTemplate !== \"Tour\") {\n      if (currentStep < totalSteps) {\n        setCurrentStep(currentStep + 1);\n        onContinue();\n        renderNextPopup(currentStep < totalSteps);\n      }\n    } else {\n      setCurrentStep(currentStep + 1);\n      const existingHotspot = document.getElementById(\"hotspotBlink\");\n      if (existingHotspot) {\n        existingHotspot.style.display = \"none\";\n        existingHotspot.remove();\n      }\n    }\n  };\n  const renderNextPopup = shouldRenderNextPopup => {\n    var _savedGuideData$Guide3, _savedGuideData$Guide4, _savedGuideData$Guide5, _savedGuideData$Guide6, _savedGuideData$Guide7, _savedGuideData$Guide8, _savedGuideData$Guide9, _savedGuideData$Guide10, _savedGuideData$Guide11, _savedGuideData$Guide12;\n    return shouldRenderNextPopup ? /*#__PURE__*/_jsxDEV(HotspotPreview, {\n      isHotspotPopupOpen: isHotspotPopupOpen,\n      showHotspotenduser: showHotspotenduser,\n      handleHotspotHover: handleHotspotHover,\n      handleHotspotClick: handleHotspotClick,\n      anchorEl: anchorEl,\n      savedGuideData: savedGuideData,\n      guideStep: guideStep,\n      onClose: onClose,\n      onPrevious: handlePrevious,\n      onContinue: handleContinue,\n      title: title,\n      text: text,\n      imageUrl: imageUrl,\n      currentStep: currentStep + 1,\n      totalSteps: totalSteps,\n      onDontShowAgain: onDontShowAgain,\n      progress: progress,\n      textFieldProperties: savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide3 = savedGuideData.GuideStep) === null || _savedGuideData$Guide3 === void 0 ? void 0 : (_savedGuideData$Guide4 = _savedGuideData$Guide3[currentStep]) === null || _savedGuideData$Guide4 === void 0 ? void 0 : _savedGuideData$Guide4.TextFieldProperties,\n      imageProperties: savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide5 = savedGuideData.GuideStep) === null || _savedGuideData$Guide5 === void 0 ? void 0 : (_savedGuideData$Guide6 = _savedGuideData$Guide5[currentStep]) === null || _savedGuideData$Guide6 === void 0 ? void 0 : _savedGuideData$Guide6.ImageProperties,\n      customButton: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide7 = savedGuideData.GuideStep) === null || _savedGuideData$Guide7 === void 0 ? void 0 : (_savedGuideData$Guide8 = _savedGuideData$Guide7[currentStep]) === null || _savedGuideData$Guide8 === void 0 ? void 0 : (_savedGuideData$Guide9 = _savedGuideData$Guide8.ButtonSection) === null || _savedGuideData$Guide9 === void 0 ? void 0 : (_savedGuideData$Guide10 = _savedGuideData$Guide9.map(section => section.CustomButtons.map(button => ({\n        ...button,\n        ContainerId: section.Id // Attach the container ID for grouping\n      })))) === null || _savedGuideData$Guide10 === void 0 ? void 0 : _savedGuideData$Guide10.reduce((acc, curr) => acc.concat(curr), [])) || [],\n      modalProperties: modalProperties,\n      canvasProperties: canvasProperties,\n      htmlSnippet: htmlSnippet,\n      OverlayValue: OverlayValue,\n      hotspotProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide11 = savedGuideData.GuideStep) === null || _savedGuideData$Guide11 === void 0 ? void 0 : (_savedGuideData$Guide12 = _savedGuideData$Guide11[currentStep - 1]) === null || _savedGuideData$Guide12 === void 0 ? void 0 : _savedGuideData$Guide12.Hotspot) || {}\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 4\n    }, this) : null;\n  };\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n      onPrevious();\n    }\n  };\n  useEffect(() => {\n    if (OverlayValue) {\n      setOverlayValue(true);\n    } else {\n      setOverlayValue(false);\n    }\n  }, [OverlayValue]);\n  // Image fit is used directly in the component\n  const getAnchorAndTransformOrigins = position => {\n    switch (position) {\n      case \"top-left\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          }\n        };\n      case \"top-right\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n          }\n        };\n      case \"bottom-left\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"top\",\n            horizontal: \"right\"\n          }\n        };\n      case \"bottom-right\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          }\n        };\n      case \"center-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n      case \"top-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"center\"\n          }\n        };\n      case \"left-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"right\"\n          }\n        };\n      case \"bottom-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n      case \"right-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          }\n        };\n      default:\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n    }\n  };\n  const {\n    anchorOrigin,\n    transformOrigin\n  } = getAnchorAndTransformOrigins((canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center center\");\n  const textStyle = {\n    fontWeight: textFieldProperties !== null && textFieldProperties !== void 0 && (_textFieldProperties$ = textFieldProperties.TextProperties) !== null && _textFieldProperties$ !== void 0 && _textFieldProperties$.Bold ? \"bold\" : \"normal\",\n    fontStyle: textFieldProperties !== null && textFieldProperties !== void 0 && (_textFieldProperties$2 = textFieldProperties.TextProperties) !== null && _textFieldProperties$2 !== void 0 && _textFieldProperties$2.Italic ? \"italic\" : \"normal\",\n    color: (textFieldProperties === null || textFieldProperties === void 0 ? void 0 : (_textFieldProperties$3 = textFieldProperties.TextProperties) === null || _textFieldProperties$3 === void 0 ? void 0 : _textFieldProperties$3.TextColor) || \"#000000\",\n    textAlign: (textFieldProperties === null || textFieldProperties === void 0 ? void 0 : textFieldProperties.Alignment) || \"left\"\n  };\n\n  // Image styles are applied directly in the component\n\n  const renderHtmlSnippet = snippet => {\n    // Return the raw HTML snippet for rendering\n    return {\n      __html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (_match, p1, p2, p3) => {\n        return `${p1}${p2}\" target=\"_blank\"${p3}`;\n      })\n    };\n  };\n\n  // Helper function to check if popup has only buttons (no text or images)\n  const hasOnlyButtons = () => {\n    const hasImage = imageProperties && imageProperties.length > 0 && imageProperties.some(prop => prop.CustomImage && prop.CustomImage.some(img => img.Url));\n    const hasText = textFieldProperties && textFieldProperties.length > 0 && textFieldProperties.some(field => field.Text && field.Text.trim() !== \"\");\n    const hasButtons = customButton && customButton.length > 0;\n    return hasButtons && !hasImage && !hasText;\n  };\n\n  // Helper function to check if popup has only text (no buttons or images)\n  const hasOnlyText = () => {\n    const hasImage = imageProperties && imageProperties.length > 0 && imageProperties.some(prop => prop.CustomImage && prop.CustomImage.some(img => img.Url));\n    const hasText = textFieldProperties && textFieldProperties.length > 0 && textFieldProperties.some(field => field.Text && field.Text.trim() !== \"\");\n    const hasButtons = customButton && customButton.length > 0;\n    return hasText && !hasImage && !hasButtons;\n  };\n\n  // Function to calculate the optimal width based on content and buttons\n  const calculateOptimalWidth = () => {\n    var _contentRef$current, _buttonContainerRef$c;\n    // If we have a fixed width from canvas settings and not a compact popup, use that\n    if (canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.Width && !hasOnlyButtons() && !hasOnlyText()) {\n      return `${canvasProperties.Width}px`;\n    }\n\n    // For popups with only buttons or only text, use auto width\n    if (hasOnlyButtons() || hasOnlyText()) {\n      return \"auto\";\n    }\n\n    // Get the width of content and button container\n    const contentWidth = ((_contentRef$current = contentRef.current) === null || _contentRef$current === void 0 ? void 0 : _contentRef$current.scrollWidth) || 0;\n    const buttonWidth = ((_buttonContainerRef$c = buttonContainerRef.current) === null || _buttonContainerRef$c === void 0 ? void 0 : _buttonContainerRef$c.scrollWidth) || 0;\n\n    // Use the larger of the two, with some minimum and maximum constraints\n    const optimalWidth = Math.max(contentWidth, buttonWidth);\n\n    // Add some padding to ensure text has room to wrap naturally\n    const paddedWidth = optimalWidth + 20; // 10px padding on each side\n\n    // Ensure width is between reasonable bounds\n    const minWidth = 250; // Minimum width\n    const maxWidth = 800; // Maximum width\n\n    const finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));\n    return `${finalWidth}px`;\n  };\n\n  // Update dynamic width when content or buttons change\n  useEffect(() => {\n    // Use requestAnimationFrame to ensure DOM has been updated\n    requestAnimationFrame(() => {\n      const newWidth = calculateOptimalWidth();\n      setDynamicWidth(newWidth);\n    });\n  }, [textFieldProperties, imageProperties, customButton, currentStep]);\n\n  // Recalculate popup position when hotspot size changes or content dimensions change\n  useEffect(() => {\n    if (xpath && hotspotSize) {\n      const element = getElementByXPath(xpath);\n      if (element) {\n        var _toolTipGuideMetaData;\n        const rect = element.getBoundingClientRect();\n        const hotspotPropData = (_toolTipGuideMetaData = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData === void 0 ? void 0 : _toolTipGuideMetaData.hotspots;\n        const xOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.XPosition) || \"4\");\n        const yOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.YPosition) || \"4\");\n        const popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\n        setPopupPosition(popupPos);\n      }\n    }\n  }, [hotspotSize, xpath, toolTipGuideMetaData, dynamicWidth, textFieldProperties, imageProperties, customButton]);\n\n  // Recalculate popup position on window resize\n  useEffect(() => {\n    const handleResize = () => {\n      if (xpath && hotspotSize) {\n        const element = getElementByXPath(xpath);\n        if (element) {\n          var _toolTipGuideMetaData2;\n          const rect = element.getBoundingClientRect();\n          const hotspotPropData = (_toolTipGuideMetaData2 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.hotspots;\n          const xOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.XPosition) || \"4\");\n          const yOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.YPosition) || \"4\");\n          const popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\n          setPopupPosition(popupPos);\n        }\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, [xpath, hotspotSize, toolTipGuideMetaData]);\n  const groupedButtons = customButton.reduce((acc, button) => {\n    const containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\n    if (!acc[containerId]) {\n      acc[containerId] = [];\n    }\n    acc[containerId].push(button);\n    return acc;\n  }, {});\n  const canvasStyle = {\n    position: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center-center\",\n    borderRadius: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Radius) || \"4px\",\n    borderWidth: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BorderSize) || \"0px\",\n    borderColor: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BorderColor) || \"black\",\n    borderStyle: \"solid\",\n    backgroundColor: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BackgroundColor) || \"white\",\n    maxWidth: hasOnlyButtons() || hasOnlyText() ? \"none !important\" : dynamicWidth ? `${dynamicWidth} !important` : canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.Width ? `${canvasProperties.Width}px !important` : \"800px\",\n    width: hasOnlyButtons() || hasOnlyText() ? \"auto !important\" : dynamicWidth ? `${dynamicWidth} !important` : canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.Width ? `${canvasProperties.Width}px !important` : \"300px\"\n  };\n  const sectionHeight = ((_imageProperties = imageProperties[currentStep - 1]) === null || _imageProperties === void 0 ? void 0 : (_imageProperties$Cust = _imageProperties.CustomImage) === null || _imageProperties$Cust === void 0 ? void 0 : (_imageProperties$Cust2 = _imageProperties$Cust[currentStep - 1]) === null || _imageProperties$Cust2 === void 0 ? void 0 : _imageProperties$Cust2.SectionHeight) || \"auto\";\n  const handleButtonAction = action => {\n    if (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\n      const targetUrl = action.TargetUrl;\n      if (action.ActionValue === \"same-tab\") {\n        // Open the URL in the same tab\n        window.location.href = targetUrl;\n      } else {\n        // Open the URL in a new tab\n        window.open(targetUrl, \"_blank\", \"noopener noreferrer\");\n      }\n    } else {\n      if (action.Action == \"Previous\" || action.Action == \"previous\" || action.ActionValue == \"Previous\" || action.ActionValue == \"Previous\") {\n        handlePrevious();\n      } else if (action.Action == \"Next\" || action.Action == \"next\" || action.ActionValue == \"Next\" || action.ActionValue == \"next\") {\n        handleContinue();\n      } else if (action.Action == \"Restart\" || action.ActionValue == \"Restart\") {\n        var _savedGuideData$Guide13, _savedGuideData$Guide14;\n        // Reset to the first step\n        setCurrentStep(1);\n        // If there's a specific URL for the first step, navigate to it\n        if (savedGuideData !== null && savedGuideData !== void 0 && (_savedGuideData$Guide13 = savedGuideData.GuideStep) !== null && _savedGuideData$Guide13 !== void 0 && (_savedGuideData$Guide14 = _savedGuideData$Guide13[0]) !== null && _savedGuideData$Guide14 !== void 0 && _savedGuideData$Guide14.ElementPath) {\n          const firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\n          if (firstStepElement) {\n            firstStepElement.scrollIntoView({\n              behavior: 'smooth'\n            });\n          }\n        }\n      }\n    }\n    setOverlayValue(false);\n  };\n  useEffect(() => {\n    var _guideStep2, _guideStep2$Hotspot;\n    if (guideStep !== null && guideStep !== void 0 && (_guideStep2 = guideStep[currentStep - 1]) !== null && _guideStep2 !== void 0 && (_guideStep2$Hotspot = _guideStep2.Hotspot) !== null && _guideStep2$Hotspot !== void 0 && _guideStep2$Hotspot.ShowByDefault) {\n      // Show tooltip by default\n      setOpenTooltip(true);\n    }\n  }, [guideStep === null || guideStep === void 0 ? void 0 : guideStep[currentStep - 1], currentStep, setOpenTooltip]);\n\n  // Add effect to handle isHotspotPopupOpen prop changes\n  useEffect(() => {\n    if (isHotspotPopupOpen) {\n      var _toolTipGuideMetaData3, _toolTipGuideMetaData4, _savedGuideData$Guide15, _savedGuideData$Guide16, _savedGuideData$Guide17, _savedGuideData$Guide18;\n      // Get the ShowUpon property\n      const hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData3 !== void 0 && _toolTipGuideMetaData3.hotspots ? toolTipGuideMetaData[currentStep - 1].hotspots : (_toolTipGuideMetaData4 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.hotspots;\n      const hotspotData = selectedTemplateTour === \"Hotspot\" ? savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide15 = savedGuideData.GuideStep) === null || _savedGuideData$Guide15 === void 0 ? void 0 : (_savedGuideData$Guide16 = _savedGuideData$Guide15[currentStep - 1]) === null || _savedGuideData$Guide16 === void 0 ? void 0 : _savedGuideData$Guide16.Hotspot : savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide17 = savedGuideData.GuideStep) === null || _savedGuideData$Guide17 === void 0 ? void 0 : (_savedGuideData$Guide18 = _savedGuideData$Guide17[0]) === null || _savedGuideData$Guide18 === void 0 ? void 0 : _savedGuideData$Guide18.Hotspot;\n\n      // Only show tooltip by default if ShowByDefault is true\n      // For \"Hovering Hotspot\", we'll wait for the hover event\n      if (hotspotPropData !== null && hotspotPropData !== void 0 && hotspotPropData.ShowByDefault) {\n        // Set openTooltip to true\n        setOpenTooltip(true);\n      } else {\n        // Otherwise, initially hide the tooltip\n        setOpenTooltip(false);\n      }\n    }\n  }, [isHotspotPopupOpen, toolTipGuideMetaData]);\n\n  // Add effect to handle showHotspotenduser prop changes\n  useEffect(() => {\n    if (showHotspotenduser) {\n      var _toolTipGuideMetaData5, _toolTipGuideMetaData6, _savedGuideData$Guide19, _savedGuideData$Guide20, _savedGuideData$Guide21, _savedGuideData$Guide22;\n      // Get the ShowUpon property\n      const hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData5 !== void 0 && _toolTipGuideMetaData5.hotspots ? toolTipGuideMetaData[currentStep - 1].hotspots : (_toolTipGuideMetaData6 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.hotspots;\n      const hotspotData = selectedTemplateTour === \"Hotspot\" ? savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide19 = savedGuideData.GuideStep) === null || _savedGuideData$Guide19 === void 0 ? void 0 : (_savedGuideData$Guide20 = _savedGuideData$Guide19[currentStep - 1]) === null || _savedGuideData$Guide20 === void 0 ? void 0 : _savedGuideData$Guide20.Hotspot : savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide21 = savedGuideData.GuideStep) === null || _savedGuideData$Guide21 === void 0 ? void 0 : (_savedGuideData$Guide22 = _savedGuideData$Guide21[0]) === null || _savedGuideData$Guide22 === void 0 ? void 0 : _savedGuideData$Guide22.Hotspot;\n\n      // Only show tooltip by default if ShowByDefault is true\n      if (hotspotPropData !== null && hotspotPropData !== void 0 && hotspotPropData.ShowByDefault) {\n        // Set openTooltip to true\n        setOpenTooltip(true);\n      } else {\n        // Otherwise, initially hide the tooltip\n        setOpenTooltip(false);\n      }\n    }\n  }, [showHotspotenduser, toolTipGuideMetaData]);\n\n  // Add a global click handler to detect clicks outside the hotspot to close the tooltip\n  useEffect(() => {\n    const handleGlobalClick = e => {\n      const hotspotElement = document.getElementById(\"hotspotBlink\");\n\n      // Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\n      if (hotspotElement && hotspotElement.contains(e.target)) {\n        return;\n      }\n\n      // We want to keep the tooltip open once it's been displayed\n      // So we're not closing it on clicks outside anymore\n    };\n    document.addEventListener(\"click\", handleGlobalClick);\n    return () => {\n      document.removeEventListener(\"click\", handleGlobalClick);\n    };\n  }, [toolTipGuideMetaData]);\n  // Check if content needs scrolling with improved detection\n  useEffect(() => {\n    const checkScrollNeeded = () => {\n      if (contentRef.current) {\n        // Force a reflow to get accurate measurements\n        contentRef.current.style.height = 'auto';\n        const contentHeight = contentRef.current.scrollHeight;\n        const containerHeight = 320; // max-height value\n        const shouldScroll = contentHeight > containerHeight;\n        setNeedsScrolling(shouldScroll);\n\n        // Force update scrollbar\n        if (scrollbarRef.current) {\n          // Try multiple methods to update the scrollbar\n          if (scrollbarRef.current.updateScroll) {\n            scrollbarRef.current.updateScroll();\n          }\n          // Force re-initialization if needed\n          setTimeout(() => {\n            if (scrollbarRef.current && scrollbarRef.current.updateScroll) {\n              scrollbarRef.current.updateScroll();\n            }\n          }, 10);\n        }\n      }\n    };\n    checkScrollNeeded();\n    const timeouts = [setTimeout(checkScrollNeeded, 50), setTimeout(checkScrollNeeded, 100), setTimeout(checkScrollNeeded, 200), setTimeout(checkScrollNeeded, 500)];\n    let resizeObserver = null;\n    let mutationObserver = null;\n    if (contentRef.current && window.ResizeObserver) {\n      resizeObserver = new ResizeObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      resizeObserver.observe(contentRef.current);\n    }\n    if (contentRef.current && window.MutationObserver) {\n      mutationObserver = new MutationObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      mutationObserver.observe(contentRef.current, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        attributeFilter: ['style', 'class']\n      });\n    }\n    return () => {\n      timeouts.forEach(clearTimeout);\n      if (resizeObserver) {\n        resizeObserver.disconnect();\n      }\n      if (mutationObserver) {\n        mutationObserver.disconnect();\n      }\n    };\n  }, [currentStep]);\n  // We no longer need the persistent monitoring effect since we want the tooltip\n  // to close when the mouse leaves the hotspot\n\n  function getAlignment(alignment) {\n    switch (alignment) {\n      case \"start\":\n        return \"flex-start\";\n      case \"end\":\n        return \"flex-end\";\n      case \"center\":\n      default:\n        return \"center\";\n    }\n  }\n  const getCanvasPosition = (position = \"center-center\") => {\n    switch (position) {\n      case \"bottom-left\":\n        return {\n          top: \"auto !important\"\n        };\n      case \"bottom-right\":\n        return {\n          top: \"auto !important\"\n        };\n      case \"bottom-center\":\n        return {\n          top: \"auto !important\"\n        };\n      case \"center-center\":\n        return {\n          top: \"25% !important\"\n        };\n      case \"left-center\":\n        return {\n          top: imageUrl === \"\" ? \"40% !important\" : \"20% !important\"\n        };\n      case \"right-center\":\n        return {\n          top: \"10% !important\"\n        };\n      case \"top-left\":\n        return {\n          top: \"10% !important\"\n        };\n      case \"top-right\":\n        return {\n          top: \"10% !important\"\n        };\n      case \"top-center\":\n        return {\n          top: \"9% !important\"\n        };\n      default:\n        return {\n          top: \"25% !important\"\n        };\n    }\n  };\n\n  // function to get the correct property value based on tour vs normal hotspot\n  const getHotspotProperty = (propName, hotspotPropData, hotspotData) => {\n    if (selectedTemplateTour === \"Hotspot\") {\n      // For tour hotspots, use saved data first, fallback to metadata\n      switch (propName) {\n        case 'PulseAnimation':\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.PulseAnimation) !== undefined ? hotspotData.PulseAnimation : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.PulseAnimation;\n        case 'StopAnimation':\n          // Always use stopAnimationUponInteraction for consistency\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.stopAnimationUponInteraction) !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.stopAnimationUponInteraction;\n        case 'ShowUpon':\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.ShowUpon) !== undefined ? hotspotData.ShowUpon : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.ShowUpon;\n        case 'ShowByDefault':\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.ShowByDefault) !== undefined ? hotspotData.ShowByDefault : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.ShowByDefault;\n        default:\n          return hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData[propName];\n      }\n    } else {\n      // For normal hotspots, use metadata\n      if (propName === 'StopAnimation') {\n        return hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.stopAnimationUponInteraction;\n      }\n      return hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData[propName];\n    }\n  };\n  const applyHotspotStyles = (hotspot, hotspotPropData, hotspotData, left, top) => {\n    hotspot.style.position = \"absolute\";\n    hotspot.style.left = `${left}px`;\n    hotspot.style.top = `${top}px`;\n    hotspot.style.width = `${hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Size}px`; // Default size if not provided\n    hotspot.style.height = `${hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Size}px`;\n    hotspot.style.backgroundColor = hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Color;\n    hotspot.style.borderRadius = \"50%\";\n    hotspot.style.zIndex = \"auto !important\"; // Increased z-index\n    hotspot.style.transition = \"none\";\n    hotspot.style.pointerEvents = \"auto\"; // Ensure clicks are registered\n    hotspot.innerHTML = \"\";\n    if ((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Type) === \"Info\" || (hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Type) === \"Question\") {\n      const textSpan = document.createElement(\"span\");\n      textSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\n      textSpan.style.color = \"white\";\n      textSpan.style.fontSize = \"14px\";\n      textSpan.style.fontWeight = \"bold\";\n      textSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\n      textSpan.style.display = \"flex\";\n      textSpan.style.alignItems = \"center\";\n      textSpan.style.justifyContent = \"center\";\n      textSpan.style.width = \"100%\";\n      textSpan.style.height = \"100%\";\n      hotspot.appendChild(textSpan);\n    }\n\n    // Apply animation class if needed\n    // Track if pulse has been stopped by hover\n    const pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);\n    const shouldPulse = selectedTemplateTour === \"Hotspot\" ? pulseAnimationEnabled !== false && !hotspot._pulseStopped : hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped;\n    if (shouldPulse) {\n      hotspot.classList.add(\"pulse-animation\");\n      hotspot.classList.remove(\"pulse-animation-removed\");\n    } else {\n      hotspot.classList.remove(\"pulse-animation\");\n      hotspot.classList.add(\"pulse-animation-removed\");\n    }\n\n    // Ensure the hotspot is visible and clickable\n    hotspot.style.display = \"flex\";\n    hotspot.style.pointerEvents = \"auto\";\n\n    // No need for separate animation control functions here\n    // Animation will be controlled directly in the event handlers\n    // Set initial state of openTooltip based on ShowByDefault and ShowUpon\n    const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\n    if (showByDefault) {\n      setOpenTooltip(true);\n    } else {\n      // If not showing by default, only show based on interaction type\n      //setOpenTooltip(false);\n    }\n\n    // Only clone and replace if the hotspot doesn't have event listeners already\n    // This prevents losing the _pulseStopped state unnecessarily\n    if (!hotspot.hasAttribute('data-listeners-attached')) {\n      const newHotspot = hotspot.cloneNode(true);\n      // Copy the _pulseStopped property if it exists\n      if (hotspot._pulseStopped !== undefined) {\n        newHotspot._pulseStopped = hotspot._pulseStopped;\n      }\n      if (hotspot.parentNode) {\n        hotspot.parentNode.replaceChild(newHotspot, hotspot);\n        hotspot = newHotspot;\n      }\n    }\n\n    // Ensure pointer events are enabled\n    hotspot.style.pointerEvents = \"auto\";\n\n    // Define combined event handlers that handle both animation and tooltip\n    const showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);\n    const handleHover = e => {\n      e.stopPropagation();\n      console.log(\"Hover detected on hotspot\");\n\n      // Show tooltip if ShowUpon is \"Hovering Hotspot\"\n      if (showUpon === \"Hovering Hotspot\") {\n        // Set openTooltip to true when hovering\n        setOpenTooltip(true);\n\n        // Call the passed hover handler if it exists\n        if (typeof handleHotspotHover === \"function\") {\n          handleHotspotHover();\n        }\n\n        // Stop animation if configured to do so\n        const stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\n        if (stopAnimationSetting) {\n          hotspot.classList.remove(\"pulse-animation\");\n          hotspot.classList.add(\"pulse-animation-removed\");\n          hotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\n        }\n      }\n    };\n    const handleMouseOut = e => {\n      e.stopPropagation();\n\n      // Hide tooltip when mouse leaves the hotspot\n      // Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\n      const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\n      if (showUpon === \"Hovering Hotspot\" && !showByDefault) {\n        // setOpenTooltip(false);\n      }\n    };\n    const handleClick = e => {\n      e.stopPropagation();\n      console.log(\"Click detected on hotspot\");\n\n      // Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\n      if (showUpon === \"Clicking Hotspot\" || !showUpon) {\n        // Toggle the tooltip state\n        setOpenTooltip(!openTooltip);\n\n        // Call the passed click handler if it exists\n        if (typeof handleHotspotClick === \"function\") {\n          handleHotspotClick();\n        }\n\n        // Stop animation if configured to do so\n        const stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\n        if (stopAnimationSetting) {\n          hotspot.classList.remove(\"pulse-animation\");\n          hotspot.classList.add(\"pulse-animation-removed\");\n          hotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\n        }\n      }\n    };\n\n    // Add appropriate event listeners based on ShowUpon property\n    if (!hotspot.hasAttribute('data-listeners-attached')) {\n      if (showUpon === \"Hovering Hotspot\") {\n        // For hover interaction\n        hotspot.addEventListener(\"mouseover\", handleHover);\n        hotspot.addEventListener(\"mouseout\", handleMouseOut);\n\n        // Also add click handler for better user experience\n        hotspot.addEventListener(\"click\", handleClick);\n      } else {\n        // For click interaction (default)\n        hotspot.addEventListener(\"click\", handleClick);\n      }\n\n      // Mark that listeners have been attached\n      hotspot.setAttribute('data-listeners-attached', 'true');\n    }\n  };\n  useEffect(() => {\n    let element;\n    let steps;\n    const fetchGuideDetails = async () => {\n      try {\n        var _savedGuideData$Guide23, _savedGuideData$Guide24, _steps, _steps$;\n        //   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\n        steps = (savedGuideData === null || savedGuideData === void 0 ? void 0 : savedGuideData.GuideStep) || [];\n\n        // For tour hotspots, use the current step's element path\n        const elementPath = selectedTemplateTour === \"Hotspot\" && savedGuideData !== null && savedGuideData !== void 0 && (_savedGuideData$Guide23 = savedGuideData.GuideStep) !== null && _savedGuideData$Guide23 !== void 0 && (_savedGuideData$Guide24 = _savedGuideData$Guide23[currentStep - 1]) !== null && _savedGuideData$Guide24 !== void 0 && _savedGuideData$Guide24.ElementPath ? savedGuideData.GuideStep[currentStep - 1].ElementPath : ((_steps = steps) === null || _steps === void 0 ? void 0 : (_steps$ = _steps[0]) === null || _steps$ === void 0 ? void 0 : _steps$.ElementPath) || \"\";\n        element = getElementByXPath(elementPath || \"\");\n        setTargetElement(element);\n        if (element) {\n          // element.style.outline = \"2px solid red\";\n        }\n\n        // Check if this is a hotspot scenario (normal or tour)\n        const isHotspotScenario = selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\" || title === \"Hotspot\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Hotspot\";\n        if (isHotspotScenario) {\n          var _toolTipGuideMetaData7, _toolTipGuideMetaData8, _hotspotPropData, _hotspotPropData2, _hotspotPropData3, _hotspotPropData4;\n          // Get hotspot properties - prioritize tour data for tour hotspots\n          let hotspotPropData;\n          let hotspotData;\n          if (selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData7 !== void 0 && _toolTipGuideMetaData7.hotspots) {\n            var _savedGuideData$Guide25, _savedGuideData$Guide26;\n            // Tour hotspot - use current step metadata\n            hotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;\n            hotspotData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide25 = savedGuideData.GuideStep) === null || _savedGuideData$Guide25 === void 0 ? void 0 : (_savedGuideData$Guide26 = _savedGuideData$Guide25[currentStep - 1]) === null || _savedGuideData$Guide26 === void 0 ? void 0 : _savedGuideData$Guide26.Hotspot;\n          } else if (toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData8 = toolTipGuideMetaData[0]) !== null && _toolTipGuideMetaData8 !== void 0 && _toolTipGuideMetaData8.hotspots) {\n            var _savedGuideData$Guide27, _savedGuideData$Guide28;\n            // Normal hotspot - use first metadata entry\n            hotspotPropData = toolTipGuideMetaData[0].hotspots;\n            hotspotData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide27 = savedGuideData.GuideStep) === null || _savedGuideData$Guide27 === void 0 ? void 0 : (_savedGuideData$Guide28 = _savedGuideData$Guide27[0]) === null || _savedGuideData$Guide28 === void 0 ? void 0 : _savedGuideData$Guide28.Hotspot;\n          } else {\n            var _savedGuideData$Guide29, _savedGuideData$Guide30;\n            // Fallback to default values for tour hotspots without metadata\n            hotspotPropData = {\n              XPosition: \"4\",\n              YPosition: \"4\",\n              Type: \"Question\",\n              Color: \"yellow\",\n              Size: \"16\",\n              PulseAnimation: true,\n              stopAnimationUponInteraction: true,\n              ShowUpon: \"Hovering Hotspot\",\n              ShowByDefault: false\n            };\n            hotspotData = (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide29 = savedGuideData.GuideStep) === null || _savedGuideData$Guide29 === void 0 ? void 0 : (_savedGuideData$Guide30 = _savedGuideData$Guide29[currentStep - 1]) === null || _savedGuideData$Guide30 === void 0 ? void 0 : _savedGuideData$Guide30.Hotspot) || {};\n          }\n          const xOffset = parseFloat(((_hotspotPropData = hotspotPropData) === null || _hotspotPropData === void 0 ? void 0 : _hotspotPropData.XPosition) || \"4\");\n          const yOffset = parseFloat(((_hotspotPropData2 = hotspotPropData) === null || _hotspotPropData2 === void 0 ? void 0 : _hotspotPropData2.YPosition) || \"4\");\n          const currentHotspotSize = parseFloat(((_hotspotPropData3 = hotspotPropData) === null || _hotspotPropData3 === void 0 ? void 0 : _hotspotPropData3.Size) || \"30\");\n\n          // Update hotspot size state\n          setHotspotSize(currentHotspotSize);\n          let left, top;\n          if (element) {\n            const rect = element.getBoundingClientRect();\n            left = rect.x + xOffset;\n            top = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\n\n            // Calculate popup position below the hotspot\n            const popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\n            setPopupPosition(popupPos);\n          }\n\n          // Check if hotspot already exists, preserve it to maintain _pulseStopped state\n          const existingHotspot = document.getElementById(\"hotspotBlink\");\n          if (existingHotspot) {\n            hotspot = existingHotspot;\n            // Don't reset _pulseStopped if it already exists\n          } else {\n            // Create new hotspot only if it doesn't exist\n            hotspot = document.createElement(\"div\");\n            hotspot.id = \"hotspotBlink\"; // Fixed ID for easier reference\n            hotspot._pulseStopped = false; // Set only on creation\n            document.body.appendChild(hotspot);\n          }\n          hotspot.style.cursor = \"pointer\";\n          hotspot.style.pointerEvents = \"auto\"; // Ensure it can receive mouse events\n\n          // Make sure the hotspot is visible and clickable\n          hotspot.style.zIndex = \"9999\";\n\n          // If ShowByDefault is true, set openTooltip to true immediately\n          if ((_hotspotPropData4 = hotspotPropData) !== null && _hotspotPropData4 !== void 0 && _hotspotPropData4.ShowByDefault) {\n            setOpenTooltip(true);\n          }\n\n          // Set styles first\n          applyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);\n\n          // Set initial tooltip visibility based on ShowByDefault\n          const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\n          if (showByDefault) {\n            setOpenTooltip(true);\n          } else {\n            //setOpenTooltip(false);\n          }\n\n          // We don't need to add event listeners here as they're already added in applyHotspotStyles\n        }\n      } catch (error) {\n        console.error(\"Error in fetchGuideDetails:\", error);\n      }\n    };\n    fetchGuideDetails();\n    return () => {\n      const existingHotspot = document.getElementById(\"hotspotBlink\");\n      if (existingHotspot) {\n        existingHotspot.onclick = null;\n        existingHotspot.onmouseover = null;\n        existingHotspot.onmouseout = null;\n      }\n    };\n  }, [savedGuideData, toolTipGuideMetaData, isHotspotPopupOpen, showHotspotenduser, selectedTemplateTour, currentStep\n  // Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\n  ]);\n  const enableProgress = (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide31 = savedGuideData.GuideStep) === null || _savedGuideData$Guide31 === void 0 ? void 0 : (_savedGuideData$Guide32 = _savedGuideData$Guide31[0]) === null || _savedGuideData$Guide32 === void 0 ? void 0 : (_savedGuideData$Guide33 = _savedGuideData$Guide32.Tooltip) === null || _savedGuideData$Guide33 === void 0 ? void 0 : _savedGuideData$Guide33.EnableProgress) || false;\n  function getProgressTemplate(selectedOption) {\n    var _savedGuideData$Guide34, _savedGuideData$Guide35, _savedGuideData$Guide36;\n    if (selectedOption === 1) {\n      return \"dots\";\n    } else if (selectedOption === 2) {\n      return \"linear\";\n    } else if (selectedOption === 3) {\n      return \"BreadCrumbs\";\n    } else if (selectedOption === 4) {\n      return \"breadcrumbs\";\n    }\n    return (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide34 = savedGuideData.GuideStep) === null || _savedGuideData$Guide34 === void 0 ? void 0 : (_savedGuideData$Guide35 = _savedGuideData$Guide34[0]) === null || _savedGuideData$Guide35 === void 0 ? void 0 : (_savedGuideData$Guide36 = _savedGuideData$Guide35.Tooltip) === null || _savedGuideData$Guide36 === void 0 ? void 0 : _savedGuideData$Guide36.ProgressTemplate) || \"dots\";\n  }\n  const progressTemplate = getProgressTemplate(selectedOption);\n  const renderProgress = () => {\n    if (!enableProgress) return null;\n    if (progressTemplate === \"dots\") {\n      return /*#__PURE__*/_jsxDEV(MobileStepper, {\n        variant: \"dots\",\n        steps: totalSteps,\n        position: \"static\",\n        activeStep: currentStep - 1,\n        sx: {\n          backgroundColor: \"transparent\",\n          position: \"inherit !important\",\n          \"& .MuiMobileStepper-dotActive\": {\n            backgroundColor: ProgressColor // Active dot\n          }\n        },\n        backButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1182,\n          columnNumber: 18\n        }, this),\n        nextButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1183,\n          columnNumber: 18\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1170,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"BreadCrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"center\",\n          gap: \"5px\",\n          padding: \"8px\"\n        },\n        children: Array.from({\n          length: totalSteps\n        }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"14px\",\n            height: \"4px\",\n            backgroundColor: index === currentStep - 1 ? ProgressColor : \"#e0e0e0\",\n            // Active color and inactive color\n            borderRadius: \"100px\"\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1193,\n          columnNumber: 7\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1189,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"breadcrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"flex-start\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            padding: \"8px\",\n            color: ProgressColor\n          },\n          children: [\"Step \", currentStep, \" of \", totalSteps]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1209,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1208,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"linear\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n            variant: \"determinate\",\n            value: progress,\n            sx: {\n              height: \"6px\",\n              borderRadius: \"20px\",\n              margin: \"6px 10px\",\n              \"& .MuiLinearProgress-bar\": {\n                backgroundColor: ProgressColor // progress bar color\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1220,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1219,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1218,\n        columnNumber: 5\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [targetElement && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: openTooltip && /*#__PURE__*/_jsxDEV(Popover, {\n        open: Boolean(popupPosition) || Boolean(anchorEl),\n        anchorEl: anchorEl,\n        onClose: () => {\n          // We want to keep the tooltip open once it's been displayed\n          // So we're not closing it on Popover close events\n        },\n        anchorOrigin: anchorOrigin,\n        transformOrigin: transformOrigin,\n        anchorReference: \"anchorPosition\",\n        anchorPosition: popupPosition ? {\n          top: popupPosition.top + 10 + (parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\"))),\n          left: popupPosition.left + 10 + parseFloat(tooltipXaxis || \"0\")\n        } : undefined,\n        sx: {\n          // \"& .MuiBackdrop-root\": {\n          //     position: 'relative !important', // Ensures higher specificity\n          // },\n          \"pointer-events\": anchorEl ? \"auto\" : \"auto\",\n          '& .MuiPaper-root:not(.MuiMobileStepper-root)': {\n            zIndex: 1000,\n            // borderRadius: \"1px\",\n            ...canvasStyle,\n            //...getAnchorAndTransformOrigins,\n            //top: \"16% !important\",\n            // top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\n            //     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\n            //         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\n            //             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\n            //                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\n            //                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\n            //                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\n            //                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\n            //                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\n            ...getCanvasPosition((canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center-center\"),\n            top: `${((popupPosition === null || popupPosition === void 0 ? void 0 : popupPosition.top) || 0) + (tooltipYaxis && tooltipYaxis != 'undefined' ? parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\")) : 0)}px !important`,\n            left: `${((popupPosition === null || popupPosition === void 0 ? void 0 : popupPosition.left) || 0) + (tooltipXaxis && tooltipXaxis != 'undefined' ? parseFloat(tooltipXaxis) || 0 : 0)}px !important`,\n            overflow: \"hidden\",\n            // Add smooth transitions for position changes\n            transition: 'top 0.3s ease-out, left 0.3s ease-out'\n          }\n        },\n        disableScrollLock: true,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            placeContent: \"end\",\n            display: \"flex\"\n          },\n          children: (modalProperties === null || modalProperties === void 0 ? void 0 : modalProperties.DismissOption) && /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => {\n              // Only close if explicitly requested by user clicking the close button\n              //setOpenTooltip(false);\n            },\n            sx: {\n              position: \"fixed\",\n              boxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\n              left: \"auto\",\n              right: \"auto\",\n              margin: \"-15px\",\n              background: \"#fff !important\",\n              border: \"1px solid #ccc\",\n              zIndex: \"999999\",\n              borderRadius: \"50px\",\n              padding: \"5px !important\"\n            },\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n              sx: {\n                zoom: 1,\n                color: \"#000\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1332,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1314,\n            columnNumber: 10\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1312,\n          columnNumber: 8\n        }, this), /*#__PURE__*/_jsxDEV(PerfectScrollbar, {\n          ref: scrollbarRef,\n          style: {\n            maxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\"\n          },\n          options: {\n            suppressScrollY: !needsScrolling,\n            suppressScrollX: true,\n            wheelPropagation: false,\n            swipeEasing: true,\n            minScrollbarLength: 20,\n            scrollingThreshold: 1000,\n            scrollYMarginOffset: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",\n              overflow: hasOnlyButtons() || hasOnlyText() ? \"visible\" : \"hidden auto\",\n              width: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\n              margin: hasOnlyButtons() ? \"0\" : undefined\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              style: {\n                padding: hasOnlyButtons() ? \"0\" : hasOnlyText() ? \"0\" : (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Padding) || \"10px\",\n                height: hasOnlyButtons() ? \"auto\" : sectionHeight,\n                width: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\n                margin: hasOnlyButtons() ? \"0\" : undefined\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                ref: contentRef,\n                display: \"flex\",\n                flexDirection: \"column\",\n                flexWrap: \"wrap\",\n                justifyContent: \"center\",\n                sx: {\n                  width: hasOnlyText() ? \"auto\" : \"100%\",\n                  padding: hasOnlyText() ? \"0\" : undefined\n                },\n                children: [imageProperties === null || imageProperties === void 0 ? void 0 : imageProperties.map(imageProp => imageProp.CustomImage.map((customImg, imgIndex) => /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"img\",\n                  src: customImg.Url,\n                  alt: customImg.AltText || \"Image\",\n                  sx: {\n                    maxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\n                    textAlign: imageProp.Alignment || \"center\",\n                    objectFit: customImg.Fit || \"contain\",\n                    //  width: \"500px\",\n                    height: `${customImg.SectionHeight || 250}px`,\n                    background: customImg.BackgroundColor || \"#ffffff\",\n                    margin: \"10px 0\"\n                  },\n                  onClick: () => {\n                    if (imageProp.Hyperlink) {\n                      const targetUrl = imageProp.Hyperlink;\n                      window.open(targetUrl, \"_blank\", \"noopener noreferrer\");\n                    }\n                  },\n                  style: {\n                    cursor: imageProp.Hyperlink ? \"pointer\" : \"default\"\n                  }\n                }, `${imageProp.Id}-${imgIndex}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1376,\n                  columnNumber: 13\n                }, this))), textFieldProperties === null || textFieldProperties === void 0 ? void 0 : textFieldProperties.map((textField, index) => {\n                  var _textField$TextProper, _textField$TextProper2;\n                  return textField.Text && /*#__PURE__*/_jsxDEV(Typography, {\n                    className: \"qadpt-preview\",\n                    // Use a unique key, either Id or index\n                    sx: {\n                      textAlign: ((_textField$TextProper = textField.TextProperties) === null || _textField$TextProper === void 0 ? void 0 : _textField$TextProper.TextFormat) || textStyle.textAlign,\n                      color: ((_textField$TextProper2 = textField.TextProperties) === null || _textField$TextProper2 === void 0 ? void 0 : _textField$TextProper2.TextColor) || textStyle.color,\n                      whiteSpace: \"pre-wrap\",\n                      wordBreak: \"break-word\",\n                      padding: \"0 5px\"\n                    },\n                    dangerouslySetInnerHTML: renderHtmlSnippet(textField.Text) // Render the raw HTML\n                  }, textField.Id || index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1404,\n                    columnNumber: 14\n                  }, this);\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1363,\n                columnNumber: 10\n              }, this), Object.keys(groupedButtons).map(containerId => {\n                var _groupedButtons$conta, _groupedButtons$conta2;\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  ref: buttonContainerRef,\n                  sx: {\n                    display: \"flex\",\n                    justifyContent: getAlignment((_groupedButtons$conta = groupedButtons[containerId][0]) === null || _groupedButtons$conta === void 0 ? void 0 : _groupedButtons$conta.Alignment),\n                    flexWrap: \"wrap\",\n                    margin: hasOnlyButtons() ? 0 : \"5px 0\",\n                    backgroundColor: (_groupedButtons$conta2 = groupedButtons[containerId][0]) === null || _groupedButtons$conta2 === void 0 ? void 0 : _groupedButtons$conta2.BackgroundColor,\n                    padding: hasOnlyButtons() ? \"4px\" : \"5px 0\",\n                    width: hasOnlyButtons() ? \"auto\" : \"100%\",\n                    borderRadius: hasOnlyButtons() ? \"15px\" : undefined\n                  },\n                  children: groupedButtons[containerId].map((button, index) => {\n                    var _button$ButtonPropert, _button$ButtonPropert2, _button$ButtonPropert3, _button$ButtonPropert4, _button$ButtonPropert5, _button$ButtonPropert6, _button$ButtonPropert7;\n                    return /*#__PURE__*/_jsxDEV(Button, {\n                      onClick: () => handleButtonAction(button.ButtonAction),\n                      variant: \"contained\",\n                      sx: {\n                        marginRight: hasOnlyButtons() ? \"5px\" : \"13px\",\n                        margin: hasOnlyButtons() ? \"4px\" : \"0 5px 5px 5px\",\n                        backgroundColor: ((_button$ButtonPropert = button.ButtonProperties) === null || _button$ButtonPropert === void 0 ? void 0 : _button$ButtonPropert.ButtonBackgroundColor) || \"#007bff\",\n                        color: ((_button$ButtonPropert2 = button.ButtonProperties) === null || _button$ButtonPropert2 === void 0 ? void 0 : _button$ButtonPropert2.ButtonTextColor) || \"#fff\",\n                        border: ((_button$ButtonPropert3 = button.ButtonProperties) === null || _button$ButtonPropert3 === void 0 ? void 0 : _button$ButtonPropert3.ButtonBorderColor) || \"transparent\",\n                        fontSize: ((_button$ButtonPropert4 = button.ButtonProperties) === null || _button$ButtonPropert4 === void 0 ? void 0 : _button$ButtonPropert4.FontSize) || \"15px\",\n                        width: ((_button$ButtonPropert5 = button.ButtonProperties) === null || _button$ButtonPropert5 === void 0 ? void 0 : _button$ButtonPropert5.Width) || \"auto\",\n                        padding: hasOnlyButtons() ? \"var(--button-padding) !important\" : \"4px 8px\",\n                        lineHeight: hasOnlyButtons() ? \"var(--button-lineheight)\" : \"normal\",\n                        textTransform: \"none\",\n                        borderRadius: ((_button$ButtonPropert6 = button.ButtonProperties) === null || _button$ButtonPropert6 === void 0 ? void 0 : _button$ButtonPropert6.BorderRadius) || \"8px\",\n                        minWidth: hasOnlyButtons() ? \"fit-content\" : undefined,\n                        boxShadow: \"none !important\",\n                        // Remove box shadow in normal state\n                        \"&:hover\": {\n                          backgroundColor: ((_button$ButtonPropert7 = button.ButtonProperties) === null || _button$ButtonPropert7 === void 0 ? void 0 : _button$ButtonPropert7.ButtonBackgroundColor) || \"#007bff\",\n                          // Keep the same background color on hover\n                          opacity: 0.9,\n                          // Slightly reduce opacity on hover for visual feedback\n                          boxShadow: \"none !important\" // Remove box shadow in hover state\n                        }\n                      },\n                      children: button.ButtonName\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1436,\n                      columnNumber: 13\n                    }, this);\n                  })\n                }, containerId, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1421,\n                  columnNumber: 11\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1356,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1350,\n            columnNumber: 8\n          }, this)\n        }, `scrollbar-${needsScrolling}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1336,\n          columnNumber: 8\n        }, this), enableProgress && totalSteps > 1 && selectedTemplate === \"Tour\" && /*#__PURE__*/_jsxDEV(Box, {\n          children: renderProgress()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1472,\n          columnNumber: 75\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1257,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1242,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          @keyframes pulse {\n            0% {\n              transform: scale(1);\n              opacity: 1;\n            }\n            50% {\n              transform: scale(1.5);\n              opacity: 0.6;\n            }\n            100% {\n              transform: scale(1);\n              opacity: 1;\n            }\n          }\n\n          .pulse-animation {\n            animation: pulse 1.5s infinite;\n            pointer-events: auto !important;\n          }\n\n          .pulse-animation-removed {\n            pointer-events: auto !important;\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1478,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true);\n};\n_s(HotspotPreview, \"PgDB4N+0EhrNv098qSwM/HOvCCQ=\", false, function () {\n  return [useDrawerStore];\n});\n_c = HotspotPreview;\nexport default HotspotPreview;\nvar _c;\n$RefreshReg$(_c, \"HotspotPreview\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "<PERSON><PERSON>", "IconButton", "LinearProgress", "MobileStepper", "Popover", "Typography", "CloseIcon", "useDrawerStore", "PerfectScrollbar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HotspotPreview", "anchorEl", "guideStep", "title", "text", "imageUrl", "onClose", "onPrevious", "onContinue", "videoUrl", "currentStep", "totalSteps", "onDontShowAgain", "progress", "textFieldProperties", "imageProperties", "customButton", "modalProperties", "canvasProperties", "htmlSnippet", "previousButtonStyles", "continueButtonStyles", "OverlayValue", "savedGuideData", "hotspotProperties", "handleHotspotHover", "handleHotspotClick", "isHotspotPopupOpen", "showHotspotenduser", "_s", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_textFieldProperties$", "_textFieldProperties$2", "_textFieldProperties$3", "_imageProperties", "_imageProperties$Cust", "_imageProperties$Cust2", "_savedGuideData$Guide31", "_savedGuideData$Guide32", "_savedGuideData$Guide33", "setCurrentStep", "selectedTemplate", "toolTipGuideMetaData", "elementSelected", "axisData", "tooltipXaxis", "tooltipYaxis", "setOpenTooltip", "openTooltip", "pulseAnimationsH", "hotspotGuideMetaData", "selectedTemplateTour", "selectedOption", "ProgressColor", "state", "targetElement", "setTargetElement", "popupPosition", "setPopupPosition", "dynamicWidth", "setDynamicWidth", "hotspotSize", "setHotspotSize", "contentRef", "buttonContainerRef", "hotspot", "getElementByXPath", "xpath", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "node", "singleNodeValue", "HTMLElement", "parentElement", "GuideStep", "<PERSON>ement<PERSON><PERSON>", "getElementPosition", "element", "rect", "getBoundingClientRect", "top", "left", "needsScrolling", "setNeedsScrolling", "scrollbarRef", "getEstimatedPopupDimensions", "current", "contentRect", "width", "Math", "max", "height", "estimatedWidth", "parseInt", "<PERSON><PERSON><PERSON>", "estimatedHeight", "hasOnlyButtons", "hasOnlyText", "calculatePopupPosition", "elementRect", "xOffset", "yOffset", "hotspotLeft", "x", "hotspotTop", "y", "viewportWidth", "window", "innerWidth", "viewportHeight", "innerHeight", "estimatedPopupWidth", "estimatedPopupHeight", "dynamicOffsetX", "dynamicOffsetY", "wouldOverflowRight", "wouldOverflowBottom", "wouldOverflowLeft", "wouldOverflowTop", "process", "env", "NODE_ENV", "console", "log", "hotspotPosition", "popupDimensions", "viewport", "overflows", "right", "bottom", "finalOffset", "scrollY", "scrollX", "undefined", "position", "_guideStep", "style", "backgroundColor", "setOverlayValue", "handleContinue", "renderNextPopup", "existingHotspot", "getElementById", "display", "remove", "shouldRenderNextPopup", "_savedGuideData$Guide3", "_savedGuideData$Guide4", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "_savedGuideData$Guide10", "_savedGuideData$Guide11", "_savedGuideData$Guide12", "handlePrevious", "TextFieldProperties", "ImageProperties", "ButtonSection", "map", "section", "CustomButtons", "button", "ContainerId", "Id", "reduce", "acc", "curr", "concat", "Hotspot", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getAnchorAndTransformOrigins", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "Position", "textStyle", "fontWeight", "TextProperties", "Bold", "fontStyle", "Italic", "color", "TextColor", "textAlign", "Alignment", "renderHtmlSnippet", "snippet", "__html", "replace", "_match", "p1", "p2", "p3", "hasImage", "length", "some", "prop", "CustomImage", "img", "Url", "hasText", "field", "Text", "trim", "hasButtons", "calculateOptimalWidth", "_contentRef$current", "_buttonContainerRef$c", "contentWidth", "scrollWidth", "buttonWidth", "optimalWidth", "<PERSON><PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "finalWidth", "min", "requestAnimationFrame", "newWidth", "_toolTipGuideMetaData", "hotspotPropData", "hotspots", "parseFloat", "XPosition", "YPosition", "popupPos", "handleResize", "_toolTipGuideMetaData2", "addEventListener", "removeEventListener", "groupedButtons", "containerId", "push", "canvasStyle", "borderRadius", "<PERSON><PERSON>", "borderWidth", "BorderSize", "borderColor", "BorderColor", "borderStyle", "BackgroundColor", "sectionHeight", "SectionHeight", "handleButtonAction", "action", "Action", "targetUrl", "TargetUrl", "ActionValue", "location", "href", "open", "_savedGuideData$Guide13", "_savedGuideData$Guide14", "firstStepElement", "scrollIntoView", "behavior", "_guideStep2", "_guideStep2$Hotspot", "ShowByDefault", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_savedGuideData$Guide15", "_savedGuideData$Guide16", "_savedGuideData$Guide17", "_savedGuideData$Guide18", "hotspotData", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "_savedGuideData$Guide19", "_savedGuideData$Guide20", "_savedGuideData$Guide21", "_savedGuideData$Guide22", "handleGlobalClick", "e", "hotspotElement", "contains", "target", "checkScrollNeeded", "contentHeight", "scrollHeight", "containerHeight", "shouldScroll", "updateScroll", "setTimeout", "timeouts", "resizeObserver", "mutationObserver", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "for<PERSON>ach", "clearTimeout", "disconnect", "getAlignment", "alignment", "getCanvasPosition", "getHotspotProperty", "propName", "PulseAnimation", "stopAnimationUponInteraction", "ShowUpon", "applyHotspotStyles", "Size", "Color", "zIndex", "transition", "pointerEvents", "innerHTML", "Type", "textSpan", "createElement", "innerText", "fontSize", "alignItems", "justifyContent", "append<PERSON><PERSON><PERSON>", "pulseAnimationEnabled", "shouldPulse", "_pulseStopped", "classList", "add", "showByDefault", "hasAttribute", "newHotspot", "cloneNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showUpon", "handleHover", "stopPropagation", "stopAnimationSetting", "handleMouseOut", "handleClick", "setAttribute", "steps", "fetchGuideDetails", "_savedGuideData$Guide23", "_savedGuideData$Guide24", "_steps", "_steps$", "elementPath", "isHotspotScenario", "_toolTipGuideMetaData7", "_toolTipGuideMetaData8", "_hotspotPropData", "_hotspotPropData2", "_hotspotPropData3", "_hotspotPropData4", "_savedGuideData$Guide25", "_savedGuideData$Guide26", "_savedGuideData$Guide27", "_savedGuideData$Guide28", "_savedGuideData$Guide29", "_savedGuideData$Guide30", "currentHotspotSize", "abs", "id", "body", "cursor", "error", "onclick", "on<PERSON><PERSON>ver", "onmouseout", "enableProgress", "<PERSON><PERSON><PERSON>", "EnableProgress", "getProgressTemplate", "_savedGuideData$Guide34", "_savedGuideData$Guide35", "_savedGuideData$Guide36", "ProgressTemplate", "progressTemplate", "renderProgress", "variant", "activeStep", "sx", "backButton", "visibility", "nextButton", "place<PERSON><PERSON>nt", "gap", "padding", "children", "Array", "from", "_", "index", "value", "margin", "Boolean", "anchorReference", "anchorPosition", "overflow", "disableScrollLock", "DismissOption", "onClick", "boxShadow", "background", "border", "zoom", "ref", "maxHeight", "options", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "Padding", "flexDirection", "flexWrap", "imageProp", "customImg", "imgIndex", "component", "src", "alt", "AltText", "MaxImageHeight", "objectFit", "Fit", "Hyperlink", "textField", "_textField$TextProper", "_textField$TextProper2", "className", "TextFormat", "whiteSpace", "wordBreak", "dangerouslySetInnerHTML", "Object", "keys", "_groupedButtons$conta", "_groupedButtons$conta2", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "_button$ButtonPropert6", "_button$ButtonPropert7", "ButtonAction", "marginRight", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "ButtonBorderColor", "FontSize", "lineHeight", "textTransform", "BorderRadius", "opacity", "ButtonName", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/GuidesPreview/HotspotPreview.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, PopoverOrigin, Typography } from \"@mui/material\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\n// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\n\r\n\r\ninterface ButtonAction {\r\n  Action: string;\r\n  ActionValue: string;\r\n  TargetUrl: string;\r\n}\r\ninterface PopupProps {\r\n    isHotspotPopupOpen: any;\r\n    showHotspotenduser: any;\r\n    anchorEl: null | HTMLElement;\r\n    guideStep: any[];\r\n    title: string;\r\n    text: string;\r\n    imageUrl?: string;\r\n    videoUrl?: string;\r\n    previousButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    continueButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    onClose: () => void;\r\n    onPrevious: () => void;\r\n    onContinue: () => void;\r\n    currentStep: number;\r\n    totalSteps: number;\r\n    onDontShowAgain: () => void;\r\n    progress: number;\r\n    textFieldProperties?: any;\r\n    imageProperties?: any;\r\n    customButton?: any;\r\n    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };\r\n    canvasProperties?: {\r\n        Position?: string;\r\n        Padding?: string;\r\n        Radius?: string;\r\n        BorderSize?: string;\r\n        BorderColor?: string;\r\n        BackgroundColor?: string;\r\n        Width?: string;\r\n    };\r\n    htmlSnippet: string;\r\n    OverlayValue: boolean;\r\n    savedGuideData: GuideData | null;\r\n    hotspotProperties: any;\r\n    handleHotspotHover: () => any;\r\n    handleHotspotClick: () => any;\r\n}\r\n\r\ninterface ButtonProperties {\r\n  Padding: number;\r\n  Width: number;\r\n  Font: number;\r\n  FontSize: number;\r\n  ButtonTextColor: string;\r\n  ButtonBackgroundColor: string;\r\n}\r\n\r\ninterface ButtonData {\r\n  ButtonStyle: string;\r\n  ButtonName: string;\r\n  Alignment: string;\r\n  BackgroundColor: string;\r\n  ButtonAction: ButtonAction;\r\n  Padding: {\r\n    Top: number;\r\n    Right: number;\r\n    Bottom: number;\r\n    Left: number;\r\n  };\r\n  ButtonProperties: ButtonProperties;\r\n}\r\n\r\ninterface HotspotProperties {\r\n  size: string;\r\n  type: string;\r\n  color: string;\r\n  showUpon: string;\r\n  showByDefault: boolean;\r\n  stopAnimation: boolean;\r\n  pulseAnimation: boolean;\r\n  position: {\r\n    XOffset: string;\r\n    YOffset: string;\r\n  };\r\n}\r\n\r\ninterface Step {\r\n  xpath: string;\r\n  hotspotProperties: HotspotProperties;\r\n  content: string | JSX.Element;\r\n  targetUrl: string;\r\n  imageUrl: string;\r\n  buttonData: ButtonData[];\r\n}\r\n\r\ninterface HotspotGuideProps {\r\n  steps: Step[];\r\n  currentUrl: string;\r\n  onClose: () => void;\r\n}\r\n\r\nconst HotspotPreview: React.FC<PopupProps> = ({\r\n    anchorEl,\r\n    guideStep,\r\n    title,\r\n    text,\r\n    imageUrl,\r\n    onClose,\r\n    onPrevious,\r\n    onContinue,\r\n    videoUrl,\r\n    currentStep,\r\n    totalSteps,\r\n    onDontShowAgain,\r\n    progress,\r\n    textFieldProperties,\r\n    imageProperties,\r\n    customButton,\r\n    modalProperties,\r\n    canvasProperties,\r\n    htmlSnippet,\r\n    previousButtonStyles,\r\n    continueButtonStyles,\r\n    OverlayValue,\r\n    savedGuideData,\r\n    hotspotProperties,\r\n    handleHotspotHover,\r\n    handleHotspotClick,\r\n    isHotspotPopupOpen,\r\n   showHotspotenduser\r\n\r\n}) => {\r\n\tconst {\r\n\t\tsetCurrentStep,\r\n\t\tselectedTemplate,\r\n\t\ttoolTipGuideMetaData,\r\n\t\telementSelected,\r\n\t\taxisData,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\tsetOpenTooltip,\r\n\t\topenTooltip,\r\n\t\tpulseAnimationsH,\r\n\t\thotspotGuideMetaData,\r\n\t\tselectedTemplateTour,\r\n\t\tselectedOption,\r\n\t\tProgressColor,\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n\tconst [targetElement, setTargetElement] = useState<HTMLElement | null>(null);\r\n\t// State to track if the popover should be shown\r\n\t// State for popup visibility is managed through openTooltip\r\n\tconst [popupPosition, setPopupPosition] = useState<{ top: number; left: number } | null>(null);\r\n\tconst [dynamicWidth, setDynamicWidth] = useState<string | null>(null);\r\n\tconst [hotspotSize, setHotspotSize] = useState<number>(30); // Track hotspot size for dynamic popup positioning\r\n\tconst contentRef = useRef<HTMLDivElement>(null);\r\n\tconst buttonContainerRef = useRef<HTMLDivElement>(null);\r\n\tlet hotspot: any;\r\n\tconst getElementByXPath = (xpath: string): HTMLElement | null => {\r\n\t\tconst result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\tconst node = result.singleNodeValue;\r\n\t\tif (node instanceof HTMLElement) {\r\n\t\t\treturn node;\r\n\t\t} else if (node?.parentElement) {\r\n\t\t\treturn node.parentElement; // Return parent if it's a text node\r\n\t\t} else {\r\n\t\t\treturn null;\r\n\t\t}\r\n\t};\r\n\tlet xpath: any;\r\n\tif (savedGuideData) xpath = savedGuideData?.GuideStep?.[0]?.ElementPath;\r\n\tconst getElementPosition = (xpath: string | undefined) => {\r\n\t\tconst element = getElementByXPath(xpath || \"\");\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\ttop: rect.top, //+ window.scrollY + yOffset, // Adjust for vertical scroll\r\n\t\t\t\tleft: rect.left, // + window.scrollX + xOffset, // Adjust for horizontal scroll\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn null;\r\n\t};\r\n\t  // State to track if scrolling is needed\r\n\t  const [needsScrolling, setNeedsScrolling] = useState(false);\r\n\t  const scrollbarRef = useRef<any>(null);\r\n\t// Function to get estimated popup dimensions\r\n\tconst getEstimatedPopupDimensions = () => {\r\n\t\t// Try to get actual dimensions from content if available\r\n\t\tif (contentRef.current) {\r\n\t\t\tconst contentRect = contentRef.current.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\twidth: Math.max(contentRect.width, 250), // Minimum width\r\n\t\t\t\theight: Math.max(contentRect.height + 100, 150) // Add padding for buttons/progress\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\t// Fallback to estimated dimensions\r\n\t\tconst estimatedWidth = dynamicWidth ? parseInt(dynamicWidth) :\r\n\t\t\t(canvasProperties?.Width ? parseInt(canvasProperties.Width) : 300);\r\n\t\tconst estimatedHeight = hasOnlyButtons() ? 80 :\r\n\t\t\t(hasOnlyText() ? 120 : 250);\r\n\r\n\t\treturn {\r\n\t\t\twidth: estimatedWidth,\r\n\t\t\theight: estimatedHeight\r\n\t\t};\r\n\t};\r\n\r\n\t// Function to calculate smart popup position that stays within viewport\r\n\tconst calculatePopupPosition = (elementRect: DOMRect, hotspotSize: number, xOffset: number, yOffset: number) => {\r\n\t\tconst hotspotLeft = elementRect.x + xOffset;\r\n\t\tconst hotspotTop = elementRect.y + yOffset;\r\n\r\n\t\t// Get viewport dimensions\r\n\t\tconst viewportWidth = window.innerWidth;\r\n\t\tconst viewportHeight = window.innerHeight;\r\n\r\n\t\t// Get estimated popup dimensions\r\n\t\tconst { width: estimatedPopupWidth, height: estimatedPopupHeight } = getEstimatedPopupDimensions();\r\n\r\n\t\t// Default positioning (right and below)\r\n\t\tlet dynamicOffsetX = hotspotSize + 10;\r\n\t\tlet dynamicOffsetY = hotspotSize + 10;\r\n\r\n\t\t// Check if popup would go outside viewport on the right\r\n\t\tconst wouldOverflowRight = (hotspotLeft + dynamicOffsetX + estimatedPopupWidth) > (viewportWidth - 50); // 20px margin\r\n\r\n\t\t// Check if popup would go outside viewport at the bottom\r\n\t\tconst wouldOverflowBottom = (hotspotTop + dynamicOffsetY + estimatedPopupHeight) > (viewportHeight - 50); // 20px margin\r\n\r\n\t\t// Check if popup would go outside viewport on the left (when positioning to the left)\r\n\t\tconst wouldOverflowLeft = (hotspotLeft - estimatedPopupWidth - 10) < 20; // 20px margin\r\n\r\n\t\t// Check if popup would go outside viewport at the top (when positioning above)\r\n\t\tconst wouldOverflowTop = (hotspotTop - estimatedPopupHeight - 10) < 20; // 20px margin\r\n\r\n\t\t// Smart positioning logic\r\n\t\tif (wouldOverflowRight && !wouldOverflowLeft) {\r\n\t\t\t// Position to the left of the hotspot\r\n\t\t\tdynamicOffsetX = -(estimatedPopupWidth -160);\r\n\t\t} else if (wouldOverflowRight && wouldOverflowLeft) {\r\n\t\t\t// Center horizontally if both sides would overflow\r\n\t\t\tdynamicOffsetX = -(estimatedPopupWidth / 2);\r\n\t\t}\r\n\r\n\t\tif (wouldOverflowBottom && !wouldOverflowTop) {\r\n\t\t\t// Position above the hotspot\r\n\t\t\tdynamicOffsetY = -(estimatedPopupHeight -160);\r\n\t\t} else if (wouldOverflowBottom && wouldOverflowTop) {\r\n\t\t\t// Center vertically if both top and bottom would overflow\r\n\t\t\tdynamicOffsetY = -(estimatedPopupHeight / 2);\r\n\t\t}\r\n\r\n\t\t// Debug logging (can be removed in production)\r\n\t\tif (process.env.NODE_ENV === 'development') {\r\n\t\t\tconsole.log('Smart positioning:', {\r\n\t\t\t\thotspotPosition: { left: hotspotLeft, top: hotspotTop },\r\n\t\t\t\tpopupDimensions: { width: estimatedPopupWidth, height: estimatedPopupHeight },\r\n\t\t\t\tviewport: { width: viewportWidth, height: viewportHeight },\r\n\t\t\t\toverflows: { right: wouldOverflowRight, bottom: wouldOverflowBottom, left: wouldOverflowLeft, top: wouldOverflowTop },\r\n\t\t\t\tfinalOffset: { x: dynamicOffsetX, y: dynamicOffsetY }\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn {\r\n\t\t\ttop: hotspotTop + window.scrollY + dynamicOffsetY,\r\n\t\t\tleft: hotspotLeft + window.scrollX + dynamicOffsetX\r\n\t\t};\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY, // Account for scrolling\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tif (typeof window !== undefined) {\r\n\t\t\tconst position = getElementPosition(xpath || \"\");\r\n\t\t\tif (position) {\r\n\t\t\t\tsetPopupPosition(position);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\t// setTargetElement(element);\r\n\t\tif (element) {\r\n\t\t}\r\n\t}, [savedGuideData]);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(guideStep?.[currentStep - 1]?.ElementPath);\r\n\t\tsetTargetElement(element);\r\n\t\tif (element) {\r\n\t\t\telement.style.backgroundColor = \"red !important\";\r\n\r\n\t\t\t// Update popup position when target element changes\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY,\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [guideStep, currentStep]);\r\n\r\n\t// Hotspot styles are applied directly in the applyHotspotStyles function\r\n\t// State for overlay value\r\n\tconst [, setOverlayValue] = useState(false);\r\n\tconst handleContinue = () => {\r\n\t\tif (selectedTemplate !== \"Tour\") {\r\n\t\t\tif (currentStep < totalSteps) {\r\n\t\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\t\tonContinue();\r\n\t\t\t\trenderNextPopup(currentStep < totalSteps);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.style.display = \"none\";\r\n\t\t\t\texistingHotspot.remove();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\tconst renderNextPopup = (shouldRenderNextPopup: boolean) => {\r\n\t\treturn shouldRenderNextPopup ? (\r\n\t\t\t<HotspotPreview\r\n\t\t\t\tisHotspotPopupOpen={isHotspotPopupOpen}\r\n\t\t\t\tshowHotspotenduser={showHotspotenduser}\r\n\t\t\t\thandleHotspotHover={handleHotspotHover}\r\n\t\t\t\thandleHotspotClick={handleHotspotClick}\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\tsavedGuideData={savedGuideData}\r\n\t\t\t\tguideStep={guideStep}\r\n\t\t\t\tonClose={onClose}\r\n\t\t\t\tonPrevious={handlePrevious}\r\n\t\t\t\tonContinue={handleContinue}\r\n\t\t\t\ttitle={title}\r\n\t\t\t\ttext={text}\r\n\t\t\t\timageUrl={imageUrl}\r\n\t\t\t\tcurrentStep={currentStep + 1}\r\n\t\t\t\ttotalSteps={totalSteps}\r\n\t\t\t\tonDontShowAgain={onDontShowAgain}\r\n\t\t\t\tprogress={progress}\r\n\t\t\t\ttextFieldProperties={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties}\r\n\t\t\t\timageProperties={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties}\r\n\t\t\t\tcustomButton={\r\n\t\t\t\t\tsavedGuideData?.GuideStep?.[currentStep]?.ButtonSection?.map((section: any) =>\r\n\t\t\t\t\t\tsection.CustomButtons.map((button: any) => ({\r\n\t\t\t\t\t\t\t...button,\r\n\t\t\t\t\t\t\tContainerId: section.Id, // Attach the container ID for grouping\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t)?.reduce((acc: string | any[], curr: any) => acc.concat(curr), []) || []\r\n\t\t\t\t}\r\n\t\t\t\tmodalProperties={modalProperties}\r\n\t\t\t\tcanvasProperties={canvasProperties}\r\n\t\t\t\thtmlSnippet={htmlSnippet}\r\n\t\t\t\tOverlayValue={OverlayValue}\r\n\t\t\t\thotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\r\n\t\t\t/>\r\n\t\t) : null;\r\n\t};\r\n\r\n\tconst handlePrevious = () => {\r\n\t\tif (currentStep > 1) {\r\n\t\t\tsetCurrentStep(currentStep - 1);\r\n\t\t\tonPrevious();\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (OverlayValue) {\r\n\t\t\tsetOverlayValue(true);\r\n\t\t} else {\r\n\t\t\tsetOverlayValue(false);\r\n\t\t}\r\n\t}, [OverlayValue]);\r\n\t// Image fit is used directly in the component\r\n\tconst getAnchorAndTransformOrigins = (\r\n\t\tposition: string\r\n\t): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tdefault:\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t}\r\n\t};\r\n\r\n\tconst { anchorOrigin, transformOrigin } = getAnchorAndTransformOrigins(canvasProperties?.Position || \"center center\");\r\n\r\n\tconst textStyle = {\r\n\t\tfontWeight: textFieldProperties?.TextProperties?.Bold ? \"bold\" : \"normal\",\r\n\t\tfontStyle: textFieldProperties?.TextProperties?.Italic ? \"italic\" : \"normal\",\r\n\t\tcolor: textFieldProperties?.TextProperties?.TextColor || \"#000000\",\r\n\t\ttextAlign: textFieldProperties?.Alignment || \"left\",\r\n\t};\r\n\r\n\t// Image styles are applied directly in the component\r\n\r\n\tconst renderHtmlSnippet = (snippet: string) => {\r\n\t\t// Return the raw HTML snippet for rendering\r\n\t\treturn {\r\n\t\t\t__html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (_match, p1, p2, p3) => {\r\n\t\t\t\treturn `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n\t\t\t}),\r\n\t\t};\r\n\t};\r\n\r\n\t// Helper function to check if popup has only buttons (no text or images)\r\n\tconst hasOnlyButtons = () => {\r\n\t\tconst hasImage = imageProperties && imageProperties.length > 0 &&\r\n\t\t\timageProperties.some((prop: any) =>\r\n\t\t\t\tprop.CustomImage && prop.CustomImage.some((img: any) => img.Url)\r\n\t\t\t);\r\n\r\n\t\tconst hasText = textFieldProperties && textFieldProperties.length > 0 &&\r\n\t\t\ttextFieldProperties.some((field: any) => field.Text && field.Text.trim() !== \"\");\r\n\r\n\t\tconst hasButtons = customButton && customButton.length > 0;\r\n\r\n\t\treturn hasButtons && !hasImage && !hasText;\r\n\t};\r\n\r\n\t// Helper function to check if popup has only text (no buttons or images)\r\n\tconst hasOnlyText = () => {\r\n\t\tconst hasImage = imageProperties && imageProperties.length > 0 &&\r\n\t\t\timageProperties.some((prop: any) =>\r\n\t\t\t\tprop.CustomImage && prop.CustomImage.some((img: any) => img.Url)\r\n\t\t\t);\r\n\r\n\t\tconst hasText = textFieldProperties && textFieldProperties.length > 0 &&\r\n\t\t\ttextFieldProperties.some((field: any) => field.Text && field.Text.trim() !== \"\");\r\n\r\n\t\tconst hasButtons = customButton && customButton.length > 0;\r\n\r\n\t\treturn hasText && !hasImage && !hasButtons;\r\n\t};\r\n\r\n\t// Function to calculate the optimal width based on content and buttons\r\n\tconst calculateOptimalWidth = () => {\r\n\t\t// If we have a fixed width from canvas settings and not a compact popup, use that\r\n\t\tif (canvasProperties?.Width && !hasOnlyButtons() && !hasOnlyText()) {\r\n\t\t\treturn `${canvasProperties.Width}px`;\r\n\t\t}\r\n\r\n\t\t// For popups with only buttons or only text, use auto width\r\n\t\tif (hasOnlyButtons() || hasOnlyText()) {\r\n\t\t\treturn \"auto\";\r\n\t\t}\r\n\r\n\t\t// Get the width of content and button container\r\n\t\tconst contentWidth = contentRef.current?.scrollWidth || 0;\r\n\t\tconst buttonWidth = buttonContainerRef.current?.scrollWidth || 0;\r\n\r\n\t\t// Use the larger of the two, with some minimum and maximum constraints\r\n\t\tconst optimalWidth = Math.max(contentWidth, buttonWidth);\r\n\r\n\t\t// Add some padding to ensure text has room to wrap naturally\r\n\t\tconst paddedWidth = optimalWidth + 20; // 10px padding on each side\r\n\r\n\t\t// Ensure width is between reasonable bounds\r\n\t\tconst minWidth = 250; // Minimum width\r\n\t\tconst maxWidth = 800; // Maximum width\r\n\r\n\t\tconst finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));\r\n\r\n\t\treturn `${finalWidth}px`;\r\n\t};\r\n\r\n\t// Update dynamic width when content or buttons change\r\n\tuseEffect(() => {\r\n\t\t// Use requestAnimationFrame to ensure DOM has been updated\r\n\t\trequestAnimationFrame(() => {\r\n\t\t\tconst newWidth = calculateOptimalWidth();\r\n\t\t\tsetDynamicWidth(newWidth);\r\n\t\t});\r\n\t}, [textFieldProperties, imageProperties, customButton, currentStep]);\r\n\r\n\t// Recalculate popup position when hotspot size changes or content dimensions change\r\n\tuseEffect(() => {\r\n\t\tif (xpath && hotspotSize) {\r\n\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\tif (element) {\r\n\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [hotspotSize, xpath, toolTipGuideMetaData, dynamicWidth, textFieldProperties, imageProperties, customButton]);\r\n\r\n\t// Recalculate popup position on window resize\r\n\tuseEffect(() => {\r\n\t\tconst handleResize = () => {\r\n\t\t\tif (xpath && hotspotSize) {\r\n\t\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\twindow.addEventListener('resize', handleResize);\r\n\t\treturn () => window.removeEventListener('resize', handleResize);\r\n\t}, [xpath, hotspotSize, toolTipGuideMetaData]);\r\n\r\n\tconst groupedButtons = customButton.reduce((acc: any, button: any) => {\r\n\t\tconst containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\r\n\t\tif (!acc[containerId]) {\r\n\t\t\tacc[containerId] = [];\r\n\t\t}\r\n\t\tacc[containerId].push(button);\r\n\t\treturn acc;\r\n\t}, {});\r\n\r\n\tconst canvasStyle = {\r\n\t\tposition: canvasProperties?.Position || \"center-center\",\r\n\t\tborderRadius: canvasProperties?.Radius || \"4px\",\r\n\t\tborderWidth: canvasProperties?.BorderSize || \"0px\",\r\n\t\tborderColor: canvasProperties?.BorderColor || \"black\",\r\n\t\tborderStyle: \"solid\",\r\n\t\tbackgroundColor: canvasProperties?.BackgroundColor || \"white\",\r\n\t\tmaxWidth: (hasOnlyButtons() || hasOnlyText()) ? \"none !important\" :\r\n\t\t\t\t  dynamicWidth ? `${dynamicWidth} !important` :\r\n\t\t\t\t  canvasProperties?.Width ? `${canvasProperties.Width}px !important` : \"800px\",\r\n\t\twidth: (hasOnlyButtons() || hasOnlyText()) ? \"auto !important\" :\r\n\t\t\t   dynamicWidth ? `${dynamicWidth} !important` :\r\n\t\t\t   canvasProperties?.Width ? `${canvasProperties.Width}px !important` : \"300px\",\r\n\t};\r\n\tconst sectionHeight = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || \"auto\";\r\n\tconst handleButtonAction = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\r\n\t\t\tconst targetUrl = action.TargetUrl;\r\n\t\t\tif (action.ActionValue === \"same-tab\") {\r\n\t\t\t\t// Open the URL in the same tab\r\n\t\t\t\twindow.location.href = targetUrl;\r\n\t\t\t} else {\r\n\t\t\t\t// Open the URL in a new tab\r\n\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (\r\n\t\t\t\taction.Action == \"Previous\" ||\r\n\t\t\t\taction.Action == \"previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\"\r\n\t\t\t) {\r\n\t\t\t\thandlePrevious();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Next\" ||\r\n\t\t\t\taction.Action == \"next\" ||\r\n\t\t\t\taction.ActionValue == \"Next\" ||\r\n\t\t\t\taction.ActionValue == \"next\"\r\n\t\t\t) {\r\n\t\t\t\thandleContinue();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Restart\" ||\r\n\t\t\t\taction.ActionValue == \"Restart\"\r\n\t\t\t) {\r\n\t\t\t\t// Reset to the first step\r\n\t\t\t\tsetCurrentStep(1);\r\n\t\t\t\t// If there's a specific URL for the first step, navigate to it\r\n\t\t\t\tif (savedGuideData?.GuideStep?.[0]?.ElementPath) {\r\n\t\t\t\t\tconst firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\r\n\t\t\t\t\tif (firstStepElement) {\r\n\t\t\t\t\t\tfirstStepElement.scrollIntoView({ behavior: 'smooth' });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetOverlayValue(false);\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (guideStep?.[currentStep - 1]?.Hotspot?.ShowByDefault) {\r\n\t\t\t// Show tooltip by default\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t}\r\n\t}, [guideStep?.[currentStep - 1], currentStep, setOpenTooltip]);\r\n\r\n\t// Add effect to handle isHotspotPopupOpen prop changes\r\n\tuseEffect(() => {\r\n\t\tif (isHotspotPopupOpen) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\t// For \"Hovering Hotspot\", we'll wait for the hover event\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [isHotspotPopupOpen, toolTipGuideMetaData]);\r\n\r\n\t// Add effect to handle showHotspotenduser prop changes\r\n\tuseEffect(() => {\r\n\t\tif (showHotspotenduser) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [showHotspotenduser, toolTipGuideMetaData]);\r\n\r\n\t// Add a global click handler to detect clicks outside the hotspot to close the tooltip\r\n\tuseEffect(() => {\r\n\t\tconst handleGlobalClick = (e: MouseEvent) => {\r\n\t\t\tconst hotspotElement = document.getElementById(\"hotspotBlink\");\r\n\r\n\t\t\t// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\r\n\t\t\tif (hotspotElement && hotspotElement.contains(e.target as Node)) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t// So we're not closing it on clicks outside anymore\r\n\t\t};\r\n\r\n\t\tdocument.addEventListener(\"click\", handleGlobalClick);\r\n\r\n\t\treturn () => {\r\n\t\t\tdocument.removeEventListener(\"click\", handleGlobalClick);\r\n\t\t};\r\n\t}, [toolTipGuideMetaData]);\r\n\t// Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [currentStep]);\r\n\t// We no longer need the persistent monitoring effect since we want the tooltip\r\n\t// to close when the mouse leaves the hotspot\r\n\r\n\tfunction getAlignment(alignment: string) {\r\n\t\tswitch (alignment) {\r\n\t\t\tcase \"start\":\r\n\t\t\t\treturn \"flex-start\";\r\n\t\t\tcase \"end\":\r\n\t\t\t\treturn \"flex-end\";\r\n\t\t\tcase \"center\":\r\n\t\t\tdefault:\r\n\t\t\t\treturn \"center\";\r\n\t\t}\r\n\t}\r\n\tconst getCanvasPosition = (position: string = \"center-center\") => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn { top: imageUrl === \"\" ? \"40% !important\" : \"20% !important\" };\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn { top: \"9% !important\" };\r\n\t\t\tdefault:\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t}\r\n\t};\r\n\r\n\t\t// function to get the correct property value based on tour vs normal hotspot\r\n\tconst getHotspotProperty = (propName: string, hotspotPropData: any, hotspotData: any) => {\r\n\t\tif (selectedTemplateTour === \"Hotspot\") {\r\n\t\t\t// For tour hotspots, use saved data first, fallback to metadata\r\n\t\t\tswitch (propName) {\r\n\t\t\t\tcase 'PulseAnimation':\r\n\t\t\t\t\treturn hotspotData?.PulseAnimation !== undefined ? hotspotData.PulseAnimation : hotspotPropData?.PulseAnimation;\r\n\t\t\t\tcase 'StopAnimation':\r\n\t\t\t\t\t// Always use stopAnimationUponInteraction for consistency\r\n\t\t\t\t\treturn hotspotData?.stopAnimationUponInteraction !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t\tcase 'ShowUpon':\r\n\t\t\t\t\treturn hotspotData?.ShowUpon !== undefined ? hotspotData.ShowUpon : hotspotPropData?.ShowUpon;\r\n\t\t\t\tcase 'ShowByDefault':\r\n\t\t\t\t\treturn hotspotData?.ShowByDefault !== undefined ? hotspotData.ShowByDefault : hotspotPropData?.ShowByDefault;\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn hotspotPropData?.[propName];\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// For normal hotspots, use metadata\r\n\t\t\tif (propName === 'StopAnimation') {\r\n\t\t\t\treturn hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t}\r\n\t\t\treturn hotspotPropData?.[propName];\r\n\t\t}\r\n\t};\r\n\r\n\tconst applyHotspotStyles = (hotspot: any, hotspotPropData: any, hotspotData: any, left: any, top: any) => {\r\n\t\thotspot.style.position = \"absolute\";\r\n\t\thotspot.style.left = `${left}px`;\r\n\t\thotspot.style.top = `${top}px`;\r\n\t\thotspot.style.width = `${hotspotPropData?.Size}px`; // Default size if not provided\r\n\t\thotspot.style.height = `${hotspotPropData?.Size}px`;\r\n\t\thotspot.style.backgroundColor = hotspotPropData?.Color;\r\n\t\thotspot.style.borderRadius = \"50%\";\r\n\t\thotspot.style.zIndex = \"auto !important\"; // Increased z-index\r\n\t\thotspot.style.transition = \"none\";\r\n\t\thotspot.style.pointerEvents = \"auto\"; // Ensure clicks are registered\r\n\t\thotspot.innerHTML = \"\";\r\n\r\n\t\tif (hotspotPropData?.Type === \"Info\" || hotspotPropData?.Type === \"Question\") {\r\n\t\t\tconst textSpan = document.createElement(\"span\");\r\n\t\t\ttextSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\r\n\t\t\ttextSpan.style.color = \"white\";\r\n\t\t\ttextSpan.style.fontSize = \"14px\";\r\n\t\t\ttextSpan.style.fontWeight = \"bold\";\r\n\t\t\ttextSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\r\n\t\t\ttextSpan.style.display = \"flex\";\r\n\t\t\ttextSpan.style.alignItems = \"center\";\r\n\t\t\ttextSpan.style.justifyContent = \"center\";\r\n\t\t\ttextSpan.style.width = \"100%\";\r\n\t\t\ttextSpan.style.height = \"100%\";\r\n\t\t\thotspot.appendChild(textSpan);\r\n\t\t}\r\n\r\n\t\t// Apply animation class if needed\r\n\t\t// Track if pulse has been stopped by hover\r\n\t\tconst pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);\r\n\t\tconst shouldPulse = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t? (pulseAnimationEnabled !== false && !hotspot._pulseStopped)\r\n\t\t\t: (hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped);\r\n\r\n\t\tif (shouldPulse) {\r\n            hotspot.classList.add(\"pulse-animation\");\r\n            hotspot.classList.remove(\"pulse-animation-removed\");\r\n        } else {\r\n            hotspot.classList.remove(\"pulse-animation\");\r\n            hotspot.classList.add(\"pulse-animation-removed\");\r\n        }\r\n\r\n\t\t// Ensure the hotspot is visible and clickable\r\n\t\thotspot.style.display = \"flex\";\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// No need for separate animation control functions here\r\n\t\t// Animation will be controlled directly in the event handlers\r\n\t\t// Set initial state of openTooltip based on ShowByDefault and ShowUpon\r\n\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\tif (showByDefault) {\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t} else {\r\n\t\t\t// If not showing by default, only show based on interaction type\r\n\t\t\t//setOpenTooltip(false);\r\n\t\t}\r\n\r\n\t\t// Only clone and replace if the hotspot doesn't have event listeners already\r\n\t\t// This prevents losing the _pulseStopped state unnecessarily\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tconst newHotspot = hotspot.cloneNode(true) as HTMLElement;\r\n\t\t\t// Copy the _pulseStopped property if it exists\r\n\t\t\tif (hotspot._pulseStopped !== undefined) {\r\n\t            (newHotspot as any)._pulseStopped = hotspot._pulseStopped;\r\n\t        }\r\n\t\t\tif (hotspot.parentNode) {\r\n\t\t\t\thotspot.parentNode.replaceChild(newHotspot, hotspot);\r\n\t\t\t\thotspot = newHotspot;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Ensure pointer events are enabled\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// Define combined event handlers that handle both animation and tooltip\r\n\t\tconst showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);\r\n\t\tconst handleHover = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Hover detected on hotspot\");\r\n\r\n\t\t\t// Show tooltip if ShowUpon is \"Hovering Hotspot\"\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// Set openTooltip to true when hovering\r\n\t\t\t\tsetOpenTooltip(true);\r\n\r\n\t\t\t\t// Call the passed hover handler if it exists\r\n\t\t\t\tif (typeof handleHotspotHover === \"function\") {\r\n\t\t\t\t\thandleHotspotHover();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\n\t\t\t\tconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleMouseOut = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\r\n\t\t\t// Hide tooltip when mouse leaves the hotspot\r\n\t\t\t// Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\r\n\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\tif (showUpon === \"Hovering Hotspot\" && !showByDefault) {\r\n\t\t\t\t// setOpenTooltip(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleClick = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Click detected on hotspot\");\r\n\r\n\t\t\t// Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\r\n\t\t\tif (showUpon === \"Clicking Hotspot\" || !showUpon) {\r\n\t\t\t\t// Toggle the tooltip state\r\n\t\t\t\tsetOpenTooltip(!openTooltip);\r\n\r\n\t\t\t\t// Call the passed click handler if it exists\r\n\t\t\t\tif (typeof handleHotspotClick === \"function\") {\r\n\t\t\t\t\thandleHotspotClick();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\nconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Add appropriate event listeners based on ShowUpon property\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// For hover interaction\r\n\t\t\t\thotspot.addEventListener(\"mouseover\", handleHover);\r\n\t\t\t\thotspot.addEventListener(\"mouseout\", handleMouseOut);\r\n\r\n\t\t\t\t// Also add click handler for better user experience\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t} else {\r\n\t\t\t\t// For click interaction (default)\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t}\r\n\r\n\t\t\t// Mark that listeners have been attached\r\n\t\t\thotspot.setAttribute('data-listeners-attached', 'true');\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tlet element;\r\n\t\tlet steps;\r\n\r\n\t\tconst fetchGuideDetails = async () => {\r\n\t\t\ttry {\r\n\t\t\t\t//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\r\n\t\t\t\tsteps = savedGuideData?.GuideStep || [];\r\n\r\n\t\t\t\t// For tour hotspots, use the current step's element path\r\n\t\t\t\tconst elementPath = selectedTemplateTour === \"Hotspot\" && savedGuideData?.GuideStep?.[currentStep - 1]?.ElementPath\r\n\t\t\t\t\t? (savedGuideData.GuideStep[currentStep - 1] as any).ElementPath\r\n\t\t\t\t\t: steps?.[0]?.ElementPath || \"\";\r\n\r\n\t\t\t\telement = getElementByXPath(elementPath || \"\");\r\n\t\t\t\tsetTargetElement(element);\r\n\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\t// element.style.outline = \"2px solid red\";\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Check if this is a hotspot scenario (normal or tour)\r\n\t\t\t\tconst isHotspotScenario = selectedTemplate === \"Hotspot\" ||\r\n\t\t\t\t\tselectedTemplateTour === \"Hotspot\" ||\r\n\t\t\t\t\ttitle === \"Hotspot\" ||\r\n\t\t\t\t\t(selectedTemplate === \"Tour\" && selectedTemplateTour === \"Hotspot\");\r\n\r\n\t\t\t\tif (isHotspotScenario) {\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\r\n\t\t\t\t\t// Get hotspot properties - prioritize tour data for tour hotspots\r\n\t\t\t\t\tlet hotspotPropData;\r\n\t\t\t\t\tlet hotspotData;\r\n\r\n\t\t\t\t\tif (selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots) {\r\n\t\t\t\t\t\t// Tour hotspot - use current step metadata\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot;\r\n\t\t\t\t\t} else if (toolTipGuideMetaData?.[0]?.hotspots) {\r\n\t\t\t\t\t\t// Normal hotspot - use first metadata entry\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[0].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Fallback to default values for tour hotspots without metadata\r\n\t\t\t\t\t\thotspotPropData = {\r\n\t\t\t\t\t\t\tXPosition: \"4\",\r\n\t\t\t\t\t\t\tYPosition: \"4\",\r\n\t\t\t\t\t\t\tType: \"Question\",\r\n\t\t\t\t\t\t\tColor: \"yellow\",\r\n\t\t\t\t\t\t\tSize: \"16\",\r\n\t\t\t\t\t\t\tPulseAnimation: true,\r\n\t\t\t\t\t\t\tstopAnimationUponInteraction: true,\r\n\t\t\t\t\t\t\tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t\t\t\t\tShowByDefault: false,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {};\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\t\t\t\t\tconst currentHotspotSize = parseFloat(hotspotPropData?.Size || \"30\");\r\n\r\n\t\t\t\t\t// Update hotspot size state\r\n\t\t\t\t\tsetHotspotSize(currentHotspotSize);\r\n\r\n\t\t\t\t\tlet left, top;\r\n\t\t\t\t\tif (element) {\r\n\t\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\t\tleft = rect.x + xOffset;\r\n\t\t\t\t\t\ttop = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\r\n\r\n\t\t\t\t\t\t// Calculate popup position below the hotspot\r\n\t\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\r\n\t\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Check if hotspot already exists, preserve it to maintain _pulseStopped state\r\n\t\t\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\t\t\tif (existingHotspot) {\r\n\t\t\t\t\t\thotspot = existingHotspot;\r\n\t\t\t\t\t\t// Don't reset _pulseStopped if it already exists\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Create new hotspot only if it doesn't exist\r\n\t\t\t\t\t\thotspot = document.createElement(\"div\");\r\n\t\t\t\t\t\thotspot.id = \"hotspotBlink\"; // Fixed ID for easier reference\r\n\t\t\t\t\t\thotspot._pulseStopped = false; // Set only on creation\r\n\t\t\t\t\t\tdocument.body.appendChild(hotspot);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\thotspot.style.cursor = \"pointer\";\r\n\t\t\t\t\thotspot.style.pointerEvents = \"auto\"; // Ensure it can receive mouse events\r\n\r\n\t\t\t\t\t// Make sure the hotspot is visible and clickable\r\n\t\t\t\t\thotspot.style.zIndex = \"9999\";\r\n\r\n\t\t\t\t\t// If ShowByDefault is true, set openTooltip to true immediately\r\n\t\t\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Set styles first\r\n\t\t\t\t\tapplyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);\r\n\r\n\t\t\t\t\t// Set initial tooltip visibility based on ShowByDefault\r\n\t\t\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\t\t\tif (showByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// We don't need to add event listeners here as they're already added in applyHotspotStyles\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error in fetchGuideDetails:\", error);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchGuideDetails();\r\n\r\n\t\treturn () => {\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.onclick = null;\r\n\t\t\t\texistingHotspot.onmouseover = null;\r\n\t\t\t\texistingHotspot.onmouseout = null;\r\n\t\t\t}\r\n\t\t};\r\n\t}, [\r\n\t\tsavedGuideData,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tisHotspotPopupOpen,\r\n\t\tshowHotspotenduser,\r\n\t\tselectedTemplateTour,\r\n\t\tcurrentStep,\r\n\t\t// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\r\n\t]);\r\n\tconst enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;\r\n\r\n\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t} else if (selectedOption === 4) {\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n\t\tif (!enableProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={totalSteps}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\t\t\tposition: \"inherit !important\",\r\n\t\t\t\t\t\t\"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // Active dot\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"center\", gap: \"5px\", padding: \"8px\" }}>\r\n\t\t\t\t\t{/* Custom Step Indicators */}\r\n\r\n\t\t\t\t\t{Array.from({ length: totalSteps }).map((_, index) => (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\twidth: \"14px\",\r\n\t\t\t\t\t\t\t\theight: \"4px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: index === currentStep - 1 ? ProgressColor : \"#e0e0e0\", // Active color and inactive color\r\n\t\t\t\t\t\t\t\tborderRadius: \"100px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t))}\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"flex-start\" }}>\r\n\t\t\t\t\t<Typography sx={{ padding: \"8px\", color: ProgressColor }}>\r\n\t\t\t\t\t\tStep {currentStep} of {totalSteps}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\t\"& .MuiLinearProgress-bar\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // progress bar color\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{targetElement && (\r\n\t\t\t\t<div>\r\n\t\t\t\t\t{/* {overlay && (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\t\t\tzIndex: 999,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)} */}\r\n\t\t\t\t\t{openTooltip && (\r\n\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\topen={Boolean(popupPosition) || Boolean(anchorEl)}\r\n\t\t\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\t\t\tonClose={() => {\r\n\t\t\t\t\t\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t\t\t\t\t\t// So we're not closing it on Popover close events\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tanchorOrigin={anchorOrigin}\r\n\t\t\t\t\t\t\ttransformOrigin={transformOrigin}\r\n\t\t\t\t\t\t\tanchorReference=\"anchorPosition\"\r\n\t\t\t\t\t\t\tanchorPosition={\r\n\t\t\t\t\t\t\t\tpopupPosition\r\n\t\t\t\t\t\t\t\t\t? {\r\n\t\t\t\t\t\t\t\t\t\t\ttop: popupPosition.top +10+ (parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\"))),\r\n\t\t\t\t\t\t\t\t\t\t\tleft: popupPosition.left +10+ parseFloat(tooltipXaxis || \"0\"),\r\n\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t: undefined\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t// \"& .MuiBackdrop-root\": {\r\n\t\t\t\t\t\t\t\t//     position: 'relative !important', // Ensures higher specificity\r\n\t\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t\t\t\"pointer-events\": anchorEl ? \"auto\" : \"auto\",\r\n\t\t\t\t\t\t\t\t'& .MuiPaper-root:not(.MuiMobileStepper-root)': {\r\n\t\t\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\t\t\t// borderRadius: \"1px\",\r\n\t\t\t\t\t\t\t\t\t...canvasStyle,\r\n\t\t\t\t\t\t\t\t\t//...getAnchorAndTransformOrigins,\r\n\t\t\t\t\t\t\t\t\t//top: \"16% !important\",\r\n\t\t\t\t\t\t\t\t\t// top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\r\n\t\t\t\t\t\t\t\t\t//                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\r\n\t\t\t\t\t\t\t\t\t...getCanvasPosition(canvasProperties?.Position || \"center-center\"),\r\n\t\t\t\t\t\t\t\t\ttop: `${(popupPosition?.top || 0)\r\n\t\t\t\t\t\t\t\t\t\t+ (tooltipYaxis && tooltipYaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t\t? parseFloat(tooltipYaxis || \"0\") > 0\r\n\t\t\t\t\t\t\t\t\t\t\t\t? -parseFloat(tooltipYaxis || \"0\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t: Math.abs(parseFloat(tooltipYaxis || \"0\"))\r\n\t\t\t\t\t\t\t\t\t\t\t: 0)}px !important`,\r\n\t\t\t\t\t\t\t\t\tleft: `${(popupPosition?.left || 0) + (tooltipXaxis && tooltipXaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t? (parseFloat(tooltipXaxis) || 0)\r\n\t\t\t\t\t\t\t\t\t\t: 0 )}px !important`,\r\n\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t// Add smooth transitions for position changes\r\n\t\t\t\t\t\t\t\t\ttransition: 'top 0.3s ease-out, left 0.3s ease-out',\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisableScrollLock={true}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n\t\t\t\t\t\t\t\t{modalProperties?.DismissOption && (\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t// Only close if explicitly requested by user clicking the close button\r\n\t\t\t\t\t\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tleft: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tright: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tmargin: \"-15px\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: \"999999\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<CloseIcon sx={{ zoom: 1, color: \"#000\" }} />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{ maxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",}}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{\r\n\t\t\t\t\t\t\t\tmaxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",\r\n\t\t\t\t\t\t\t\toverflow: hasOnlyButtons() || hasOnlyText() ? \"visible\" : \"hidden auto\",\r\n\t\t\t\t\t\t\t\twidth: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t<Box style={{\r\n\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"0\" :\r\n\t\t\t\t\t\t\t\t\t\t\thasOnlyText() ? \"0\" : (canvasProperties?.Padding || \"10px\"),\r\n\t\t\t\t\t\t\t\t\theight: hasOnlyButtons() ? \"auto\" : sectionHeight,\r\n\t\t\t\t\t\t\t\t\twidth: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tref={contentRef}\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\t\tflexWrap=\"wrap\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: hasOnlyText() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyText() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{imageProperties?.map((imageProp: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\timageProp.CustomImage.map((customImg: any, imgIndex: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={`${imageProp.Id}-${imgIndex}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={customImg.Url}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\talt={customImg.AltText || \"Image\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmaxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: imageProp.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tobjectFit: customImg.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//  width: \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: `${customImg.SectionHeight || 250}px`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: customImg.BackgroundColor || \"#ffffff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"10px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (imageProp.Hyperlink) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst targetUrl = imageProp.Hyperlink;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ cursor: imageProp.Hyperlink ? \"pointer\" : \"default\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t{textFieldProperties?.map(\r\n\t\t\t\t\t\t\t\t\t\t\t(textField: any, index: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextField.Text && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-preview\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={textField.Id || index} // Use a unique key, either Id or index\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: textField.TextProperties?.TextFormat || textStyle.textAlign,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: textField.TextProperties?.TextColor || textStyle.color,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={renderHtmlSnippet(textField.Text)} // Render the raw HTML\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t{Object.keys(groupedButtons).map((containerId) => (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tref={buttonContainerRef}\r\n\t\t\t\t\t\t\t\t\t\t\tkey={containerId}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),\r\n\t\t\t\t\t\t\t\t\t\t\t\tflexWrap: \"wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? 0 : \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: groupedButtons[containerId][0]?.BackgroundColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"4px\" : \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: hasOnlyButtons() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: hasOnlyButtons() ? \"15px\" : undefined\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{groupedButtons[containerId].map((button: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonAction(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmarginRight: hasOnlyButtons() ? \"5px\" : \"13px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"4px\" : \"0 5px 5px 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor || \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: button.ButtonProperties?.ButtonBorderColor || \"transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || \"15px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"var(--button-padding) !important\" : \"4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: hasOnlyButtons() ? \"var(--button-lineheight)\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: button.ButtonProperties?.BorderRadius || \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tminWidth: hasOnlyButtons() ? \"fit-content\" : undefined,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\", // Keep the same background color on hover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\topacity: 0.9, // Slightly reduce opacity on hover for visual feedback\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</PerfectScrollbar>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t{enableProgress && totalSteps>1 && selectedTemplate === \"Tour\" && <Box>{renderProgress()}</Box>}{\" \"}\r\n\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\t\t\t)}\r\n\r\n\t\t\t<style>\r\n\t\t\t\t{`\r\n          @keyframes pulse {\r\n            0% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n            50% {\r\n              transform: scale(1.5);\r\n              opacity: 0.6;\r\n            }\r\n            100% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n          }\r\n\r\n          .pulse-animation {\r\n            animation: pulse 1.5s infinite;\r\n            pointer-events: auto !important;\r\n          }\r\n\r\n          .pulse-animation-removed {\r\n            pointer-events: auto !important;\r\n          }\r\n        `}\r\n\t\t\t</style>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default HotspotPreview;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,GAAG,EAAEC,MAAM,EAAEC,UAAU,EAAEC,cAAc,EAAEC,aAAa,EAAEC,OAAO,EAAiBC,UAAU,QAAQ,eAAe;AAE1H,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAAuB,yBAAyB;AACrE;AACA,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,OAAO,6CAA6C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA6GrD,MAAMC,cAAoC,GAAGA,CAAC;EAC1CC,QAAQ;EACRC,SAAS;EACTC,KAAK;EACLC,IAAI;EACJC,QAAQ;EACRC,OAAO;EACPC,UAAU;EACVC,UAAU;EACVC,QAAQ;EACRC,WAAW;EACXC,UAAU;EACVC,eAAe;EACfC,QAAQ;EACRC,mBAAmB;EACnBC,eAAe;EACfC,YAAY;EACZC,eAAe;EACfC,gBAAgB;EAChBC,WAAW;EACXC,oBAAoB;EACpBC,oBAAoB;EACpBC,YAAY;EACZC,cAAc;EACdC,iBAAiB;EACjBC,kBAAkB;EAClBC,kBAAkB;EAClBC,kBAAkB;EACnBC;AAEH,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EACL,MAAM;IACLC,cAAc;IACdC,gBAAgB;IAChBC,oBAAoB;IACpBC,eAAe;IACfC,QAAQ;IACRC,YAAY;IACZC,YAAY;IACZC,cAAc;IACdC,WAAW;IACXC,gBAAgB;IAChBC,oBAAoB;IACpBC,oBAAoB;IACpBC,cAAc;IACdC;EACD,CAAC,GAAG5D,cAAc,CAAE6D,KAAkB,IAAKA,KAAK,CAAC;EACjD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzE,QAAQ,CAAqB,IAAI,CAAC;EAC5E;EACA;EACA,MAAM,CAAC0E,aAAa,EAAEC,gBAAgB,CAAC,GAAG3E,QAAQ,CAAuC,IAAI,CAAC;EAC9F,MAAM,CAAC4E,YAAY,EAAEC,eAAe,CAAC,GAAG7E,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC8E,WAAW,EAAEC,cAAc,CAAC,GAAG/E,QAAQ,CAAS,EAAE,CAAC,CAAC,CAAC;EAC5D,MAAMgF,UAAU,GAAG/E,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAMgF,kBAAkB,GAAGhF,MAAM,CAAiB,IAAI,CAAC;EACvD,IAAIiF,OAAY;EAChB,MAAMC,iBAAiB,GAAIC,KAAa,IAAyB;IAChE,MAAMC,MAAM,GAAGC,QAAQ,CAACC,QAAQ,CAACH,KAAK,EAAEE,QAAQ,EAAE,IAAI,EAAEE,WAAW,CAACC,uBAAuB,EAAE,IAAI,CAAC;IAClG,MAAMC,IAAI,GAAGL,MAAM,CAACM,eAAe;IACnC,IAAID,IAAI,YAAYE,WAAW,EAAE;MAChC,OAAOF,IAAI;IACZ,CAAC,MAAM,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEG,aAAa,EAAE;MAC/B,OAAOH,IAAI,CAACG,aAAa,CAAC,CAAC;IAC5B,CAAC,MAAM;MACN,OAAO,IAAI;IACZ;EACD,CAAC;EACD,IAAIT,KAAU;EACd,IAAI7C,cAAc,EAAE6C,KAAK,GAAG7C,cAAc,aAAdA,cAAc,wBAAAO,qBAAA,GAAdP,cAAc,CAAEuD,SAAS,cAAAhD,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,uBAA9BA,sBAAA,CAAgCgD,WAAW;EACvE,MAAMC,kBAAkB,GAAIZ,KAAyB,IAAK;IACzD,MAAMa,OAAO,GAAGd,iBAAiB,CAACC,KAAK,IAAI,EAAE,CAAC;IAC9C,IAAIa,OAAO,EAAE;MACZ,MAAMC,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;MAC5C,OAAO;QACNC,GAAG,EAAEF,IAAI,CAACE,GAAG;QAAE;QACfC,IAAI,EAAEH,IAAI,CAACG,IAAI,CAAE;MAClB,CAAC;IACF;IACA,OAAO,IAAI;EACZ,CAAC;EACC;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMwG,YAAY,GAAGvG,MAAM,CAAM,IAAI,CAAC;EACxC;EACA,MAAMwG,2BAA2B,GAAGA,CAAA,KAAM;IACzC;IACA,IAAIzB,UAAU,CAAC0B,OAAO,EAAE;MACvB,MAAMC,WAAW,GAAG3B,UAAU,CAAC0B,OAAO,CAACP,qBAAqB,CAAC,CAAC;MAC9D,OAAO;QACNS,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACH,WAAW,CAACC,KAAK,EAAE,GAAG,CAAC;QAAE;QACzCG,MAAM,EAAEF,IAAI,CAACC,GAAG,CAACH,WAAW,CAACI,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;MACjD,CAAC;IACF;;IAEA;IACA,MAAMC,cAAc,GAAGpC,YAAY,GAAGqC,QAAQ,CAACrC,YAAY,CAAC,GAC1D1C,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEgF,KAAK,GAAGD,QAAQ,CAAC/E,gBAAgB,CAACgF,KAAK,CAAC,GAAG,GAAI;IACnE,MAAMC,eAAe,GAAGC,cAAc,CAAC,CAAC,GAAG,EAAE,GAC3CC,WAAW,CAAC,CAAC,GAAG,GAAG,GAAG,GAAI;IAE5B,OAAO;MACNT,KAAK,EAAEI,cAAc;MACrBD,MAAM,EAAEI;IACT,CAAC;EACF,CAAC;;EAED;EACA,MAAMG,sBAAsB,GAAGA,CAACC,WAAoB,EAAEzC,WAAmB,EAAE0C,OAAe,EAAEC,OAAe,KAAK;IAC/G,MAAMC,WAAW,GAAGH,WAAW,CAACI,CAAC,GAAGH,OAAO;IAC3C,MAAMI,UAAU,GAAGL,WAAW,CAACM,CAAC,GAAGJ,OAAO;;IAE1C;IACA,MAAMK,aAAa,GAAGC,MAAM,CAACC,UAAU;IACvC,MAAMC,cAAc,GAAGF,MAAM,CAACG,WAAW;;IAEzC;IACA,MAAM;MAAEtB,KAAK,EAAEuB,mBAAmB;MAAEpB,MAAM,EAAEqB;IAAqB,CAAC,GAAG3B,2BAA2B,CAAC,CAAC;;IAElG;IACA,IAAI4B,cAAc,GAAGvD,WAAW,GAAG,EAAE;IACrC,IAAIwD,cAAc,GAAGxD,WAAW,GAAG,EAAE;;IAErC;IACA,MAAMyD,kBAAkB,GAAIb,WAAW,GAAGW,cAAc,GAAGF,mBAAmB,GAAKL,aAAa,GAAG,EAAG,CAAC,CAAC;;IAExG;IACA,MAAMU,mBAAmB,GAAIZ,UAAU,GAAGU,cAAc,GAAGF,oBAAoB,GAAKH,cAAc,GAAG,EAAG,CAAC,CAAC;;IAE1G;IACA,MAAMQ,iBAAiB,GAAIf,WAAW,GAAGS,mBAAmB,GAAG,EAAE,GAAI,EAAE,CAAC,CAAC;;IAEzE;IACA,MAAMO,gBAAgB,GAAId,UAAU,GAAGQ,oBAAoB,GAAG,EAAE,GAAI,EAAE,CAAC,CAAC;;IAExE;IACA,IAAIG,kBAAkB,IAAI,CAACE,iBAAiB,EAAE;MAC7C;MACAJ,cAAc,GAAG,EAAEF,mBAAmB,GAAE,GAAG,CAAC;IAC7C,CAAC,MAAM,IAAII,kBAAkB,IAAIE,iBAAiB,EAAE;MACnD;MACAJ,cAAc,GAAG,EAAEF,mBAAmB,GAAG,CAAC,CAAC;IAC5C;IAEA,IAAIK,mBAAmB,IAAI,CAACE,gBAAgB,EAAE;MAC7C;MACAJ,cAAc,GAAG,EAAEF,oBAAoB,GAAE,GAAG,CAAC;IAC9C,CAAC,MAAM,IAAII,mBAAmB,IAAIE,gBAAgB,EAAE;MACnD;MACAJ,cAAc,GAAG,EAAEF,oBAAoB,GAAG,CAAC,CAAC;IAC7C;;IAEA;IACA,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;MAC3CC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;QACjCC,eAAe,EAAE;UAAE3C,IAAI,EAAEqB,WAAW;UAAEtB,GAAG,EAAEwB;QAAW,CAAC;QACvDqB,eAAe,EAAE;UAAErC,KAAK,EAAEuB,mBAAmB;UAAEpB,MAAM,EAAEqB;QAAqB,CAAC;QAC7Ec,QAAQ,EAAE;UAAEtC,KAAK,EAAEkB,aAAa;UAAEf,MAAM,EAAEkB;QAAe,CAAC;QAC1DkB,SAAS,EAAE;UAAEC,KAAK,EAAEb,kBAAkB;UAAEc,MAAM,EAAEb,mBAAmB;UAAEnC,IAAI,EAAEoC,iBAAiB;UAAErC,GAAG,EAAEsC;QAAiB,CAAC;QACrHY,WAAW,EAAE;UAAE3B,CAAC,EAAEU,cAAc;UAAER,CAAC,EAAES;QAAe;MACrD,CAAC,CAAC;IACH;IAEA,OAAO;MACNlC,GAAG,EAAEwB,UAAU,GAAGG,MAAM,CAACwB,OAAO,GAAGjB,cAAc;MACjDjC,IAAI,EAAEqB,WAAW,GAAGK,MAAM,CAACyB,OAAO,GAAGnB;IACtC,CAAC;EACF,CAAC;EACDtI,SAAS,CAAC,MAAM;IACf,MAAMkG,OAAO,GAAGd,iBAAiB,CAACC,KAAK,CAAC;IACxC,IAAIa,OAAO,EAAE;MACZ,MAAMC,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;MAC5CxB,gBAAgB,CAAC;QAChByB,GAAG,EAAEF,IAAI,CAACE,GAAG,GAAG2B,MAAM,CAACwB,OAAO;QAAE;QAChClD,IAAI,EAAEH,IAAI,CAACG,IAAI,GAAG0B,MAAM,CAACyB;MAC1B,CAAC,CAAC;IACH;EACD,CAAC,EAAE,CAACpE,KAAK,CAAC,CAAC;EACXrF,SAAS,CAAC,MAAM;IACf,IAAI,OAAOgI,MAAM,KAAK0B,SAAS,EAAE;MAChC,MAAMC,QAAQ,GAAG1D,kBAAkB,CAACZ,KAAK,IAAI,EAAE,CAAC;MAChD,IAAIsE,QAAQ,EAAE;QACb/E,gBAAgB,CAAC+E,QAAQ,CAAC;MAC3B;IACD;EACD,CAAC,EAAE,CAACtE,KAAK,CAAC,CAAC;EACXrF,SAAS,CAAC,MAAM;IACf,MAAMkG,OAAO,GAAGd,iBAAiB,CAACC,KAAK,CAAC;IACxC;IACA,IAAIa,OAAO,EAAE,CACb;EACD,CAAC,EAAE,CAAC1D,cAAc,CAAC,CAAC;EAEpBxC,SAAS,CAAC,MAAM;IAAA,IAAA4J,UAAA;IACf,MAAM1D,OAAO,GAAGd,iBAAiB,CAACjE,SAAS,aAATA,SAAS,wBAAAyI,UAAA,GAATzI,SAAS,CAAGQ,WAAW,GAAG,CAAC,CAAC,cAAAiI,UAAA,uBAA5BA,UAAA,CAA8B5D,WAAW,CAAC;IAC5EtB,gBAAgB,CAACwB,OAAO,CAAC;IACzB,IAAIA,OAAO,EAAE;MACZA,OAAO,CAAC2D,KAAK,CAACC,eAAe,GAAG,gBAAgB;;MAEhD;MACA,MAAM3D,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;MAC5CxB,gBAAgB,CAAC;QAChByB,GAAG,EAAEF,IAAI,CAACE,GAAG,GAAG2B,MAAM,CAACwB,OAAO;QAC9BlD,IAAI,EAAEH,IAAI,CAACG,IAAI,GAAG0B,MAAM,CAACyB;MAC1B,CAAC,CAAC;IACH;EACD,CAAC,EAAE,CAACtI,SAAS,EAAEQ,WAAW,CAAC,CAAC;;EAE5B;EACA;EACA,MAAM,GAAGoI,eAAe,CAAC,GAAG9J,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM+J,cAAc,GAAGA,CAAA,KAAM;IAC5B,IAAIrG,gBAAgB,KAAK,MAAM,EAAE;MAChC,IAAIhC,WAAW,GAAGC,UAAU,EAAE;QAC7B8B,cAAc,CAAC/B,WAAW,GAAG,CAAC,CAAC;QAC/BF,UAAU,CAAC,CAAC;QACZwI,eAAe,CAACtI,WAAW,GAAGC,UAAU,CAAC;MAC1C;IACD,CAAC,MAAM;MACN8B,cAAc,CAAC/B,WAAW,GAAG,CAAC,CAAC;MAC/B,MAAMuI,eAAe,GAAG3E,QAAQ,CAAC4E,cAAc,CAAC,cAAc,CAAC;MAC/D,IAAID,eAAe,EAAE;QACpBA,eAAe,CAACL,KAAK,CAACO,OAAO,GAAG,MAAM;QACtCF,eAAe,CAACG,MAAM,CAAC,CAAC;MACzB;IACD;EACD,CAAC;EAED,MAAMJ,eAAe,GAAIK,qBAA8B,IAAK;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;IAC3D,OAAOV,qBAAqB,gBAC3BxJ,OAAA,CAACG,cAAc;MACd2B,kBAAkB,EAAEA,kBAAmB;MACvCC,kBAAkB,EAAEA,kBAAmB;MACvCH,kBAAkB,EAAEA,kBAAmB;MACvCC,kBAAkB,EAAEA,kBAAmB;MACvCzB,QAAQ,EAAEA,QAAS;MACnBsB,cAAc,EAAEA,cAAe;MAC/BrB,SAAS,EAAEA,SAAU;MACrBI,OAAO,EAAEA,OAAQ;MACjBC,UAAU,EAAEyJ,cAAe;MAC3BxJ,UAAU,EAAEuI,cAAe;MAC3B5I,KAAK,EAAEA,KAAM;MACbC,IAAI,EAAEA,IAAK;MACXC,QAAQ,EAAEA,QAAS;MACnBK,WAAW,EAAEA,WAAW,GAAG,CAAE;MAC7BC,UAAU,EAAEA,UAAW;MACvBC,eAAe,EAAEA,eAAgB;MACjCC,QAAQ,EAAEA,QAAS;MACnBC,mBAAmB,EAAES,cAAc,aAAdA,cAAc,wBAAA+H,sBAAA,GAAd/H,cAAc,CAAEuD,SAAS,cAAAwE,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4B5I,WAAW,CAAC,cAAA6I,sBAAA,uBAAxCA,sBAAA,CAA0CU,mBAAoB;MACnFlJ,eAAe,EAAEQ,cAAc,aAAdA,cAAc,wBAAAiI,sBAAA,GAAdjI,cAAc,CAAEuD,SAAS,cAAA0E,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4B9I,WAAW,CAAC,cAAA+I,sBAAA,uBAAxCA,sBAAA,CAA0CS,eAAgB;MAC3ElJ,YAAY,EACX,CAAAO,cAAc,aAAdA,cAAc,wBAAAmI,sBAAA,GAAdnI,cAAc,CAAEuD,SAAS,cAAA4E,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4BhJ,WAAW,CAAC,cAAAiJ,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA0CQ,aAAa,cAAAP,sBAAA,wBAAAC,uBAAA,GAAvDD,sBAAA,CAAyDQ,GAAG,CAAEC,OAAY,IACzEA,OAAO,CAACC,aAAa,CAACF,GAAG,CAAEG,MAAW,KAAM;QAC3C,GAAGA,MAAM;QACTC,WAAW,EAAEH,OAAO,CAACI,EAAE,CAAE;MAC1B,CAAC,CAAC,CACH,CAAC,cAAAZ,uBAAA,uBALDA,uBAAA,CAKGa,MAAM,CAAC,CAACC,GAAmB,EAAEC,IAAS,KAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,EAAE,EAAE,CAAC,KAAI,EACvE;MACD3J,eAAe,EAAEA,eAAgB;MACjCC,gBAAgB,EAAEA,gBAAiB;MACnCC,WAAW,EAAEA,WAAY;MACzBG,YAAY,EAAEA,YAAa;MAC3BE,iBAAiB,EAAE,CAAAD,cAAc,aAAdA,cAAc,wBAAAuI,uBAAA,GAAdvI,cAAc,CAAEuD,SAAS,cAAAgF,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BpJ,WAAW,GAAG,CAAC,CAAC,cAAAqJ,uBAAA,uBAA5CA,uBAAA,CAA8Ce,OAAO,KAAI,CAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E,CAAC,GACC,IAAI;EACT,CAAC;EAED,MAAMlB,cAAc,GAAGA,CAAA,KAAM;IAC5B,IAAItJ,WAAW,GAAG,CAAC,EAAE;MACpB+B,cAAc,CAAC/B,WAAW,GAAG,CAAC,CAAC;MAC/BH,UAAU,CAAC,CAAC;IACb;EACD,CAAC;EACDxB,SAAS,CAAC,MAAM;IACf,IAAIuC,YAAY,EAAE;MACjBwH,eAAe,CAAC,IAAI,CAAC;IACtB,CAAC,MAAM;MACNA,eAAe,CAAC,KAAK,CAAC;IACvB;EACD,CAAC,EAAE,CAACxH,YAAY,CAAC,CAAC;EAClB;EACA,MAAM6J,4BAA4B,GACjCzC,QAAgB,IACqD;IACrE,QAAQA,QAAQ;MACf,KAAK,UAAU;QACd,OAAO;UACN0C,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAO,CAAC;UACrDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ;QAC5D,CAAC;MACF,KAAK,WAAW;QACf,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACtDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAC3D,CAAC;MACF,KAAK,aAAa;QACjB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO,CAAC;UACxDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAQ;QACzD,CAAC;MACF,KAAK,cAAc;QAClB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACzDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAC3D,CAAC;MACF,KAAK,eAAe;QACnB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAC1DC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;MACF,KAAK,YAAY;QAChB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAS,CAAC;UACvDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;MACF,KAAK,aAAa;QACjB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO,CAAC;UACxDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ;QAC5D,CAAC;MACF,KAAK,eAAe;QACnB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAC1DC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;MACF,KAAK,cAAc;QAClB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACzDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAC3D,CAAC;MACF;QACC,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAC1DC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;IACH;EACD,CAAC;EAED,MAAM;IAAEF,YAAY;IAAEG;EAAgB,CAAC,GAAGJ,4BAA4B,CAAC,CAAAjK,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEsK,QAAQ,KAAI,eAAe,CAAC;EAErH,MAAMC,SAAS,GAAG;IACjBC,UAAU,EAAE5K,mBAAmB,aAAnBA,mBAAmB,gBAAAkB,qBAAA,GAAnBlB,mBAAmB,CAAE6K,cAAc,cAAA3J,qBAAA,eAAnCA,qBAAA,CAAqC4J,IAAI,GAAG,MAAM,GAAG,QAAQ;IACzEC,SAAS,EAAE/K,mBAAmB,aAAnBA,mBAAmB,gBAAAmB,sBAAA,GAAnBnB,mBAAmB,CAAE6K,cAAc,cAAA1J,sBAAA,eAAnCA,sBAAA,CAAqC6J,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5EC,KAAK,EAAE,CAAAjL,mBAAmB,aAAnBA,mBAAmB,wBAAAoB,sBAAA,GAAnBpB,mBAAmB,CAAE6K,cAAc,cAAAzJ,sBAAA,uBAAnCA,sBAAA,CAAqC8J,SAAS,KAAI,SAAS;IAClEC,SAAS,EAAE,CAAAnL,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEoL,SAAS,KAAI;EAC9C,CAAC;;EAED;;EAEA,MAAMC,iBAAiB,GAAIC,OAAe,IAAK;IAC9C;IACA,OAAO;MACNC,MAAM,EAAED,OAAO,CAACE,OAAO,CAAC,qCAAqC,EAAE,CAACC,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;QACtF,OAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE;MAC1C,CAAC;IACF,CAAC;EACF,CAAC;;EAED;EACA,MAAMtG,cAAc,GAAGA,CAAA,KAAM;IAC5B,MAAMuG,QAAQ,GAAG5L,eAAe,IAAIA,eAAe,CAAC6L,MAAM,GAAG,CAAC,IAC7D7L,eAAe,CAAC8L,IAAI,CAAEC,IAAS,IAC9BA,IAAI,CAACC,WAAW,IAAID,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEG,GAAQ,IAAKA,GAAG,CAACC,GAAG,CAChE,CAAC;IAEF,MAAMC,OAAO,GAAGpM,mBAAmB,IAAIA,mBAAmB,CAAC8L,MAAM,GAAG,CAAC,IACpE9L,mBAAmB,CAAC+L,IAAI,CAAEM,KAAU,IAAKA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IAEjF,MAAMC,UAAU,GAAGtM,YAAY,IAAIA,YAAY,CAAC4L,MAAM,GAAG,CAAC;IAE1D,OAAOU,UAAU,IAAI,CAACX,QAAQ,IAAI,CAACO,OAAO;EAC3C,CAAC;;EAED;EACA,MAAM7G,WAAW,GAAGA,CAAA,KAAM;IACzB,MAAMsG,QAAQ,GAAG5L,eAAe,IAAIA,eAAe,CAAC6L,MAAM,GAAG,CAAC,IAC7D7L,eAAe,CAAC8L,IAAI,CAAEC,IAAS,IAC9BA,IAAI,CAACC,WAAW,IAAID,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEG,GAAQ,IAAKA,GAAG,CAACC,GAAG,CAChE,CAAC;IAEF,MAAMC,OAAO,GAAGpM,mBAAmB,IAAIA,mBAAmB,CAAC8L,MAAM,GAAG,CAAC,IACpE9L,mBAAmB,CAAC+L,IAAI,CAAEM,KAAU,IAAKA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IAEjF,MAAMC,UAAU,GAAGtM,YAAY,IAAIA,YAAY,CAAC4L,MAAM,GAAG,CAAC;IAE1D,OAAOM,OAAO,IAAI,CAACP,QAAQ,IAAI,CAACW,UAAU;EAC3C,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAAA,IAAAC,mBAAA,EAAAC,qBAAA;IACnC;IACA,IAAIvM,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEgF,KAAK,IAAI,CAACE,cAAc,CAAC,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE;MACnE,OAAO,GAAGnF,gBAAgB,CAACgF,KAAK,IAAI;IACrC;;IAEA;IACA,IAAIE,cAAc,CAAC,CAAC,IAAIC,WAAW,CAAC,CAAC,EAAE;MACtC,OAAO,MAAM;IACd;;IAEA;IACA,MAAMqH,YAAY,GAAG,EAAAF,mBAAA,GAAAxJ,UAAU,CAAC0B,OAAO,cAAA8H,mBAAA,uBAAlBA,mBAAA,CAAoBG,WAAW,KAAI,CAAC;IACzD,MAAMC,WAAW,GAAG,EAAAH,qBAAA,GAAAxJ,kBAAkB,CAACyB,OAAO,cAAA+H,qBAAA,uBAA1BA,qBAAA,CAA4BE,WAAW,KAAI,CAAC;;IAEhE;IACA,MAAME,YAAY,GAAGhI,IAAI,CAACC,GAAG,CAAC4H,YAAY,EAAEE,WAAW,CAAC;;IAExD;IACA,MAAME,WAAW,GAAGD,YAAY,GAAG,EAAE,CAAC,CAAC;;IAEvC;IACA,MAAME,QAAQ,GAAG,GAAG,CAAC,CAAC;IACtB,MAAMC,QAAQ,GAAG,GAAG,CAAC,CAAC;;IAEtB,MAAMC,UAAU,GAAGpI,IAAI,CAACC,GAAG,CAACiI,QAAQ,EAAElI,IAAI,CAACqI,GAAG,CAACJ,WAAW,EAAEE,QAAQ,CAAC,CAAC;IAEtE,OAAO,GAAGC,UAAU,IAAI;EACzB,CAAC;;EAED;EACAlP,SAAS,CAAC,MAAM;IACf;IACAoP,qBAAqB,CAAC,MAAM;MAC3B,MAAMC,QAAQ,GAAGb,qBAAqB,CAAC,CAAC;MACxC1J,eAAe,CAACuK,QAAQ,CAAC;IAC1B,CAAC,CAAC;EACH,CAAC,EAAE,CAACtN,mBAAmB,EAAEC,eAAe,EAAEC,YAAY,EAAEN,WAAW,CAAC,CAAC;;EAErE;EACA3B,SAAS,CAAC,MAAM;IACf,IAAIqF,KAAK,IAAIN,WAAW,EAAE;MACzB,MAAMmB,OAAO,GAAGd,iBAAiB,CAACC,KAAK,CAAC;MACxC,IAAIa,OAAO,EAAE;QAAA,IAAAoJ,qBAAA;QACZ,MAAMnJ,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;QAC5C,MAAMmJ,eAAe,IAAAD,qBAAA,GAAG1L,oBAAoB,CAAC,CAAC,CAAC,cAAA0L,qBAAA,uBAAvBA,qBAAA,CAAyBE,QAAQ;QACzD,MAAM/H,OAAO,GAAGgI,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,SAAS,KAAI,GAAG,CAAC;QAC7D,MAAMhI,OAAO,GAAG+H,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,SAAS,KAAI,GAAG,CAAC;QAE7D,MAAMC,QAAQ,GAAGrI,sBAAsB,CAACpB,IAAI,EAAEpB,WAAW,EAAE0C,OAAO,EAAEC,OAAO,CAAC;QAC5E9C,gBAAgB,CAACgL,QAAQ,CAAC;MAC3B;IACD;EACD,CAAC,EAAE,CAAC7K,WAAW,EAAEM,KAAK,EAAEzB,oBAAoB,EAAEiB,YAAY,EAAE9C,mBAAmB,EAAEC,eAAe,EAAEC,YAAY,CAAC,CAAC;;EAEhH;EACAjC,SAAS,CAAC,MAAM;IACf,MAAM6P,YAAY,GAAGA,CAAA,KAAM;MAC1B,IAAIxK,KAAK,IAAIN,WAAW,EAAE;QACzB,MAAMmB,OAAO,GAAGd,iBAAiB,CAACC,KAAK,CAAC;QACxC,IAAIa,OAAO,EAAE;UAAA,IAAA4J,sBAAA;UACZ,MAAM3J,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;UAC5C,MAAMmJ,eAAe,IAAAO,sBAAA,GAAGlM,oBAAoB,CAAC,CAAC,CAAC,cAAAkM,sBAAA,uBAAvBA,sBAAA,CAAyBN,QAAQ;UACzD,MAAM/H,OAAO,GAAGgI,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,SAAS,KAAI,GAAG,CAAC;UAC7D,MAAMhI,OAAO,GAAG+H,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,SAAS,KAAI,GAAG,CAAC;UAE7D,MAAMC,QAAQ,GAAGrI,sBAAsB,CAACpB,IAAI,EAAEpB,WAAW,EAAE0C,OAAO,EAAEC,OAAO,CAAC;UAC5E9C,gBAAgB,CAACgL,QAAQ,CAAC;QAC3B;MACD;IACD,CAAC;IAED5H,MAAM,CAAC+H,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/C,OAAO,MAAM7H,MAAM,CAACgI,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EAChE,CAAC,EAAE,CAACxK,KAAK,EAAEN,WAAW,EAAEnB,oBAAoB,CAAC,CAAC;EAE9C,MAAMqM,cAAc,GAAGhO,YAAY,CAAC0J,MAAM,CAAC,CAACC,GAAQ,EAAEJ,MAAW,KAAK;IACrE,MAAM0E,WAAW,GAAG1E,MAAM,CAACC,WAAW,IAAI,SAAS,CAAC,CAAC;IACrD,IAAI,CAACG,GAAG,CAACsE,WAAW,CAAC,EAAE;MACtBtE,GAAG,CAACsE,WAAW,CAAC,GAAG,EAAE;IACtB;IACAtE,GAAG,CAACsE,WAAW,CAAC,CAACC,IAAI,CAAC3E,MAAM,CAAC;IAC7B,OAAOI,GAAG;EACX,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMwE,WAAW,GAAG;IACnBzG,QAAQ,EAAE,CAAAxH,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEsK,QAAQ,KAAI,eAAe;IACvD4D,YAAY,EAAE,CAAAlO,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEmO,MAAM,KAAI,KAAK;IAC/CC,WAAW,EAAE,CAAApO,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqO,UAAU,KAAI,KAAK;IAClDC,WAAW,EAAE,CAAAtO,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuO,WAAW,KAAI,OAAO;IACrDC,WAAW,EAAE,OAAO;IACpB7G,eAAe,EAAE,CAAA3H,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyO,eAAe,KAAI,OAAO;IAC7D3B,QAAQ,EAAG5H,cAAc,CAAC,CAAC,IAAIC,WAAW,CAAC,CAAC,GAAI,iBAAiB,GAC7DzC,YAAY,GAAG,GAAGA,YAAY,aAAa,GAC3C1C,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEgF,KAAK,GAAG,GAAGhF,gBAAgB,CAACgF,KAAK,eAAe,GAAG,OAAO;IAChFN,KAAK,EAAGQ,cAAc,CAAC,CAAC,IAAIC,WAAW,CAAC,CAAC,GAAI,iBAAiB,GAC1DzC,YAAY,GAAG,GAAGA,YAAY,aAAa,GAC3C1C,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEgF,KAAK,GAAG,GAAGhF,gBAAgB,CAACgF,KAAK,eAAe,GAAG;EAC1E,CAAC;EACD,MAAM0J,aAAa,GAAG,EAAAzN,gBAAA,GAAApB,eAAe,CAACL,WAAW,GAAG,CAAC,CAAC,cAAAyB,gBAAA,wBAAAC,qBAAA,GAAhCD,gBAAA,CAAkC4K,WAAW,cAAA3K,qBAAA,wBAAAC,sBAAA,GAA7CD,qBAAA,CAAgD1B,WAAW,GAAG,CAAC,CAAC,cAAA2B,sBAAA,uBAAhEA,sBAAA,CAAkEwN,aAAa,KAAI,MAAM;EAC/G,MAAMC,kBAAkB,GAAIC,MAAW,IAAK;IAC3C,IAAIA,MAAM,CAACC,MAAM,KAAK,UAAU,IAAID,MAAM,CAACC,MAAM,KAAK,MAAM,IAAID,MAAM,CAACC,MAAM,KAAK,SAAS,EAAE;MAC5F,MAAMC,SAAS,GAAGF,MAAM,CAACG,SAAS;MAClC,IAAIH,MAAM,CAACI,WAAW,KAAK,UAAU,EAAE;QACtC;QACApJ,MAAM,CAACqJ,QAAQ,CAACC,IAAI,GAAGJ,SAAS;MACjC,CAAC,MAAM;QACN;QACAlJ,MAAM,CAACuJ,IAAI,CAACL,SAAS,EAAE,QAAQ,EAAE,qBAAqB,CAAC;MACxD;IACD,CAAC,MAAM;MACN,IACCF,MAAM,CAACC,MAAM,IAAI,UAAU,IAC3BD,MAAM,CAACC,MAAM,IAAI,UAAU,IAC3BD,MAAM,CAACI,WAAW,IAAI,UAAU,IAChCJ,MAAM,CAACI,WAAW,IAAI,UAAU,EAC/B;QACDnG,cAAc,CAAC,CAAC;MACjB,CAAC,MAAM,IACN+F,MAAM,CAACC,MAAM,IAAI,MAAM,IACvBD,MAAM,CAACC,MAAM,IAAI,MAAM,IACvBD,MAAM,CAACI,WAAW,IAAI,MAAM,IAC5BJ,MAAM,CAACI,WAAW,IAAI,MAAM,EAC3B;QACDpH,cAAc,CAAC,CAAC;MACjB,CAAC,MAAM,IACNgH,MAAM,CAACC,MAAM,IAAI,SAAS,IAC1BD,MAAM,CAACI,WAAW,IAAI,SAAS,EAC9B;QAAA,IAAAI,uBAAA,EAAAC,uBAAA;QACD;QACA/N,cAAc,CAAC,CAAC,CAAC;QACjB;QACA,IAAIlB,cAAc,aAAdA,cAAc,gBAAAgP,uBAAA,GAAdhP,cAAc,CAAEuD,SAAS,cAAAyL,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,eAA9BA,uBAAA,CAAgCzL,WAAW,EAAE;UAChD,MAAM0L,gBAAgB,GAAGtM,iBAAiB,CAAC5C,cAAc,CAACuD,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UACnF,IAAI0L,gBAAgB,EAAE;YACrBA,gBAAgB,CAACC,cAAc,CAAC;cAAEC,QAAQ,EAAE;YAAS,CAAC,CAAC;UACxD;QACD;MACD;IACD;IACA7H,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EACD/J,SAAS,CAAC,MAAM;IAAA,IAAA6R,WAAA,EAAAC,mBAAA;IACf,IAAI3Q,SAAS,aAATA,SAAS,gBAAA0Q,WAAA,GAAT1Q,SAAS,CAAGQ,WAAW,GAAG,CAAC,CAAC,cAAAkQ,WAAA,gBAAAC,mBAAA,GAA5BD,WAAA,CAA8B9F,OAAO,cAAA+F,mBAAA,eAArCA,mBAAA,CAAuCC,aAAa,EAAE;MACzD;MACA9N,cAAc,CAAC,IAAI,CAAC;IACrB;EACD,CAAC,EAAE,CAAC9C,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGQ,WAAW,GAAG,CAAC,CAAC,EAAEA,WAAW,EAAEsC,cAAc,CAAC,CAAC;;EAE/D;EACAjE,SAAS,CAAC,MAAM;IACf,IAAI4C,kBAAkB,EAAE;MAAA,IAAAoP,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;MACvB;MACA,MAAM9C,eAAe,GAAGlL,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAAoO,sBAAA,GAApBpO,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAAqQ,sBAAA,eAAvCA,sBAAA,CAAyCxC,QAAQ,GAC5G5L,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAAC6N,QAAQ,IAAAyC,sBAAA,GAC9CrO,oBAAoB,CAAC,CAAC,CAAC,cAAAqO,sBAAA,uBAAvBA,sBAAA,CAAyBzC,QAAQ;MACpC,MAAM8C,WAAW,GAAGjO,oBAAoB,KAAK,SAAS,GACnD7B,cAAc,aAAdA,cAAc,wBAAA0P,uBAAA,GAAd1P,cAAc,CAAEuD,SAAS,cAAAmM,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BvQ,WAAW,GAAG,CAAC,CAAC,cAAAwQ,uBAAA,uBAA5CA,uBAAA,CAA8CpG,OAAO,GACrDvJ,cAAc,aAAdA,cAAc,wBAAA4P,uBAAA,GAAd5P,cAAc,CAAEuD,SAAS,cAAAqM,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,uBAA9BA,uBAAA,CAAgCtG,OAAO;;MAE1C;MACA;MACA,IAAIwD,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEwC,aAAa,EAAE;QACnC;QACA9N,cAAc,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACN;QACAA,cAAc,CAAC,KAAK,CAAC;MACtB;IACD;EACD,CAAC,EAAE,CAACrB,kBAAkB,EAAEgB,oBAAoB,CAAC,CAAC;;EAE9C;EACA5D,SAAS,CAAC,MAAM;IACf,IAAI6C,kBAAkB,EAAE;MAAA,IAAA0P,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;MACvB;MACA,MAAMrD,eAAe,GAAGlL,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAA2O,sBAAA,GAApB3O,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAA4Q,sBAAA,eAAvCA,sBAAA,CAAyC/C,QAAQ,GAC5G5L,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAAC6N,QAAQ,IAAAgD,sBAAA,GAC9C5O,oBAAoB,CAAC,CAAC,CAAC,cAAA4O,sBAAA,uBAAvBA,sBAAA,CAAyBhD,QAAQ;MACpC,MAAM8C,WAAW,GAAGjO,oBAAoB,KAAK,SAAS,GACnD7B,cAAc,aAAdA,cAAc,wBAAAiQ,uBAAA,GAAdjQ,cAAc,CAAEuD,SAAS,cAAA0M,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B9Q,WAAW,GAAG,CAAC,CAAC,cAAA+Q,uBAAA,uBAA5CA,uBAAA,CAA8C3G,OAAO,GACrDvJ,cAAc,aAAdA,cAAc,wBAAAmQ,uBAAA,GAAdnQ,cAAc,CAAEuD,SAAS,cAAA4M,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,uBAA9BA,uBAAA,CAAgC7G,OAAO;;MAE1C;MACA,IAAIwD,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEwC,aAAa,EAAE;QACnC;QACA9N,cAAc,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACN;QACAA,cAAc,CAAC,KAAK,CAAC;MACtB;IACD;EACD,CAAC,EAAE,CAACpB,kBAAkB,EAAEe,oBAAoB,CAAC,CAAC;;EAE9C;EACA5D,SAAS,CAAC,MAAM;IACf,MAAM6S,iBAAiB,GAAIC,CAAa,IAAK;MAC5C,MAAMC,cAAc,GAAGxN,QAAQ,CAAC4E,cAAc,CAAC,cAAc,CAAC;;MAE9D;MACA,IAAI4I,cAAc,IAAIA,cAAc,CAACC,QAAQ,CAACF,CAAC,CAACG,MAAc,CAAC,EAAE;QAChE;MACD;;MAEA;MACA;IACD,CAAC;IAED1N,QAAQ,CAACwK,gBAAgB,CAAC,OAAO,EAAE8C,iBAAiB,CAAC;IAErD,OAAO,MAAM;MACZtN,QAAQ,CAACyK,mBAAmB,CAAC,OAAO,EAAE6C,iBAAiB,CAAC;IACzD,CAAC;EACF,CAAC,EAAE,CAACjP,oBAAoB,CAAC,CAAC;EAC1B;EACA5D,SAAS,CAAC,MAAM;IACf,MAAMkT,iBAAiB,GAAGA,CAAA,KAAM;MAC/B,IAAIjO,UAAU,CAAC0B,OAAO,EAAE;QACvB;QACA1B,UAAU,CAAC0B,OAAO,CAACkD,KAAK,CAAC7C,MAAM,GAAG,MAAM;QACxC,MAAMmM,aAAa,GAAGlO,UAAU,CAAC0B,OAAO,CAACyM,YAAY;QACrD,MAAMC,eAAe,GAAG,GAAG,CAAC,CAAC;QAC7B,MAAMC,YAAY,GAAGH,aAAa,GAAGE,eAAe;QAGpD7M,iBAAiB,CAAC8M,YAAY,CAAC;;QAE/B;QACA,IAAI7M,YAAY,CAACE,OAAO,EAAE;UACzB;UACA,IAAIF,YAAY,CAACE,OAAO,CAAC4M,YAAY,EAAE;YACtC9M,YAAY,CAACE,OAAO,CAAC4M,YAAY,CAAC,CAAC;UACpC;UACA;UACAC,UAAU,CAAC,MAAM;YAChB,IAAI/M,YAAY,CAACE,OAAO,IAAIF,YAAY,CAACE,OAAO,CAAC4M,YAAY,EAAE;cAC9D9M,YAAY,CAACE,OAAO,CAAC4M,YAAY,CAAC,CAAC;YACpC;UACD,CAAC,EAAE,EAAE,CAAC;QACP;MACD;IACD,CAAC;IAGDL,iBAAiB,CAAC,CAAC;IAGnB,MAAMO,QAAQ,GAAG,CAChBD,UAAU,CAACN,iBAAiB,EAAE,EAAE,CAAC,EACjCM,UAAU,CAACN,iBAAiB,EAAE,GAAG,CAAC,EAClCM,UAAU,CAACN,iBAAiB,EAAE,GAAG,CAAC,EAClCM,UAAU,CAACN,iBAAiB,EAAE,GAAG,CAAC,CAClC;IAGD,IAAIQ,cAAqC,GAAG,IAAI;IAChD,IAAIC,gBAAyC,GAAG,IAAI;IAEpD,IAAI1O,UAAU,CAAC0B,OAAO,IAAIqB,MAAM,CAAC4L,cAAc,EAAE;MAChDF,cAAc,GAAG,IAAIE,cAAc,CAAC,MAAM;QACzCJ,UAAU,CAACN,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFQ,cAAc,CAACG,OAAO,CAAC5O,UAAU,CAAC0B,OAAO,CAAC;IAC3C;IAGA,IAAI1B,UAAU,CAAC0B,OAAO,IAAIqB,MAAM,CAAC8L,gBAAgB,EAAE;MAClDH,gBAAgB,GAAG,IAAIG,gBAAgB,CAAC,MAAM;QAC7CN,UAAU,CAACN,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFS,gBAAgB,CAACE,OAAO,CAAC5O,UAAU,CAAC0B,OAAO,EAAE;QAC5CoN,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO;MACnC,CAAC,CAAC;IACH;IAEA,OAAO,MAAM;MACZT,QAAQ,CAACU,OAAO,CAACC,YAAY,CAAC;MAC9B,IAAIV,cAAc,EAAE;QACnBA,cAAc,CAACW,UAAU,CAAC,CAAC;MAC5B;MACA,IAAIV,gBAAgB,EAAE;QACrBA,gBAAgB,CAACU,UAAU,CAAC,CAAC;MAC9B;IACD,CAAC;EACF,CAAC,EAAE,CAAC1S,WAAW,CAAC,CAAC;EACjB;EACA;;EAEA,SAAS2S,YAAYA,CAACC,SAAiB,EAAE;IACxC,QAAQA,SAAS;MAChB,KAAK,OAAO;QACX,OAAO,YAAY;MACpB,KAAK,KAAK;QACT,OAAO,UAAU;MAClB,KAAK,QAAQ;MACb;QACC,OAAO,QAAQ;IACjB;EACD;EACA,MAAMC,iBAAiB,GAAGA,CAAC7K,QAAgB,GAAG,eAAe,KAAK;IACjE,QAAQA,QAAQ;MACf,KAAK,aAAa;QACjB,OAAO;UAAEtD,GAAG,EAAE;QAAkB,CAAC;MAClC,KAAK,cAAc;QAClB,OAAO;UAAEA,GAAG,EAAE;QAAkB,CAAC;MAClC,KAAK,eAAe;QACnB,OAAO;UAAEA,GAAG,EAAE;QAAkB,CAAC;MAClC,KAAK,eAAe;QACnB,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,aAAa;QACjB,OAAO;UAAEA,GAAG,EAAE/E,QAAQ,KAAK,EAAE,GAAG,gBAAgB,GAAG;QAAiB,CAAC;MACtE,KAAK,cAAc;QAClB,OAAO;UAAE+E,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,UAAU;QACd,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,WAAW;QACf,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,YAAY;QAChB,OAAO;UAAEA,GAAG,EAAE;QAAgB,CAAC;MAChC;QACC,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;IAClC;EACD,CAAC;;EAEA;EACD,MAAMoO,kBAAkB,GAAGA,CAACC,QAAgB,EAAEnF,eAAoB,EAAE+C,WAAgB,KAAK;IACxF,IAAIjO,oBAAoB,KAAK,SAAS,EAAE;MACvC;MACA,QAAQqQ,QAAQ;QACf,KAAK,gBAAgB;UACpB,OAAO,CAAApC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqC,cAAc,MAAKjL,SAAS,GAAG4I,WAAW,CAACqC,cAAc,GAAGpF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoF,cAAc;QAChH,KAAK,eAAe;UACnB;UACA,OAAO,CAAArC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsC,4BAA4B,MAAKlL,SAAS,GAAG4I,WAAW,CAACsC,4BAA4B,GAAGrF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqF,4BAA4B;QAC1J,KAAK,UAAU;UACd,OAAO,CAAAtC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuC,QAAQ,MAAKnL,SAAS,GAAG4I,WAAW,CAACuC,QAAQ,GAAGtF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsF,QAAQ;QAC9F,KAAK,eAAe;UACnB,OAAO,CAAAvC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEP,aAAa,MAAKrI,SAAS,GAAG4I,WAAW,CAACP,aAAa,GAAGxC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEwC,aAAa;QAC7G;UACC,OAAOxC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGmF,QAAQ,CAAC;MACpC;IACD,CAAC,MAAM;MACN;MACA,IAAIA,QAAQ,KAAK,eAAe,EAAE;QACjC,OAAOnF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqF,4BAA4B;MACrD;MACA,OAAOrF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGmF,QAAQ,CAAC;IACnC;EACD,CAAC;EAED,MAAMI,kBAAkB,GAAGA,CAAC3P,OAAY,EAAEoK,eAAoB,EAAE+C,WAAgB,EAAEhM,IAAS,EAAED,GAAQ,KAAK;IACzGlB,OAAO,CAAC0E,KAAK,CAACF,QAAQ,GAAG,UAAU;IACnCxE,OAAO,CAAC0E,KAAK,CAACvD,IAAI,GAAG,GAAGA,IAAI,IAAI;IAChCnB,OAAO,CAAC0E,KAAK,CAACxD,GAAG,GAAG,GAAGA,GAAG,IAAI;IAC9BlB,OAAO,CAAC0E,KAAK,CAAChD,KAAK,GAAG,GAAG0I,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEwF,IAAI,IAAI,CAAC,CAAC;IACpD5P,OAAO,CAAC0E,KAAK,CAAC7C,MAAM,GAAG,GAAGuI,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEwF,IAAI,IAAI;IACnD5P,OAAO,CAAC0E,KAAK,CAACC,eAAe,GAAGyF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyF,KAAK;IACtD7P,OAAO,CAAC0E,KAAK,CAACwG,YAAY,GAAG,KAAK;IAClClL,OAAO,CAAC0E,KAAK,CAACoL,MAAM,GAAG,iBAAiB,CAAC,CAAC;IAC1C9P,OAAO,CAAC0E,KAAK,CAACqL,UAAU,GAAG,MAAM;IACjC/P,OAAO,CAAC0E,KAAK,CAACsL,aAAa,GAAG,MAAM,CAAC,CAAC;IACtChQ,OAAO,CAACiQ,SAAS,GAAG,EAAE;IAEtB,IAAI,CAAA7F,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE8F,IAAI,MAAK,MAAM,IAAI,CAAA9F,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE8F,IAAI,MAAK,UAAU,EAAE;MAC7E,MAAMC,QAAQ,GAAG/P,QAAQ,CAACgQ,aAAa,CAAC,MAAM,CAAC;MAC/CD,QAAQ,CAACE,SAAS,GAAGjG,eAAe,CAAC8F,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;MAChEC,QAAQ,CAACzL,KAAK,CAACmD,KAAK,GAAG,OAAO;MAC9BsI,QAAQ,CAACzL,KAAK,CAAC4L,QAAQ,GAAG,MAAM;MAChCH,QAAQ,CAACzL,KAAK,CAAC8C,UAAU,GAAG,MAAM;MAClC2I,QAAQ,CAACzL,KAAK,CAACiD,SAAS,GAAGyC,eAAe,CAAC8F,IAAI,KAAK,MAAM,GAAG,QAAQ,GAAG,QAAQ;MAChFC,QAAQ,CAACzL,KAAK,CAACO,OAAO,GAAG,MAAM;MAC/BkL,QAAQ,CAACzL,KAAK,CAAC6L,UAAU,GAAG,QAAQ;MACpCJ,QAAQ,CAACzL,KAAK,CAAC8L,cAAc,GAAG,QAAQ;MACxCL,QAAQ,CAACzL,KAAK,CAAChD,KAAK,GAAG,MAAM;MAC7ByO,QAAQ,CAACzL,KAAK,CAAC7C,MAAM,GAAG,MAAM;MAC9B7B,OAAO,CAACyQ,WAAW,CAACN,QAAQ,CAAC;IAC9B;;IAEA;IACA;IACA,MAAMO,qBAAqB,GAAGpB,kBAAkB,CAAC,gBAAgB,EAAElF,eAAe,EAAE+C,WAAW,CAAC;IAChG,MAAMwD,WAAW,GAAGzR,oBAAoB,KAAK,SAAS,GAClDwR,qBAAqB,KAAK,KAAK,IAAI,CAAC1Q,OAAO,CAAC4Q,aAAa,GACzDxG,eAAe,IAAIpL,gBAAgB,IAAI,CAACgB,OAAO,CAAC4Q,aAAc;IAElE,IAAID,WAAW,EAAE;MACP3Q,OAAO,CAAC6Q,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;MACxC9Q,OAAO,CAAC6Q,SAAS,CAAC3L,MAAM,CAAC,yBAAyB,CAAC;IACvD,CAAC,MAAM;MACHlF,OAAO,CAAC6Q,SAAS,CAAC3L,MAAM,CAAC,iBAAiB,CAAC;MAC3ClF,OAAO,CAAC6Q,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACpD;;IAEN;IACA9Q,OAAO,CAAC0E,KAAK,CAACO,OAAO,GAAG,MAAM;IAC9BjF,OAAO,CAAC0E,KAAK,CAACsL,aAAa,GAAG,MAAM;;IAEpC;IACA;IACA;IACA,MAAMe,aAAa,GAAGzB,kBAAkB,CAAC,eAAe,EAAElF,eAAe,EAAE+C,WAAW,CAAC;IACvF,IAAI4D,aAAa,EAAE;MAClBjS,cAAc,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACN;MACA;IAAA;;IAGD;IACA;IACA,IAAI,CAACkB,OAAO,CAACgR,YAAY,CAAC,yBAAyB,CAAC,EAAE;MACrD,MAAMC,UAAU,GAAGjR,OAAO,CAACkR,SAAS,CAAC,IAAI,CAAgB;MACzD;MACA,IAAIlR,OAAO,CAAC4Q,aAAa,KAAKrM,SAAS,EAAE;QAC9B0M,UAAU,CAASL,aAAa,GAAG5Q,OAAO,CAAC4Q,aAAa;MAC7D;MACN,IAAI5Q,OAAO,CAACmR,UAAU,EAAE;QACvBnR,OAAO,CAACmR,UAAU,CAACC,YAAY,CAACH,UAAU,EAAEjR,OAAO,CAAC;QACpDA,OAAO,GAAGiR,UAAU;MACrB;IACD;;IAEA;IACAjR,OAAO,CAAC0E,KAAK,CAACsL,aAAa,GAAG,MAAM;;IAEpC;IACA,MAAMqB,QAAQ,GAAG/B,kBAAkB,CAAC,UAAU,EAAElF,eAAe,EAAE+C,WAAW,CAAC;IAC7E,MAAMmE,WAAW,GAAI3D,CAAQ,IAAK;MACjCA,CAAC,CAAC4D,eAAe,CAAC,CAAC;MACnB3N,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACA,IAAIwN,QAAQ,KAAK,kBAAkB,EAAE;QACpC;QACAvS,cAAc,CAAC,IAAI,CAAC;;QAEpB;QACA,IAAI,OAAOvB,kBAAkB,KAAK,UAAU,EAAE;UAC7CA,kBAAkB,CAAC,CAAC;QACrB;;QAEA;QACA,MAAMiU,oBAAoB,GAAGlC,kBAAkB,CAAC,eAAe,EAAElF,eAAe,EAAE+C,WAAW,CAAC;QAC9F,IAAIqE,oBAAoB,EAAE;UACzBxR,OAAO,CAAC6Q,SAAS,CAAC3L,MAAM,CAAC,iBAAiB,CAAC;UAC3ClF,OAAO,CAAC6Q,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;UAChD9Q,OAAO,CAAC4Q,aAAa,GAAG,IAAI,CAAC,CAAC;QAC/B;MACD;IACD,CAAC;IAED,MAAMa,cAAc,GAAI9D,CAAQ,IAAK;MACpCA,CAAC,CAAC4D,eAAe,CAAC,CAAC;;MAEnB;MACA;MACA,MAAMR,aAAa,GAAGzB,kBAAkB,CAAC,eAAe,EAAElF,eAAe,EAAE+C,WAAW,CAAC;MACvF,IAAIkE,QAAQ,KAAK,kBAAkB,IAAI,CAACN,aAAa,EAAE;QACtD;MAAA;IAEF,CAAC;IAED,MAAMW,WAAW,GAAI/D,CAAQ,IAAK;MACjCA,CAAC,CAAC4D,eAAe,CAAC,CAAC;MACnB3N,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACA,IAAIwN,QAAQ,KAAK,kBAAkB,IAAI,CAACA,QAAQ,EAAE;QACjD;QACAvS,cAAc,CAAC,CAACC,WAAW,CAAC;;QAE5B;QACA,IAAI,OAAOvB,kBAAkB,KAAK,UAAU,EAAE;UAC7CA,kBAAkB,CAAC,CAAC;QACrB;;QAEA;QACJ,MAAMgU,oBAAoB,GAAGlC,kBAAkB,CAAC,eAAe,EAAElF,eAAe,EAAE+C,WAAW,CAAC;QAC1F,IAAIqE,oBAAoB,EAAE;UACzBxR,OAAO,CAAC6Q,SAAS,CAAC3L,MAAM,CAAC,iBAAiB,CAAC;UAC3ClF,OAAO,CAAC6Q,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;UAChD9Q,OAAO,CAAC4Q,aAAa,GAAG,IAAI,CAAC,CAAC;QAC/B;MACD;IACD,CAAC;;IAED;IACA,IAAI,CAAC5Q,OAAO,CAACgR,YAAY,CAAC,yBAAyB,CAAC,EAAE;MACrD,IAAIK,QAAQ,KAAK,kBAAkB,EAAE;QACpC;QACArR,OAAO,CAAC4K,gBAAgB,CAAC,WAAW,EAAE0G,WAAW,CAAC;QAClDtR,OAAO,CAAC4K,gBAAgB,CAAC,UAAU,EAAE6G,cAAc,CAAC;;QAEpD;QACAzR,OAAO,CAAC4K,gBAAgB,CAAC,OAAO,EAAE8G,WAAW,CAAC;MAC/C,CAAC,MAAM;QACN;QACA1R,OAAO,CAAC4K,gBAAgB,CAAC,OAAO,EAAE8G,WAAW,CAAC;MAC/C;;MAEA;MACA1R,OAAO,CAAC2R,YAAY,CAAC,yBAAyB,EAAE,MAAM,CAAC;IACxD;EACD,CAAC;EACD9W,SAAS,CAAC,MAAM;IACf,IAAIkG,OAAO;IACX,IAAI6Q,KAAK;IAET,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QAAA,IAAAC,uBAAA,EAAAC,uBAAA,EAAAC,MAAA,EAAAC,OAAA;QACH;QACAL,KAAK,GAAG,CAAAvU,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEuD,SAAS,KAAI,EAAE;;QAEvC;QACA,MAAMsR,WAAW,GAAGhT,oBAAoB,KAAK,SAAS,IAAI7B,cAAc,aAAdA,cAAc,gBAAAyU,uBAAA,GAAdzU,cAAc,CAAEuD,SAAS,cAAAkR,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BtV,WAAW,GAAG,CAAC,CAAC,cAAAuV,uBAAA,eAA5CA,uBAAA,CAA8ClR,WAAW,GAC/GxD,cAAc,CAACuD,SAAS,CAACpE,WAAW,GAAG,CAAC,CAAC,CAASqE,WAAW,GAC9D,EAAAmR,MAAA,GAAAJ,KAAK,cAAAI,MAAA,wBAAAC,OAAA,GAALD,MAAA,CAAQ,CAAC,CAAC,cAAAC,OAAA,uBAAVA,OAAA,CAAYpR,WAAW,KAAI,EAAE;QAEhCE,OAAO,GAAGd,iBAAiB,CAACiS,WAAW,IAAI,EAAE,CAAC;QAC9C3S,gBAAgB,CAACwB,OAAO,CAAC;QAEzB,IAAIA,OAAO,EAAE;UACZ;QAAA;;QAGD;QACA,MAAMoR,iBAAiB,GAAG3T,gBAAgB,KAAK,SAAS,IACvDU,oBAAoB,KAAK,SAAS,IAClCjD,KAAK,KAAK,SAAS,IAClBuC,gBAAgB,KAAK,MAAM,IAAIU,oBAAoB,KAAK,SAAU;QAEpE,IAAIiT,iBAAiB,EAAE;UAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;UAItB;UACA,IAAIrI,eAAe;UACnB,IAAI+C,WAAW;UAEf,IAAIjO,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAA2T,sBAAA,GAApB3T,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAA4V,sBAAA,eAAvCA,sBAAA,CAAyC/H,QAAQ,EAAE;YAAA,IAAAqI,uBAAA,EAAAC,uBAAA;YAC5F;YACAvI,eAAe,GAAG3L,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAAC6N,QAAQ;YAChE8C,WAAW,GAAG9P,cAAc,aAAdA,cAAc,wBAAAqV,uBAAA,GAAdrV,cAAc,CAAEuD,SAAS,cAAA8R,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BlW,WAAW,GAAG,CAAC,CAAC,cAAAmW,uBAAA,uBAA5CA,uBAAA,CAA8C/L,OAAO;UACpE,CAAC,MAAM,IAAInI,oBAAoB,aAApBA,oBAAoB,gBAAA4T,sBAAA,GAApB5T,oBAAoB,CAAG,CAAC,CAAC,cAAA4T,sBAAA,eAAzBA,sBAAA,CAA2BhI,QAAQ,EAAE;YAAA,IAAAuI,uBAAA,EAAAC,uBAAA;YAC/C;YACAzI,eAAe,GAAG3L,oBAAoB,CAAC,CAAC,CAAC,CAAC4L,QAAQ;YAClD8C,WAAW,GAAG9P,cAAc,aAAdA,cAAc,wBAAAuV,uBAAA,GAAdvV,cAAc,CAAEuD,SAAS,cAAAgS,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,uBAA9BA,uBAAA,CAAgCjM,OAAO;UACtD,CAAC,MAAM;YAAA,IAAAkM,uBAAA,EAAAC,uBAAA;YACN;YACA3I,eAAe,GAAG;cACjBG,SAAS,EAAE,GAAG;cACdC,SAAS,EAAE,GAAG;cACd0F,IAAI,EAAE,UAAU;cAChBL,KAAK,EAAE,QAAQ;cACfD,IAAI,EAAE,IAAI;cACVJ,cAAc,EAAE,IAAI;cACpBC,4BAA4B,EAAE,IAAI;cAClCC,QAAQ,EAAE,kBAAkB;cAC5B9C,aAAa,EAAE;YAChB,CAAC;YACDO,WAAW,GAAG,CAAA9P,cAAc,aAAdA,cAAc,wBAAAyV,uBAAA,GAAdzV,cAAc,CAAEuD,SAAS,cAAAkS,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BtW,WAAW,GAAG,CAAC,CAAC,cAAAuW,uBAAA,uBAA5CA,uBAAA,CAA8CnM,OAAO,KAAI,CAAC,CAAC;UAC1E;UACA,MAAMtE,OAAO,GAAGgI,UAAU,CAAC,EAAAgI,gBAAA,GAAAlI,eAAe,cAAAkI,gBAAA,uBAAfA,gBAAA,CAAiB/H,SAAS,KAAI,GAAG,CAAC;UAC7D,MAAMhI,OAAO,GAAG+H,UAAU,CAAC,EAAAiI,iBAAA,GAAAnI,eAAe,cAAAmI,iBAAA,uBAAfA,iBAAA,CAAiB/H,SAAS,KAAI,GAAG,CAAC;UAC7D,MAAMwI,kBAAkB,GAAG1I,UAAU,CAAC,EAAAkI,iBAAA,GAAApI,eAAe,cAAAoI,iBAAA,uBAAfA,iBAAA,CAAiB5C,IAAI,KAAI,IAAI,CAAC;;UAEpE;UACA/P,cAAc,CAACmT,kBAAkB,CAAC;UAElC,IAAI7R,IAAI,EAAED,GAAG;UACb,IAAIH,OAAO,EAAE;YACZ,MAAMC,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;YAC5CE,IAAI,GAAGH,IAAI,CAACyB,CAAC,GAAGH,OAAO;YACvBpB,GAAG,GAAGF,IAAI,CAAC2B,CAAC,IAAIJ,OAAO,GAAG,CAAC,GAAG,CAACA,OAAO,GAAGZ,IAAI,CAACsR,GAAG,CAAC1Q,OAAO,CAAC,CAAC;;YAE3D;YACA,MAAMkI,QAAQ,GAAGrI,sBAAsB,CAACpB,IAAI,EAAEgS,kBAAkB,EAAE1Q,OAAO,EAAEC,OAAO,CAAC;YACnF9C,gBAAgB,CAACgL,QAAQ,CAAC;UAC3B;;UAEA;UACA,MAAM1F,eAAe,GAAG3E,QAAQ,CAAC4E,cAAc,CAAC,cAAc,CAAC;UAC/D,IAAID,eAAe,EAAE;YACpB/E,OAAO,GAAG+E,eAAe;YACzB;UACD,CAAC,MAAM;YACN;YACA/E,OAAO,GAAGI,QAAQ,CAACgQ,aAAa,CAAC,KAAK,CAAC;YACvCpQ,OAAO,CAACkT,EAAE,GAAG,cAAc,CAAC,CAAC;YAC7BlT,OAAO,CAAC4Q,aAAa,GAAG,KAAK,CAAC,CAAC;YAC/BxQ,QAAQ,CAAC+S,IAAI,CAAC1C,WAAW,CAACzQ,OAAO,CAAC;UACnC;UAEAA,OAAO,CAAC0E,KAAK,CAAC0O,MAAM,GAAG,SAAS;UAChCpT,OAAO,CAAC0E,KAAK,CAACsL,aAAa,GAAG,MAAM,CAAC,CAAC;;UAEtC;UACAhQ,OAAO,CAAC0E,KAAK,CAACoL,MAAM,GAAG,MAAM;;UAE7B;UACA,KAAA2C,iBAAA,GAAIrI,eAAe,cAAAqI,iBAAA,eAAfA,iBAAA,CAAiB7F,aAAa,EAAE;YACnC9N,cAAc,CAAC,IAAI,CAAC;UACrB;;UAEA;UACA6Q,kBAAkB,CAAC3P,OAAO,EAAEoK,eAAe,EAAE+C,WAAW,EAAEhM,IAAI,EAAED,GAAG,CAAC;;UAEpE;UACA,MAAM6P,aAAa,GAAGzB,kBAAkB,CAAC,eAAe,EAAElF,eAAe,EAAE+C,WAAW,CAAC;UACvF,IAAI4D,aAAa,EAAE;YAClBjS,cAAc,CAAC,IAAI,CAAC;UACrB,CAAC,MAAM;YACN;UAAA;;UAGD;QACD;MACD,CAAC,CAAC,OAAOuU,KAAK,EAAE;QACfzP,OAAO,CAACyP,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACpD;IACD,CAAC;IAEDxB,iBAAiB,CAAC,CAAC;IAEnB,OAAO,MAAM;MACZ,MAAM9M,eAAe,GAAG3E,QAAQ,CAAC4E,cAAc,CAAC,cAAc,CAAC;MAC/D,IAAID,eAAe,EAAE;QACpBA,eAAe,CAACuO,OAAO,GAAG,IAAI;QAC9BvO,eAAe,CAACwO,WAAW,GAAG,IAAI;QAClCxO,eAAe,CAACyO,UAAU,GAAG,IAAI;MAClC;IACD,CAAC;EACF,CAAC,EAAE,CACFnW,cAAc,EACdoB,oBAAoB,EACpBhB,kBAAkB,EAClBC,kBAAkB,EAClBwB,oBAAoB,EACpB1C;EACA;EAAA,CACA,CAAC;EACF,MAAMiX,cAAc,GAAG,CAAApW,cAAc,aAAdA,cAAc,wBAAAe,uBAAA,GAAdf,cAAc,CAAEuD,SAAS,cAAAxC,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,wBAAAC,uBAAA,GAA9BD,uBAAA,CAAgCqV,OAAO,cAAApV,uBAAA,uBAAvCA,uBAAA,CAAyCqV,cAAc,KAAI,KAAK;EAEvF,SAASC,mBAAmBA,CAACzU,cAAmB,EAAE;IAAA,IAAA0U,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;IACjD,IAAI5U,cAAc,KAAK,CAAC,EAAE;MACzB,OAAO,MAAM;IACd,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,QAAQ;IAChB,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,aAAa;IACrB,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,aAAa;IACrB;IAEA,OAAO,CAAA9B,cAAc,aAAdA,cAAc,wBAAAwW,uBAAA,GAAdxW,cAAc,CAAEuD,SAAS,cAAAiT,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,wBAAAC,uBAAA,GAA9BD,uBAAA,CAAgCJ,OAAO,cAAAK,uBAAA,uBAAvCA,uBAAA,CAAyCC,gBAAgB,KAAI,MAAM;EAC3E;EACA,MAAMC,gBAAgB,GAAGL,mBAAmB,CAACzU,cAAc,CAAC;EAC5D,MAAM+U,cAAc,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACT,cAAc,EAAE,OAAO,IAAI;IAEhC,IAAIQ,gBAAgB,KAAK,MAAM,EAAE;MAChC,oBACCtY,OAAA,CAACP,aAAa;QACb+Y,OAAO,EAAC,MAAM;QACdvC,KAAK,EAAEnV,UAAW;QAClB+H,QAAQ,EAAC,QAAQ;QACjB4P,UAAU,EAAE5X,WAAW,GAAG,CAAE;QAC5B6X,EAAE,EAAE;UACH1P,eAAe,EAAE,aAAa;UAC9BH,QAAQ,EAAE,oBAAoB;UAC9B,+BAA+B,EAAE;YAChCG,eAAe,EAAEvF,aAAa,CAAE;UACjC;QACD,CAAE;QACFkV,UAAU,eAAE3Y,OAAA,CAACV,MAAM;UAACyJ,KAAK,EAAE;YAAE6P,UAAU,EAAE;UAAS;QAAE;UAAA1N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxDwN,UAAU,eAAE7Y,OAAA,CAACV,MAAM;UAACyJ,KAAK,EAAE;YAAE6P,UAAU,EAAE;UAAS;QAAE;UAAA1N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAEJ;IACA,IAAIiN,gBAAgB,KAAK,aAAa,EAAE;MACvC,oBACCtY,OAAA,CAACX,GAAG;QAACqZ,EAAE,EAAE;UAAEpP,OAAO,EAAE,MAAM;UAAEsL,UAAU,EAAE,QAAQ;UAAEkE,YAAY,EAAE,QAAQ;UAAEC,GAAG,EAAE,KAAK;UAAEC,OAAO,EAAE;QAAM,CAAE;QAAAC,QAAA,EAGrGC,KAAK,CAACC,IAAI,CAAC;UAAEpM,MAAM,EAAEjM;QAAW,CAAC,CAAC,CAACyJ,GAAG,CAAC,CAAC6O,CAAC,EAAEC,KAAK,kBAChDrZ,OAAA;UAEC+I,KAAK,EAAE;YACNhD,KAAK,EAAE,MAAM;YACbG,MAAM,EAAE,KAAK;YACb8C,eAAe,EAAEqQ,KAAK,KAAKxY,WAAW,GAAG,CAAC,GAAG4C,aAAa,GAAG,SAAS;YAAE;YACxE8L,YAAY,EAAE;UACf;QAAE,GANG8J,KAAK;UAAAnO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOV,CACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAER;IACA,IAAIiN,gBAAgB,KAAK,aAAa,EAAE;MACvC,oBACCtY,OAAA,CAACX,GAAG;QAACqZ,EAAE,EAAE;UAAEpP,OAAO,EAAE,MAAM;UAAEsL,UAAU,EAAE,QAAQ;UAAEkE,YAAY,EAAE;QAAa,CAAE;QAAAG,QAAA,eAC9EjZ,OAAA,CAACL,UAAU;UAAC+Y,EAAE,EAAE;YAAEM,OAAO,EAAE,KAAK;YAAE9M,KAAK,EAAEzI;UAAc,CAAE;UAAAwV,QAAA,GAAC,OACpD,EAACpY,WAAW,EAAC,MAAI,EAACC,UAAU;QAAA;UAAAoK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,IAAIiN,gBAAgB,KAAK,QAAQ,EAAE;MAClC,oBACCtY,OAAA,CAACX,GAAG;QAAA4Z,QAAA,eACHjZ,OAAA,CAACL,UAAU;UAAC6Y,OAAO,EAAC,OAAO;UAAAS,QAAA,eAC1BjZ,OAAA,CAACR,cAAc;YACdgZ,OAAO,EAAC,aAAa;YACrBc,KAAK,EAAEtY,QAAS;YAChB0X,EAAE,EAAE;cACHxS,MAAM,EAAE,KAAK;cACXqJ,YAAY,EAAE,MAAM;cACpBgK,MAAM,EAAE,UAAU;cACpB,0BAA0B,EAAE;gBAC3BvQ,eAAe,EAAEvF,aAAa,CAAE;cACjC;YACD;UAAE;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,OAAO,IAAI;EACZ,CAAC;EACD,oBACCrL,OAAA,CAAAE,SAAA;IAAA+Y,QAAA,GACEtV,aAAa,iBACb3D,OAAA;MAAAiZ,QAAA,EAcE7V,WAAW,iBACXpD,OAAA,CAACN,OAAO;QACP+Q,IAAI,EAAE+I,OAAO,CAAC3V,aAAa,CAAC,IAAI2V,OAAO,CAACpZ,QAAQ,CAAE;QAClDA,QAAQ,EAAEA,QAAS;QACnBK,OAAO,EAAEA,CAAA,KAAM;UACd;UACA;QAAA,CACC;QACF8K,YAAY,EAAEA,YAAa;QAC3BG,eAAe,EAAEA,eAAgB;QACjC+N,eAAe,EAAC,gBAAgB;QAChCC,cAAc,EACb7V,aAAa,GACV;UACA0B,GAAG,EAAE1B,aAAa,CAAC0B,GAAG,GAAE,EAAE,IAAGoJ,UAAU,CAACzL,YAAY,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAACyL,UAAU,CAACzL,YAAY,IAAI,GAAG,CAAC,GAAG8C,IAAI,CAACsR,GAAG,CAAC3I,UAAU,CAACzL,YAAY,IAAI,GAAG,CAAC,CAAC,CAAC;UAChJsC,IAAI,EAAE3B,aAAa,CAAC2B,IAAI,GAAE,EAAE,GAAEmJ,UAAU,CAAC1L,YAAY,IAAI,GAAG;QAC5D,CAAC,GACD2F,SACH;QACD8P,EAAE,EAAE;UACH;UACA;UACA;UACA,gBAAgB,EAAEtY,QAAQ,GAAG,MAAM,GAAG,MAAM;UAC5C,8CAA8C,EAAE;YAC/C+T,MAAM,EAAE,IAAI;YACZ;YACA,GAAG7E,WAAW;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,GAAGoE,iBAAiB,CAAC,CAAArS,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEsK,QAAQ,KAAI,eAAe,CAAC;YACnEpG,GAAG,EAAE,GAAG,CAAC,CAAA1B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE0B,GAAG,KAAI,CAAC,KAC5BrC,YAAY,IAAIA,YAAY,IAAI,WAAW,GAC3CyL,UAAU,CAACzL,YAAY,IAAI,GAAG,CAAC,GAAG,CAAC,GAClC,CAACyL,UAAU,CAACzL,YAAY,IAAI,GAAG,CAAC,GAChC8C,IAAI,CAACsR,GAAG,CAAC3I,UAAU,CAACzL,YAAY,IAAI,GAAG,CAAC,CAAC,GAC1C,CAAC,CAAC,eAAe;YACrBsC,IAAI,EAAE,GAAG,CAAC,CAAA3B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE2B,IAAI,KAAI,CAAC,KAAKvC,YAAY,IAAIA,YAAY,IAAI,WAAW,GAC9E0L,UAAU,CAAC1L,YAAY,CAAC,IAAI,CAAC,GAC9B,CAAC,CAAE,eAAe;YACrB0W,QAAQ,EAAE,QAAQ;YAClB;YACAvF,UAAU,EAAE;UACb;QACD,CAAE;QACFwF,iBAAiB,EAAE,IAAK;QAAAX,QAAA,gBAExBjZ,OAAA;UAAK+I,KAAK,EAAE;YAAE+P,YAAY,EAAE,KAAK;YAAExP,OAAO,EAAE;UAAO,CAAE;UAAA2P,QAAA,EACnD,CAAA7X,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyY,aAAa,kBAC9B7Z,OAAA,CAACT,UAAU;YACVua,OAAO,EAAEA,CAAA,KAAM;cACd;cACA;YAAA,CACC;YACFpB,EAAE,EAAE;cACH7P,QAAQ,EAAE,OAAO;cACjBkR,SAAS,EAAE,iCAAiC;cAC5CvU,IAAI,EAAE,MAAM;cACZ+C,KAAK,EAAE,MAAM;cACbgR,MAAM,EAAE,OAAO;cACfS,UAAU,EAAE,iBAAiB;cAC7BC,MAAM,EAAE,gBAAgB;cACxB9F,MAAM,EAAE,QAAQ;cAChB5E,YAAY,EAAE,MAAM;cACpByJ,OAAO,EAAE;YACV,CAAE;YAAAC,QAAA,eAEFjZ,OAAA,CAACJ,SAAS;cAAC8Y,EAAE,EAAE;gBAAEwB,IAAI,EAAE,CAAC;gBAAEhO,KAAK,EAAE;cAAO;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QACZ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACNrL,OAAA,CAACF,gBAAgB;UAEpBqa,GAAG,EAAExU,YAAa;UAClBoD,KAAK,EAAE;YAAEqR,SAAS,EAAE7T,cAAc,CAAC,CAAC,IAAIC,WAAW,CAAC,CAAC,GAAG,MAAM,GAAG;UAAQ,CAAE;UAC3E6T,OAAO,EAAE;YACRC,eAAe,EAAE,CAAC7U,cAAc;YAChC8U,eAAe,EAAE,IAAI;YACrBC,gBAAgB,EAAE,KAAK;YACvBC,WAAW,EAAE,IAAI;YACjBC,kBAAkB,EAAE,EAAE;YACtBC,kBAAkB,EAAE,IAAI;YACxBC,mBAAmB,EAAE;UACtB,CAAE;UAAA3B,QAAA,eAECjZ,OAAA;YAAK+I,KAAK,EAAE;cACXqR,SAAS,EAAE7T,cAAc,CAAC,CAAC,IAAIC,WAAW,CAAC,CAAC,GAAG,MAAM,GAAG,OAAO;cAC/DmT,QAAQ,EAAEpT,cAAc,CAAC,CAAC,IAAIC,WAAW,CAAC,CAAC,GAAG,SAAS,GAAG,aAAa;cACvET,KAAK,EAAEQ,cAAc,CAAC,CAAC,IAAIC,WAAW,CAAC,CAAC,GAAG,MAAM,GAAGoC,SAAS;cAC7D2Q,MAAM,EAAEhT,cAAc,CAAC,CAAC,GAAG,GAAG,GAAGqC;YAClC,CAAE;YAAAqQ,QAAA,eACDjZ,OAAA,CAACX,GAAG;cAAC0J,KAAK,EAAE;gBACXiQ,OAAO,EAAEzS,cAAc,CAAC,CAAC,GAAG,GAAG,GAC7BC,WAAW,CAAC,CAAC,GAAG,GAAG,GAAI,CAAAnF,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEwZ,OAAO,KAAI,MAAO;gBAC7D3U,MAAM,EAAEK,cAAc,CAAC,CAAC,GAAG,MAAM,GAAGwJ,aAAa;gBACjDhK,KAAK,EAAEQ,cAAc,CAAC,CAAC,IAAIC,WAAW,CAAC,CAAC,GAAG,MAAM,GAAGoC,SAAS;gBAC7D2Q,MAAM,EAAEhT,cAAc,CAAC,CAAC,GAAG,GAAG,GAAGqC;cAClC,CAAE;cAAAqQ,QAAA,gBACDjZ,OAAA,CAACX,GAAG;gBACH8a,GAAG,EAAEhW,UAAW;gBAChBmF,OAAO,EAAC,MAAM;gBACdwR,aAAa,EAAC,QAAQ;gBACtBC,QAAQ,EAAC,MAAM;gBACflG,cAAc,EAAC,QAAQ;gBACvB6D,EAAE,EAAE;kBACH3S,KAAK,EAAES,WAAW,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM;kBACtCwS,OAAO,EAAExS,WAAW,CAAC,CAAC,GAAG,GAAG,GAAGoC;gBAChC,CAAE;gBAAAqQ,QAAA,GAED/X,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqJ,GAAG,CAAEyQ,SAAc,IACpCA,SAAS,CAAC9N,WAAW,CAAC3C,GAAG,CAAC,CAAC0Q,SAAc,EAAEC,QAAgB,kBAC1Dlb,OAAA,CAACX,GAAG;kBAEH8b,SAAS,EAAC,KAAK;kBACfC,GAAG,EAAEH,SAAS,CAAC7N,GAAI;kBACnBiO,GAAG,EAAEJ,SAAS,CAACK,OAAO,IAAI,OAAQ;kBAClC5C,EAAE,EAAE;oBACH0B,SAAS,EAAEY,SAAS,CAACO,cAAc,IAAIN,SAAS,CAACM,cAAc,IAAI,OAAO;oBAC1EnP,SAAS,EAAE4O,SAAS,CAAC3O,SAAS,IAAI,QAAQ;oBAC1CmP,SAAS,EAAEP,SAAS,CAACQ,GAAG,IAAI,SAAS;oBACrC;oBACAvV,MAAM,EAAE,GAAG+U,SAAS,CAACjL,aAAa,IAAI,GAAG,IAAI;oBAC7CgK,UAAU,EAAEiB,SAAS,CAACnL,eAAe,IAAI,SAAS;oBAClDyJ,MAAM,EAAE;kBACT,CAAE;kBACFO,OAAO,EAAEA,CAAA,KAAM;oBACd,IAAIkB,SAAS,CAACU,SAAS,EAAE;sBACxB,MAAMtL,SAAS,GAAG4K,SAAS,CAACU,SAAS;sBACrCxU,MAAM,CAACuJ,IAAI,CAACL,SAAS,EAAE,QAAQ,EAAE,qBAAqB,CAAC;oBACxD;kBACD,CAAE;kBACFrH,KAAK,EAAE;oBAAE0O,MAAM,EAAEuD,SAAS,CAACU,SAAS,GAAG,SAAS,GAAG;kBAAU;gBAAE,GAnB1D,GAAGV,SAAS,CAACpQ,EAAE,IAAIsQ,QAAQ,EAAE;kBAAAhQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBlC,CACD,CACF,CAAC,EAEApK,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEsJ,GAAG,CACxB,CAACoR,SAAc,EAAEtC,KAAU;kBAAA,IAAAuC,qBAAA,EAAAC,sBAAA;kBAAA,OAC1BF,SAAS,CAACpO,IAAI,iBACbvN,OAAA,CAACL,UAAU;oBACVmc,SAAS,EAAC,eAAe;oBACG;oBAC5BpD,EAAE,EAAE;sBACHtM,SAAS,EAAE,EAAAwP,qBAAA,GAAAD,SAAS,CAAC7P,cAAc,cAAA8P,qBAAA,uBAAxBA,qBAAA,CAA0BG,UAAU,KAAInQ,SAAS,CAACQ,SAAS;sBACtEF,KAAK,EAAE,EAAA2P,sBAAA,GAAAF,SAAS,CAAC7P,cAAc,cAAA+P,sBAAA,uBAAxBA,sBAAA,CAA0B1P,SAAS,KAAIP,SAAS,CAACM,KAAK;sBAC7D8P,UAAU,EAAE,UAAU;sBACtBC,SAAS,EAAE,YAAY;sBACvBjD,OAAO,EAAE;oBACV,CAAE;oBACFkD,uBAAuB,EAAE5P,iBAAiB,CAACqP,SAAS,CAACpO,IAAI,CAAE,CAAC;kBAAA,GARvDoO,SAAS,CAAC/Q,EAAE,IAAIyO,KAAK;oBAAAnO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAS1B,CACD;gBAAA,CACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,EAEL8Q,MAAM,CAACC,IAAI,CAACjN,cAAc,CAAC,CAAC5E,GAAG,CAAE6E,WAAW;gBAAA,IAAAiN,qBAAA,EAAAC,sBAAA;gBAAA,oBAC5Ctc,OAAA,CAACX,GAAG;kBACH8a,GAAG,EAAE/V,kBAAmB;kBAExBsU,EAAE,EAAE;oBACHpP,OAAO,EAAE,MAAM;oBACfuL,cAAc,EAAErB,YAAY,EAAA6I,qBAAA,GAAClN,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAAiN,qBAAA,uBAA9BA,qBAAA,CAAgChQ,SAAS,CAAC;oBACvE0O,QAAQ,EAAE,MAAM;oBAChBxB,MAAM,EAAEhT,cAAc,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO;oBACtCyC,eAAe,GAAAsT,sBAAA,GAAEnN,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAAkN,sBAAA,uBAA9BA,sBAAA,CAAgCxM,eAAe;oBAChEkJ,OAAO,EAAEzS,cAAc,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO;oBAC3CR,KAAK,EAAEQ,cAAc,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM;oBACzCgJ,YAAY,EAAEhJ,cAAc,CAAC,CAAC,GAAG,MAAM,GAAGqC;kBAC3C,CAAE;kBAAAqQ,QAAA,EAED9J,cAAc,CAACC,WAAW,CAAC,CAAC7E,GAAG,CAAC,CAACG,MAAW,EAAE2O,KAAa;oBAAA,IAAAkD,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;oBAAA,oBAC3D7c,OAAA,CAACV,MAAM;sBAENwa,OAAO,EAAEA,CAAA,KAAM7J,kBAAkB,CAACvF,MAAM,CAACoS,YAAY,CAAE;sBACvDtE,OAAO,EAAC,WAAW;sBACnBE,EAAE,EAAE;wBACHqE,WAAW,EAAExW,cAAc,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM;wBAC9CgT,MAAM,EAAEhT,cAAc,CAAC,CAAC,GAAG,KAAK,GAAG,eAAe;wBAClDyC,eAAe,EAAE,EAAAuT,qBAAA,GAAA7R,MAAM,CAACsS,gBAAgB,cAAAT,qBAAA,uBAAvBA,qBAAA,CAAyBU,qBAAqB,KAAI,SAAS;wBAC5E/Q,KAAK,EAAE,EAAAsQ,sBAAA,GAAA9R,MAAM,CAACsS,gBAAgB,cAAAR,sBAAA,uBAAvBA,sBAAA,CAAyBU,eAAe,KAAI,MAAM;wBACzDjD,MAAM,EAAE,EAAAwC,sBAAA,GAAA/R,MAAM,CAACsS,gBAAgB,cAAAP,sBAAA,uBAAvBA,sBAAA,CAAyBU,iBAAiB,KAAI,aAAa;wBACnExI,QAAQ,EAAE,EAAA+H,sBAAA,GAAAhS,MAAM,CAACsS,gBAAgB,cAAAN,sBAAA,uBAAvBA,sBAAA,CAAyBU,QAAQ,KAAI,MAAM;wBACrDrX,KAAK,EAAE,EAAA4W,sBAAA,GAAAjS,MAAM,CAACsS,gBAAgB,cAAAL,sBAAA,uBAAvBA,sBAAA,CAAyBtW,KAAK,KAAI,MAAM;wBAC/C2S,OAAO,EAAEzS,cAAc,CAAC,CAAC,GAAG,kCAAkC,GAAG,SAAS;wBAC1E8W,UAAU,EAAE9W,cAAc,CAAC,CAAC,GAAG,0BAA0B,GAAG,QAAQ;wBACpE+W,aAAa,EAAE,MAAM;wBACrB/N,YAAY,EAAE,EAAAqN,sBAAA,GAAAlS,MAAM,CAACsS,gBAAgB,cAAAJ,sBAAA,uBAAvBA,sBAAA,CAAyBW,YAAY,KAAI,KAAK;wBAC5DrP,QAAQ,EAAE3H,cAAc,CAAC,CAAC,GAAG,aAAa,GAAGqC,SAAS;wBACtDmR,SAAS,EAAE,iBAAiB;wBAAE;wBAC9B,SAAS,EAAE;0BACV/Q,eAAe,EAAE,EAAA6T,sBAAA,GAAAnS,MAAM,CAACsS,gBAAgB,cAAAH,sBAAA,uBAAvBA,sBAAA,CAAyBI,qBAAqB,KAAI,SAAS;0BAAE;0BAC9EO,OAAO,EAAE,GAAG;0BAAE;0BACdzD,SAAS,EAAE,iBAAiB,CAAE;wBAC/B;sBACD,CAAE;sBAAAd,QAAA,EAEDvO,MAAM,CAAC+S;oBAAU,GAxBbpE,KAAK;sBAAAnO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAyBH,CAAC;kBAAA,CACT;gBAAC,GAxCG+D,WAAW;kBAAAlE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyCZ,CAAC;cAAA,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGD;QAAC,GApIL,aAAa5F,cAAc,EAAE;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqIZ,CAAC,EAElByM,cAAc,IAAIhX,UAAU,GAAC,CAAC,IAAI+B,gBAAgB,KAAK,MAAM,iBAAI7C,OAAA,CAACX,GAAG;UAAA4Z,QAAA,EAAEV,cAAc,CAAC;QAAC;UAAArN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAAE,GAAG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACL,eAEDrL,OAAA;MAAAiZ,QAAA,EACE;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAA/N,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA,eACP,CAAC;AAEL,CAAC;AAACrJ,EAAA,CA92CI7B,cAAoC;EAAA,QA8CrCN,cAAc;AAAA;AAAA6d,EAAA,GA9Cbvd,cAAoC;AAg3C1C,eAAeA,cAAc;AAAC,IAAAud,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}