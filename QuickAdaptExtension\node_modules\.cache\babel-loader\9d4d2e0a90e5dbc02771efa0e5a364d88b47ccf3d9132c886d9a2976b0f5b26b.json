{"ast": null, "code": "import{adminApiService}from\"./APIService\";export const saveGuide=async(guideData,onSuccess,onError,setLoading)=>{try{setLoading(true);const response=await adminApiService.post(\"/Guide/Saveguide\",guideData);if(response&&response.status===200&&response.data.Success){onSuccess(response);}else{var _response$data;console.error(\"Unexpected response:\",response);const err=\"Unexpected response status\";const errorMessage=(response===null||response===void 0?void 0:(_response$data=response.data)===null||_response$data===void 0?void 0:_response$data.ErrorMessage)||err;onError(errorMessage);}}catch(error){console.error(\"Error saving guide:\",error);onError(error);}finally{setLoading(false);}};export const updateGuide=async(guideData,onSuccess,onError,setLoading)=>{try{setLoading(true);const response=await adminApiService.post(\"/Guide/Updateguide\",guideData);if(response&&response.status===200&&response.data.Success){onSuccess(response);}else{var _response$data2;console.error(\"Unexpected response:\",response);const err=\"Unexpected response status\";const errorMessage=(response===null||response===void 0?void 0:(_response$data2=response.data)===null||_response$data2===void 0?void 0:_response$data2.ErrorMessage)||err;onError(errorMessage);}}catch(error){console.error(\"Error saving guide:\",error);onError(error);}finally{setLoading(false);}};", "map": {"version": 3, "names": ["adminApiService", "saveGuide", "guideData", "onSuccess", "onError", "setLoading", "response", "post", "status", "data", "Success", "_response$data", "console", "error", "err", "errorMessage", "ErrorMessage", "updateGuide", "_response$data2"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/services/SaveGuideService.tsx"], "sourcesContent": ["import { AxiosResponse } from \"axios\";\r\nimport { adminApiService } from \"./APIService\";\r\n\r\nexport interface SaveGuideRequest {\r\n\tGuideId: string;\r\n\tGuideType: string;\r\n\tName: string;\r\n\tContent: string;\r\n\tOrganizationId: string;\r\n\tCreatedDate: string;\r\n\tUpdatedDate: string;\r\n\tCreatedBy: string;\r\n\tUpdatedBy: string;\r\n\tTargetUrl: string;\r\n\tFrequency: string;\r\n\tSegment: string;\r\n\tAccountId: string;\r\n\tGuideStatus: string;\r\n\tGuideStep: Array<any>;\r\n}\r\n\r\nexport const saveGuide = async (\r\n\tguideData: SaveGuideRequest,\r\n\tonSuccess: (response?: AxiosResponse<any, any>) => void,\r\n\tonError: (error: any) => void,\r\n\tsetLoading: (loading: boolean) => void\r\n) => {\r\n\ttry {\r\n\t\tsetLoading(true);\r\n\t\tconst response = await adminApiService.post(\"/Guide/Saveguide\", guideData);\r\n\r\n\t\tif (response && response.status === 200 && response.data.Success) {\r\n\t\t\tonSuccess(response);\r\n\r\n\t\t} else {\r\n\t\t\tconsole.error(\"Unexpected response:\", response);\r\n\t\t\tconst err = \"Unexpected response status\";\r\n\t\t\tconst errorMessage = response?.data?.ErrorMessage || err;\r\n\t\t\tonError(errorMessage);\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error(\"Error saving guide:\", error);\r\n\t\tonError(error);\r\n\t} finally {\r\n\t\tsetLoading(false);\r\n\t}\r\n};\r\n\r\nexport const updateGuide = async (\r\n\tguideData: SaveGuideRequest,\r\n\tonSuccess: (response?: AxiosResponse<any, any>) => void,\r\n\tonError: (error: any) => void,\r\n\tsetLoading: (loading: boolean) => void\r\n) => {\r\n\ttry {\r\n\t\tsetLoading(true);\r\n\t\tconst response = await adminApiService.post(\"/Guide/Updateguide\", guideData);\r\n\r\n\t\tif (response && response.status === 200 && response.data.Success) {\r\n\t\t\tonSuccess(response);\r\n\t\t} else {\r\n\t\t\tconsole.error(\"Unexpected response:\", response);\r\n\t\t\tconst err = \"Unexpected response status\";\r\n\t\t\tconst errorMessage = response?.data?.ErrorMessage || err;\r\n\t\t\tonError(errorMessage);\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error(\"Error saving guide:\", error);\r\n\t\tonError(error);\r\n\t} finally {\r\n\t\tsetLoading(false);\r\n\t}\r\n};\r\n"], "mappings": "AACA,OAASA,eAAe,KAAQ,cAAc,CAoB9C,MAAO,MAAM,CAAAC,SAAS,CAAG,KAAAA,CACxBC,SAA2B,CAC3BC,SAAuD,CACvDC,OAA6B,CAC7BC,UAAsC,GAClC,CACJ,GAAI,CACHA,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAN,eAAe,CAACO,IAAI,CAAC,kBAAkB,CAAEL,SAAS,CAAC,CAE1E,GAAII,QAAQ,EAAIA,QAAQ,CAACE,MAAM,GAAK,GAAG,EAAIF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAE,CACjEP,SAAS,CAACG,QAAQ,CAAC,CAEpB,CAAC,IAAM,KAAAK,cAAA,CACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,CAAEP,QAAQ,CAAC,CAC/C,KAAM,CAAAQ,GAAG,CAAG,4BAA4B,CACxC,KAAM,CAAAC,YAAY,CAAG,CAAAT,QAAQ,SAARA,QAAQ,kBAAAK,cAAA,CAARL,QAAQ,CAAEG,IAAI,UAAAE,cAAA,iBAAdA,cAAA,CAAgBK,YAAY,GAAIF,GAAG,CACxDV,OAAO,CAACW,YAAY,CAAC,CACtB,CACD,CAAE,MAAOF,KAAK,CAAE,CACfD,OAAO,CAACC,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3CT,OAAO,CAACS,KAAK,CAAC,CACf,CAAC,OAAS,CACTR,UAAU,CAAC,KAAK,CAAC,CAClB,CACD,CAAC,CAED,MAAO,MAAM,CAAAY,WAAW,CAAG,KAAAA,CAC1Bf,SAA2B,CAC3BC,SAAuD,CACvDC,OAA6B,CAC7BC,UAAsC,GAClC,CACJ,GAAI,CACHA,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAN,eAAe,CAACO,IAAI,CAAC,oBAAoB,CAAEL,SAAS,CAAC,CAE5E,GAAII,QAAQ,EAAIA,QAAQ,CAACE,MAAM,GAAK,GAAG,EAAIF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAE,CACjEP,SAAS,CAACG,QAAQ,CAAC,CACpB,CAAC,IAAM,KAAAY,eAAA,CACNN,OAAO,CAACC,KAAK,CAAC,sBAAsB,CAAEP,QAAQ,CAAC,CAC/C,KAAM,CAAAQ,GAAG,CAAG,4BAA4B,CACxC,KAAM,CAAAC,YAAY,CAAG,CAAAT,QAAQ,SAARA,QAAQ,kBAAAY,eAAA,CAARZ,QAAQ,CAAEG,IAAI,UAAAS,eAAA,iBAAdA,eAAA,CAAgBF,YAAY,GAAIF,GAAG,CACxDV,OAAO,CAACW,YAAY,CAAC,CACtB,CACD,CAAE,MAAOF,KAAK,CAAE,CACfD,OAAO,CAACC,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3CT,OAAO,CAACS,KAAK,CAAC,CACf,CAAC,OAAS,CACTR,UAAU,CAAC,KAAK,CAAC,CAClB,CACD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}