{"ast": null, "code": "import{adminApiService,userApiService}from'./APIService';export const NewAgentTraining=async agent=>{try{const response=await userApiService.post(`Assistant/NewAgentTraining`,agent);return response.data;}catch(error){console.error(\"Error uploading agent\",error);return{message:\"Upload failed\"};}};export const CreateInteraction=async(userCommand,accountId,targetUrl)=>{try{const requestBody={userCommand,accountId,targetUrl};const response=await adminApiService.post(`Ai/CreateInteraction`,requestBody);return response.data;}catch(error){console.error(\"Error in creating integration\",error);return[];}};", "map": {"version": 3, "names": ["adminApiService", "userApiService", "NewAgentTraining", "agent", "response", "post", "data", "error", "console", "message", "CreateInteraction", "userCommand", "accountId", "targetUrl", "requestBody"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/services/AIService.ts"], "sourcesContent": ["import { adminApiService, userApiService } from './APIService';\r\nimport { ScrapedElement } from './ScrapingService';\r\nexport interface Agent {\r\n  Name: string | undefined;\r\n  Description: string | undefined;\r\n  AccountId:string;\r\n  url: string;\r\n  TrainingFields: ScrapedElement[];\r\n  AdditionalContext?: string;\r\n}\r\n\r\n\r\nexport const NewAgentTraining = async (agent: Agent) => {\r\n    try {\r\n        const response = await userApiService.post(`Assistant/NewAgentTraining`, agent);\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error uploading agent\", error);\r\n        return { message: \"Upload failed\" };\r\n    }\r\n};\r\n\r\n\r\nexport const CreateInteraction = async (userCommand: string, accountId: string,targetUrl:string) => {\r\n    \r\n    try {\r\n        const requestBody = {\r\n\t\t\tuserCommand,\r\n\t\t\taccountId,\r\n\t\t\ttargetUrl,\r\n\t\t};\r\n        const response = await adminApiService.post(`Ai/CreateInteraction`, requestBody)\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error in creating integration\", error);\r\n        return [];\r\n    }\r\n}"], "mappings": "AAAA,OAASA,eAAe,CAAEC,cAAc,KAAQ,cAAc,CAY9D,MAAO,MAAM,CAAAC,gBAAgB,CAAG,KAAO,CAAAC,KAAY,EAAK,CACpD,GAAI,CACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAH,cAAc,CAACI,IAAI,CAAC,4BAA4B,CAAEF,KAAK,CAAC,CAC/E,MAAO,CAAAC,QAAQ,CAACE,IAAI,CACxB,CAAE,MAAOC,KAAK,CAAE,CACZC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,MAAO,CAAEE,OAAO,CAAE,eAAgB,CAAC,CACvC,CACJ,CAAC,CAGD,MAAO,MAAM,CAAAC,iBAAiB,CAAG,KAAAA,CAAOC,WAAmB,CAAEC,SAAiB,CAACC,SAAgB,GAAK,CAEhG,GAAI,CACA,KAAM,CAAAC,WAAW,CAAG,CACzBH,WAAW,CACXC,SAAS,CACTC,SACD,CAAC,CACK,KAAM,CAAAT,QAAQ,CAAG,KAAM,CAAAJ,eAAe,CAACK,IAAI,CAAC,sBAAsB,CAAES,WAAW,CAAC,CAChF,MAAO,CAAAV,QAAQ,CAACE,IAAI,CACxB,CAAE,MAAOC,KAAK,CAAE,CACZC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,MAAO,EAAE,CACb,CACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}