{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\Tooltips\\\\designFields\\\\TooltipCanvasSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography, TextField, Grid, IconButton, Button } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport { AlignHorizontalLeft as TopLeftIcon, AlignHorizontalCenter as TopCenterIcon, AlignHorizontalRight as TopRightIcon, AlignVerticalTop as MiddleLeftIcon, AlignVerticalCenter as MiddleCenterIcon, AlignVerticalBottom as MiddleRightIcon, AlignHorizontalLeft as BottomLeftIcon, AlignHorizontalCenter as BottomCenterIcon, AlignHorizontalRight as BottomRightIcon } from \"@mui/icons-material\";\nimport \"./Canvas.module.css\";\nimport useDrawerStore, { CANVAS_DEFAULT_VALUE } from \"../../../store/drawerStore\";\nimport { tooltipLeft, tooltipRight, tooltipTop, tooltipBottom, tooltipCenter, warning } from \"../../../assets/icons/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TooltipCanvasSettings = ({\n  zindeex,\n  setZindeex,\n  setShowTooltipCanvasSettings\n}) => {\n  _s();\n  var _toolTipGuideMetaData2;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    setTooltipXaxis,\n    setTooltipYaxis,\n    updateCanvasInTooltip,\n    tooltipXaxis,\n    tooltipYaxis,\n    tooltipWidth,\n    setTooltipWidth,\n    setTooltipPadding,\n    setTooltipBorderradius,\n    setTooltipBordersize,\n    setTooltipBordercolor,\n    setTooltipBackgroundcolor,\n    tooltippadding,\n    tooltipborderradius,\n    tooltipbordersize,\n    tooltipBordercolor,\n    tooltipBackgroundcolor,\n    tooltipPosition,\n    setTooltipPosition,\n    setElementSelected,\n    setIsTooltipPopup,\n    toolTipGuideMetaData,\n    currentStep,\n    autoPosition,\n    setAutoPosition,\n    selectedTemplate,\n    updateDesignelementInTooltip,\n    selectedTemplateTour,\n    CANVAS_DEFAULT_VALUE_HOTSPOT,\n    setIsUnSavedChanges\n  } = useDrawerStore(state => state);\n  const [isOpen, setIsOpen] = useState(true);\n  const [dismiss, setDismiss] = useState(false);\n  const [selectedPosition, setSelectedPosition] = useState(\"middle-center\");\n  const [error, setError] = useState(false);\n  const [paddingError, setPaddingError] = useState(false);\n  const [cornerRadiusError, setCornerRadiusError] = useState(false);\n  const [borderSizeError, setBorderSizeError] = useState(false);\n  const [widthError, setWidthError] = useState(false);\n  const [tempBorderColor, setTempBorderColor] = useState(tooltipBordercolor);\n  useEffect(() => {\n    // Sync tempBorderColor with store, using default if empty or transparent\n    const validColor = tooltipBordercolor && tooltipBordercolor !== \"transparent\" && tooltipBordercolor !== \"\" ? tooltipBordercolor : \"#000000\";\n    setTempBorderColor(validColor);\n  }, [tooltipBordercolor]);\n  const positions = [{\n    label: translate(\"Top Left\", {\n      defaultValue: \"Top Left\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(TopLeftIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 71\n    }, this),\n    value: \"top-left\"\n  }, {\n    label: translate(\"Top Center\", {\n      defaultValue: \"Top Center\"\n    }),\n    icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: tooltipTop\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 146\n    }, this) : /*#__PURE__*/_jsxDEV(TopCenterIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 236\n    }, this),\n    value: \"top\"\n  }, {\n    label: translate(\"Top Right\", {\n      defaultValue: \"Top Right\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(TopRightIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 73\n    }, this),\n    value: \"top-right\"\n  }, {\n    label: translate(\"Middle Left\", {\n      defaultValue: \"Middle Left\"\n    }),\n    icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: tooltipLeft\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 148\n    }, this) : /*#__PURE__*/_jsxDEV(MiddleLeftIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 239\n    }, this),\n    value: \"left\"\n  }, {\n    label: translate(\"Middle Center\", {\n      defaultValue: \"Middle Center\"\n    }),\n    icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: tooltipCenter\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 152\n    }, this) : /*#__PURE__*/_jsxDEV(MiddleCenterIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 245\n    }, this),\n    value: \"center\"\n  }, {\n    label: translate(\"Middle Right\", {\n      defaultValue: \"Middle Right\"\n    }),\n    icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: tooltipRight\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 150\n    }, this) : /*#__PURE__*/_jsxDEV(MiddleRightIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 242\n    }, this),\n    value: \"right\"\n  }, {\n    label: translate(\"Bottom Left\", {\n      defaultValue: \"Bottom Left\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(BottomLeftIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 77\n    }, this),\n    value: \"bottom-left\"\n  }, {\n    label: translate(\"Bottom Center\", {\n      defaultValue: \"Bottom Center\"\n    }),\n    icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: tooltipBottom\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 152\n    }, this) : /*#__PURE__*/_jsxDEV(BottomCenterIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 245\n    }, this),\n    value: \"bottom\"\n  }, {\n    label: translate(\"Bottom Right\", {\n      defaultValue: \"Bottom Right\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(BottomRightIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 79\n    }, this),\n    value: \"bottom-right\"\n  }];\n  const handlePositionClick = e => {\n    var _e$target;\n    if (e !== null && e !== void 0 && (_e$target = e.target) !== null && _e$target !== void 0 && _e$target.id) {\n      //setSelectedPosition(e.target.id);\n      setTooltipPosition(e.target.id);\n    }\n  };\n  const onReselectElement = () => {\n    TooltipCanvasSettings({\n      ReSelection: false,\n      XPosition: 4,\n      YPosition: 4,\n      width: \"300\",\n      Padding: \"2\",\n      borderradius: \"8\",\n      bordersize: \"0\",\n      borderColor: \"\",\n      backgroundColor: \"\",\n      // PulseAnimation: true,\n      // stopAnimationUponInteraction: true,\n      // ShowUpon: \"Hovering Hotspot\",\n      ShowByDefault: false\n    });\n    updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\n    setElementSelected(true);\n    setIsTooltipPopup(false);\n    setShowTooltipCanvasSettings(false);\n  };\n  const handleBorderColorChange = e => {\n    var _e$target2;\n    if (e !== null && e !== void 0 && (_e$target2 = e.target) !== null && _e$target2 !== void 0 && _e$target2.value) {\n      setTempBorderColor(e.target.value);\n    }\n  };\n  const handleBackgroundColorChange = e => {\n    var _e$target3;\n    if (e !== null && e !== void 0 && (_e$target3 = e.target) !== null && _e$target3 !== void 0 && _e$target3.value) {\n      setTooltipBackgroundcolor(e.target.value);\n    }\n  };\n  const handleAutoSelect = e => {\n    //\tsetDismiss(e.target.checked);\n    setAutoPosition(e.target.checked);\n  };\n  const handleClose = () => {\n    setIsOpen(false);\n    setShowTooltipCanvasSettings(false);\n  };\n  const handleApplyChanges = () => {\n    // Create the new canvas settings\n    const updatedCanvasSettings = {\n      position: tooltipPosition,\n      backgroundColor: tooltipBackgroundcolor,\n      width: tooltipWidth,\n      borderRadius: tooltipborderradius,\n      padding: tooltippadding,\n      borderColor: tempBorderColor,\n      borderSize: tooltipbordersize,\n      autoposition: autoPosition,\n      xaxis: tooltipXaxis,\n      yaxis: tooltipYaxis\n    };\n\n    // Apply the changes - updateCanvasInTooltip will handle recording the change for undo/redo\n    updateCanvasInTooltip(updatedCanvasSettings); // Updates canvas and tooltipBordercolor\n    handleClose();\n    setIsUnSavedChanges(true);\n  };\n  useEffect(() => {\n    var _toolTipGuideMetaData;\n    if ((_toolTipGuideMetaData = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData !== void 0 && _toolTipGuideMetaData.canvas) {\n      const canvasData = toolTipGuideMetaData[currentStep - 1].canvas;\n\n      // Handle border color - use default if empty, transparent, or invalid\n      const borderColor = canvasData === null || canvasData === void 0 ? void 0 : canvasData.borderColor;\n      const validBorderColor = borderColor && borderColor !== \"transparent\" && borderColor !== \"\" ? borderColor : \"#000000\";\n      setTooltipPosition((canvasData === null || canvasData === void 0 ? void 0 : canvasData.position) || \"middle-center\");\n      setTooltipPadding((canvasData === null || canvasData === void 0 ? void 0 : canvasData.padding) || \"10px\");\n      setTooltipBorderradius((canvasData === null || canvasData === void 0 ? void 0 : canvasData.borderRadius) || \"8px\");\n      setTooltipBordersize((canvasData === null || canvasData === void 0 ? void 0 : canvasData.borderSize) || \"0px\");\n      setTooltipBordercolor(borderColor || \"\");\n      setTempBorderColor(validBorderColor); // Use valid color for the input\n      setTooltipBackgroundcolor((canvasData === null || canvasData === void 0 ? void 0 : canvasData.backgroundColor) || \"#FFFFFF\");\n      setTooltipWidth((canvasData === null || canvasData === void 0 ? void 0 : canvasData.width) || \"300px\");\n      if (selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") {\n        setTooltipXaxis((canvasData === null || canvasData === void 0 ? void 0 : canvasData.xaxis) || \"2px\");\n        setTooltipYaxis((canvasData === null || canvasData === void 0 ? void 0 : canvasData.yaxis) || \"2px\");\n      } else {\n        setTooltipXaxis((canvasData === null || canvasData === void 0 ? void 0 : canvasData.xaxis) || \"100px\");\n        setTooltipYaxis((canvasData === null || canvasData === void 0 ? void 0 : canvasData.yaxis) || \"100px\");\n      }\n    }\n  }, [(_toolTipGuideMetaData2 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.canvas]);\n  if (!isOpen) return null;\n  const formatValueWithPixelOrPercentage = value => {\n    const v = String(value);\n    let newValue = v;\n    if (v !== null && v !== void 0 && v.endsWith(\"px\") || v !== null && v !== void 0 && v.endsWith(\"%\")) {\n      newValue = v.split(/px|%/)[0];\n    }\n    return newValue;\n  };\n  const handleChange = e => {\n    // Only allow numeric input\n    const value = e.target.value;\n    if (value === '') {\n      setTooltipWidth('0px');\n      setWidthError(true); // Empty value is invalid\n      return;\n    }\n    if (!/^-?\\d*$/.test(value)) {\n      return;\n    }\n    let inputValue = parseInt(value) || 0;\n\n    // Validate width - minimum 300 pixels\n    if (inputValue < 300) {\n      setWidthError(true);\n    } else {\n      setWidthError(false);\n    }\n    setTooltipWidth(`${inputValue}px`);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"qadpt-designpopup\",\n    className: \"qadpt-designpopup\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-design-header\",\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": \"back\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? translate(\"Canvas\") : translate(\"Canvas\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": \"close\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-canblock\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-controls\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Auto Position\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"toggle-switch\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: autoPosition,\n                  onChange: handleAutoSelect,\n                  name: \"autoPosition\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 5\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"slider\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 5\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-position-grid\",\n            sx: {\n              opacity: selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\" ? 0.5 : 1,\n              cursor: selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\" ? \"not-allowed\" : \"\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-ctrl-title\",\n              children: translate(\"Position\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  disabled: autoPosition,\n                  sx: {\n                    opacity: selectedPosition === positions[0].value ? 1 : 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  id: positions[1].value,\n                  onClick: () => {\n                    //setSelectedPosition(positions[1].value);\n                    setTooltipPosition(positions[1].value);\n                  },\n                  disabled: autoPosition || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\",\n                  disableRipple: true,\n                  sx: {\n                    opacity: tooltipPosition === positions[1].value ? 1 : 0.5\n                  },\n                  children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: tooltipTop\n                    },\n                    id: positions[1].value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 9\n                  }, this) : /*#__PURE__*/_jsxDEV(TopCenterIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  disabled: autoPosition,\n                  sx: {\n                    opacity: selectedPosition === positions[2].value ? 1 : 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  id: positions[3].value,\n                  onClick: () => {\n                    // setSelectedPosition(positions[3].value);\n                    setTooltipPosition(positions[3].value);\n                  },\n                  disabled: autoPosition,\n                  disableRipple: true,\n                  sx: {\n                    opacity: tooltipPosition === positions[3].value ? 1 : 0.5\n                  },\n                  children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: tooltipLeft\n                    },\n                    id: positions[3].value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 9\n                  }, this) : /*#__PURE__*/_jsxDEV(MiddleLeftIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  id: positions[4].value,\n                  onClick: () => {\n                    // setSelectedPosition(positions[4].value);\n                    setTooltipPosition(positions[4].value);\n                  },\n                  disabled: autoPosition,\n                  disableRipple: true,\n                  sx: {\n                    opacity: tooltipPosition === positions[4].value ? 1 : 0.5,\n                    paddingLeft: \"0 !important\"\n                  },\n                  children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: tooltipCenter\n                    },\n                    id: positions[4].value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 9\n                  }, this) : /*#__PURE__*/_jsxDEV(MiddleCenterIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  id: positions[5].value,\n                  onClick: () => {\n                    // setSelectedPosition(positions[5].value);\n                    setTooltipPosition(positions[5].value);\n                  },\n                  disabled: autoPosition,\n                  disableRipple: true,\n                  sx: {\n                    opacity: tooltipPosition === positions[5].value ? 1 : 0.5\n                  },\n                  children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: tooltipRight\n                    },\n                    id: positions[5].value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 9\n                  }, this) : /*#__PURE__*/_jsxDEV(MiddleRightIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  disabled: autoPosition,\n                  sx: {\n                    opacity: selectedPosition === positions[6].value ? 1 : 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  id: positions[7].value,\n                  onClick: () => {\n                    // setSelectedPosition(positions[7].value);\n                    setTooltipPosition(positions[7].value);\n                  },\n                  disabled: autoPosition,\n                  disableRipple: true,\n                  sx: {\n                    opacity: tooltipPosition === positions[7].value ? 1 : 0.5\n                  },\n                  children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: tooltipBottom\n                    },\n                    id: positions[7].value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 9\n                  }, this) : /*#__PURE__*/_jsxDEV(BottomCenterIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  disabled: autoPosition,\n                  sx: {\n                    opacity: selectedPosition === positions[8].value ? 1 : 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 3\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: [\"X \", translate(\"Axis Offset\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                value: formatValueWithPixelOrPercentage(tooltipXaxis),\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                onChange: e => setTooltipXaxis(`${parseInt(e.target.value) || 0}px`),\n                InputProps: {\n                  endAdornment: \"px\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    }\n                  }\n                },\n                disabled: autoPosition\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: [\"Y \", translate(\"Axis Offset\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                value: formatValueWithPixelOrPercentage(tooltipYaxis),\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                onChange: e => setTooltipYaxis(`${parseInt(e.target.value) || 0}px`),\n                InputProps: {\n                  endAdornment: \"px\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    }\n                  }\n                },\n                disabled: autoPosition\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            style: {\n              flexDirection: \"column\",\n              height: \"auto\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              style: {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: \"8px\",\n                width: \"100%\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate(\"Width\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: formatValueWithPixelOrPercentage(tooltipWidth),\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: handleChange,\n                  error: widthError,\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 9\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 7\n          }, this), widthError && /*#__PURE__*/_jsxDEV(Typography, {\n            style: {\n              fontSize: \"12px\",\n              color: \"#e9a971\",\n              textAlign: \"left\",\n              top: \"100%\",\n              left: 0,\n              marginBottom: \"5px\",\n              display: \"flex\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: \"flex\",\n                fontSize: \"12px\",\n                alignItems: \"center\",\n                marginRight: \"4px\"\n              },\n              dangerouslySetInnerHTML: {\n                __html: warning\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 9\n            }, this), translate(\"Width must be at least 300 pixels\")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Padding\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                value: formatValueWithPixelOrPercentage(tooltippadding),\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                onChange: e => {\n                  // Only allow numeric input\n                  const value = e.target.value;\n                  if (!/^-?\\d*$/.test(value)) {\n                    return;\n                  }\n                  const inputValue = parseInt(value) || 0;\n                  // Validate padding between 0px and 20px\n                  if (inputValue < 0 || inputValue > 20) {\n                    setPaddingError(true);\n                  } else {\n                    setPaddingError(false);\n                  }\n                  setTooltipPadding(`${inputValue}px`);\n                },\n                InputProps: {\n                  endAdornment: \"px\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    }\n                  }\n                },\n                error: paddingError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 7\n          }, this), paddingError && /*#__PURE__*/_jsxDEV(Typography, {\n            style: {\n              fontSize: \"12px\",\n              color: \"#e9a971\",\n              textAlign: \"left\",\n              top: \"100%\",\n              left: 0,\n              marginBottom: \"5px\",\n              display: \"flex\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: \"flex\",\n                fontSize: \"12px\",\n                alignItems: \"center\",\n                marginRight: \"4px\"\n              },\n              dangerouslySetInnerHTML: {\n                __html: warning\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 9\n            }, this), translate(\"Value must be between 0px and 20px.\")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Corner Radius\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                value: formatValueWithPixelOrPercentage(tooltipborderradius),\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                onChange: e => {\n                  // Only allow numeric input\n                  const value = e.target.value;\n                  if (!/^-?\\d*$/.test(value)) {\n                    return;\n                  }\n                  const inputValue = parseInt(value) || 0;\n                  // Validate corner radius between 0px and 20px\n                  if (inputValue < 0 || inputValue > 20) {\n                    setCornerRadiusError(true);\n                  } else {\n                    setCornerRadiusError(false);\n                  }\n                  setTooltipBorderradius(`${inputValue}`);\n                },\n                InputProps: {\n                  endAdornment: \"px\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    }\n                  }\n                },\n                error: cornerRadiusError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 7\n          }, this), cornerRadiusError && /*#__PURE__*/_jsxDEV(Typography, {\n            style: {\n              fontSize: \"12px\",\n              color: \"#e9a971\",\n              textAlign: \"left\",\n              top: \"100%\",\n              left: 0,\n              marginBottom: \"5px\",\n              display: \"flex\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: \"flex\",\n                fontSize: \"12px\",\n                alignItems: \"center\",\n                marginRight: \"4px\"\n              },\n              dangerouslySetInnerHTML: {\n                __html: warning\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 9\n            }, this), translate(\"Value must be between 0px and 20px.\")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Border Size\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                value: formatValueWithPixelOrPercentage(tooltipbordersize),\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                onChange: e => {\n                  // Only allow numeric input\n                  const value = e.target.value;\n                  if (!/^-?\\d*$/.test(value)) {\n                    return;\n                  }\n                  const inputValue = parseInt(value) || 0;\n                  // Validate border size between 0px and 20px\n                  if (inputValue < 0 || inputValue > 5) {\n                    setBorderSizeError(true);\n                  } else {\n                    setBorderSizeError(false);\n                  }\n                  setTooltipBordersize(`${inputValue}px`);\n                },\n                InputProps: {\n                  endAdornment: \"px\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    }\n                  }\n                },\n                error: borderSizeError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 5\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 3\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 1\n          }, this), borderSizeError && /*#__PURE__*/_jsxDEV(Typography, {\n            style: {\n              fontSize: \"12px\",\n              color: \"#e9a971\",\n              textAlign: \"left\",\n              top: \"100%\",\n              left: 0,\n              marginBottom: \"5px\",\n              display: \"flex\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: \"flex\",\n                fontSize: \"12px\",\n                alignItems: \"center\",\n                marginRight: \"4px\"\n              },\n              dangerouslySetInnerHTML: {\n                __html: warning\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 5\n            }, this), \"Value must be between 0px and 5px.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Border\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: tempBorderColor || \"#000000\",\n                onChange: handleBorderColorChange,\n                className: \"qadpt-color-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Background\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: tooltipBackgroundcolor,\n                onChange: handleBackgroundColorChange,\n                className: \"qadpt-color-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 819,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-drawerFooter\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleApplyChanges,\n          className: `qadpt-btn ${paddingError || cornerRadiusError || borderSizeError ? \"disabled\" : \"\"}`,\n          disabled: paddingError || cornerRadiusError || borderSizeError // Disable button if any validation errors exist\n          ,\n          children: translate(\"Apply\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 833,\n          columnNumber: 5\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 832,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 4\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 224,\n    columnNumber: 3\n  }, this);\n};\n_s(TooltipCanvasSettings, \"vsyOU7AZH6IopTB37h4Zu9USJiI=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = TooltipCanvasSettings;\nexport default TooltipCanvasSettings;\nvar _c;\n$RefreshReg$(_c, \"TooltipCanvasSettings\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "TextField", "Grid", "IconButton", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "AlignHorizontalLeft", "TopLeftIcon", "AlignHorizontalCenter", "TopCenterIcon", "AlignHorizontalRight", "TopRightIcon", "AlignVerticalTop", "MiddleLeftIcon", "AlignVerticalCenter", "MiddleCenterIcon", "AlignVerticalBottom", "MiddleRightIcon", "BottomLeftIcon", "BottomCenterIcon", "BottomRightIcon", "useDrawerStore", "CANVAS_DEFAULT_VALUE", "tooltipLeft", "tooltipRight", "tooltipTop", "tooltipBottom", "tooltipCenter", "warning", "useTranslation", "jsxDEV", "_jsxDEV", "TooltipCanvasSettings", "zindeex", "setZindeex", "setShowTooltipCanvasSettings", "_s", "_toolTipGuideMetaData2", "t", "translate", "setTooltipXaxis", "setTooltipYaxis", "updateCanvasInTooltip", "tooltipXaxis", "tooltipYaxis", "tooltipWidth", "setTooltipWidth", "setTooltipPadding", "setTooltipBorderradius", "setTooltipBordersize", "setTooltipBordercolor", "setTooltipBackgroundcolor", "tooltippadding", "tooltipborderradius", "tooltipbordersize", "tooltipBordercolor", "tooltipBackgroundcolor", "tooltipPosition", "setTooltipPosition", "setElementSelected", "setIsTooltipPopup", "toolTipGuideMetaData", "currentStep", "autoPosition", "setAutoPosition", "selectedTemplate", "updateDesignelementInTooltip", "selectedTemplateTour", "CANVAS_DEFAULT_VALUE_HOTSPOT", "setIsUnSavedChanges", "state", "isOpen", "setIsOpen", "dismiss", "<PERSON><PERSON><PERSON><PERSON>", "selectedPosition", "setSelectedPosition", "error", "setError", "paddingError", "setPaddingError", "cornerRadiusError", "setCornerRadiusError", "borderSizeError", "setBorderSizeError", "widthError", "setWidthError", "tempBorderColor", "setTempBorderColor", "validColor", "positions", "label", "defaultValue", "icon", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "dangerouslySetInnerHTML", "__html", "style", "handlePositionClick", "e", "_e$target", "target", "id", "onReselectElement", "ReSelection", "XPosition", "YPosition", "width", "Padding", "<PERSON><PERSON><PERSON>", "bordersize", "borderColor", "backgroundColor", "ShowByDefault", "handleBorderColorChange", "_e$target2", "handleBackgroundColorChange", "_e$target3", "handleAutoSelect", "checked", "handleClose", "handleApplyChanges", "updatedCanvasSettings", "position", "borderRadius", "padding", "borderSize", "autoposition", "xaxis", "yaxis", "_toolTipGuideMetaData", "canvas", "canvasData", "validBorderColor", "formatValueWithPixelOrPercentage", "v", "String", "newValue", "endsWith", "split", "handleChange", "test", "inputValue", "parseInt", "className", "children", "onClick", "size", "type", "onChange", "name", "sx", "opacity", "cursor", "container", "spacing", "item", "xs", "disabled", "disable<PERSON><PERSON><PERSON>", "paddingLeft", "variant", "InputProps", "endAdornment", "border", "flexDirection", "height", "display", "alignItems", "gap", "color", "textAlign", "top", "left", "marginBottom", "marginRight", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/Tooltips/designFields/TooltipCanvasSettings.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { <PERSON>, Typo<PERSON>, TextField, Grid, IconButton, Button, Tooltip, Switch } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport {\r\n\tAlignHorizontalLeft as TopLeftIcon,\r\n\tAlignHorizontalCenter as TopCenterIcon,\r\n\tAlignHorizontalRight as TopRightIcon,\r\n\tAlignVerticalTop as MiddleLeftIcon,\r\n\tAlignVerticalCenter as MiddleCenterIcon,\r\n\tAlignVerticalBottom as MiddleRightIcon,\r\n\tAlignHorizontalLeft as BottomLeftIcon,\r\n\tAlignHorizontalCenter as BottomCenterIcon,\r\n\tAlignHorizontalRight as BottomRightIcon,\r\n\tPadding,\r\n} from \"@mui/icons-material\";\r\nimport \"./Canvas.module.css\";\r\nimport useDrawerStore, { CANVAS_DEFAULT_VALUE, CANVAS_DEFAULT_VALUE_HOTSPOT, TCan<PERSON> } from \"../../../store/drawerStore\";\r\nimport { tooltipLeft, tooltipRight, tooltipTop, tooltipBottom, tooltipCenter, warning } from \"../../../assets/icons/icons\";\r\nimport { TouchAppSharp } from \"@mui/icons-material\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst TooltipCanvasSettings = ({ zindeex, setZindeex, setShowTooltipCanvasSettings }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tsetTooltipXaxis,\r\n\t\tsetTooltipYaxis,\r\n\t\tupdateCanvasInTooltip,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\ttooltipWidth,\r\n\t\tsetTooltipWidth,\r\n\t\tsetTooltipPadding,\r\n\t\tsetTooltipBorderradius,\r\n\t\tsetTooltipBordersize,\r\n\t\tsetTooltipBordercolor,\r\n\t\tsetTooltipBackgroundcolor,\r\n\t\ttooltippadding,\r\n\t\ttooltipborderradius,\r\n\t\ttooltipbordersize,\r\n\t\ttooltipBordercolor,\r\n\t\ttooltipBackgroundcolor,\r\n\t\ttooltipPosition,\r\n\t\tsetTooltipPosition,\r\n\t\tsetElementSelected,\r\n\t\tsetIsTooltipPopup,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tcurrentStep,\r\n\t\tautoPosition,\r\n\t\tsetAutoPosition,\r\n\t\tselectedTemplate,\r\n\t\tupdateDesignelementInTooltip,\r\n\t\tselectedTemplateTour,\r\n\t\tCANVAS_DEFAULT_VALUE_HOTSPOT,\r\n\t\tsetIsUnSavedChanges\r\n\t} = useDrawerStore((state: any) => state);\r\n\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [dismiss, setDismiss] = useState(false);\r\n\tconst [selectedPosition, setSelectedPosition] = useState(\"middle-center\");\r\n\tconst [error, setError] = useState(false);\r\n\tconst [paddingError, setPaddingError] = useState(false);\r\n\tconst [cornerRadiusError, setCornerRadiusError] = useState(false);\r\n\tconst [borderSizeError, setBorderSizeError] = useState(false);\r\n\tconst [widthError, setWidthError] = useState(false);\r\n\tconst [tempBorderColor, setTempBorderColor] = useState(tooltipBordercolor);\r\n\r\n\tuseEffect(() => {\r\n\t\t// Sync tempBorderColor with store, using default if empty or transparent\r\n\t\tconst validColor = tooltipBordercolor && tooltipBordercolor !== \"transparent\" && tooltipBordercolor !== \"\" ? tooltipBordercolor : \"#000000\";\r\n\t\tsetTempBorderColor(validColor);\r\n\t  }, [tooltipBordercolor]);\r\n\r\n\tconst positions = [\r\n\t\t{ label: translate(\"Top Left\", { defaultValue: \"Top Left\" }), icon: <TopLeftIcon fontSize=\"small\" />, value: \"top-left\" },\r\n\t\t{ label: translate(\"Top Center\", { defaultValue: \"Top Center\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipTop }} style={{ fontSize: \"small\" }} /> : <TopCenterIcon fontSize=\"small\" />, value: \"top\" },\r\n\t\t{ label: translate(\"Top Right\", { defaultValue: \"Top Right\" }), icon: <TopRightIcon fontSize=\"small\" />, value: \"top-right\" },\r\n\t\t{ label: translate(\"Middle Left\", { defaultValue: \"Middle Left\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipLeft }} style={{ fontSize: \"small\" }} /> : <MiddleLeftIcon fontSize=\"small\" />, value: \"left\" },\r\n\t\t{ label: translate(\"Middle Center\", { defaultValue: \"Middle Center\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipCenter }} style={{ fontSize: \"small\" }} /> : <MiddleCenterIcon fontSize=\"small\" />, value: \"center\" },\r\n\t\t{ label: translate(\"Middle Right\", { defaultValue: \"Middle Right\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipRight }} style={{ fontSize: \"small\" }} /> : <MiddleRightIcon fontSize=\"small\" />, value: \"right\" },\r\n\t\t{ label: translate(\"Bottom Left\", { defaultValue: \"Bottom Left\" }), icon: <BottomLeftIcon fontSize=\"small\" />, value: \"bottom-left\" },\r\n\t\t{ label: translate(\"Bottom Center\", { defaultValue: \"Bottom Center\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipBottom }} style={{ fontSize: \"small\" }} /> : <BottomCenterIcon fontSize=\"small\" />, value: \"bottom\" },\r\n\t\t{ label: translate(\"Bottom Right\", { defaultValue: \"Bottom Right\" }), icon: <BottomRightIcon fontSize=\"small\" />, value: \"bottom-right\" },\r\n\t];\r\n\r\n\tconst handlePositionClick = (e: any) => {\r\n\t\tif (e?.target?.id) {\r\n\t\t\t//setSelectedPosition(e.target.id);\r\n\t\t\tsetTooltipPosition(e.target.id);\r\n\t\t}\r\n\t};\r\n\r\n\tconst onReselectElement = () => {\r\n\r\n\t\tTooltipCanvasSettings({\r\n\t\t\tReSelection: false,\r\n\t\t\tXPosition: 4,\r\n\t\t\tYPosition: 4,\r\n\t\t\twidth: \"300\",\r\n\t\t\tPadding: \"2\",\r\n\t\t\tborderradius: \"8\",\r\n\t\t\tbordersize: \"0\",\r\n\t\t\tborderColor: \"\",\r\n\t\t\tbackgroundColor: \"\",\r\n\t\t\t// PulseAnimation: true,\r\n\t\t\t// stopAnimationUponInteraction: true,\r\n\t\t\t// ShowUpon: \"Hovering Hotspot\",\r\n\t\t\tShowByDefault: false,\r\n\t\t});\r\n\t\tupdateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\r\n\t\tsetElementSelected(true);\r\n\t\tsetIsTooltipPopup(false);\r\n\t\tsetShowTooltipCanvasSettings(false);\r\n\t};\r\n\tconst handleBorderColorChange = (e: any) => {\r\n\t\tif (e?.target?.value) {\r\n\t\t\tsetTempBorderColor(e.target.value);\r\n\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleBackgroundColorChange = (e: any) => {\r\n\t\tif (e?.target?.value) {\r\n\t\t\tsetTooltipBackgroundcolor(e.target.value);\r\n\t\t}\r\n\t};\r\n\tconst handleAutoSelect = (e: any) => {\r\n\t\t//\tsetDismiss(e.target.checked);\r\n\t\tsetAutoPosition(e.target.checked);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowTooltipCanvasSettings(false);\r\n\t};\r\n\r\n\tconst handleApplyChanges = () => {\r\n\t\t// Create the new canvas settings\r\n\t\tconst updatedCanvasSettings = {\r\n\t\t\tposition: tooltipPosition,\r\n\t\t\tbackgroundColor: tooltipBackgroundcolor,\r\n\t\t\twidth: tooltipWidth,\r\n\t\t\tborderRadius: tooltipborderradius,\r\n\t\t\tpadding: tooltippadding,\r\n\t\t\tborderColor: tempBorderColor,\r\n\t\t\tborderSize: tooltipbordersize,\r\n\t\t\tautoposition: autoPosition,\r\n\t\t\txaxis: tooltipXaxis,\r\n\t\t\tyaxis: tooltipYaxis,\r\n\t\t};\r\n\r\n\t\t// Apply the changes - updateCanvasInTooltip will handle recording the change for undo/redo\r\n\t\tupdateCanvasInTooltip(updatedCanvasSettings); // Updates canvas and tooltipBordercolor\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (toolTipGuideMetaData[currentStep - 1]?.canvas) {\r\n\t\t\tconst canvasData = toolTipGuideMetaData[currentStep - 1].canvas;\r\n\r\n\t\t\t// Handle border color - use default if empty, transparent, or invalid\r\n\t\t\tconst borderColor = canvasData?.borderColor;\r\n\t\t\tconst validBorderColor = borderColor && borderColor !== \"transparent\" && borderColor !== \"\" ? borderColor : \"#000000\";\r\n\r\n\t\t\tsetTooltipPosition(canvasData?.position || \"middle-center\");\r\n\t\t\tsetTooltipPadding(canvasData?.padding || \"10px\");\r\n\t\t\tsetTooltipBorderradius(canvasData?.borderRadius || \"8px\");\r\n\t\t\tsetTooltipBordersize(canvasData?.borderSize || \"0px\");\r\n\t\t\tsetTooltipBordercolor(borderColor || \"\");\r\n\t\t\tsetTempBorderColor(validBorderColor); // Use valid color for the input\r\n\t\t\tsetTooltipBackgroundcolor(canvasData?.backgroundColor || \"#FFFFFF\");\r\n\t\t\tsetTooltipWidth(canvasData?.width || \"300px\");\r\n\t\t\tif (selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") {\r\n\t\t\t\tsetTooltipXaxis(canvasData?.xaxis || \"2px\");\r\n\t\t\t\tsetTooltipYaxis(canvasData?.yaxis || \"2px\");\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tsetTooltipXaxis(canvasData?.xaxis || \"100px\");\r\n\t\t\t\tsetTooltipYaxis(canvasData?.yaxis || \"100px\");\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}, [toolTipGuideMetaData[currentStep - 1]?.canvas]);\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\tconst formatValueWithPixelOrPercentage = (value: string) => {\r\n\t\tconst v = String(value);\r\n\t\tlet newValue = v;\r\n\t\tif (v?.endsWith(\"px\") || v?.endsWith(\"%\")) {\r\n\t\t\tnewValue = v.split(/px|%/)[0];\r\n\t\t}\r\n\t\treturn newValue;\r\n\t};\r\n\tconst handleChange = (e: any) => {\r\n\t\t// Only allow numeric input\r\n\t\tconst value = e.target.value;\r\n\t\tif (value === '') {\r\n\t\t\tsetTooltipWidth('0px');\r\n\t\t\tsetWidthError(true); // Empty value is invalid\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tlet inputValue = parseInt(value) || 0;\r\n\r\n\t\t// Validate width - minimum 300 pixels\r\n\t\tif (inputValue < 300) {\r\n\t\t\tsetWidthError(true);\r\n\t\t} else {\r\n\t\t\tsetWidthError(false);\r\n\t\t}\r\n\r\n\t\tsetTooltipWidth(`${inputValue}px`);\r\n\t};\r\n\r\n\r\n\r\n\treturn (\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"back\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? translate(\"Canvas\") : translate(\"Canvas\")}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t{/* <Button\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\tonClick={onReselectElement}\r\n\t\t\t\t\t\t\t\t\t//startIcon={<DesignServicesIcon />}\r\n\t\t\t\t\t\t\t\t\tendIcon={<TouchAppSharp />}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tReselect Element\r\n\t\t\t\t\t\t\t\t</Button> */}\r\n\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{translate(\"Auto Position\")}\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={autoPosition}\r\n        onChange={handleAutoSelect}\r\n        name=\"autoPosition\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-position-grid\"\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\topacity: selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\"? 0.5 : 1,\r\n\t\t\t\t\t\t\t\tcursor:selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\"?\"not-allowed\":\"\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-ctrl-title\"\r\n\t\t\t\t\t\t\t>{translate(\"Position\")}</Typography>\r\n\t\t\t\t\t\t\t<Grid container spacing={1}>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[0].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Top Left - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[1].value}\r\n      onClick={() => {\r\n\r\n        //setSelectedPosition(positions[1].value);\r\n        setTooltipPosition(positions[1].value);\r\n      }}\r\n\tdisabled={autoPosition || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\"}\r\n\tdisableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[1].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipTop }}  id={positions[1].value} />\r\n      ) : (\r\n        <TopCenterIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[2].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Top Right - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[3].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[3].value);\r\n        setTooltipPosition(positions[3].value);\r\n      }}\r\n      disabled={autoPosition}\r\n\t  disableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[3].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipLeft }} id={positions[3].value} />\r\n      ) : (\r\n        <MiddleLeftIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[4].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[4].value);\r\n        setTooltipPosition(positions[4].value);\r\n      }}\r\n\t\t\t\t\t\t\t\t\t\tdisabled={autoPosition}\r\n\t\t\t\t\t\t\t\t\t\tdisableRipple\r\n      sx={{\r\n\t\t  opacity: tooltipPosition === positions[4].value ? 1 : 0.5,\r\n\t\t  paddingLeft:\"0 !important\"\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipCenter }} id={positions[4].value}/>\r\n      ) : (\r\n        <MiddleCenterIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[5].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[5].value);\r\n        setTooltipPosition(positions[5].value);\r\n      }}\r\n      disabled={autoPosition}\r\n\t  disableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[5].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipRight }}  id={positions[5].value} />\r\n      ) : (\r\n        <MiddleRightIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[6].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Bottom Left - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[7].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[7].value);\r\n        setTooltipPosition(positions[7].value);\r\n      }}\r\n      disabled={autoPosition}\r\n\t  disableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[7].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipBottom }} id={positions[7].value} />\r\n      ) : (\r\n        <BottomCenterIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[8].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Bottom Right - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n</Grid>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{/* Width Control */}\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">X {translate(\"Axis Offset\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipXaxis)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => setTooltipXaxis(`${parseInt(e.target.value) || 0}px`)}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdisabled={autoPosition}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">Y {translate(\"Axis Offset\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipYaxis)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => setTooltipYaxis(`${parseInt(e.target.value) || 0}px`)}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdisabled={autoPosition}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\tstyle={{ flexDirection: \"column\", height: \"auto\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box style={{ display: \"flex\", alignItems: \"center\", gap: \"8px\", width: \"100%\" }}>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Width\")}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipWidth)}\r\n\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\t\terror={widthError}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{widthError && (\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\talignItems: \"center\"\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Width must be at least 300 pixels\")}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Padding\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltippadding)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\t\t\t\t\t\t\t\t\t// Validate padding between 0px and 20px\r\n\t\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\t\tsetPaddingError(true);\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tsetPaddingError(false);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tsetTooltipPadding(`${inputValue}px`);\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\terror={paddingError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{paddingError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Corner Radius\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipborderradius)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\t\t\t\t\t\t\t\t\t// Validate corner radius between 0px and 20px\r\n\t\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\t\tsetCornerRadiusError(true);\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tsetCornerRadiusError(false);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tsetTooltipBorderradius(`${inputValue}`);\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\terror={cornerRadiusError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{cornerRadiusError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\r\n\r\n<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Border Size\")}</div>\r\n  <div>\r\n    <TextField\r\n      variant=\"outlined\"\r\n      value={formatValueWithPixelOrPercentage(tooltipbordersize)}\r\n      size=\"small\"\r\n      className=\"qadpt-control-input\"\r\n      onChange={(e) => {\r\n        // Only allow numeric input\r\n        const value = e.target.value;\r\n        if (!/^-?\\d*$/.test(value)) {\r\n          return;\r\n        }\r\n        const inputValue = parseInt(value) || 0;\r\n        // Validate border size between 0px and 20px\r\n        if (inputValue < 0 || inputValue > 5) {\r\n          setBorderSizeError(true);\r\n        } else {\r\n          setBorderSizeError(false);\r\n        }\r\n        setTooltipBordersize(`${inputValue}px`);\r\n      }}\r\n      InputProps={{\r\n        endAdornment: \"px\",\r\n        sx: {\r\n          \"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n          \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n          \"& fieldset\": { border: \"none\" },\r\n        },\r\n      }}\r\n      error={borderSizeError}\r\n    />\r\n  </div>\r\n</Box>\r\n{borderSizeError && (\r\n  <Typography\r\n    style={{\r\n      fontSize: \"12px\",\r\n      color: \"#e9a971\",\r\n      textAlign: \"left\",\r\n      top: \"100%\",\r\n      left: 0,\r\n      marginBottom: \"5px\",\r\n      display: \"flex\",\r\n    }}\r\n  >\r\n    <span\r\n      style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight: \"4px\" }}\r\n      dangerouslySetInnerHTML={{ __html: warning }}\r\n    />\r\n    Value must be between 0px and 5px.\r\n  </Typography>\r\n)}\r\n\r\n{/* Button Selection Dropdown - Only show for Tooltip template */}\r\n{/* {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") && (\r\n  <Box className=\"qadpt-control-box\" style={{ flexDirection: \"column\", height: \"auto\" }}>\r\n    <Typography className=\"qadpt-ctrl-title\">Selected Button</Typography>\r\n    <Box style={{ width: \"100%\" }}>\r\n      {toolTipGuideMetaData?.[currentStep - 1]?.containers?.[1]?.buttons ? (\r\n        <Select\r\n          value={\r\n            toolTipGuideMetaData[currentStep - 1].containers[1].buttons.find(\r\n              (button: any) =>\r\n                button.name === toolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonName\r\n            )?.id || \"\"\r\n          }\r\n          onChange={(event) => {\r\n            const selectedValue = event.target.value;\r\n            const selectedButton = toolTipGuideMetaData[currentStep - 1]?.containers[1]?.buttons?.find(\r\n              (button: any) => button.id === selectedValue\r\n            );\r\n\r\n            // Set dropdown value and button name\r\n            setDropdownValue(selectedValue);\r\n            setElementButtonName(selectedValue);\r\n            setbtnidss(selectedValue);\r\n            setElementClick(\"button\");\r\n\r\n            // Save button ID, button name, and \"NextStep\" in the Design object's \"goToNext\" object\r\n            const updatedCanvasSettings = {\r\n              NextStep: \"button\",\r\n              ButtonId: selectedValue,\r\n              ElementPath: \"\",\r\n              ButtonName: selectedButton?.name,\r\n            };\r\n\r\n            updateDesignelementInTooltip(updatedCanvasSettings);\r\n\r\n            // Set the button action to \"Next\" using updateTooltipButtonAction\r\n            if (selectedButton && selectedButton.id) {\r\n              // Find the container ID for the button\r\n              const containerId = toolTipGuideMetaData[currentStep - 1]?.containers[1]?.id;\r\n\r\n              // Update the button action to \"Next\"\r\n              updateTooltipButtonAction(containerId, selectedValue, {\r\n                value: \"Next\",\r\n                targetURL: \"\",\r\n                tab: \"same-tab\",\r\n                interaction: null,\r\n              });\r\n\r\n              // Set the selected action to \"Next\" in the UI\r\n              setSelectActions(\"Next\");\r\n\r\n              // Mark changes as unsaved\r\n              setIsUnSavedChanges(true);\r\n            }\r\n          }}\r\n          displayEmpty\r\n          style={{ width: \"100%\" }}\r\n          sx={{\r\n            \"& .MuiSelect-select\": {\r\n              textAlign: \"left\",\r\n              padding: \"9px 14px !important\",\r\n            },\r\n            \"& .MuiSvgIcon-root\": {\r\n              height: \"20px\",\r\n              width: \"20px\",\r\n              top: \"10px\",\r\n            },\r\n          }}\r\n        >\r\n          <MenuItem value=\"\" disabled>\r\n            Select a button\r\n          </MenuItem>\r\n          {toolTipGuideMetaData[currentStep - 1].containers[1].buttons.map(\r\n            (button: any, buttonIndex: number) => (\r\n              <MenuItem key={buttonIndex} value={button.id}>\r\n                {button.name}\r\n              </MenuItem>\r\n            )\r\n          )}\r\n        </Select>\r\n      ) : (\r\n        <Typography variant=\"body2\" color=\"textSecondary\">\r\n          No buttons available\r\n        </Typography>\r\n      )}\r\n    </Box>\r\n  </Box>\r\n)} */}\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Border\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={tempBorderColor || \"#000000\"}\r\n\t\t\t\t\t\t\t\tonChange={handleBorderColorChange}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Background\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={tooltipBackgroundcolor}\r\n\t\t\t\t\t\t\t\tonChange={handleBackgroundColorChange}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t<Button\r\n\t\t\t\tvariant=\"contained\"\r\n\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\tclassName={`qadpt-btn ${paddingError || cornerRadiusError || borderSizeError ? \"disabled\" : \"\"}`}\r\n\t\t\t\tdisabled={paddingError || cornerRadiusError || borderSizeError} // Disable button if any validation errors exist\r\n\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t</Button>\r\n\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default TooltipCanvasSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,EAAEC,MAAM,QAAyB,eAAe;AACrG,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,SACCC,mBAAmB,IAAIC,WAAW,EAClCC,qBAAqB,IAAIC,aAAa,EACtCC,oBAAoB,IAAIC,YAAY,EACpCC,gBAAgB,IAAIC,cAAc,EAClCC,mBAAmB,IAAIC,gBAAgB,EACvCC,mBAAmB,IAAIC,eAAe,EACtCX,mBAAmB,IAAIY,cAAc,EACrCV,qBAAqB,IAAIW,gBAAgB,EACzCT,oBAAoB,IAAIU,eAAe,QAEjC,qBAAqB;AAC5B,OAAO,qBAAqB;AAC5B,OAAOC,cAAc,IAAIC,oBAAoB,QAA+C,4BAA4B;AACxH,SAASC,WAAW,EAAEC,YAAY,EAAEC,UAAU,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AAE1H,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,qBAAqB,GAAGA,CAAC;EAAEC,OAAO;EAAEC,UAAU;EAAEC;AAAkC,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,sBAAA;EAC7F,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGV,cAAc,CAAC,CAAC;EACzC,MAAM;IACLW,eAAe;IACfC,eAAe;IACfC,qBAAqB;IACrBC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,eAAe;IACfC,iBAAiB;IACjBC,sBAAsB;IACtBC,oBAAoB;IACpBC,qBAAqB;IACrBC,yBAAyB;IACzBC,cAAc;IACdC,mBAAmB;IACnBC,iBAAiB;IACjBC,kBAAkB;IAClBC,sBAAsB;IACtBC,eAAe;IACfC,kBAAkB;IAClBC,kBAAkB;IAClBC,iBAAiB;IACjBC,oBAAoB;IACpBC,WAAW;IACXC,YAAY;IACZC,eAAe;IACfC,gBAAgB;IAChBC,4BAA4B;IAC5BC,oBAAoB;IACpBC,4BAA4B;IAC5BC;EACD,CAAC,GAAGhD,cAAc,CAAEiD,KAAU,IAAKA,KAAK,CAAC;EAEzC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC4E,OAAO,EAAEC,UAAU,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/E,QAAQ,CAAC,eAAe,CAAC;EACzE,MAAM,CAACgF,KAAK,EAAEC,QAAQ,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACzC,MAAM,CAACkF,YAAY,EAAEC,eAAe,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACsF,eAAe,EAAEC,kBAAkB,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACwF,UAAU,EAAEC,aAAa,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC0D,kBAAkB,CAAC;EAE1E3D,SAAS,CAAC,MAAM;IACf;IACA,MAAM6F,UAAU,GAAGlC,kBAAkB,IAAIA,kBAAkB,KAAK,aAAa,IAAIA,kBAAkB,KAAK,EAAE,GAAGA,kBAAkB,GAAG,SAAS;IAC3IiC,kBAAkB,CAACC,UAAU,CAAC;EAC7B,CAAC,EAAE,CAAClC,kBAAkB,CAAC,CAAC;EAE1B,MAAMmC,SAAS,GAAG,CACjB;IAAEC,KAAK,EAAEpD,SAAS,CAAC,UAAU,EAAE;MAAEqD,YAAY,EAAE;IAAW,CAAC,CAAC;IAAEC,IAAI,eAAE9D,OAAA,CAACxB,WAAW;MAACuF,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAW,CAAC,EACzH;IAAER,KAAK,EAAEpD,SAAS,CAAC,YAAY,EAAE;MAAEqD,YAAY,EAAE;IAAa,CAAC,CAAC;IAAEC,IAAI,EAAE5B,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBAAGpC,OAAA;MAAMqE,uBAAuB,EAAE;QAAEC,MAAM,EAAE5E;MAAW,CAAE;MAAC6E,KAAK,EAAE;QAAER,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGnE,OAAA,CAACtB,aAAa;MAACqF,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC3R;IAAER,KAAK,EAAEpD,SAAS,CAAC,WAAW,EAAE;MAAEqD,YAAY,EAAE;IAAY,CAAC,CAAC;IAAEC,IAAI,eAAE9D,OAAA,CAACpB,YAAY;MAACmF,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAY,CAAC,EAC7H;IAAER,KAAK,EAAEpD,SAAS,CAAC,aAAa,EAAE;MAAEqD,YAAY,EAAE;IAAc,CAAC,CAAC;IAAEC,IAAI,EAAE5B,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBAAGpC,OAAA;MAAMqE,uBAAuB,EAAE;QAAEC,MAAM,EAAE9E;MAAY,CAAE;MAAC+E,KAAK,EAAE;QAAER,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGnE,OAAA,CAAClB,cAAc;MAACiF,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAO,CAAC,EAChS;IAAER,KAAK,EAAEpD,SAAS,CAAC,eAAe,EAAE;MAAEqD,YAAY,EAAE;IAAgB,CAAC,CAAC;IAAEC,IAAI,EAAE5B,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBAAGpC,OAAA;MAAMqE,uBAAuB,EAAE;QAAEC,MAAM,EAAE1E;MAAc,CAAE;MAAC2E,KAAK,EAAE;QAAER,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGnE,OAAA,CAAChB,gBAAgB;MAAC+E,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAS,CAAC,EAC1S;IAAER,KAAK,EAAEpD,SAAS,CAAC,cAAc,EAAE;MAAEqD,YAAY,EAAE;IAAe,CAAC,CAAC;IAAEC,IAAI,EAAE5B,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBAAGpC,OAAA;MAAMqE,uBAAuB,EAAE;QAAEC,MAAM,EAAE7E;MAAa,CAAE;MAAC8E,KAAK,EAAE;QAAER,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGnE,OAAA,CAACd,eAAe;MAAC6E,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACrS;IAAER,KAAK,EAAEpD,SAAS,CAAC,aAAa,EAAE;MAAEqD,YAAY,EAAE;IAAc,CAAC,CAAC;IAAEC,IAAI,eAAE9D,OAAA,CAACb,cAAc;MAAC4E,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAc,CAAC,EACrI;IAAER,KAAK,EAAEpD,SAAS,CAAC,eAAe,EAAE;MAAEqD,YAAY,EAAE;IAAgB,CAAC,CAAC;IAAEC,IAAI,EAAE5B,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBAAGpC,OAAA;MAAMqE,uBAAuB,EAAE;QAAEC,MAAM,EAAE3E;MAAc,CAAE;MAAC4E,KAAK,EAAE;QAAER,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGnE,OAAA,CAACZ,gBAAgB;MAAC2E,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAS,CAAC,EAC1S;IAAER,KAAK,EAAEpD,SAAS,CAAC,cAAc,EAAE;MAAEqD,YAAY,EAAE;IAAe,CAAC,CAAC;IAAEC,IAAI,eAAE9D,OAAA,CAACX,eAAe;MAAC0E,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAe,CAAC,CACzI;EAED,MAAMI,mBAAmB,GAAIC,CAAM,IAAK;IAAA,IAAAC,SAAA;IACvC,IAAID,CAAC,aAADA,CAAC,gBAAAC,SAAA,GAADD,CAAC,CAAEE,MAAM,cAAAD,SAAA,eAATA,SAAA,CAAWE,EAAE,EAAE;MAClB;MACAjD,kBAAkB,CAAC8C,CAAC,CAACE,MAAM,CAACC,EAAE,CAAC;IAChC;EACD,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAE/B5E,qBAAqB,CAAC;MACrB6E,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,GAAG;MACZC,YAAY,EAAE,GAAG;MACjBC,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE,EAAE;MACnB;MACA;MACA;MACAC,aAAa,EAAE;IAChB,CAAC,CAAC;IACF5E,qBAAqB,CAACpB,oBAAoB,CAAC;IAC3CqC,kBAAkB,CAAC,IAAI,CAAC;IACxBC,iBAAiB,CAAC,KAAK,CAAC;IACxBzB,4BAA4B,CAAC,KAAK,CAAC;EACpC,CAAC;EACD,MAAMoF,uBAAuB,GAAIf,CAAM,IAAK;IAAA,IAAAgB,UAAA;IAC3C,IAAIhB,CAAC,aAADA,CAAC,gBAAAgB,UAAA,GAADhB,CAAC,CAAEE,MAAM,cAAAc,UAAA,eAATA,UAAA,CAAWrB,KAAK,EAAE;MACrBX,kBAAkB,CAACgB,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC;IAEnC;EACD,CAAC;EAED,MAAMsB,2BAA2B,GAAIjB,CAAM,IAAK;IAAA,IAAAkB,UAAA;IAC/C,IAAIlB,CAAC,aAADA,CAAC,gBAAAkB,UAAA,GAADlB,CAAC,CAAEE,MAAM,cAAAgB,UAAA,eAATA,UAAA,CAAWvB,KAAK,EAAE;MACrBhD,yBAAyB,CAACqD,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC;IAC1C;EACD,CAAC;EACD,MAAMwB,gBAAgB,GAAInB,CAAM,IAAK;IACpC;IACAxC,eAAe,CAACwC,CAAC,CAACE,MAAM,CAACkB,OAAO,CAAC;EAClC,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACzBrD,SAAS,CAAC,KAAK,CAAC;IAChBrC,4BAA4B,CAAC,KAAK,CAAC;EACpC,CAAC;EAED,MAAM2F,kBAAkB,GAAGA,CAAA,KAAM;IAChC;IACA,MAAMC,qBAAqB,GAAG;MAC7BC,QAAQ,EAAEvE,eAAe;MACzB4D,eAAe,EAAE7D,sBAAsB;MACvCwD,KAAK,EAAEnE,YAAY;MACnBoF,YAAY,EAAE5E,mBAAmB;MACjC6E,OAAO,EAAE9E,cAAc;MACvBgE,WAAW,EAAE7B,eAAe;MAC5B4C,UAAU,EAAE7E,iBAAiB;MAC7B8E,YAAY,EAAErE,YAAY;MAC1BsE,KAAK,EAAE1F,YAAY;MACnB2F,KAAK,EAAE1F;IACR,CAAC;;IAED;IACAF,qBAAqB,CAACqF,qBAAqB,CAAC,CAAC,CAAC;IAC9CF,WAAW,CAAC,CAAC;IACbxD,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAEDzE,SAAS,CAAC,MAAM;IAAA,IAAA2I,qBAAA;IACf,KAAAA,qBAAA,GAAI1E,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAAyE,qBAAA,eAArCA,qBAAA,CAAuCC,MAAM,EAAE;MAClD,MAAMC,UAAU,GAAG5E,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,CAAC0E,MAAM;;MAE/D;MACA,MAAMpB,WAAW,GAAGqB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAErB,WAAW;MAC3C,MAAMsB,gBAAgB,GAAGtB,WAAW,IAAIA,WAAW,KAAK,aAAa,IAAIA,WAAW,KAAK,EAAE,GAAGA,WAAW,GAAG,SAAS;MAErH1D,kBAAkB,CAAC,CAAA+E,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAET,QAAQ,KAAI,eAAe,CAAC;MAC3DjF,iBAAiB,CAAC,CAAA0F,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEP,OAAO,KAAI,MAAM,CAAC;MAChDlF,sBAAsB,CAAC,CAAAyF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAER,YAAY,KAAI,KAAK,CAAC;MACzDhF,oBAAoB,CAAC,CAAAwF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEN,UAAU,KAAI,KAAK,CAAC;MACrDjF,qBAAqB,CAACkE,WAAW,IAAI,EAAE,CAAC;MACxC5B,kBAAkB,CAACkD,gBAAgB,CAAC,CAAC,CAAC;MACtCvF,yBAAyB,CAAC,CAAAsF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEpB,eAAe,KAAI,SAAS,CAAC;MACnEvE,eAAe,CAAC,CAAA2F,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEzB,KAAK,KAAI,OAAO,CAAC;MAC7C,IAAI/C,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,EAAE;QACzE3B,eAAe,CAAC,CAAAiG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEJ,KAAK,KAAI,KAAK,CAAC;QAC3C5F,eAAe,CAAC,CAAAgG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEH,KAAK,KAAI,KAAK,CAAC;MAC5C,CAAC,MACI;QACJ9F,eAAe,CAAC,CAAAiG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEJ,KAAK,KAAI,OAAO,CAAC;QAC7C5F,eAAe,CAAC,CAAAgG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEH,KAAK,KAAI,OAAO,CAAC;MAE9C;IACD;EACD,CAAC,EAAE,EAAAjG,sBAAA,GAACwB,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAAzB,sBAAA,uBAArCA,sBAAA,CAAuCmG,MAAM,CAAC,CAAC;EAEnD,IAAI,CAACjE,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMoE,gCAAgC,GAAIxC,KAAa,IAAK;IAC3D,MAAMyC,CAAC,GAAGC,MAAM,CAAC1C,KAAK,CAAC;IACvB,IAAI2C,QAAQ,GAAGF,CAAC;IAChB,IAAIA,CAAC,aAADA,CAAC,eAADA,CAAC,CAAEG,QAAQ,CAAC,IAAI,CAAC,IAAIH,CAAC,aAADA,CAAC,eAADA,CAAC,CAAEG,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC1CD,QAAQ,GAAGF,CAAC,CAACI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9B;IACA,OAAOF,QAAQ;EAChB,CAAC;EACD,MAAMG,YAAY,GAAIzC,CAAM,IAAK;IAChC;IACA,MAAML,KAAK,GAAGK,CAAC,CAACE,MAAM,CAACP,KAAK;IAC5B,IAAIA,KAAK,KAAK,EAAE,EAAE;MACjBrD,eAAe,CAAC,KAAK,CAAC;MACtBwC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;MACrB;IACD;IAEA,IAAI,CAAC,SAAS,CAAC4D,IAAI,CAAC/C,KAAK,CAAC,EAAE;MAC3B;IACD;IAEA,IAAIgD,UAAU,GAAGC,QAAQ,CAACjD,KAAK,CAAC,IAAI,CAAC;;IAErC;IACA,IAAIgD,UAAU,GAAG,GAAG,EAAE;MACrB7D,aAAa,CAAC,IAAI,CAAC;IACpB,CAAC,MAAM;MACNA,aAAa,CAAC,KAAK,CAAC;IACrB;IAEAxC,eAAe,CAAC,GAAGqG,UAAU,IAAI,CAAC;EACnC,CAAC;EAID,oBACCpH,OAAA;IACC4E,EAAE,EAAC,mBAAmB;IACtB0C,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAE7BvH,OAAA;MAAKsH,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC7BvH,OAAA;QAAKsH,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACnCvH,OAAA,CAAC7B,UAAU;UACV,cAAW,MAAM;UACjBqJ,OAAO,EAAE1B,WAAY;UAAAyB,QAAA,eAErBvH,OAAA,CAAC1B,2BAA2B;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACbnE,OAAA;UAAKsH,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAGrF,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,GAAI5B,SAAS,CAAC,QAAQ,CAAC,GAAGA,SAAS,CAAC,QAAQ;QAAC;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvJnE,OAAA,CAAC7B,UAAU;UACVsJ,IAAI,EAAC,OAAO;UACZ,cAAW,OAAO;UAClBD,OAAO,EAAE1B,WAAY;UAAAyB,QAAA,eAErBvH,OAAA,CAAC3B,SAAS;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNnE,OAAA;QAAKsH,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC9BvH,OAAA;UAAKsH,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC9BvH,OAAA,CAACjC,GAAG;YAACuJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAUjCvH,OAAA;cACCsH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAG9B/G,SAAS,CAAC,eAAe;YAAC;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACNnE,OAAA;cAAAuH,QAAA,eACAvH,OAAA;gBAAOsH,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBACnCvH,OAAA;kBACI0H,IAAI,EAAC,UAAU;kBACf7B,OAAO,EAAE7D,YAAa;kBACtB2F,QAAQ,EAAE/B,gBAAiB;kBAC3BgC,IAAI,EAAC;gBAAc;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACFnE,OAAA;kBAAMsH,SAAS,EAAC;gBAAQ;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnE,OAAA,CAACjC,GAAG;YAACuJ,SAAS,EAAC,qBAAqB;YACnCO,EAAE,EAAE;cACHC,OAAO,EAAE5F,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,GAAE,GAAG,GAAG,CAAC;cACtF2F,MAAM,EAAC7F,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,GAAC,aAAa,GAAC;YAC3F,CAAE;YAAAmF,QAAA,gBAEFvH,OAAA,CAAChC,UAAU;cAACsJ,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EACtC/G,SAAS,CAAC,UAAU;YAAC;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACrCnE,OAAA,CAAC9B,IAAI;cAAC8J,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAV,QAAA,gBAChCvH,OAAA,CAAC9B,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfvH,OAAA,CAAC7B,UAAU;kBACTsJ,IAAI,EAAC,OAAO;kBACZW,QAAQ,EAAEpG,YAAa;kBACvB6F,EAAE,EAAE;oBACFC,OAAO,EAAElF,gBAAgB,KAAKe,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACzD;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPnE,OAAA,CAAC9B,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfvH,OAAA,CAAC7B,UAAU;kBACTsJ,IAAI,EAAC,OAAO;kBACZ7C,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAM;kBACvBoD,OAAO,EAAEA,CAAA,KAAM;oBAEb;oBACA7F,kBAAkB,CAACgC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;kBACxC,CAAE;kBACPgE,QAAQ,EAAEpG,YAAY,IAAIE,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAU;kBAC/FiG,aAAa;kBACRR,EAAE,EAAE;oBACFC,OAAO,EAAEpG,eAAe,KAAKiC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACxD,CAAE;kBAAAmD,QAAA,EAEArF,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBACpEpC,OAAA;oBAAMqE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE5E;oBAAW,CAAE;oBAAEkF,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAElFnE,OAAA,CAACtB,aAAa;oBAACqF,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAClC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPnE,OAAA,CAAC9B,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfvH,OAAA,CAAC7B,UAAU;kBACTsJ,IAAI,EAAC,OAAO;kBACZW,QAAQ,EAAEpG,YAAa;kBACvB6F,EAAE,EAAE;oBACFC,OAAO,EAAElF,gBAAgB,KAAKe,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACzD;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPnE,OAAA,CAAC9B,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfvH,OAAA,CAAC7B,UAAU;kBACTsJ,IAAI,EAAC,OAAO;kBACZ7C,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAM;kBACvBoD,OAAO,EAAEA,CAAA,KAAM;oBACd;oBACC7F,kBAAkB,CAACgC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;kBACxC,CAAE;kBACFgE,QAAQ,EAAEpG,YAAa;kBAC1BqG,aAAa;kBACVR,EAAE,EAAE;oBACFC,OAAO,EAAEpG,eAAe,KAAKiC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACxD,CAAE;kBAAAmD,QAAA,EAEArF,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBACpEpC,OAAA;oBAAMqE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE9E;oBAAY,CAAE;oBAACoF,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAElFnE,OAAA,CAAClB,cAAc;oBAACiF,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACnC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPnE,OAAA,CAAC9B,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfvH,OAAA,CAAC7B,UAAU;kBACTsJ,IAAI,EAAC,OAAO;kBACZ7C,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAM;kBACvBoD,OAAO,EAAEA,CAAA,KAAM;oBACd;oBACC7F,kBAAkB,CAACgC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;kBACxC,CAAE;kBACEgE,QAAQ,EAAEpG,YAAa;kBACvBqG,aAAa;kBACjBR,EAAE,EAAE;oBACNC,OAAO,EAAEpG,eAAe,KAAKiC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG,GAAG;oBACzDkE,WAAW,EAAC;kBACV,CAAE;kBAAAf,QAAA,EAEArF,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBACpEpC,OAAA;oBAAMqE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE1E;oBAAc,CAAE;oBAACgF,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,gBAEnFnE,OAAA,CAAChB,gBAAgB;oBAAC+E,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACrC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPnE,OAAA,CAAC9B,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfvH,OAAA,CAAC7B,UAAU;kBACTsJ,IAAI,EAAC,OAAO;kBACZ7C,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAM;kBACvBoD,OAAO,EAAEA,CAAA,KAAM;oBACd;oBACC7F,kBAAkB,CAACgC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;kBACxC,CAAE;kBACFgE,QAAQ,EAAEpG,YAAa;kBAC1BqG,aAAa;kBACVR,EAAE,EAAE;oBACFC,OAAO,EAAEpG,eAAe,KAAKiC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACxD,CAAE;kBAAAmD,QAAA,EAEArF,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBACpEpC,OAAA;oBAAMqE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE7E;oBAAa,CAAE;oBAAEmF,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEpFnE,OAAA,CAACd,eAAe;oBAAC6E,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACpC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPnE,OAAA,CAAC9B,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfvH,OAAA,CAAC7B,UAAU;kBACTsJ,IAAI,EAAC,OAAO;kBACZW,QAAQ,EAAEpG,YAAa;kBACvB6F,EAAE,EAAE;oBACFC,OAAO,EAAElF,gBAAgB,KAAKe,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACzD;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPnE,OAAA,CAAC9B,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfvH,OAAA,CAAC7B,UAAU;kBACTsJ,IAAI,EAAC,OAAO;kBACZ7C,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAM;kBACvBoD,OAAO,EAAEA,CAAA,KAAM;oBACd;oBACC7F,kBAAkB,CAACgC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;kBACxC,CAAE;kBACFgE,QAAQ,EAAEpG,YAAa;kBAC1BqG,aAAa;kBACVR,EAAE,EAAE;oBACFC,OAAO,EAAEpG,eAAe,KAAKiC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACxD,CAAE;kBAAAmD,QAAA,EAEArF,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBACpEpC,OAAA;oBAAMqE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE3E;oBAAc,CAAE;oBAACiF,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEpFnE,OAAA,CAACZ,gBAAgB;oBAAC2E,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACrC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPnE,OAAA,CAAC9B,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACfvH,OAAA,CAAC7B,UAAU;kBACTsJ,IAAI,EAAC,OAAO;kBACZW,QAAQ,EAAEpG,YAAa;kBACvB6F,EAAE,EAAE;oBACFC,OAAO,EAAElF,gBAAgB,KAAKe,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACzD;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGNnE,OAAA,CAACjC,GAAG;YAACuJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCvH,OAAA;cAAKsH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GAAC,IAAE,EAAC/G,SAAS,CAAC,aAAa,CAAC;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEnE,OAAA;cAAAuH,QAAA,eACAvH,OAAA,CAAC/B,SAAS;gBACTsK,OAAO,EAAC,UAAU;gBAClBnE,KAAK,EAAEwC,gCAAgC,CAAChG,YAAY,CAAE;gBAEtD6G,IAAI,EAAC,OAAO;gBACZH,SAAS,EAAC,qBAAqB;gBAC/BK,QAAQ,EAAGlD,CAAC,IAAKhE,eAAe,CAAC,GAAG4G,QAAQ,CAAC5C,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC,IAAI,CAAC,IAAI,CAAE;gBACvEoE,UAAU,EAAE;kBACXC,YAAY,EAAE,IAAI;kBAClBZ,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM;kBAE5B;gBACD,CAAE;gBACFN,QAAQ,EAAEpG;cAAa;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnE,OAAA,CAACjC,GAAG;YAACuJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCvH,OAAA;cAAKsH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GAAC,IAAE,EAAC/G,SAAS,CAAC,aAAa,CAAC;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEnE,OAAA;cAAAuH,QAAA,eACAvH,OAAA,CAAC/B,SAAS;gBACTsK,OAAO,EAAC,UAAU;gBAClBnE,KAAK,EAAEwC,gCAAgC,CAAC/F,YAAY,CAAE;gBAEtD4G,IAAI,EAAC,OAAO;gBACZH,SAAS,EAAC,qBAAqB;gBAC/BK,QAAQ,EAAGlD,CAAC,IAAK/D,eAAe,CAAC,GAAG2G,QAAQ,CAAC5C,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC,IAAI,CAAC,IAAI,CAAE;gBACvEoE,UAAU,EAAE;kBACXC,YAAY,EAAE,IAAI;kBAClBZ,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM;kBAE5B;gBACD,CAAE;gBACFN,QAAQ,EAAEpG;cAAa;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnE,OAAA,CAACjC,GAAG;YACHuJ,SAAS,EAAC,mBAAmB;YAC7B/C,KAAK,EAAE;cAAEoE,aAAa,EAAE,QAAQ;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAArB,QAAA,eAEnDvH,OAAA,CAACjC,GAAG;cAACwG,KAAK,EAAE;gBAAEsE,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE,KAAK;gBAAE9D,KAAK,EAAE;cAAO,CAAE;cAAAsC,QAAA,gBAChFvH,OAAA;gBACCsH,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAG9B/G,SAAS,CAAC,OAAO;cAAC;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACNnE,OAAA;gBAAAuH,QAAA,eACAvH,OAAA,CAAC/B,SAAS;kBACTsK,OAAO,EAAC,UAAU;kBAClBnE,KAAK,EAAEwC,gCAAgC,CAAC9F,YAAY,CAAE;kBAEtD2G,IAAI,EAAC,OAAO;kBACZH,SAAS,EAAC,qBAAqB;kBAC/BK,QAAQ,EAAET,YAAa;kBACvBpE,KAAK,EAAEQ,UAAW;kBAClBkF,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBZ,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEa,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD;gBAAE;kBAAA1E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEF,CAAC,EACLb,UAAU,iBACVtD,OAAA,CAAChC,UAAU;YACVuG,KAAK,EAAE;cACNR,QAAQ,EAAE,MAAM;cAChBiF,KAAK,EAAE,SAAS;cAChBC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACPC,YAAY,EAAE,KAAK;cACnBP,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE;YACb,CAAE;YAAAvB,QAAA,gBAEFvH,OAAA;cAAMuE,KAAK,EAAE;gBAAEsE,OAAO,EAAE,MAAM;gBAAE9E,QAAQ,EAAE,MAAM;gBAAE+E,UAAU,EAAE,QAAQ;gBAAEO,WAAW,EAAC;cAAM,CAAE;cAC3FhF,uBAAuB,EAAE;gBAAEC,MAAM,EAAEzE;cAAQ;YAAE;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,EACD3D,SAAS,CAAC,mCAAmC,CAAC;UAAA;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CACZ,eAGDnE,OAAA,CAACjC,GAAG;YAACuJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCvH,OAAA;cAAKsH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE/G,SAAS,CAAC,SAAS;YAAC;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjEnE,OAAA;cAAAuH,QAAA,eACAvH,OAAA,CAAC/B,SAAS;gBACTsK,OAAO,EAAC,UAAU;gBAClBnE,KAAK,EAAEwC,gCAAgC,CAACvF,cAAc,CAAE;gBAExDoG,IAAI,EAAC,OAAO;gBACZH,SAAS,EAAC,qBAAqB;gBAC/BK,QAAQ,EAAGlD,CAAC,IAAK;kBAChB;kBACA,MAAML,KAAK,GAAGK,CAAC,CAACE,MAAM,CAACP,KAAK;kBAC5B,IAAI,CAAC,SAAS,CAAC+C,IAAI,CAAC/C,KAAK,CAAC,EAAE;oBAC3B;kBACD;kBACA,MAAMgD,UAAU,GAAGC,QAAQ,CAACjD,KAAK,CAAC,IAAI,CAAC;kBACvC;kBACA,IAAIgD,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;oBACtCnE,eAAe,CAAC,IAAI,CAAC;kBACtB,CAAC,MAAM;oBACNA,eAAe,CAAC,KAAK,CAAC;kBACvB;kBACAjC,iBAAiB,CAAC,GAAGoG,UAAU,IAAI,CAAC;gBACrC,CAAE;gBACFoB,UAAU,EAAE;kBACXC,YAAY,EAAE,IAAI;kBAClBZ,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM;kBAE5B;gBACD,CAAE;gBACF5F,KAAK,EAAEE;cAAa;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLnB,YAAY,iBACbhD,OAAA,CAAChC,UAAU;YACXuG,KAAK,EAAE;cACNR,QAAQ,EAAE,MAAM;cAChBiF,KAAK,EAAE,SAAS;cAChBC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACPC,YAAY,EAAE,KAAK;cACnBP,OAAO,EAAE;YACV,CAAE;YAAAtB,QAAA,gBACAvH,OAAA;cAAMuE,KAAK,EAAE;gBAAEsE,OAAO,EAAE,MAAM;gBAAE9E,QAAQ,EAAE,MAAM;gBAAE+E,UAAU,EAAE,QAAQ;gBAAEO,WAAW,EAAC;cAAM,CAAE;cAE9FhF,uBAAuB,EAAE;gBAAEC,MAAM,EAAEzE;cAAQ;YAAE;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,EACE3D,SAAS,CAAC,qCAAqC,CAAC;UAAA;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CACZ,eAEDnE,OAAA,CAACjC,GAAG;YAACuJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCvH,OAAA;cAAKsH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE/G,SAAS,CAAC,eAAe;YAAC;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEnE,OAAA;cAAAuH,QAAA,eACAvH,OAAA,CAAC/B,SAAS;gBACTsK,OAAO,EAAC,UAAU;gBAClBnE,KAAK,EAAEwC,gCAAgC,CAACtF,mBAAmB,CAAE;gBAE7DmG,IAAI,EAAC,OAAO;gBACZH,SAAS,EAAC,qBAAqB;gBAC/BK,QAAQ,EAAGlD,CAAC,IAAK;kBAChB;kBACA,MAAML,KAAK,GAAGK,CAAC,CAACE,MAAM,CAACP,KAAK;kBAC5B,IAAI,CAAC,SAAS,CAAC+C,IAAI,CAAC/C,KAAK,CAAC,EAAE;oBAC3B;kBACD;kBACA,MAAMgD,UAAU,GAAGC,QAAQ,CAACjD,KAAK,CAAC,IAAI,CAAC;kBACvC;kBACA,IAAIgD,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;oBACtCjE,oBAAoB,CAAC,IAAI,CAAC;kBAC3B,CAAC,MAAM;oBACNA,oBAAoB,CAAC,KAAK,CAAC;kBAC5B;kBACAlC,sBAAsB,CAAC,GAAGmG,UAAU,EAAE,CAAC;gBACxC,CAAE;gBACFoB,UAAU,EAAE;kBACXC,YAAY,EAAE,IAAI;kBAClBZ,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM;kBAE5B;gBACD,CAAE;gBACF5F,KAAK,EAAEI;cAAkB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLjB,iBAAiB,iBAClBlD,OAAA,CAAChC,UAAU;YACXuG,KAAK,EAAE;cACNR,QAAQ,EAAE,MAAM;cAChBiF,KAAK,EAAE,SAAS;cAChBC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACPC,YAAY,EAAE,KAAK;cACnBP,OAAO,EAAE;YACV,CAAE;YAAAtB,QAAA,gBACAvH,OAAA;cAAMuE,KAAK,EAAE;gBAAEsE,OAAO,EAAE,MAAM;gBAAE9E,QAAQ,EAAE,MAAM;gBAAE+E,UAAU,EAAE,QAAQ;gBAAEO,WAAW,EAAC;cAAM,CAAE;cAE9FhF,uBAAuB,EAAE;gBAAEC,MAAM,EAAEzE;cAAQ;YAAE;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,EACE3D,SAAS,CAAC,qCAAqC,CAAC;UAAA;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CACZ,eAGPnE,OAAA,CAACjC,GAAG;YAACuJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC3BvH,OAAA;cAAKsH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE/G,SAAS,CAAC,aAAa;YAAC;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1EnE,OAAA;cAAAuH,QAAA,eACEvH,OAAA,CAAC/B,SAAS;gBACRsK,OAAO,EAAC,UAAU;gBAClBnE,KAAK,EAAEwC,gCAAgC,CAACrF,iBAAiB,CAAE;gBAC3DkG,IAAI,EAAC,OAAO;gBACZH,SAAS,EAAC,qBAAqB;gBAC/BK,QAAQ,EAAGlD,CAAC,IAAK;kBACf;kBACA,MAAML,KAAK,GAAGK,CAAC,CAACE,MAAM,CAACP,KAAK;kBAC5B,IAAI,CAAC,SAAS,CAAC+C,IAAI,CAAC/C,KAAK,CAAC,EAAE;oBAC1B;kBACF;kBACA,MAAMgD,UAAU,GAAGC,QAAQ,CAACjD,KAAK,CAAC,IAAI,CAAC;kBACvC;kBACA,IAAIgD,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;oBACpC/D,kBAAkB,CAAC,IAAI,CAAC;kBAC1B,CAAC,MAAM;oBACLA,kBAAkB,CAAC,KAAK,CAAC;kBAC3B;kBACAnC,oBAAoB,CAAC,GAAGkG,UAAU,IAAI,CAAC;gBACzC,CAAE;gBACFoB,UAAU,EAAE;kBACVC,YAAY,EAAE,IAAI;kBAClBZ,EAAE,EAAE;oBACF,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAE;sBAAEA,MAAM,EAAE;oBAAO;kBACjC;gBACF,CAAE;gBACF5F,KAAK,EAAEM;cAAgB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLf,eAAe,iBACdpD,OAAA,CAAChC,UAAU;YACTuG,KAAK,EAAE;cACLR,QAAQ,EAAE,MAAM;cAChBiF,KAAK,EAAE,SAAS;cAChBC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACPC,YAAY,EAAE,KAAK;cACnBP,OAAO,EAAE;YACX,CAAE;YAAAtB,QAAA,gBAEFvH,OAAA;cACEuE,KAAK,EAAE;gBAAEsE,OAAO,EAAE,MAAM;gBAAE9E,QAAQ,EAAE,MAAM;gBAAE+E,UAAU,EAAE,QAAQ;gBAAEO,WAAW,EAAE;cAAM,CAAE;cACvFhF,uBAAuB,EAAE;gBAAEC,MAAM,EAAEzE;cAAQ;YAAE;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,sCAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb,eA2FKnE,OAAA,CAACjC,GAAG;YAACuJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCvH,OAAA;cAAKsH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE/G,SAAS,CAAC,QAAQ;YAAC;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChEnE,OAAA;cAAAuH,QAAA,eACAvH,OAAA;gBACC0H,IAAI,EAAC,OAAO;gBACZtD,KAAK,EAAEZ,eAAe,IAAI,SAAU;gBACpCmE,QAAQ,EAAEnC,uBAAwB;gBAClC8B,SAAS,EAAC;cAAmB;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnE,OAAA,CAACjC,GAAG;YAACuJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCvH,OAAA;cAAKsH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE/G,SAAS,CAAC,YAAY;YAAC;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpEnE,OAAA;cAAAuH,QAAA,eACAvH,OAAA;gBACC0H,IAAI,EAAC,OAAO;gBACZtD,KAAK,EAAE3C,sBAAuB;gBAC9BkG,QAAQ,EAAEjC,2BAA4B;gBACtC4B,SAAS,EAAC;cAAmB;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNnE,OAAA;QAAKsH,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACnCvH,OAAA,CAAC5B,MAAM;UACPmK,OAAO,EAAC,WAAW;UACnBf,OAAO,EAAEzB,kBAAmB;UAC5BuB,SAAS,EAAE,aAAatE,YAAY,IAAIE,iBAAiB,IAAIE,eAAe,GAAG,UAAU,GAAG,EAAE,EAAG;UACjGgF,QAAQ,EAAEpF,YAAY,IAAIE,iBAAiB,IAAIE,eAAgB,CAAC;UAAA;UAAAmE,QAAA,EAE7D/G,SAAS,CAAC,OAAO;QAAC;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAER,CAAC;AAAC9D,EAAA,CAvzBIJ,qBAAqB;EAAA,QACDH,cAAc,EAgCnCR,cAAc;AAAA;AAAAgK,EAAA,GAjCbrJ,qBAAqB;AAyzB3B,eAAeA,qBAAqB;AAAC,IAAAqJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}