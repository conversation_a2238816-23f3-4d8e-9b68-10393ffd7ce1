{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import useDrawerStore from\"../../store/drawerStore\";import\"./Tooltip.css\";import{Tooltip,tooltipClasses,MobileStepper}from\"@mui/material\";import{styled}from\"@mui/material/styles\";import TooltipBody from\"./TooltipBody\";import{CustomCursor}from\"../../assets/icons/icons\";import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";export const TOOLTIP_MX_WIDTH=500;export const TOOLTIP_MN_WIDTH=300;export const TOOLTIP_HEIGHT=500;export const EXTENSION_PART=\"extension-part\";export const CustomWidthTooltip=styled(_ref=>{let{className,selectedTemplate,selectedTemplateTour,...props}=_ref;const{toolTipGuideMetaData,currentStep,elementSelected}=useDrawerStore();useEffect(()=>{if((selectedTemplate===\"Tooltip\"||selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Tooltip\"||selectedTemplateTour===\"Hotspot\")&&elementSelected){document.body.style.overflow=\"hidden\";}else{document.body.style.overflow=\"\";}// Cleanup\nreturn()=>{document.body.style.overflow=\"\";};},[selectedTemplate,selectedTemplateTour,elementSelected]);return/*#__PURE__*/_jsx(Tooltip,{...props,classes:{popper:className},id:\"Tooltip-unique\",disableHoverListener:true,disableTouchListener:true,PopperProps:{className:\"qadpt-tlprte\",sx:{top:selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\"?\"-110px !important\":\"-160px !important\",zIndex:'99999 !important'}}});})(_ref2=>{var _toolTipGuideMetaData,_toolTipGuideMetaData2,_toolTipGuideMetaData3,_toolTipGuideMetaData4,_toolTipGuideMetaData5,_toolTipGuideMetaData6,_toolTipGuideMetaData7,_toolTipGuideMetaData8,_toolTipGuideMetaData9,_toolTipGuideMetaData10,_toolTipGuideMetaData11,_toolTipGuideMetaData12,_toolTipGuideMetaData13,_toolTipGuideMetaData14;let{selectedTemplate}=_ref2;const{toolTipGuideMetaData,currentStep}=useDrawerStore();return{[`& .${tooltipClasses.tooltip}`]:{// maxWidth: TOOLTIP_MX_WIDTH,\nminWidth:TOOLTIP_MN_WIDTH,width:`${(_toolTipGuideMetaData=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData===void 0?void 0:(_toolTipGuideMetaData2=_toolTipGuideMetaData.canvas)===null||_toolTipGuideMetaData2===void 0?void 0:_toolTipGuideMetaData2.width} !important`||\"250px !important\",maxWidth:`${(_toolTipGuideMetaData3=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData3===void 0?void 0:(_toolTipGuideMetaData4=_toolTipGuideMetaData3.canvas)===null||_toolTipGuideMetaData4===void 0?void 0:_toolTipGuideMetaData4.width} !important`||\"800px !important\",backgroundColor:`${(_toolTipGuideMetaData5=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData5===void 0?void 0:(_toolTipGuideMetaData6=_toolTipGuideMetaData5.canvas)===null||_toolTipGuideMetaData6===void 0?void 0:_toolTipGuideMetaData6.backgroundColor}`||\"#fff\",color:\"transparent\",fontSize:\"14px\",borderRadius:`${(_toolTipGuideMetaData7=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData7===void 0?void 0:(_toolTipGuideMetaData8=_toolTipGuideMetaData7.canvas)===null||_toolTipGuideMetaData8===void 0?void 0:_toolTipGuideMetaData8.borderRadius} !important`,position:\"relative !important\",padding:`${(_toolTipGuideMetaData9=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData9===void 0?void 0:(_toolTipGuideMetaData10=_toolTipGuideMetaData9.canvas)===null||_toolTipGuideMetaData10===void 0?void 0:_toolTipGuideMetaData10.padding}`||\"2px\",boxShadow:\"9px 0px 10px 1px #00000033, 0px 0px 0px 0px #00000024, -4px 2px 10px 1px #0000001f\"// Adds a smooth shadow\n},[`& .${tooltipClasses.arrow}`]:{color:\"white\"// Match arrow color with tooltip background\n}};return{[`& .${tooltipClasses.tooltip}`]:{// maxWidth: TOOLTIP_MX_WIDTH,\nminWidth:TOOLTIP_MN_WIDTH,maxWidth:`${(_toolTipGuideMetaData11=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData11===void 0?void 0:(_toolTipGuideMetaData12=_toolTipGuideMetaData11.canvas)===null||_toolTipGuideMetaData12===void 0?void 0:_toolTipGuideMetaData12.width} !important`||\"500px !important\",backgroundColor:\"white\",color:\"transparent\",fontSize:\"14px\",borderRadius:`${((_toolTipGuideMetaData13=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData13===void 0?void 0:(_toolTipGuideMetaData14=_toolTipGuideMetaData13.canvas)===null||_toolTipGuideMetaData14===void 0?void 0:_toolTipGuideMetaData14.borderRadius)||\"8px\"} !important`,position:\"relative !important\",padding:\"0px !important\",boxShadow:\"9px 0px 10px 1px #00000033, 0px 0px 0px 0px #00000024, -4px 2px 10px 1px #0000001f\"// Adds a smooth shadow\n},[`& .${tooltipClasses.arrow}`]:{color:\"white\"// Match arrow color with tooltip background\n}};});const CreateTooltip=_ref3=>{var _toolTipGuideMetaData16;let{isTooltipNameScreenOpen,isUnSavedChanges,openWarning,setopenWarning,handleLeave,updatedGuideData}=_ref3;const{selectedTemplate,setTooltip,tooltip,elementSelected,setElementSelected,guideName,borderColor,backgroundColor,borderSize,borderRadius,padding,width,setXpathToTooltipMetaData,// updatedCanvasSettings,\ntooltipXaxis,tooltipYaxis,tooltipWidth,setTooltipWidth,setTooltipPadding,setTooltipBorderradius,setTooltipBordersize,setTooltipBordercolor,setTooltipBackgroundcolor,tooltippadding,tooltipborderradius,tooltipbordersize,tooltipBordercolor,tooltipBackgroundcolor,tooltipPosition,setTooltipPosition,selectedOption,steps,currentStepIndex,openTooltip,setOpenTooltip,setCurrentStepIndex,HotspotSettings,toolTipGuideMetaData,hotspotGuideMetaData,currentStep,isPopoverOpen,setIsPopoverOpen,setAxisData,currentGuideId,axisData,isALTKeywordEnabled,setIsALTKeywordEnabled,currentHoveredElement,setCurrentHoveredElement,selectedTemplateTour,isGuideInfoScreen,isCollapsed,createWithAI,syncAITooltipContainerData}=useDrawerStore(state=>state);const[hoveredElement,setHoveredElement]=useState(null);const[rectData,setRectData]=useState(null);// State to store rect object\nconst[overlayPosition,setOverlayPosition]=useState(null);const[popupPosition,setPopupPosition]=useState(null);const getXPath=element=>{const isUnique=(attr,value,tagName)=>{const parent=element===null||element===void 0?void 0:element.parentNode;if(!parent)return false;const matchingElements=[...Array.from(parent.querySelectorAll(`${tagName}[${attr}=\"${value}\"]`))];return matchingElements.length===1;};const getUniqueSelector=el=>{if(!el)return null;const tagName=el.tagName.toLowerCase();// find with event attributes\nconst eventAttributes=[...Array.from(el.attributes)].filter(attr=>attr.name.startsWith(\"on\"));for(const attr of eventAttributes){if(isUnique(attr.name,attr.value,tagName)){return`//${tagName}[@${attr.name}=\"${attr.value}\"]`;}}return null;};const getSegment=el=>{if(!el)return null;const tagName=el.tagName.toLowerCase();const uniqueSelector=getUniqueSelector(el);const bannercontainer=document.querySelector(\".quickAdopt_banner\");if(bannercontainer){if(el.closest(\".quickAdopt_banner\")){return null;}}if(uniqueSelector)return uniqueSelector;let siblingIndex=1;let sibling=el.previousElementSibling;while(sibling){if(sibling.tagName===el.tagName)siblingIndex++;sibling=sibling.previousElementSibling;}return`${tagName}[${siblingIndex}]`;};const segments=[];let currentElement=element;while(currentElement&&currentElement!==document.body&&currentElement!==document.documentElement){const segment=getSegment(currentElement);if(!segment)break;segments.unshift(segment);if(segment.startsWith(\"//*[@\"))break;currentElement=currentElement.parentElement;}if(!segments[0].startsWith(\"//\")){if(segments[0]!==\"html\"){segments.unshift(\"html\");}if(segments[1]!==\"body\"){segments.splice(1,0,\"body\");}}return`${segments.join(\"/\")}`;};const getRelativeXPath=element=>{// Check if element is valid\nif(!element||!element.tagName)return null;const isUnique=xpath=>{try{const matchingElements=document.evaluate(xpath,document,null,XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,null);return matchingElements.snapshotLength===1;}catch(e){return false;}};const isHighlightStyle=styleValue=>{// Match your specific highlighting styles\nreturn styleValue.includes(\"outline: 2px solid #6c97a6\")||styleValue.includes(\"pointer-events: none\")||styleValue.includes(\"pointer-events:\");};const isHighlightClass=className=>{// Check for your highlighting class\nreturn className===\"quickAdopt-selection\";};const getUniqueSelector=el=>{const tagName=el.tagName.toLowerCase();// Priority 1: ID\nif(el.id){const xpath=`//${tagName}[@id=\"${el.id}\"]`;if(isUnique(xpath))return xpath;}// Priority 2: Class names (excluding highlight class)\nif(el.className){const classes=el.className.trim().split(/\\s+/).filter(cls=>!isHighlightClass(cls));for(const className of classes){if(className){const xpath=`//${tagName}[contains(@class, \"${className}\")]`;if(isUnique(xpath))return xpath;}}}// Priority 3: Event attributes\nconst eventAttributes=[...Array.from(el.attributes)].filter(attr=>attr.name.startsWith(\"on\"));for(const attr of eventAttributes){const xpath=`//${tagName}[@${attr.name}=\"${attr.value}\"]`;if(isUnique(xpath))return xpath;}// Priority 4: Other attributes (excluding highlight style and disabled)\nconst attributes=[...Array.from(el.attributes)].filter(attr=>attr.name!==\"id\"&&attr.name!==\"class\"&&!attr.name.startsWith(\"on\")&&attr.name!==\"disabled\"&&!(attr.name===\"style\"&&isHighlightStyle(attr.value)));for(const attr of attributes){const xpath=`//${tagName}[@${attr.name}=\"${attr.value}\"]`;if(isUnique(xpath))return xpath;}return null;};let currentElement=element;let pathSegments=[];while(currentElement&&currentElement!==document.documentElement){var _currentElement$paren;const uniqueSelector=getUniqueSelector(currentElement);if(uniqueSelector){return uniqueSelector;}// Build positional path if no unique selector is found\nconst tagName=currentElement.tagName.toLowerCase();const siblings=Array.from(((_currentElement$paren=currentElement.parentElement)===null||_currentElement$paren===void 0?void 0:_currentElement$paren.children)||[]).filter(sib=>sib.tagName===currentElement.tagName);const index=siblings.indexOf(currentElement)+1;pathSegments.unshift(index>1?`${tagName}[${index}]`:tagName);currentElement=currentElement.parentElement;}// Check for banner container (from your original code)\nconst bannerContainer=document.querySelector(\".quickAdopt_banner\");if(bannerContainer&&element.closest(\".quickAdopt_banner\")){return null;}return`//${pathSegments.join(\"/\")}`;};// Example usage:\n// const element = document.querySelector('some-element');\n// const relativeXPath = getRelativeXPath(element);\n// console.log(relativeXPath);\nconst shouldIgnoreHighlight=element=>{return element.classList.contains(\"mdc-tooltip__surface\")||element.classList.contains(\"mdc-tooltip__surface-animation\")||element.classList.contains(\"mdc-tooltip\")||element.classList.contains(\"mdc-tooltip--shown\")||element.classList.contains(\"mdc-tooltip--showing\")||element.classList.contains(\"mdc-tooltip--hiding\")||element.getAttribute(\"role\")===\"tooltip\"||element.closest(\"#Tooltip-unique\")||element.closest(\"#my-react-drawer\")||element.closest(\"#tooltip-section-popover\")||element.closest(\"#btn-setting-toolbar\")||element.closest(\"#button-toolbar\")||element.closest(\"#color-picker\")||element.closest(\".qadpt-ext-banner\")||element.closest(\"#leftDrawer\")||element.closest(\"#image-popover\")||element.closest(\"#toggle-fit\")||element.closest(\"#color-popover\")||element.closest(\"#rte-popover\")||element.closest(\"#rte-alignment\")||element.closest(\"#rte-alignment-menu\")||element.closest(\"#rte-font\")||element.closest(\"#rte-bold\")||element.closest(\"#rte-italic\")||element.closest(\"#rte-underline\")||element.closest(\"#rte-strke-through\")||element.closest(\"#rte-alignment-menu-items\")||element.closest(\"#rte-more\")||element.closest(\"#rte-text-color\")||element.closest(\"#rte-text-color-popover\")||element.closest(\"#rte-text-highlight\")||element.closest(\"#rte-text-highlight-pop\")||element.closest(\"#rte-text-heading\")||element.closest(\"#rte-text-heading-menu-items\")||element.closest(\"#rte-text-format\")||element.closest(\"#rte-text-ul\")||element.closest(\"#rte-text-hyperlink\")||element.closest(\"#rte-video\")||element.closest(\"#rte-clear-formatting\")||element.closest(\"#rte-hyperlink-popover\")||element.closest(\"#rte-box\")||element.closest(element.id.startsWith(\"#rt-editor\")?`${element.id}`:\"nope\")||element.closest(\"#rte-placeholder\")||element.closest(\"#qadpt-designpopup\")||element.closest(\"#image-properties\")||element.closest(\"#rte-toolbar\")||element.closest(\"#tooltipdialog\")||element.closest(\"#rte-toolbar-paper\")||element.closest(\"#rte-alignment-menu\")||element.closest(\"#rte-alignment-menu-items\");};const shouldIgnoreEvents=element=>{return element.classList.contains(\"mdc-tooltip__surface\")||element.classList.contains(\"mdc-tooltip__surface-animation\")||element.classList.contains(\"mdc-tooltip\")||element.classList.contains(\"mdc-tooltip--shown\")||element.classList.contains(\"mdc-tooltip--showing\")||element.classList.contains(\"mdc-tooltip--hiding\")||element.getAttribute(\"role\")===\"tooltip\"||element.closest(\"#Tooltip-unique\")||element.closest(\"#tooltip-section-popover\")||element.closest(\"#btn-setting-toolbar\")||element.closest(\"#button-toolbar\")||element.closest(\"#color-picker\")||element.closest(\".qadpt-ext-banner\")||element.closest(\"#leftDrawer\")||element.closest(\"#rte-popover\")||element.closest(element.id.startsWith(\"#rt-editor\")?`${element.id}`:\"nope\")||element.closest(\"#rte-box\")||element.closest(\"#rte-placeholder\")||element.closest(\"#rte-alignment-menu-items\")||element.closest(\"#qadpt-designpopup\");};let hotspot;const altElementBorderRemove=()=>{const previousSelectedDOM=document.querySelector(\".quickAdopt-selection\");if(previousSelectedDOM){setTimeout(()=>{previousSelectedDOM.style.outline=\"none\";//previousSelectedDOM.style.backgroundColor = \"\";\npreviousSelectedDOM.style.boxShadow=\"\";previousSelectedDOM.style.pointerEvents=\"\";previousSelectedDOM.removeAttribute(\"disabled\");previousSelectedDOM.classList.remove(\"quickAdopt-selection\");},10);}};const removeAllElementHighlights=()=>{// Remove all elements with quickAdopt-selection class\nconst highlightedElements=document.querySelectorAll('.quickAdopt-selection');highlightedElements.forEach(element=>{const htmlElement=element;htmlElement.style.outline='';//htmlElement.style.backgroundColor = '';\nhtmlElement.style.boxShadow='';htmlElement.style.pointerEvents='';htmlElement.removeAttribute('disabled');htmlElement.classList.remove('quickAdopt-selection');});// Remove any elements with data-target-element attribute\nconst targetElements=document.querySelectorAll('[data-target-element=\"true\"]');targetElements.forEach(element=>{const htmlElement=element;htmlElement.style.outline='';//htmlElement.style.backgroundColor = '';\nhtmlElement.style.boxShadow='';htmlElement.style.pointerEvents='';htmlElement.removeAttribute('disabled');htmlElement.removeAttribute('data-target-element');});// Remove any elements with quickadapt highlighting attributes\nconst quickAdaptElements=document.querySelectorAll('[data-quickadapt-highlighted=\"true\"]');quickAdaptElements.forEach(element=>{const htmlElement=element;htmlElement.style.outline='';htmlElement.style.outlineOffset='';//htmlElement.style.backgroundColor = '';\nhtmlElement.style.boxShadow='';htmlElement.style.pointerEvents='';htmlElement.removeAttribute('disabled');htmlElement.removeAttribute('data-quickadapt-highlighted');});};const removeAppliedStyleOfEle=element=>{element.removeAttribute(\"disabled\");element.style.outline=\"\";element.style.pointerEvents=\"unset\";};// Custom cursor utility functions\nconst createCustomCursorDataURL=()=>{// Create a data URL from the SVG\nconst svgString=CustomCursor;const encodedSvg=encodeURIComponent(svgString);return`data:image/svg+xml,${encodedSvg}`;};const applyCustomCursor=()=>{const cursorDataURL=createCustomCursorDataURL();// Apply cursor to document body and html to ensure it covers the entire page\n// Using 16 16 as hotspot (center of 32x32 cursor)\ndocument.body.style.cursor=`url(\"${cursorDataURL}\") 16 16, crosshair`;document.documentElement.style.cursor=`url(\"${cursorDataURL}\") 16 16, crosshair`;// Also apply to all elements that might override the cursor, but exclude extension UI\nconst style=document.createElement('style');style.id='quickadapt-custom-cursor-style';style.textContent=`\n\t\t\t* {\n\t\t\t\tcursor: url(\"${cursorDataURL}\") 16 16, crosshair !important;\n\t\t\t}\n\t\t\t/* Exclude extension UI elements from custom cursor */\n\t\t\t#my-react-drawer,\n\t\t\t#my-react-drawer *,\n\t\t\t.MuiDrawer-root,\n\t\t\t.MuiDrawer-root *,\n\t\t\t.MuiDialog-root,\n\t\t\t.MuiDialog-root *,\n\t\t\t.MuiPopover-root,\n\t\t\t.MuiPopover-root *,\n\t\t\t.MuiTooltip-popper,\n\t\t\t.MuiTooltip-popper *,\n\t\t\t.qadpt-ext-banner,\n\t\t\t.qadpt-ext-banner *,\n\t\t\t#leftDrawer,\n\t\t\t#leftDrawer *,\n\t\t\t#rte-popover,\n\t\t\t#rte-popover *,\n\t\t\t#rte-box,\n\t\t\t#rte-box *,\n\t\t\t#qadpt-designpopup,\n\t\t\t#qadpt-designpopup *,\n\t\t\t.quickadapt-no-custom-cursor,\n\t\t\t.quickadapt-no-custom-cursor * {\n\t\t\t\tcursor: default !important;\n\t\t\t}\n\t\t`;document.head.appendChild(style);};const removeCustomCursor=()=>{// Remove custom cursor from body and html\ndocument.body.style.cursor='';document.documentElement.style.cursor='';// Remove the custom cursor style\nconst existingStyle=document.getElementById('quickadapt-custom-cursor-style');if(existingStyle){existingStyle.remove();}};useEffect(()=>{let isElementHover=false;const handleMouseOver=event=>{if(isALTKeywordEnabled&&currentHoveredElement&&!isTooltipNameScreenOpen&&!isCollapsed){const element=event.target;setCurrentHoveredElement(element);if(!shouldIgnoreHighlight(element)){altElementBorderRemove();const rect=element.getBoundingClientRect();const overlay=document.createElement(\"div\");overlay.className=\"quickAdopt-overlay\";Object.assign(overlay.style,{position:\"fixed\",top:`${rect.top+window.scrollY}px`,left:`${rect.left+window.scrollX}px`,width:`${rect.width}px`,height:`${rect.height}px`,backgroundColor:\"rgba(108, 151, 166, 0.2)\",outline:\"2px dashed #6c97a6\",boxShadow:\"0 0 0 3px transparent\",pointerEvents:\"none\",zIndex:\"9999\"});overlay.setAttribute(\"data-overlay\",\"true\");document.body.appendChild(overlay);// Optional: Remove overlay on mouseout\nelement.addEventListener(\"mouseout\",()=>{document.querySelectorAll(\"[data-overlay='true']\").forEach(el=>el.remove());},{once:true});}}};const handleMouseOut=event=>{const element=event.target;setCurrentHoveredElement(element);if(!shouldIgnoreHighlight(element)){if([\"input\",\"select\",\"textarea\",\"button\",\"a\",\"select\"].includes(element.tagName.toLowerCase())){element.style.pointerEvents=\"\";}element.removeAttribute(\"disabled\");element.style.outline=\"\";//element.style.backgroundColor = \"\";\nelement.style.boxShadow=\"\";isElementHover=false;}};const existingHotspot=document.getElementById(\"hotspotBlinkCreation\");const handleClick=event=>{if(!isGuideInfoScreen&&!isTooltipNameScreenOpen&&!isCollapsed){const element=event.target;event.stopPropagation();event.stopImmediatePropagation();event.preventDefault();event.stopImmediatePropagation();setElementSelected(true);if(isALTKeywordEnabled){if(!shouldIgnoreHighlight(element)&&!shouldIgnoreEvents(element)){if(!element.hasAttribute(\"data-target-element\")){element.setAttribute(\"data-target-element\",\"true\");}const xpath=getXPath(element);//element.classList.add(\"\");\nelement.style.outline=\"\";const getRelativexpath=getRelativeXPath(element);//element.style.position = \"relative\";\n//element.style.backgroundColor = \"rgba(108, 151, 166, 0.2)\";\nelement.style.outline=\"2px dashed #6c97a6\";element.style.boxShadow=\"0 0 0 3px transparent\";console.log(getRelativexpath);const rect=element.getBoundingClientRect();setPopupPosition({left:`${rect.left}px`,top:`${element.offsetHeight+window.scrollY}px`});setRectData(rect);setAxisData(rect);if(![\"BODY\",\"HTML\"].includes(element.tagName)){const tooltipPosition={x:rect.left+5,y:rect.top};setTooltip({visible:true,text:xpath,position:{x:rect.left+5,y:rect.top}});if(xpath){setXpathToTooltipMetaData({value:xpath,PossibleElementPath:getRelativexpath,position:tooltipPosition});// For AI-created tooltips, sync the data after element selection\nif(createWithAI&&selectedTemplate===\"Tooltip\"){// Use setTimeout to ensure the xpath data is set before syncing\nsetTimeout(()=>{syncAITooltipContainerData();},0);}}setOpenTooltip(true);if(!existingHotspot){applyHotspotProperties(rect);}}}}}};const handleDocumentClick=event=>{const target=event.target;if(isALTKeywordEnabled&&!isGuideInfoScreen){if(!shouldIgnoreHighlight(target)&&!shouldIgnoreEvents(target)){handleClick(event);}}};if(rectData&&existingHotspot){applyHotspotProperties(rectData);}if(!isALTKeywordEnabled&&currentHoveredElement){// Clean up all highlighted elements when element selection is disabled\nremoveAllElementHighlights();// Remove custom cursor when switching to page interaction mode\nremoveCustomCursor();document.removeEventListener(\"mouseover\",handleMouseOver);document.removeEventListener(\"mouseout\",handleMouseOut);document.removeEventListener(\"click\",handleDocumentClick,true);return;}else if(elementSelected===false&&(selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Hotspot\")){// Apply custom cursor for hotspot element selection\nif(isALTKeywordEnabled){applyCustomCursor();}document.addEventListener(\"mouseover\",handleMouseOver);document.addEventListener(\"mouseout\",handleMouseOut);document.addEventListener(\"click\",handleDocumentClick,true);}else if(!elementSelected&&(selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\")&&isALTKeywordEnabled){// Apply custom cursor for tooltip element selection\napplyCustomCursor();document.addEventListener(\"mouseover\",handleMouseOver);document.addEventListener(\"mouseout\",handleMouseOut);document.addEventListener(\"click\",handleDocumentClick,true);setElementSelected(false);}return()=>{document.removeEventListener(\"mouseover\",handleMouseOver);document.removeEventListener(\"mouseout\",handleMouseOut);document.removeEventListener(\"click\",handleDocumentClick,true);// Clean up custom cursor on component unmount\nremoveCustomCursor();};},[toolTipGuideMetaData,elementSelected,borderSize,isALTKeywordEnabled,currentHoveredElement]);// Separate useEffect to handle custom cursor based on ALT keyword enabled state\nuseEffect(()=>{if(isALTKeywordEnabled&&!elementSelected&&(selectedTemplate===\"Tooltip\"||selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Tooltip\"||selectedTemplateTour===\"Hotspot\")){// Apply custom cursor when in element selection mode\napplyCustomCursor();}else{// Remove custom cursor when not in element selection mode\nremoveCustomCursor();}// Cleanup on unmount\nreturn()=>{removeCustomCursor();};},[isALTKeywordEnabled,elementSelected,selectedTemplate,selectedTemplateTour]);const generateDynamicId=()=>{return\"hotspotBlinkCreation\";};const applyHotspotProperties=rect=>{var _toolTipGuideMetaData15;const hotspotPropData=toolTipGuideMetaData&&((_toolTipGuideMetaData15=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData15===void 0?void 0:_toolTipGuideMetaData15.hotspots);//const xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\n//const yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\nconst size=hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Size;// const left = rect.left + window.scrollX + xOffset;\n// const top = rect.top + window.scrollY + yOffset;\nconst left=\"375\";const top=\"160\";if(selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Hotspot\"){hotspot=document.getElementById(\"hotspotBlinkCreation\");if(!hotspot){hotspot=document.createElement(\"div\");hotspot.id=generateDynamicId();document.body.appendChild(hotspot);}hotspot.style.position=\"absolute\";hotspot.style.left=`${left}px`;hotspot.style.top=`${top}px`;hotspot.style.width=`${size}px`;hotspot.style.height=`${size}px`;hotspot.style.backgroundColor=hotspotPropData!==null&&hotspotPropData!==void 0&&hotspotPropData.Color?hotspotPropData.Color:\"yellow\";hotspot.style.borderRadius=\"50%\";hotspot.style.zIndex=\"9999\";hotspot.style.transition=\"none\";hotspot.innerHTML=\"\";if((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Type)===\"Info\"||(hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Type)===\"Question\"){const textSpan=document.createElement(\"span\");textSpan.innerText=hotspotPropData.Type===\"Info\"?\"i\":\"?\";textSpan.style.color=\"white\";textSpan.style.fontSize=\"14px\";textSpan.style.fontWeight=\"bold\";textSpan.style.fontStyle=hotspotPropData.Type===\"Info\"?\"italic\":\"normal\";textSpan.style.display=\"flex\";textSpan.style.alignItems=\"center\";textSpan.style.justifyContent=\"center\";textSpan.style.width=\"100%\";textSpan.style.height=\"100%\";textSpan.style.pointerEvents=\"none\";hotspot.appendChild(textSpan);}if(hotspotPropData!==null&&hotspotPropData!==void 0&&hotspotPropData.PulseAnimation){hotspot.classList.add(\"pulse-animation\");if(hotspotPropData!==null&&hotspotPropData!==void 0&&hotspotPropData.stopAnimationUponInteraction){const stopAnimation=()=>{if(hotspot){hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");// Ensure the hotspot remains visible by keeping its styles\nhotspot.style.display=\"flex\";hotspot.style.opacity=\"1\";hotspot.style.transform=\"scale(1)\";// Don't remove event handlers to ensure tooltip still works\n}};// Clear existing event handlers\nhotspot.onclick=null;hotspot.onmouseover=null;// Add appropriate event handlers based on ShowUpon property\nif((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.ShowUpon)===\"Hovering Hotspot\"){hotspot.addEventListener(\"mouseover\",stopAnimation);}else if((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.ShowUpon)===\"Clicking Hotspot\"){hotspot.addEventListener(\"click\",stopAnimation);}else{// Default to click if ShowUpon is not specified\nhotspot.addEventListener(\"click\",stopAnimation);}}}else{hotspot.classList.remove(\"pulse-animation\");}}};// Note: \"Ctrl+Q\" key combination handling is now done in the Drawer component for proper navigation logic\nconst canvasProperties=(_toolTipGuideMetaData16=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData16===void 0?void 0:_toolTipGuideMetaData16.canvas;return/*#__PURE__*/_jsxs(_Fragment,{children:[(selectedTemplate===\"Hotspot\"||selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Hotspot\"||selectedTemplateTour===\"Tooltip\")&&elementSelected===true&&/*#__PURE__*/_jsx(\"div\",{className:\"backdrop\",style:{position:\"fixed\",top:0,left:0,right:0,bottom:0,backgroundColor:\"rgba(0, 0, 0, 0.5)\",zIndex:99999}}),/*#__PURE__*/_jsx(CustomWidthTooltip,{selectedTemplate:selectedTemplate,selectedTemplateTour:selectedTemplateTour,title:/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(TooltipBody,{isPopoverOpen:isPopoverOpen,setIsPopoverOpen:setIsPopoverOpen,popupPosition:popupPosition,isUnSavedChanges:isUnSavedChanges,openWarning:openWarning,setopenWarning:setopenWarning,handleLeave:handleLeave,updatedGuideData:updatedGuideData// isRtl={isRtl}\t\n})}),open:(selectedTemplate===\"Hotspot\"||selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\"||selectedTemplateTour===\"Hotspot\")&&elementSelected===true&&openTooltip,placement:\"bottom\",slotProps:{tooltip:{sx:{borderRadius:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.borderRadius)||tooltipborderradius,border:`${(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.borderSize)||\"0px\"} solid ${(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.borderColor)||tooltipBordercolor}`,backgroundColor:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.backgroundColor)||tooltipBackgroundcolor,padding:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.padding)||tooltippadding,Width:`${(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.width)||\"300px\"} !important`,zIndex:1000// Ensure tooltip is above the backdrop\n}}},children:/*#__PURE__*/_jsx(\"div\",{})})]});};const normalizePx=function(value){let fallback=arguments.length>1&&arguments[1]!==undefined?arguments[1]:\"8px\";if(!value)return fallback;const val=typeof value===\"string\"?value.trim():`${value}`;return val.endsWith(\"px\")?val:`${val}px`;};export default CreateTooltip;const DotsStepper=_ref4=>{let{steps,activeStep}=_ref4;return/*#__PURE__*/_jsx(MobileStepper,{variant:\"dots\",steps:steps,position:\"static\",activeStep:activeStep-1,sx:{maxWidth:400,flexGrow:1,display:\"flex\",justifyContent:\"center\"},nextButton:/*#__PURE__*/_jsx(_Fragment,{}),backButton:/*#__PURE__*/_jsx(_Fragment,{})});};", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDrawerStore", "<PERSON><PERSON><PERSON>", "tooltipClasses", "MobileStepper", "styled", "TooltipBody", "CustomCursor", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "TOOLTIP_MX_WIDTH", "TOOLTIP_MN_WIDTH", "TOOLTIP_HEIGHT", "EXTENSION_PART", "CustomWidthTooltip", "_ref", "className", "selectedTemplate", "selectedTemplateTour", "props", "toolTipGuideMetaData", "currentStep", "elementSelected", "document", "body", "style", "overflow", "classes", "popper", "id", "disableHoverListener", "disableTouch<PERSON><PERSON>ener", "PopperProps", "sx", "top", "zIndex", "_ref2", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "_toolTipGuideMetaData7", "_toolTipGuideMetaData8", "_toolTipGuideMetaData9", "_toolTipGuideMetaData10", "_toolTipGuideMetaData11", "_toolTipGuideMetaData12", "_toolTipGuideMetaData13", "_toolTipGuideMetaData14", "tooltip", "min<PERSON><PERSON><PERSON>", "width", "canvas", "max<PERSON><PERSON><PERSON>", "backgroundColor", "color", "fontSize", "borderRadius", "position", "padding", "boxShadow", "arrow", "CreateTooltip", "_ref3", "_toolTipGuideMetaData16", "isTooltipNameScreenOpen", "isUnSavedChanges", "openWarning", "setopenWarning", "handleLeave", "updatedGuideData", "setTooltip", "setElementSelected", "guideName", "borderColor", "borderSize", "setXpathToTooltipMetaData", "tooltipXaxis", "tooltipYaxis", "tooltipWidth", "setTooltipWidth", "setTooltipPadding", "setTooltipBorderradius", "setTooltipBordersize", "setTooltipBordercolor", "setTooltipBackgroundcolor", "tooltippadding", "tooltipborderradius", "tooltipbordersize", "tooltipBordercolor", "tooltipBackgroundcolor", "tooltipPosition", "setTooltipPosition", "selectedOption", "steps", "currentStepIndex", "openTooltip", "setOpenTooltip", "setCurrentStepIndex", "HotspotSettings", "hotspotGuideMetaData", "isPopoverOpen", "setIsPopoverOpen", "setAxisData", "currentGuideId", "axisData", "isALTKeywordEnabled", "setIsALTKeywordEnabled", "currentHoveredElement", "setCurrentHoveredElement", "isGuideInfoScreen", "isCollapsed", "createWithAI", "syncAITooltipContainerData", "state", "hoveredElement", "setHoveredElement", "rectData", "setRectData", "overlayPosition", "setOverlayPosition", "popupPosition", "setPopupPosition", "getXPath", "element", "isUnique", "attr", "value", "tagName", "parent", "parentNode", "matchingElements", "Array", "from", "querySelectorAll", "length", "getUniqueSelector", "el", "toLowerCase", "eventAttributes", "attributes", "filter", "name", "startsWith", "getSegment", "uniqueSelector", "bannercontainer", "querySelector", "closest", "siblingIndex", "sibling", "previousElementSibling", "segments", "currentElement", "documentElement", "segment", "unshift", "parentElement", "splice", "join", "getRelativeXPath", "xpath", "evaluate", "XPathResult", "ORDERED_NODE_SNAPSHOT_TYPE", "snapshotLength", "e", "isHighlightStyle", "styleValue", "includes", "isHighlightClass", "trim", "split", "cls", "pathSegments", "_currentElement$paren", "siblings", "children", "sib", "index", "indexOf", "bannerContainer", "shouldIgnoreHighlight", "classList", "contains", "getAttribute", "shouldIgnoreEvents", "hotspot", "altElementBorderRemove", "previousSelectedDOM", "setTimeout", "outline", "pointerEvents", "removeAttribute", "remove", "removeAllElementHighlights", "highlightedElements", "for<PERSON>ach", "htmlElement", "targetElements", "quickAdaptElements", "outlineOffset", "removeAppliedStyleOfEle", "createCustomCursorDataURL", "svgString", "encodedSvg", "encodeURIComponent", "applyCustomCursor", "cursorDataURL", "cursor", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "removeCustomCursor", "existingStyle", "getElementById", "isElementHover", "handleMouseOver", "event", "target", "rect", "getBoundingClientRect", "overlay", "Object", "assign", "window", "scrollY", "left", "scrollX", "height", "setAttribute", "addEventListener", "once", "handleMouseOut", "existingHotspot", "handleClick", "stopPropagation", "stopImmediatePropagation", "preventDefault", "hasAttribute", "getRelativexpath", "console", "log", "offsetHeight", "x", "y", "visible", "text", "PossibleElementPath", "applyHotspotProperties", "handleDocumentClick", "removeEventListener", "generateDynamicId", "_toolTipGuideMetaData15", "hotspotPropData", "hotspots", "size", "Size", "Color", "transition", "innerHTML", "Type", "textSpan", "innerText", "fontWeight", "fontStyle", "display", "alignItems", "justifyContent", "PulseAnimation", "add", "stopAnimationUponInteraction", "stopAnimation", "opacity", "transform", "onclick", "on<PERSON><PERSON>ver", "ShowUpon", "canvasProperties", "right", "bottom", "title", "open", "placement", "slotProps", "border", "<PERSON><PERSON><PERSON>", "normalizePx", "fallback", "arguments", "undefined", "val", "endsWith", "DotsStepper", "_ref4", "activeStep", "variant", "flexGrow", "nextButton", "backButton"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/Tooltips/Tooltip.tsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from \"react\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport \"./Tooltip.css\";\r\nimport {\r\n\tBox,\r\n\tClickAwayListener,\r\n\tTooltip,\r\n\tTooltipProps,\r\n\ttooltipClasses,\r\n\tLinearProgress,\r\n\tMobileStepper,\r\n\tBreadcrumbs,\r\n\tTypography,\r\n} from \"@mui/material\";\r\nimport { styled } from \"@mui/material/styles\";\r\n\r\nimport { updateCacheWithNewRows } from \"@mui/x-data-grid/hooks/features/rows/gridRowsUtils\";\r\nimport TooltipBody from \"./TooltipBody\";\r\nimport { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\r\nimport { CustomCursor } from \"../../assets/icons/icons\";\r\n\r\nexport const TOOLTIP_MX_WIDTH = 500;\r\nexport const TOOLTIP_MN_WIDTH = 300;\r\nexport const TOOLTIP_HEIGHT = 500;\r\nexport const EXTENSION_PART = \"extension-part\";\r\n\r\nexport const CustomWidthTooltip = styled(\r\n\t({\r\n\t\tclassName,\r\n\t\tselectedTemplate,\r\n\t\tselectedTemplateTour,\r\n\t\t...props\r\n\t}: TooltipProps & { selectedTemplate: string; selectedTemplateTour: string }) => {\r\n\t\tconst { toolTipGuideMetaData, currentStep ,elementSelected} = useDrawerStore();\r\n\t\tuseEffect(() => {\r\n\t\t\tif (\r\n\t\t\t\t(\r\n\t\t\t\t\tselectedTemplate === \"Tooltip\" || \r\n\t\t\t\t\tselectedTemplate === \"Hotspot\" || \r\n\t\t\t\t\tselectedTemplateTour === \"Tooltip\" || \r\n\t\t\t\t\tselectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t  ) && elementSelected\r\n\t\t\t\t  \r\n\t\t\t) {\r\n\t\t\t\tdocument.body.style.overflow = \"hidden\";\r\n\t\t\t} else {\r\n\t\t\t\tdocument.body.style.overflow = \"\";\r\n\t\t\t}\r\n\t\t\r\n\t\t\t// Cleanup\r\n\t\t\treturn () => {\r\n\t\t\t\tdocument.body.style.overflow = \"\";\r\n\t\t\t};\r\n\t\t}, [selectedTemplate, selectedTemplateTour, elementSelected]);\r\n\t\treturn (\r\n\t\t\t<Tooltip\r\n\t\t\t\t{...props}\r\n\t\t\t\tclasses={{ popper: className }}\r\n\t\t\t\tid=\"Tooltip-unique\"\r\n\t\t\t\tdisableHoverListener\r\n\t\t\t\tdisableTouchListener\r\n\t\t\t\tPopperProps={{\r\n\t\t\t\t\tclassName: \"qadpt-tlprte\",\r\n\t\t\t\t\tsx: {\r\n\t\t\t\t\t\ttop:\r\n\t\t\t\t\t\t\tselectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\"\r\n\t\t\t\t\t\t\t\t? \"-110px !important\"\r\n\t\t\t\t\t\t\t\t: \"-160px !important\",\r\n\t\t\t\t\t\t\t\tzIndex:'99999 !important'\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t/>\r\n\t\t);\r\n\t}\r\n)(({ selectedTemplate }: { selectedTemplate: string }) => {\r\n\tconst { toolTipGuideMetaData, currentStep } = useDrawerStore();\r\n\r\n\treturn {\r\n\t\t[`& .${tooltipClasses.tooltip}`]: {\r\n\t\t\t// maxWidth: TOOLTIP_MX_WIDTH,\r\n\t\t\tminWidth: TOOLTIP_MN_WIDTH,\r\n\t\t\twidth: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.width} !important` || \"250px !important\",\r\n\t\t\tmaxWidth: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.width} !important` || \"800px !important\",\r\n\t\t\tbackgroundColor: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.backgroundColor}` || \"#fff\",\r\n\t\t\tcolor: \"transparent\",\r\n\t\t\tfontSize: \"14px\",\r\n\t\t\tborderRadius: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.borderRadius} !important`,\r\n\t\t\tposition: \"relative !important\",\r\n\t\t\tpadding: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.padding}` || \"2px\",\r\n\t\t\tboxShadow: \"9px 0px 10px 1px #00000033, 0px 0px 0px 0px #00000024, -4px 2px 10px 1px #0000001f\", // Adds a smooth shadow\r\n\t\t},\r\n\t\t[`& .${tooltipClasses.arrow}`]: {\r\n\t\t\tcolor: \"white\", // Match arrow color with tooltip background\r\n\t\t},\r\n\t};\r\n\treturn {\r\n\t\t[`& .${tooltipClasses.tooltip}`]: {\r\n\t\t\t// maxWidth: TOOLTIP_MX_WIDTH,\r\n\t\t\tminWidth: TOOLTIP_MN_WIDTH,\r\n\t\t\tmaxWidth: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.width} !important` || \"500px !important\",\r\n\t\t\tbackgroundColor: \"white\",\r\n\t\t\tcolor: \"transparent\",\r\n\t\t\tfontSize: \"14px\",\r\n\t\t\tborderRadius: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.borderRadius || \"8px\"} !important`,\r\n\t\t\tposition: \"relative !important\",\r\n\t\t\tpadding: \"0px !important\",\r\n\t\t\tboxShadow: \"9px 0px 10px 1px #00000033, 0px 0px 0px 0px #00000024, -4px 2px 10px 1px #0000001f\", // Adds a smooth shadow\r\n\t\t},\r\n\t\t[`& .${tooltipClasses.arrow}`]: {\r\n\t\t\tcolor: \"white\", // Match arrow color with tooltip background\r\n\t\t},\r\n\t};\r\n});\r\n\r\nconst CreateTooltip = ({\r\n\tisTooltipNameScreenOpen,\r\n\tisUnSavedChanges,\r\n\topenWarning,\r\n\tsetopenWarning,\r\n\thandleLeave,\r\n\tupdatedGuideData,\r\n}: {\r\n\tisTooltipNameScreenOpen: boolean;\r\n\tisUnSavedChanges: boolean;\r\n\topenWarning: boolean;\r\n\tsetopenWarning: (params: boolean) => void;\r\n\thandleLeave: () => void;\r\n\t\tupdatedGuideData: any;\r\n}) => {\r\n\tconst {\r\n\t\tselectedTemplate,\r\n\t\tsetTooltip,\r\n\t\ttooltip,\r\n\t\telementSelected,\r\n\t\tsetElementSelected,\r\n\t\tguideName,\r\n\t\tborderColor,\r\n\t\tbackgroundColor,\r\n\t\tborderSize,\r\n\t\tborderRadius,\r\n\t\tpadding,\r\n\t\twidth,\r\n\t\tsetXpathToTooltipMetaData,\r\n\t\t// updatedCanvasSettings,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\ttooltipWidth,\r\n\t\tsetTooltipWidth,\r\n\t\tsetTooltipPadding,\r\n\t\tsetTooltipBorderradius,\r\n\t\tsetTooltipBordersize,\r\n\t\tsetTooltipBordercolor,\r\n\t\tsetTooltipBackgroundcolor,\r\n\t\ttooltippadding,\r\n\t\ttooltipborderradius,\r\n\t\ttooltipbordersize,\r\n\t\ttooltipBordercolor,\r\n\t\ttooltipBackgroundcolor,\r\n\t\ttooltipPosition,\r\n\t\tsetTooltipPosition,\r\n\t\tselectedOption,\r\n\t\tsteps,\r\n\t\tcurrentStepIndex,\r\n\t\topenTooltip,\r\n\t\tsetOpenTooltip,\r\n\t\tsetCurrentStepIndex,\r\n\t\tHotspotSettings,\r\n\t\ttoolTipGuideMetaData,\r\n\t\thotspotGuideMetaData,\r\n\t\tcurrentStep,\r\n\t\tisPopoverOpen,\r\n\t\tsetIsPopoverOpen,\r\n\t\tsetAxisData,\r\n\t\tcurrentGuideId,\r\n\t\taxisData,\r\n\t\tisALTKeywordEnabled,\r\n\t\tsetIsALTKeywordEnabled,\r\n\t\tcurrentHoveredElement,\r\n\t\tsetCurrentHoveredElement,\r\n\t\tselectedTemplateTour,\r\n\t\tisGuideInfoScreen,\r\n\t\tisCollapsed,\r\n\t\tcreateWithAI,\r\n\t\tsyncAITooltipContainerData\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst [hoveredElement, setHoveredElement] = useState<HTMLElement | null>(null);\r\n\tconst [rectData, setRectData] = useState<DOMRect | null>(null); // State to store rect object\r\n\tconst [overlayPosition, setOverlayPosition] = useState<{\r\n\t\ttop: number;\r\n\t\tleft: number;\r\n\t\twidth: number;\r\n\t\theight: number;\r\n\t} | null>(null);\r\n\tconst [popupPosition, setPopupPosition] = useState<any>(null);\r\n\r\n\tconst getXPath = (element: HTMLElement | null) => {\r\n\t\tconst isUnique = (attr: string, value: string, tagName: string) => {\r\n\t\t\tconst parent = element?.parentNode;\r\n\t\t\tif (!parent) return false;\r\n\t\t\tconst matchingElements = [...Array.from(parent.querySelectorAll(`${tagName}[${attr}=\"${value}\"]`))];\r\n\t\t\treturn matchingElements.length === 1;\r\n\t\t};\r\n\r\n\t\tconst getUniqueSelector = (el: HTMLElement | null) => {\r\n\t\t\tif (!el) return null;\r\n\t\t\tconst tagName = el.tagName.toLowerCase();\r\n\r\n\t\t\t// find with event attributes\r\n\t\t\tconst eventAttributes = [...Array.from(el.attributes)].filter((attr) => attr.name.startsWith(\"on\"));\r\n\t\t\tfor (const attr of eventAttributes) {\r\n\t\t\t\tif (isUnique(attr.name, attr.value, tagName)) {\r\n\t\t\t\t\treturn `//${tagName}[@${attr.name}=\"${attr.value}\"]`;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn null;\r\n\t\t};\r\n\r\n\t\tconst getSegment = (el: HTMLElement | null) => {\r\n\t\t\tif (!el) return null;\r\n\t\t\tconst tagName = el.tagName.toLowerCase();\r\n\t\t\tconst uniqueSelector = getUniqueSelector(el);\r\n\t\t\tconst bannercontainer = document.querySelector(\".quickAdopt_banner\");\r\n\t\t\tif (bannercontainer) {\r\n\t\t\t\tif (el.closest(\".quickAdopt_banner\")) {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (uniqueSelector) return uniqueSelector;\r\n\r\n\t\t\tlet siblingIndex = 1;\r\n\t\t\tlet sibling = el.previousElementSibling;\r\n\r\n\t\t\twhile (sibling) {\r\n\t\t\t\tif (sibling.tagName === el.tagName) siblingIndex++;\r\n\t\t\t\tsibling = sibling.previousElementSibling;\r\n\t\t\t}\r\n\r\n\t\t\treturn `${tagName}[${siblingIndex}]`;\r\n\t\t};\r\n\r\n\t\tconst segments: string[] = [];\r\n\r\n\t\tlet currentElement = element;\r\n\r\n\t\twhile (currentElement && currentElement !== document.body && currentElement !== document.documentElement) {\r\n\t\t\tconst segment = getSegment(currentElement);\r\n\t\t\tif (!segment) break;\r\n\t\t\tsegments.unshift(segment);\r\n\t\t\tif (segment.startsWith(\"//*[@\")) break;\r\n\t\t\tcurrentElement = currentElement.parentElement as HTMLElement;\r\n\t\t}\r\n\r\n\t\tif (!segments[0].startsWith(\"//\")) {\r\n\t\t\tif (segments[0] !== \"html\") {\r\n\t\t\t\tsegments.unshift(\"html\");\r\n\t\t\t}\r\n\t\t\tif (segments[1] !== \"body\") {\r\n\t\t\t\tsegments.splice(1, 0, \"body\");\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn `${segments.join(\"/\")}`;\r\n\t};\r\n\tconst getRelativeXPath = (element: HTMLElement | null) => {\r\n\t\t// Check if element is valid\r\n\t\tif (!element || !element.tagName) return null;\r\n\r\n\t\tconst isUnique = (xpath: string) => {\r\n\t\t\ttry {\r\n\t\t\t\tconst matchingElements = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);\r\n\t\t\t\treturn matchingElements.snapshotLength === 1;\r\n\t\t\t} catch (e) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst isHighlightStyle = (styleValue: string) => {\r\n\t\t\t// Match your specific highlighting styles\r\n\t\t\treturn (\r\n\t\t\t\tstyleValue.includes(\"outline: 2px solid #6c97a6\") ||\r\n\t\t\t\tstyleValue.includes(\"pointer-events: none\") ||\r\n\t\t\t\tstyleValue.includes(\"pointer-events:\")\r\n\t\t\t);\r\n\t\t};\r\n\r\n\t\tconst isHighlightClass = (className: string) => {\r\n\t\t\t// Check for your highlighting class\r\n\t\t\treturn className === \"quickAdopt-selection\";\r\n\t\t};\r\n\r\n\t\tconst getUniqueSelector = (el: HTMLElement) => {\r\n\t\t\tconst tagName = el.tagName.toLowerCase();\r\n\r\n\t\t\t// Priority 1: ID\r\n\t\t\tif (el.id) {\r\n\t\t\t\tconst xpath = `//${tagName}[@id=\"${el.id}\"]`;\r\n\t\t\t\tif (isUnique(xpath)) return xpath;\r\n\t\t\t}\r\n\r\n\t\t\t// Priority 2: Class names (excluding highlight class)\r\n\t\t\tif (el.className) {\r\n\t\t\t\tconst classes = el.className\r\n\t\t\t\t\t.trim()\r\n\t\t\t\t\t.split(/\\s+/)\r\n\t\t\t\t\t.filter((cls) => !isHighlightClass(cls));\r\n\t\t\t\tfor (const className of classes) {\r\n\t\t\t\t\tif (className) {\r\n\t\t\t\t\t\tconst xpath = `//${tagName}[contains(@class, \"${className}\")]`;\r\n\t\t\t\t\t\tif (isUnique(xpath)) return xpath;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// Priority 3: Event attributes\r\n\t\t\tconst eventAttributes = [...Array.from(el.attributes)].filter((attr) => attr.name.startsWith(\"on\"));\r\n\t\t\tfor (const attr of eventAttributes) {\r\n\t\t\t\tconst xpath = `//${tagName}[@${attr.name}=\"${attr.value}\"]`;\r\n\t\t\t\tif (isUnique(xpath)) return xpath;\r\n\t\t\t}\r\n\r\n\t\t\t// Priority 4: Other attributes (excluding highlight style and disabled)\r\n\t\t\tconst attributes = [...Array.from(el.attributes)].filter(\r\n\t\t\t\t(attr) =>\r\n\t\t\t\t\tattr.name !== \"id\" &&\r\n\t\t\t\t\tattr.name !== \"class\" &&\r\n\t\t\t\t\t!attr.name.startsWith(\"on\") &&\r\n\t\t\t\t\tattr.name !== \"disabled\" &&\r\n\t\t\t\t\t!(attr.name === \"style\" && isHighlightStyle(attr.value))\r\n\t\t\t);\r\n\t\t\tfor (const attr of attributes) {\r\n\t\t\t\tconst xpath = `//${tagName}[@${attr.name}=\"${attr.value}\"]`;\r\n\t\t\t\tif (isUnique(xpath)) return xpath;\r\n\t\t\t}\r\n\r\n\t\t\treturn null;\r\n\t\t};\r\n\r\n\t\tlet currentElement = element;\r\n\t\tlet pathSegments: string[] = [];\r\n\r\n\t\twhile (currentElement && currentElement !== document.documentElement) {\r\n\t\t\tconst uniqueSelector = getUniqueSelector(currentElement);\r\n\r\n\t\t\tif (uniqueSelector) {\r\n\t\t\t\treturn uniqueSelector;\r\n\t\t\t}\r\n\r\n\t\t\t// Build positional path if no unique selector is found\r\n\t\t\tconst tagName = currentElement.tagName.toLowerCase();\r\n\t\t\tconst siblings = Array.from(currentElement.parentElement?.children || []).filter(\r\n\t\t\t\t(sib) => sib.tagName === currentElement.tagName\r\n\t\t\t);\r\n\t\t\tconst index = siblings.indexOf(currentElement) + 1;\r\n\r\n\t\t\tpathSegments.unshift(index > 1 ? `${tagName}[${index}]` : tagName);\r\n\t\t\tcurrentElement = currentElement.parentElement as HTMLElement;\r\n\t\t}\r\n\r\n\t\t// Check for banner container (from your original code)\r\n\t\tconst bannerContainer = document.querySelector(\".quickAdopt_banner\");\r\n\t\tif (bannerContainer && element.closest(\".quickAdopt_banner\")) {\r\n\t\t\treturn null;\r\n\t\t}\r\n\r\n\t\treturn `//${pathSegments.join(\"/\")}`;\r\n\t};\r\n\r\n\t// Example usage:\r\n\t// const element = document.querySelector('some-element');\r\n\t// const relativeXPath = getRelativeXPath(element);\r\n\t// console.log(relativeXPath);\r\n\r\n\tconst shouldIgnoreHighlight = (element: HTMLElement) => {\r\n\t\treturn (\r\n\t\t\telement.classList.contains(\"mdc-tooltip__surface\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip__surface-animation\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip--shown\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip--showing\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip--hiding\") ||\r\n\t\t\telement.getAttribute(\"role\") === \"tooltip\" ||\r\n\t\t\telement.closest(\"#Tooltip-unique\") ||\r\n\t\t\telement.closest(\"#my-react-drawer\") ||\r\n\t\t\telement.closest(\"#tooltip-section-popover\") ||\r\n\t\t\telement.closest(\"#btn-setting-toolbar\") ||\r\n\t\t\telement.closest(\"#button-toolbar\") ||\r\n\t\t\telement.closest(\"#color-picker\") ||\r\n\t\t\telement.closest(\".qadpt-ext-banner\") ||\r\n\t\t\telement.closest(\"#leftDrawer\") ||\r\n\t\t\telement.closest(\"#image-popover\") ||\r\n\t\t\telement.closest(\"#toggle-fit\") ||\r\n\t\t\telement.closest(\"#color-popover\") ||\r\n\t\t\telement.closest(\"#rte-popover\") ||\r\n\t\t\telement.closest(\"#rte-alignment\") ||\r\n\t\t\telement.closest(\"#rte-alignment-menu\") ||\r\n\t\t\telement.closest(\"#rte-font\") ||\r\n\t\t\telement.closest(\"#rte-bold\") ||\r\n\t\t\telement.closest(\"#rte-italic\") ||\r\n\t\t\telement.closest(\"#rte-underline\") ||\r\n\t\t\telement.closest(\"#rte-strke-through\") ||\r\n\t\t\telement.closest(\"#rte-alignment-menu-items\") ||\r\n\t\t\telement.closest(\"#rte-more\") ||\r\n\t\t\telement.closest(\"#rte-text-color\") ||\r\n\t\t\telement.closest(\"#rte-text-color-popover\") ||\r\n\t\t\telement.closest(\"#rte-text-highlight\") ||\r\n\t\t\telement.closest(\"#rte-text-highlight-pop\") ||\r\n\t\t\telement.closest(\"#rte-text-heading\") ||\r\n\t\t\telement.closest(\"#rte-text-heading-menu-items\") ||\r\n\t\t\telement.closest(\"#rte-text-format\") ||\r\n\t\t\telement.closest(\"#rte-text-ul\") ||\r\n\t\t\telement.closest(\"#rte-text-hyperlink\") ||\r\n\t\t\telement.closest(\"#rte-video\") ||\r\n\t\t\telement.closest(\"#rte-clear-formatting\") ||\r\n\t\t\telement.closest(\"#rte-hyperlink-popover\") ||\r\n\t\t\telement.closest(\"#rte-box\") ||\r\n\t\t\telement.closest(element.id.startsWith(\"#rt-editor\") ? `${element.id}` : \"nope\") ||\r\n\t\t\telement.closest(\"#rte-placeholder\") ||\r\n\t\t\telement.closest(\"#qadpt-designpopup\") ||\r\n\t\t\telement.closest(\"#image-properties\") ||\r\n\t\t\telement.closest(\"#rte-toolbar\") ||\r\n\t\t\telement.closest(\"#tooltipdialog\") ||\r\n\t\t\telement.closest(\"#rte-toolbar-paper\") ||\r\n\t\t\telement.closest(\"#rte-alignment-menu\") ||\r\n\t\t\telement.closest(\"#rte-alignment-menu-items\")\r\n\t\t);\r\n\t};\r\n\tconst shouldIgnoreEvents = (element: HTMLElement) => {\r\n\t\treturn (\r\n\t\t\telement.classList.contains(\"mdc-tooltip__surface\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip__surface-animation\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip--shown\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip--showing\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip--hiding\") ||\r\n\t\t\telement.getAttribute(\"role\") === \"tooltip\" ||\r\n\t\t\telement.closest(\"#Tooltip-unique\") ||\r\n\t\t\telement.closest(\"#tooltip-section-popover\") ||\r\n\t\t\telement.closest(\"#btn-setting-toolbar\") ||\r\n\t\t\telement.closest(\"#button-toolbar\") ||\r\n\t\t\telement.closest(\"#color-picker\") ||\r\n\t\t\telement.closest(\".qadpt-ext-banner\") ||\r\n\t\t\telement.closest(\"#leftDrawer\") ||\r\n\t\t\telement.closest(\"#rte-popover\") ||\r\n\t\t\telement.closest(element.id.startsWith(\"#rt-editor\") ? `${element.id}` : \"nope\") ||\r\n\t\t\telement.closest(\"#rte-box\") ||\r\n\t\t\telement.closest(\"#rte-placeholder\") ||\r\n\t\t\telement.closest(\"#rte-alignment-menu-items\") ||\r\n\t\t\telement.closest(\"#qadpt-designpopup\")\r\n\t\t);\r\n\t};\r\n\r\n\tlet hotspot: any;\r\n\tconst altElementBorderRemove = () => {\r\n\t\tconst previousSelectedDOM = document.querySelector(\".quickAdopt-selection\") as HTMLElement;\r\n\t\tif (previousSelectedDOM) {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tpreviousSelectedDOM.style.outline = \"none\";\r\n\t\t\t\t//previousSelectedDOM.style.backgroundColor = \"\";\r\n\t\t\t\tpreviousSelectedDOM.style.boxShadow = \"\";\r\n\t\t\t\tpreviousSelectedDOM.style.pointerEvents = \"\";\r\n\t\t\t\tpreviousSelectedDOM.removeAttribute(\"disabled\");\r\n\t\t\t\tpreviousSelectedDOM.classList.remove(\"quickAdopt-selection\");\r\n\t\t\t}, 10);\r\n\t\t}\r\n\t};\r\n\r\n\tconst removeAllElementHighlights = () => {\r\n\t\t// Remove all elements with quickAdopt-selection class\r\n\t\tconst highlightedElements = document.querySelectorAll('.quickAdopt-selection');\r\n\t\thighlightedElements.forEach((element: Element) => {\r\n\t\t\tconst htmlElement = element as HTMLElement;\r\n\t\t\thtmlElement.style.outline = '';\r\n\t\t\t//htmlElement.style.backgroundColor = '';\r\n\t\t\thtmlElement.style.boxShadow = '';\r\n\t\t\thtmlElement.style.pointerEvents = '';\r\n\t\t\thtmlElement.removeAttribute('disabled');\r\n\t\t\thtmlElement.classList.remove('quickAdopt-selection');\r\n\t\t});\r\n\r\n\t\t// Remove any elements with data-target-element attribute\r\n\t\tconst targetElements = document.querySelectorAll('[data-target-element=\"true\"]');\r\n\t\ttargetElements.forEach((element: Element) => {\r\n\t\t\tconst htmlElement = element as HTMLElement;\r\n\t\t\thtmlElement.style.outline = '';\r\n\t\t\t//htmlElement.style.backgroundColor = '';\r\n\t\t\thtmlElement.style.boxShadow = '';\r\n\t\t\thtmlElement.style.pointerEvents = '';\r\n\t\t\thtmlElement.removeAttribute('disabled');\r\n\t\t\thtmlElement.removeAttribute('data-target-element');\r\n\t\t});\r\n\r\n\t\t// Remove any elements with quickadapt highlighting attributes\r\n\t\tconst quickAdaptElements = document.querySelectorAll('[data-quickadapt-highlighted=\"true\"]');\r\n\t\tquickAdaptElements.forEach((element: Element) => {\r\n\t\t\tconst htmlElement = element as HTMLElement;\r\n\t\t\thtmlElement.style.outline = '';\r\n\t\t\thtmlElement.style.outlineOffset = '';\r\n\t\t\t//htmlElement.style.backgroundColor = '';\r\n\t\t\thtmlElement.style.boxShadow = '';\r\n\t\t\thtmlElement.style.pointerEvents = '';\r\n\t\t\thtmlElement.removeAttribute('disabled');\r\n\t\t\thtmlElement.removeAttribute('data-quickadapt-highlighted');\r\n\t\t});\r\n\t};\r\n\r\n\tconst removeAppliedStyleOfEle = (element: HTMLElement) => {\r\n\t\telement.removeAttribute(\"disabled\");\r\n\t\telement.style.outline = \"\";\r\n\t\telement.style.pointerEvents = \"unset\";\r\n\t};\r\n\r\n\t// Custom cursor utility functions\r\n\tconst createCustomCursorDataURL = () => {\r\n\t\t// Create a data URL from the SVG\r\n\t\tconst svgString = CustomCursor;\r\n\t\tconst encodedSvg = encodeURIComponent(svgString);\r\n\t\treturn `data:image/svg+xml,${encodedSvg}`;\r\n\t};\r\n\r\n\tconst applyCustomCursor = () => {\r\n\t\tconst cursorDataURL = createCustomCursorDataURL();\r\n\t\t// Apply cursor to document body and html to ensure it covers the entire page\r\n\t\t// Using 16 16 as hotspot (center of 32x32 cursor)\r\n\t\tdocument.body.style.cursor = `url(\"${cursorDataURL}\") 16 16, crosshair`;\r\n\t\tdocument.documentElement.style.cursor = `url(\"${cursorDataURL}\") 16 16, crosshair`;\r\n\r\n\t\t// Also apply to all elements that might override the cursor, but exclude extension UI\r\n\t\tconst style = document.createElement('style');\r\n\t\tstyle.id = 'quickadapt-custom-cursor-style';\r\n\t\tstyle.textContent = `\r\n\t\t\t* {\r\n\t\t\t\tcursor: url(\"${cursorDataURL}\") 16 16, crosshair !important;\r\n\t\t\t}\r\n\t\t\t/* Exclude extension UI elements from custom cursor */\r\n\t\t\t#my-react-drawer,\r\n\t\t\t#my-react-drawer *,\r\n\t\t\t.MuiDrawer-root,\r\n\t\t\t.MuiDrawer-root *,\r\n\t\t\t.MuiDialog-root,\r\n\t\t\t.MuiDialog-root *,\r\n\t\t\t.MuiPopover-root,\r\n\t\t\t.MuiPopover-root *,\r\n\t\t\t.MuiTooltip-popper,\r\n\t\t\t.MuiTooltip-popper *,\r\n\t\t\t.qadpt-ext-banner,\r\n\t\t\t.qadpt-ext-banner *,\r\n\t\t\t#leftDrawer,\r\n\t\t\t#leftDrawer *,\r\n\t\t\t#rte-popover,\r\n\t\t\t#rte-popover *,\r\n\t\t\t#rte-box,\r\n\t\t\t#rte-box *,\r\n\t\t\t#qadpt-designpopup,\r\n\t\t\t#qadpt-designpopup *,\r\n\t\t\t.quickadapt-no-custom-cursor,\r\n\t\t\t.quickadapt-no-custom-cursor * {\r\n\t\t\t\tcursor: default !important;\r\n\t\t\t}\r\n\t\t`;\r\n\t\tdocument.head.appendChild(style);\r\n\t};\r\n\r\n\tconst removeCustomCursor = () => {\r\n\t\t// Remove custom cursor from body and html\r\n\t\tdocument.body.style.cursor = '';\r\n\t\tdocument.documentElement.style.cursor = '';\r\n\r\n\t\t// Remove the custom cursor style\r\n\t\tconst existingStyle = document.getElementById('quickadapt-custom-cursor-style');\r\n\t\tif (existingStyle) {\r\n\t\t\texistingStyle.remove();\r\n\t\t}\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tlet isElementHover = false;\r\n\r\n\t\tconst handleMouseOver = (event: MouseEvent) => {\r\n\tif (isALTKeywordEnabled && currentHoveredElement && !isTooltipNameScreenOpen && !isCollapsed) {\r\n\t\tconst element = event.target as HTMLElement;\r\n\t\tsetCurrentHoveredElement(element);\r\n\r\n\t\tif (!shouldIgnoreHighlight(element)) {\r\n\t\t\taltElementBorderRemove();\r\n\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tconst overlay = document.createElement(\"div\");\r\n\t\t\toverlay.className = \"quickAdopt-overlay\";\r\n\t\t\tObject.assign(overlay.style, {\r\n\t\t\t\tposition: \"fixed\",\r\n\t\t\t\ttop: `${rect.top + window.scrollY}px`,\r\n\t\t\t\tleft: `${rect.left + window.scrollX}px`,\r\n\t\t\t\twidth: `${rect.width}px`,\r\n\t\t\t\theight: `${rect.height}px`,\r\n\t\t\t\tbackgroundColor: \"rgba(108, 151, 166, 0.2)\",\r\n\t\t\t\toutline: \"2px dashed #6c97a6\",\r\n\t\t\t\tboxShadow: \"0 0 0 3px transparent\",\r\n\t\t\t\tpointerEvents: \"none\",\r\n\t\t\t\tzIndex: \"9999\"\r\n\t\t\t});\r\n\r\n\t\t\toverlay.setAttribute(\"data-overlay\", \"true\");\r\n\r\n\t\t\tdocument.body.appendChild(overlay);\r\n\r\n\t\t\t// Optional: Remove overlay on mouseout\r\n\t\t\telement.addEventListener(\"mouseout\", () => {\r\n\t\t\t\tdocument.querySelectorAll(\"[data-overlay='true']\").forEach(el => el.remove());\r\n\t\t\t}, { once: true });\r\n\t\t}\r\n\t}\r\n};\r\n\t\tconst handleMouseOut = (event: MouseEvent) => {\r\n\t\t\tconst element = event.target as HTMLElement;\r\n\t\t\tsetCurrentHoveredElement(element as HTMLElement);\r\n\t\t\tif (!shouldIgnoreHighlight(element)) {\r\n\t\t\t\tif ([\"input\", \"select\", \"textarea\", \"button\", \"a\", \"select\"].includes(element.tagName.toLowerCase())) {\r\n\t\t\t\t\telement.style.pointerEvents = \"\";\r\n\t\t\t\t}\r\n\t\t\t\telement.removeAttribute(\"disabled\");\r\n\t\t\t\telement.style.outline = \"\";\r\n\t\t\t\t//element.style.backgroundColor = \"\";\r\n\t\t\t\telement.style.boxShadow = \"\";\r\n\t\t\t\tisElementHover = false;\r\n\t\t\t}\r\n\t\t};\r\n\t\tconst existingHotspot = document.getElementById(\"hotspotBlinkCreation\");\r\n\r\n\t\tconst handleClick = (event: MouseEvent) => {\r\n\t\t\tif (!isGuideInfoScreen && !isTooltipNameScreenOpen && !isCollapsed) {\r\n\t\t\t\tconst element = event.target as HTMLElement;\r\n\t\t\t\tevent.stopPropagation();\r\n\t\t\t\tevent.stopImmediatePropagation();\r\n\t\t\t\tevent.preventDefault();\r\n\t\t\t\tevent.stopImmediatePropagation();\r\n\t\t\t\tsetElementSelected(true);\r\n\t\t\t\tif (isALTKeywordEnabled) {\r\n\t\t\t\t\tif (!shouldIgnoreHighlight(element) && !shouldIgnoreEvents(element)) {\r\n\t\t\t\t\t\tif (!element.hasAttribute(\"data-target-element\")) {\r\n\t\t\t\t\t\t\telement.setAttribute(\"data-target-element\", \"true\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tconst xpath = getXPath(element);\r\n\t\t\t\t\t\t//element.classList.add(\"\");\r\n\t\t\t\t\t\telement.style.outline = \"\";\r\n\t\t\t\t\t\tconst getRelativexpath = getRelativeXPath(element);\r\n\t\t\t\t\t\t//element.style.position = \"relative\";\r\n\t\t\t\t\t\t//element.style.backgroundColor = \"rgba(108, 151, 166, 0.2)\";\r\n\t\t\t\t\t\telement.style.outline = \"2px dashed #6c97a6\";\r\n\t\t\t\t\t\telement.style.boxShadow = \"0 0 0 3px transparent\";\r\n\t\t\t\t\t\tconsole.log(getRelativexpath);\r\n\t\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\t\tsetPopupPosition({\r\n\t\t\t\t\t\t\tleft: `${rect.left}px`,\r\n\t\t\t\t\t\t\ttop: `${element.offsetHeight + window.scrollY}px`,\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\tsetRectData(rect);\r\n\t\t\t\t\t\tsetAxisData(rect);\r\n\r\n\t\t\t\t\t\tif (![\"BODY\", \"HTML\"].includes(element.tagName)) {\r\n\t\t\t\t\t\t\tconst tooltipPosition = { x: rect.left + 5, y: rect.top };\r\n\t\t\t\t\t\t\tsetTooltip({\r\n\t\t\t\t\t\t\t\tvisible: true,\r\n\t\t\t\t\t\t\t\ttext: xpath,\r\n\t\t\t\t\t\t\t\tposition: { x: rect.left + 5, y: rect.top },\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tif (xpath) {\r\n\t\t\t\t\t\t\t\tsetXpathToTooltipMetaData({\r\n\t\t\t\t\t\t\t\t\tvalue: xpath,\r\n\t\t\t\t\t\t\t\t\tPossibleElementPath: getRelativexpath,\r\n\t\t\t\t\t\t\t\t\tposition: tooltipPosition,\r\n\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t// For AI-created tooltips, sync the data after element selection\r\n\t\t\t\t\t\t\t\tif (createWithAI && selectedTemplate === \"Tooltip\") {\r\n\t\t\t\t\t\t\t\t\t// Use setTimeout to ensure the xpath data is set before syncing\r\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\tsyncAITooltipContainerData();\r\n\t\t\t\t\t\t\t\t\t}, 0);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t\t\tif (!existingHotspot) {\r\n\t\t\t\t\t\t\t\tapplyHotspotProperties(rect);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleDocumentClick = (event: MouseEvent) => {\r\n\t\t\tconst target = event.target as HTMLElement;\r\n\r\n\t\t\tif (isALTKeywordEnabled && !isGuideInfoScreen) {\r\n\t\t\t\tif (!shouldIgnoreHighlight(target) && !shouldIgnoreEvents(target)) {\r\n\t\t\t\t\thandleClick(event);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tif (rectData && existingHotspot) {\r\n\t\t\tapplyHotspotProperties(rectData);\r\n\t\t}\r\n\r\n\t\tif (!isALTKeywordEnabled && currentHoveredElement) {\r\n\t\t\t// Clean up all highlighted elements when element selection is disabled\r\n\t\t\tremoveAllElementHighlights();\r\n\t\t\t// Remove custom cursor when switching to page interaction mode\r\n\t\t\tremoveCustomCursor();\r\n\t\t\tdocument.removeEventListener(\"mouseover\", handleMouseOver);\r\n\t\t\tdocument.removeEventListener(\"mouseout\", handleMouseOut);\r\n\t\t\tdocument.removeEventListener(\"click\", handleDocumentClick, true);\r\n\t\t\treturn;\r\n\t\t} else if (elementSelected === false && (selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\")) {\r\n\t\t\t// Apply custom cursor for hotspot element selection\r\n\t\t\tif (isALTKeywordEnabled) {\r\n\t\t\t\tapplyCustomCursor();\r\n\t\t\t}\r\n\t\t\tdocument.addEventListener(\"mouseover\", handleMouseOver);\r\n\t\t\tdocument.addEventListener(\"mouseout\", handleMouseOut);\r\n\t\t\tdocument.addEventListener(\"click\", handleDocumentClick, true);\r\n\t\t} else if (\r\n\t\t\t!elementSelected &&\r\n\t\t\t(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") &&\r\n\t\t\tisALTKeywordEnabled\r\n\t\t) {\r\n\t\t\t// Apply custom cursor for tooltip element selection\r\n\t\t\tapplyCustomCursor();\r\n\t\t\tdocument.addEventListener(\"mouseover\", handleMouseOver);\r\n\t\t\tdocument.addEventListener(\"mouseout\", handleMouseOut);\r\n\t\t\tdocument.addEventListener(\"click\", handleDocumentClick, true);\r\n\t\t\tsetElementSelected(false);\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\tdocument.removeEventListener(\"mouseover\", handleMouseOver);\r\n\t\t\tdocument.removeEventListener(\"mouseout\", handleMouseOut);\r\n\t\t\tdocument.removeEventListener(\"click\", handleDocumentClick, true);\r\n\t\t\t// Clean up custom cursor on component unmount\r\n\t\t\tremoveCustomCursor();\r\n\t\t};\r\n\t}, [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement]);\r\n\r\n\t// Separate useEffect to handle custom cursor based on ALT keyword enabled state\r\n\tuseEffect(() => {\r\n\t\tif (isALTKeywordEnabled && !elementSelected &&\r\n\t\t\t(selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\" ||\r\n\t\t\t selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\")) {\r\n\t\t\t// Apply custom cursor when in element selection mode\r\n\t\t\tapplyCustomCursor();\r\n\t\t} else {\r\n\t\t\t// Remove custom cursor when not in element selection mode\r\n\t\t\tremoveCustomCursor();\r\n\t\t}\r\n\r\n\t\t// Cleanup on unmount\r\n\t\treturn () => {\r\n\t\t\tremoveCustomCursor();\r\n\t\t};\r\n\t}, [isALTKeywordEnabled, elementSelected, selectedTemplate, selectedTemplateTour]);\r\n\r\n\tconst generateDynamicId = () => {\r\n\t\treturn \"hotspotBlinkCreation\";\r\n\t};\r\n\r\n\tconst applyHotspotProperties = (rect: any) => {\r\n\t\tconst hotspotPropData = toolTipGuideMetaData && toolTipGuideMetaData[0]?.hotspots;\r\n\t\t//const xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t//const yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\t\tconst size = hotspotPropData?.Size;\r\n\t\t// const left = rect.left + window.scrollX + xOffset;\r\n\t\t// const top = rect.top + window.scrollY + yOffset;\r\n\t\tconst left = \"375\";\r\n\t\tconst top = \"160\";\r\n\t\tif (selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\") {\r\n\t\t\thotspot = document.getElementById(\"hotspotBlinkCreation\");\r\n\t\t\tif (!hotspot) {\r\n\t\t\t\thotspot = document.createElement(\"div\");\r\n\t\t\t\thotspot.id = generateDynamicId();\r\n\t\t\t\tdocument.body.appendChild(hotspot);\r\n\t\t\t}\r\n\r\n\t\t\thotspot.style.position = \"absolute\";\r\n\t\t\thotspot.style.left = `${left}px`;\r\n\t\t\thotspot.style.top = `${top}px`;\r\n\r\n\t\t\thotspot.style.width = `${size}px`;\r\n\t\t\thotspot.style.height = `${size}px`;\r\n\t\t\thotspot.style.backgroundColor = hotspotPropData?.Color ? hotspotPropData.Color : \"yellow\";\r\n\t\t\thotspot.style.borderRadius = \"50%\";\r\n\t\t\thotspot.style.zIndex = \"9999\";\r\n\t\t\thotspot.style.transition = \"none\";\r\n\t\t\thotspot.innerHTML = \"\";\r\n\t\t\tif (hotspotPropData?.Type === \"Info\" || hotspotPropData?.Type === \"Question\") {\r\n\t\t\t\tconst textSpan = document.createElement(\"span\");\r\n\t\t\t\ttextSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\r\n\t\t\t\ttextSpan.style.color = \"white\";\r\n\t\t\t\ttextSpan.style.fontSize = \"14px\";\r\n\t\t\t\ttextSpan.style.fontWeight = \"bold\";\r\n\t\t\t\ttextSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\r\n\t\t\t\ttextSpan.style.display = \"flex\";\r\n\t\t\t\ttextSpan.style.alignItems = \"center\";\r\n\t\t\t\ttextSpan.style.justifyContent = \"center\";\r\n\t\t\t\ttextSpan.style.width = \"100%\";\r\n\t\t\t\ttextSpan.style.height = \"100%\";\r\n\t\t\t\ttextSpan.style.pointerEvents = \"none\";\r\n\t\t\t\thotspot.appendChild(textSpan);\r\n\t\t\t}\r\n\r\n\t\t\tif (hotspotPropData?.PulseAnimation) {\r\n\t\t\t\thotspot.classList.add(\"pulse-animation\");\r\n\r\n\t\t\t\tif (hotspotPropData?.stopAnimationUponInteraction) {\r\n\t\t\t\t\tconst stopAnimation = () => {\r\n\t\t\t\t\t\tif (hotspot) {\r\n\t\t\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\t\t\t// Ensure the hotspot remains visible by keeping its styles\r\n\t\t\t\t\t\t\thotspot.style.display = \"flex\";\r\n\t\t\t\t\t\t\thotspot.style.opacity = \"1\";\r\n\t\t\t\t\t\t\thotspot.style.transform = \"scale(1)\";\r\n\t\t\t\t\t\t\t// Don't remove event handlers to ensure tooltip still works\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\t// Clear existing event handlers\r\n\t\t\t\t\thotspot.onclick = null;\r\n\t\t\t\t\thotspot.onmouseover = null;\r\n\r\n\t\t\t\t\t// Add appropriate event handlers based on ShowUpon property\r\n\t\t\t\t\tif (hotspotPropData?.ShowUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t\t\thotspot.addEventListener(\"mouseover\", stopAnimation);\r\n\t\t\t\t\t} else if (hotspotPropData?.ShowUpon === \"Clicking Hotspot\") {\r\n\t\t\t\t\t\thotspot.addEventListener(\"click\", stopAnimation);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Default to click if ShowUpon is not specified\r\n\t\t\t\t\t\thotspot.addEventListener(\"click\", stopAnimation);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\t// Note: \"Ctrl+Q\" key combination handling is now done in the Drawer component for proper navigation logic\r\n\r\n\tconst canvasProperties = toolTipGuideMetaData[currentStep - 1]?.canvas;\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{(selectedTemplate === \"Hotspot\" ||\r\n\t\t\t\tselectedTemplate === \"Tooltip\" ||\r\n\t\t\t\tselectedTemplateTour === \"Hotspot\" ||\r\n\t\t\t\tselectedTemplateTour === \"Tooltip\") &&\r\n\t\t\t\telementSelected === true && (\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tclassName=\"backdrop\"\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\t\tzIndex: 99999,\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)}\r\n\r\n\t\t\t<CustomWidthTooltip\r\n\t\t\t\tselectedTemplate={selectedTemplate}\r\n\t\t\t\tselectedTemplateTour={selectedTemplateTour}\r\n\t\t\t\ttitle={\r\n\t\t\t\t\t<>\r\n\t\t\t\t\t\t<TooltipBody\r\n\t\t\t\t\t\t\tisPopoverOpen={isPopoverOpen}\r\n\t\t\t\t\t\t\tsetIsPopoverOpen={setIsPopoverOpen}\r\n\t\t\t\t\t\t\tpopupPosition={popupPosition}\r\n\t\t\t\t\t\t\tisUnSavedChanges={isUnSavedChanges}\r\n\t\t\t\t\t\t\topenWarning={openWarning}\r\n\t\t\t\t\t\t\tsetopenWarning={setopenWarning}\r\n\t\t\t\t\t\t\thandleLeave={handleLeave}\r\n\t\t\t\t\t\t\tupdatedGuideData={updatedGuideData}\r\n\t\t\t\t\t\t\t// isRtl={isRtl}\t\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t{/* {selectedOption === 1 ? (\r\n\t\t\t\t\t\t\t<DotsStepper\r\n\t\t\t\t\t\t\t\tactiveStep={currentStep}\r\n\t\t\t\t\t\t\t\tsteps={steps.length}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t) : selectedOption === 2 ? (\r\n\t\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\t\tvalue={(currentStep / steps.length) * 100}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t) : selectedOption === 3 ? (\r\n\t\t\t\t\t\t\t<Breadcrumbs\r\n\t\t\t\t\t\t\t\taria-label=\"breadcrumb\"\r\n\t\t\t\t\t\t\t\tsx={{ marginTop: \"10px\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{steps.map((_: any, index: any) => (\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\tcolor={index === currentStepIndex ? \"primary\" : \"text.secondary\"}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\tStep {index + 1} of {steps.length}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t</Breadcrumbs>\r\n\t\t\t\t\t\t) : null} */}\r\n\t\t\t\t\t</>\r\n\t\t\t\t}\r\n\t\t\t\topen={\r\n\t\t\t\t\t(selectedTemplate === \"Hotspot\" ||\r\n\t\t\t\t\t\tselectedTemplate === \"Tooltip\" ||\r\n\t\t\t\t\t\tselectedTemplateTour === \"Tooltip\" ||\r\n\t\t\t\t\t\tselectedTemplateTour === \"Hotspot\") &&\r\n\t\t\t\t\telementSelected === true &&\r\n\t\t\t\t\topenTooltip\r\n\t\t\t\t}\r\n\t\t\t\tplacement=\"bottom\"\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\ttooltip: {\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tborderRadius: canvasProperties?.borderRadius || tooltipborderradius,\r\n\t\t\t\t\t\t\tborder: `${canvasProperties?.borderSize || \"0px\"} solid ${canvasProperties?.borderColor || tooltipBordercolor}`,\r\n\t\t\t\t\t\t\tbackgroundColor: canvasProperties?.backgroundColor || tooltipBackgroundcolor,\r\n\t\t\t\t\t\t\tpadding: canvasProperties?.padding || tooltippadding,\r\n\t\t\t\t\t\t\tWidth: `${canvasProperties?.width || \"300px\"} !important`,\r\n\t\t\t\t\t\t\tzIndex: 1000, // Ensure tooltip is above the backdrop\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<div></div>\r\n\t\t\t</CustomWidthTooltip>\r\n\t\t</>\r\n\t);\r\n};\r\nconst normalizePx = (value:any, fallback = \"8px\") => {\r\n\tif (!value) return fallback;\r\n\tconst val = typeof value === \"string\" ? value.trim() : `${value}`;\r\n\treturn val.endsWith(\"px\") ? val : `${val}px`;\r\n};\r\nexport default CreateTooltip;\r\n\r\nconst DotsStepper = ({ steps, activeStep }: { steps: number; activeStep: number }) => {\r\n\treturn (\r\n\t\t<MobileStepper\r\n\t\t\tvariant=\"dots\"\r\n\t\t\tsteps={steps}\r\n\t\t\tposition=\"static\"\r\n\t\t\tactiveStep={activeStep - 1}\r\n\t\t\tsx={{ maxWidth: 400, flexGrow: 1, display: \"flex\", justifyContent: \"center\" }}\r\n\t\t\tnextButton={<></>}\r\n\t\t\tbackButton={<></>}\r\n\t\t/>\r\n\t);\r\n};\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAUC,QAAQ,KAAQ,OAAO,CAC1D,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,MAAO,eAAe,CACtB,OAGCC,OAAO,CAEPC,cAAc,CAEdC,aAAa,KAGP,eAAe,CACtB,OAASC,MAAM,KAAQ,sBAAsB,CAG7C,MAAO,CAAAC,WAAW,KAAM,eAAe,CAEvC,OAASC,YAAY,KAAQ,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,MAAO,MAAM,CAAAC,gBAAgB,CAAG,GAAG,CACnC,MAAO,MAAM,CAAAC,gBAAgB,CAAG,GAAG,CACnC,MAAO,MAAM,CAAAC,cAAc,CAAG,GAAG,CACjC,MAAO,MAAM,CAAAC,cAAc,CAAG,gBAAgB,CAE9C,MAAO,MAAM,CAAAC,kBAAkB,CAAGb,MAAM,CACvCc,IAAA,EAKiF,IALhF,CACAC,SAAS,CACTC,gBAAgB,CAChBC,oBAAoB,CACpB,GAAGC,KACuE,CAAC,CAAAJ,IAAA,CAC3E,KAAM,CAAEK,oBAAoB,CAAEC,WAAW,CAAEC,eAAe,CAAC,CAAGzB,cAAc,CAAC,CAAC,CAC9EF,SAAS,CAAC,IAAM,CACf,GACC,CACCsB,gBAAgB,GAAK,SAAS,EAC9BA,gBAAgB,GAAK,SAAS,EAC9BC,oBAAoB,GAAK,SAAS,EAClCA,oBAAoB,GAAK,SAAS,GAC5BI,eAAe,CAErB,CACDC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CACxC,CAAC,IAAM,CACNH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,EAAE,CAClC,CAEA;AACA,MAAO,IAAM,CACZH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,EAAE,CAClC,CAAC,CACF,CAAC,CAAE,CAACT,gBAAgB,CAAEC,oBAAoB,CAAEI,eAAe,CAAC,CAAC,CAC7D,mBACCjB,IAAA,CAACP,OAAO,KACHqB,KAAK,CACTQ,OAAO,CAAE,CAAEC,MAAM,CAAEZ,SAAU,CAAE,CAC/Ba,EAAE,CAAC,gBAAgB,CACnBC,oBAAoB,MACpBC,oBAAoB,MACpBC,WAAW,CAAE,CACZhB,SAAS,CAAE,cAAc,CACzBiB,EAAE,CAAE,CACHC,GAAG,CACFjB,gBAAgB,GAAK,SAAS,EAAIC,oBAAoB,GAAK,SAAS,CACjE,mBAAmB,CACnB,mBAAmB,CACrBiB,MAAM,CAAC,kBACV,CACD,CAAE,CACF,CAAC,CAEJ,CACD,CAAC,CAACC,KAAA,EAAwD,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,IAAvD,CAAEjC,gBAA+C,CAAC,CAAAmB,KAAA,CACpD,KAAM,CAAEhB,oBAAoB,CAAEC,WAAY,CAAC,CAAGxB,cAAc,CAAC,CAAC,CAE9D,MAAO,CACN,CAAC,MAAME,cAAc,CAACoD,OAAO,EAAE,EAAG,CACjC;AACAC,QAAQ,CAAEzC,gBAAgB,CAC1B0C,KAAK,CAAE,IAAAhB,qBAAA,CAAGjB,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,UAAAgB,qBAAA,kBAAAC,sBAAA,CAArCD,qBAAA,CAAuCiB,MAAM,UAAAhB,sBAAA,iBAA7CA,sBAAA,CAA+Ce,KAAK,aAAa,EAAI,kBAAkB,CACjGE,QAAQ,CAAE,IAAAhB,sBAAA,CAAGnB,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,UAAAkB,sBAAA,kBAAAC,sBAAA,CAArCD,sBAAA,CAAuCe,MAAM,UAAAd,sBAAA,iBAA7CA,sBAAA,CAA+Ca,KAAK,aAAa,EAAI,kBAAkB,CACpGG,eAAe,CAAE,IAAAf,sBAAA,CAAGrB,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,UAAAoB,sBAAA,kBAAAC,sBAAA,CAArCD,sBAAA,CAAuCa,MAAM,UAAAZ,sBAAA,iBAA7CA,sBAAA,CAA+Cc,eAAe,EAAE,EAAI,MAAM,CAC9FC,KAAK,CAAE,aAAa,CACpBC,QAAQ,CAAE,MAAM,CAChBC,YAAY,CAAE,IAAAhB,sBAAA,CAAGvB,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,UAAAsB,sBAAA,kBAAAC,sBAAA,CAArCD,sBAAA,CAAuCW,MAAM,UAAAV,sBAAA,iBAA7CA,sBAAA,CAA+Ce,YAAY,aAAa,CACzFC,QAAQ,CAAE,qBAAqB,CAC/BC,OAAO,CAAE,IAAAhB,sBAAA,CAAGzB,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,UAAAwB,sBAAA,kBAAAC,uBAAA,CAArCD,sBAAA,CAAuCS,MAAM,UAAAR,uBAAA,iBAA7CA,uBAAA,CAA+Ce,OAAO,EAAE,EAAI,KAAK,CAC7EC,SAAS,CAAE,oFAAsF;AAClG,CAAC,CACD,CAAC,MAAM/D,cAAc,CAACgE,KAAK,EAAE,EAAG,CAC/BN,KAAK,CAAE,OAAS;AACjB,CACD,CAAC,CACD,MAAO,CACN,CAAC,MAAM1D,cAAc,CAACoD,OAAO,EAAE,EAAG,CACjC;AACAC,QAAQ,CAAEzC,gBAAgB,CAC1B4C,QAAQ,CAAE,IAAAR,uBAAA,CAAG3B,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,UAAA0B,uBAAA,kBAAAC,uBAAA,CAArCD,uBAAA,CAAuCO,MAAM,UAAAN,uBAAA,iBAA7CA,uBAAA,CAA+CK,KAAK,aAAa,EAAI,kBAAkB,CACpGG,eAAe,CAAE,OAAO,CACxBC,KAAK,CAAE,aAAa,CACpBC,QAAQ,CAAE,MAAM,CAChBC,YAAY,CAAE,GAAG,EAAAV,uBAAA,CAAA7B,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,UAAA4B,uBAAA,kBAAAC,uBAAA,CAArCD,uBAAA,CAAuCK,MAAM,UAAAJ,uBAAA,iBAA7CA,uBAAA,CAA+CS,YAAY,GAAI,KAAK,aAAa,CAClGC,QAAQ,CAAE,qBAAqB,CAC/BC,OAAO,CAAE,gBAAgB,CACzBC,SAAS,CAAE,oFAAsF;AAClG,CAAC,CACD,CAAC,MAAM/D,cAAc,CAACgE,KAAK,EAAE,EAAG,CAC/BN,KAAK,CAAE,OAAS;AACjB,CACD,CAAC,CACF,CAAC,CAAC,CAEF,KAAM,CAAAO,aAAa,CAAGC,KAAA,EAchB,KAAAC,uBAAA,IAdiB,CACtBC,uBAAuB,CACvBC,gBAAgB,CAChBC,WAAW,CACXC,cAAc,CACdC,WAAW,CACXC,gBAQD,CAAC,CAAAP,KAAA,CACA,KAAM,CACLhD,gBAAgB,CAChBwD,UAAU,CACVtB,OAAO,CACP7B,eAAe,CACfoD,kBAAkB,CAClBC,SAAS,CACTC,WAAW,CACXpB,eAAe,CACfqB,UAAU,CACVlB,YAAY,CACZE,OAAO,CACPR,KAAK,CACLyB,yBAAyB,CACzB;AACAC,YAAY,CACZC,YAAY,CACZC,YAAY,CACZC,eAAe,CACfC,iBAAiB,CACjBC,sBAAsB,CACtBC,oBAAoB,CACpBC,qBAAqB,CACrBC,yBAAyB,CACzBC,cAAc,CACdC,mBAAmB,CACnBC,iBAAiB,CACjBC,kBAAkB,CAClBC,sBAAsB,CACtBC,eAAe,CACfC,kBAAkB,CAClBC,cAAc,CACdC,KAAK,CACLC,gBAAgB,CAChBC,WAAW,CACXC,cAAc,CACdC,mBAAmB,CACnBC,eAAe,CACfjF,oBAAoB,CACpBkF,oBAAoB,CACpBjF,WAAW,CACXkF,aAAa,CACbC,gBAAgB,CAChBC,WAAW,CACXC,cAAc,CACdC,QAAQ,CACRC,mBAAmB,CACnBC,sBAAsB,CACtBC,qBAAqB,CACrBC,wBAAwB,CACxB7F,oBAAoB,CACpB8F,iBAAiB,CACjBC,WAAW,CACXC,YAAY,CACZC,0BACD,CAAC,CAAGtH,cAAc,CAAEuH,KAAU,EAAKA,KAAK,CAAC,CACzC,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAG1H,QAAQ,CAAqB,IAAI,CAAC,CAC9E,KAAM,CAAC2H,QAAQ,CAAEC,WAAW,CAAC,CAAG5H,QAAQ,CAAiB,IAAI,CAAC,CAAE;AAChE,KAAM,CAAC6H,eAAe,CAAEC,kBAAkB,CAAC,CAAG9H,QAAQ,CAK5C,IAAI,CAAC,CACf,KAAM,CAAC+H,aAAa,CAAEC,gBAAgB,CAAC,CAAGhI,QAAQ,CAAM,IAAI,CAAC,CAE7D,KAAM,CAAAiI,QAAQ,CAAIC,OAA2B,EAAK,CACjD,KAAM,CAAAC,QAAQ,CAAGA,CAACC,IAAY,CAAEC,KAAa,CAAEC,OAAe,GAAK,CAClE,KAAM,CAAAC,MAAM,CAAGL,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEM,UAAU,CAClC,GAAI,CAACD,MAAM,CAAE,MAAO,MAAK,CACzB,KAAM,CAAAE,gBAAgB,CAAG,CAAC,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,gBAAgB,CAAC,GAAGN,OAAO,IAAIF,IAAI,KAAKC,KAAK,IAAI,CAAC,CAAC,CAAC,CACnG,MAAO,CAAAI,gBAAgB,CAACI,MAAM,GAAK,CAAC,CACrC,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAIC,EAAsB,EAAK,CACrD,GAAI,CAACA,EAAE,CAAE,MAAO,KAAI,CACpB,KAAM,CAAAT,OAAO,CAAGS,EAAE,CAACT,OAAO,CAACU,WAAW,CAAC,CAAC,CAExC;AACA,KAAM,CAAAC,eAAe,CAAG,CAAC,GAAGP,KAAK,CAACC,IAAI,CAACI,EAAE,CAACG,UAAU,CAAC,CAAC,CAACC,MAAM,CAAEf,IAAI,EAAKA,IAAI,CAACgB,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC,CAAC,CACnG,IAAK,KAAM,CAAAjB,IAAI,GAAI,CAAAa,eAAe,CAAE,CACnC,GAAId,QAAQ,CAACC,IAAI,CAACgB,IAAI,CAAEhB,IAAI,CAACC,KAAK,CAAEC,OAAO,CAAC,CAAE,CAC7C,MAAO,KAAKA,OAAO,KAAKF,IAAI,CAACgB,IAAI,KAAKhB,IAAI,CAACC,KAAK,IAAI,CACrD,CACD,CACA,MAAO,KAAI,CACZ,CAAC,CAED,KAAM,CAAAiB,UAAU,CAAIP,EAAsB,EAAK,CAC9C,GAAI,CAACA,EAAE,CAAE,MAAO,KAAI,CACpB,KAAM,CAAAT,OAAO,CAAGS,EAAE,CAACT,OAAO,CAACU,WAAW,CAAC,CAAC,CACxC,KAAM,CAAAO,cAAc,CAAGT,iBAAiB,CAACC,EAAE,CAAC,CAC5C,KAAM,CAAAS,eAAe,CAAG7H,QAAQ,CAAC8H,aAAa,CAAC,oBAAoB,CAAC,CACpE,GAAID,eAAe,CAAE,CACpB,GAAIT,EAAE,CAACW,OAAO,CAAC,oBAAoB,CAAC,CAAE,CACrC,MAAO,KAAI,CACZ,CACD,CACA,GAAIH,cAAc,CAAE,MAAO,CAAAA,cAAc,CAEzC,GAAI,CAAAI,YAAY,CAAG,CAAC,CACpB,GAAI,CAAAC,OAAO,CAAGb,EAAE,CAACc,sBAAsB,CAEvC,MAAOD,OAAO,CAAE,CACf,GAAIA,OAAO,CAACtB,OAAO,GAAKS,EAAE,CAACT,OAAO,CAAEqB,YAAY,EAAE,CAClDC,OAAO,CAAGA,OAAO,CAACC,sBAAsB,CACzC,CAEA,MAAO,GAAGvB,OAAO,IAAIqB,YAAY,GAAG,CACrC,CAAC,CAED,KAAM,CAAAG,QAAkB,CAAG,EAAE,CAE7B,GAAI,CAAAC,cAAc,CAAG7B,OAAO,CAE5B,MAAO6B,cAAc,EAAIA,cAAc,GAAKpI,QAAQ,CAACC,IAAI,EAAImI,cAAc,GAAKpI,QAAQ,CAACqI,eAAe,CAAE,CACzG,KAAM,CAAAC,OAAO,CAAGX,UAAU,CAACS,cAAc,CAAC,CAC1C,GAAI,CAACE,OAAO,CAAE,MACdH,QAAQ,CAACI,OAAO,CAACD,OAAO,CAAC,CACzB,GAAIA,OAAO,CAACZ,UAAU,CAAC,OAAO,CAAC,CAAE,MACjCU,cAAc,CAAGA,cAAc,CAACI,aAA4B,CAC7D,CAEA,GAAI,CAACL,QAAQ,CAAC,CAAC,CAAC,CAACT,UAAU,CAAC,IAAI,CAAC,CAAE,CAClC,GAAIS,QAAQ,CAAC,CAAC,CAAC,GAAK,MAAM,CAAE,CAC3BA,QAAQ,CAACI,OAAO,CAAC,MAAM,CAAC,CACzB,CACA,GAAIJ,QAAQ,CAAC,CAAC,CAAC,GAAK,MAAM,CAAE,CAC3BA,QAAQ,CAACM,MAAM,CAAC,CAAC,CAAE,CAAC,CAAE,MAAM,CAAC,CAC9B,CACD,CACA,MAAO,GAAGN,QAAQ,CAACO,IAAI,CAAC,GAAG,CAAC,EAAE,CAC/B,CAAC,CACD,KAAM,CAAAC,gBAAgB,CAAIpC,OAA2B,EAAK,CACzD;AACA,GAAI,CAACA,OAAO,EAAI,CAACA,OAAO,CAACI,OAAO,CAAE,MAAO,KAAI,CAE7C,KAAM,CAAAH,QAAQ,CAAIoC,KAAa,EAAK,CACnC,GAAI,CACH,KAAM,CAAA9B,gBAAgB,CAAG9G,QAAQ,CAAC6I,QAAQ,CAACD,KAAK,CAAE5I,QAAQ,CAAE,IAAI,CAAE8I,WAAW,CAACC,0BAA0B,CAAE,IAAI,CAAC,CAC/G,MAAO,CAAAjC,gBAAgB,CAACkC,cAAc,GAAK,CAAC,CAC7C,CAAE,MAAOC,CAAC,CAAE,CACX,MAAO,MAAK,CACb,CACD,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAIC,UAAkB,EAAK,CAChD;AACA,MACC,CAAAA,UAAU,CAACC,QAAQ,CAAC,4BAA4B,CAAC,EACjDD,UAAU,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAC3CD,UAAU,CAACC,QAAQ,CAAC,iBAAiB,CAAC,CAExC,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAI5J,SAAiB,EAAK,CAC/C;AACA,MAAO,CAAAA,SAAS,GAAK,sBAAsB,CAC5C,CAAC,CAED,KAAM,CAAA0H,iBAAiB,CAAIC,EAAe,EAAK,CAC9C,KAAM,CAAAT,OAAO,CAAGS,EAAE,CAACT,OAAO,CAACU,WAAW,CAAC,CAAC,CAExC;AACA,GAAID,EAAE,CAAC9G,EAAE,CAAE,CACV,KAAM,CAAAsI,KAAK,CAAG,KAAKjC,OAAO,SAASS,EAAE,CAAC9G,EAAE,IAAI,CAC5C,GAAIkG,QAAQ,CAACoC,KAAK,CAAC,CAAE,MAAO,CAAAA,KAAK,CAClC,CAEA;AACA,GAAIxB,EAAE,CAAC3H,SAAS,CAAE,CACjB,KAAM,CAAAW,OAAO,CAAGgH,EAAE,CAAC3H,SAAS,CAC1B6J,IAAI,CAAC,CAAC,CACNC,KAAK,CAAC,KAAK,CAAC,CACZ/B,MAAM,CAAEgC,GAAG,EAAK,CAACH,gBAAgB,CAACG,GAAG,CAAC,CAAC,CACzC,IAAK,KAAM,CAAA/J,SAAS,GAAI,CAAAW,OAAO,CAAE,CAChC,GAAIX,SAAS,CAAE,CACd,KAAM,CAAAmJ,KAAK,CAAG,KAAKjC,OAAO,sBAAsBlH,SAAS,KAAK,CAC9D,GAAI+G,QAAQ,CAACoC,KAAK,CAAC,CAAE,MAAO,CAAAA,KAAK,CAClC,CACD,CACD,CAEA;AACA,KAAM,CAAAtB,eAAe,CAAG,CAAC,GAAGP,KAAK,CAACC,IAAI,CAACI,EAAE,CAACG,UAAU,CAAC,CAAC,CAACC,MAAM,CAAEf,IAAI,EAAKA,IAAI,CAACgB,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC,CAAC,CACnG,IAAK,KAAM,CAAAjB,IAAI,GAAI,CAAAa,eAAe,CAAE,CACnC,KAAM,CAAAsB,KAAK,CAAG,KAAKjC,OAAO,KAAKF,IAAI,CAACgB,IAAI,KAAKhB,IAAI,CAACC,KAAK,IAAI,CAC3D,GAAIF,QAAQ,CAACoC,KAAK,CAAC,CAAE,MAAO,CAAAA,KAAK,CAClC,CAEA;AACA,KAAM,CAAArB,UAAU,CAAG,CAAC,GAAGR,KAAK,CAACC,IAAI,CAACI,EAAE,CAACG,UAAU,CAAC,CAAC,CAACC,MAAM,CACtDf,IAAI,EACJA,IAAI,CAACgB,IAAI,GAAK,IAAI,EAClBhB,IAAI,CAACgB,IAAI,GAAK,OAAO,EACrB,CAAChB,IAAI,CAACgB,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC,EAC3BjB,IAAI,CAACgB,IAAI,GAAK,UAAU,EACxB,EAAEhB,IAAI,CAACgB,IAAI,GAAK,OAAO,EAAIyB,gBAAgB,CAACzC,IAAI,CAACC,KAAK,CAAC,CACzD,CAAC,CACD,IAAK,KAAM,CAAAD,IAAI,GAAI,CAAAc,UAAU,CAAE,CAC9B,KAAM,CAAAqB,KAAK,CAAG,KAAKjC,OAAO,KAAKF,IAAI,CAACgB,IAAI,KAAKhB,IAAI,CAACC,KAAK,IAAI,CAC3D,GAAIF,QAAQ,CAACoC,KAAK,CAAC,CAAE,MAAO,CAAAA,KAAK,CAClC,CAEA,MAAO,KAAI,CACZ,CAAC,CAED,GAAI,CAAAR,cAAc,CAAG7B,OAAO,CAC5B,GAAI,CAAAkD,YAAsB,CAAG,EAAE,CAE/B,MAAOrB,cAAc,EAAIA,cAAc,GAAKpI,QAAQ,CAACqI,eAAe,CAAE,KAAAqB,qBAAA,CACrE,KAAM,CAAA9B,cAAc,CAAGT,iBAAiB,CAACiB,cAAc,CAAC,CAExD,GAAIR,cAAc,CAAE,CACnB,MAAO,CAAAA,cAAc,CACtB,CAEA;AACA,KAAM,CAAAjB,OAAO,CAAGyB,cAAc,CAACzB,OAAO,CAACU,WAAW,CAAC,CAAC,CACpD,KAAM,CAAAsC,QAAQ,CAAG5C,KAAK,CAACC,IAAI,CAAC,EAAA0C,qBAAA,CAAAtB,cAAc,CAACI,aAAa,UAAAkB,qBAAA,iBAA5BA,qBAAA,CAA8BE,QAAQ,GAAI,EAAE,CAAC,CAACpC,MAAM,CAC9EqC,GAAG,EAAKA,GAAG,CAAClD,OAAO,GAAKyB,cAAc,CAACzB,OACzC,CAAC,CACD,KAAM,CAAAmD,KAAK,CAAGH,QAAQ,CAACI,OAAO,CAAC3B,cAAc,CAAC,CAAG,CAAC,CAElDqB,YAAY,CAAClB,OAAO,CAACuB,KAAK,CAAG,CAAC,CAAG,GAAGnD,OAAO,IAAImD,KAAK,GAAG,CAAGnD,OAAO,CAAC,CAClEyB,cAAc,CAAGA,cAAc,CAACI,aAA4B,CAC7D,CAEA;AACA,KAAM,CAAAwB,eAAe,CAAGhK,QAAQ,CAAC8H,aAAa,CAAC,oBAAoB,CAAC,CACpE,GAAIkC,eAAe,EAAIzD,OAAO,CAACwB,OAAO,CAAC,oBAAoB,CAAC,CAAE,CAC7D,MAAO,KAAI,CACZ,CAEA,MAAO,KAAK0B,YAAY,CAACf,IAAI,CAAC,GAAG,CAAC,EAAE,CACrC,CAAC,CAED;AACA;AACA;AACA;AAEA,KAAM,CAAAuB,qBAAqB,CAAI1D,OAAoB,EAAK,CACvD,MACC,CAAAA,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAClD5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,gCAAgC,CAAC,EAC5D5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,EACzC5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,oBAAoB,CAAC,EAChD5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAClD5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,EACjD5D,OAAO,CAAC6D,YAAY,CAAC,MAAM,CAAC,GAAK,SAAS,EAC1C7D,OAAO,CAACwB,OAAO,CAAC,iBAAiB,CAAC,EAClCxB,OAAO,CAACwB,OAAO,CAAC,kBAAkB,CAAC,EACnCxB,OAAO,CAACwB,OAAO,CAAC,0BAA0B,CAAC,EAC3CxB,OAAO,CAACwB,OAAO,CAAC,sBAAsB,CAAC,EACvCxB,OAAO,CAACwB,OAAO,CAAC,iBAAiB,CAAC,EAClCxB,OAAO,CAACwB,OAAO,CAAC,eAAe,CAAC,EAChCxB,OAAO,CAACwB,OAAO,CAAC,mBAAmB,CAAC,EACpCxB,OAAO,CAACwB,OAAO,CAAC,aAAa,CAAC,EAC9BxB,OAAO,CAACwB,OAAO,CAAC,gBAAgB,CAAC,EACjCxB,OAAO,CAACwB,OAAO,CAAC,aAAa,CAAC,EAC9BxB,OAAO,CAACwB,OAAO,CAAC,gBAAgB,CAAC,EACjCxB,OAAO,CAACwB,OAAO,CAAC,cAAc,CAAC,EAC/BxB,OAAO,CAACwB,OAAO,CAAC,gBAAgB,CAAC,EACjCxB,OAAO,CAACwB,OAAO,CAAC,qBAAqB,CAAC,EACtCxB,OAAO,CAACwB,OAAO,CAAC,WAAW,CAAC,EAC5BxB,OAAO,CAACwB,OAAO,CAAC,WAAW,CAAC,EAC5BxB,OAAO,CAACwB,OAAO,CAAC,aAAa,CAAC,EAC9BxB,OAAO,CAACwB,OAAO,CAAC,gBAAgB,CAAC,EACjCxB,OAAO,CAACwB,OAAO,CAAC,oBAAoB,CAAC,EACrCxB,OAAO,CAACwB,OAAO,CAAC,2BAA2B,CAAC,EAC5CxB,OAAO,CAACwB,OAAO,CAAC,WAAW,CAAC,EAC5BxB,OAAO,CAACwB,OAAO,CAAC,iBAAiB,CAAC,EAClCxB,OAAO,CAACwB,OAAO,CAAC,yBAAyB,CAAC,EAC1CxB,OAAO,CAACwB,OAAO,CAAC,qBAAqB,CAAC,EACtCxB,OAAO,CAACwB,OAAO,CAAC,yBAAyB,CAAC,EAC1CxB,OAAO,CAACwB,OAAO,CAAC,mBAAmB,CAAC,EACpCxB,OAAO,CAACwB,OAAO,CAAC,8BAA8B,CAAC,EAC/CxB,OAAO,CAACwB,OAAO,CAAC,kBAAkB,CAAC,EACnCxB,OAAO,CAACwB,OAAO,CAAC,cAAc,CAAC,EAC/BxB,OAAO,CAACwB,OAAO,CAAC,qBAAqB,CAAC,EACtCxB,OAAO,CAACwB,OAAO,CAAC,YAAY,CAAC,EAC7BxB,OAAO,CAACwB,OAAO,CAAC,uBAAuB,CAAC,EACxCxB,OAAO,CAACwB,OAAO,CAAC,wBAAwB,CAAC,EACzCxB,OAAO,CAACwB,OAAO,CAAC,UAAU,CAAC,EAC3BxB,OAAO,CAACwB,OAAO,CAACxB,OAAO,CAACjG,EAAE,CAACoH,UAAU,CAAC,YAAY,CAAC,CAAG,GAAGnB,OAAO,CAACjG,EAAE,EAAE,CAAG,MAAM,CAAC,EAC/EiG,OAAO,CAACwB,OAAO,CAAC,kBAAkB,CAAC,EACnCxB,OAAO,CAACwB,OAAO,CAAC,oBAAoB,CAAC,EACrCxB,OAAO,CAACwB,OAAO,CAAC,mBAAmB,CAAC,EACpCxB,OAAO,CAACwB,OAAO,CAAC,cAAc,CAAC,EAC/BxB,OAAO,CAACwB,OAAO,CAAC,gBAAgB,CAAC,EACjCxB,OAAO,CAACwB,OAAO,CAAC,oBAAoB,CAAC,EACrCxB,OAAO,CAACwB,OAAO,CAAC,qBAAqB,CAAC,EACtCxB,OAAO,CAACwB,OAAO,CAAC,2BAA2B,CAAC,CAE9C,CAAC,CACD,KAAM,CAAAsC,kBAAkB,CAAI9D,OAAoB,EAAK,CACpD,MACC,CAAAA,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAClD5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,gCAAgC,CAAC,EAC5D5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,EACzC5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,oBAAoB,CAAC,EAChD5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAClD5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,EACjD5D,OAAO,CAAC6D,YAAY,CAAC,MAAM,CAAC,GAAK,SAAS,EAC1C7D,OAAO,CAACwB,OAAO,CAAC,iBAAiB,CAAC,EAClCxB,OAAO,CAACwB,OAAO,CAAC,0BAA0B,CAAC,EAC3CxB,OAAO,CAACwB,OAAO,CAAC,sBAAsB,CAAC,EACvCxB,OAAO,CAACwB,OAAO,CAAC,iBAAiB,CAAC,EAClCxB,OAAO,CAACwB,OAAO,CAAC,eAAe,CAAC,EAChCxB,OAAO,CAACwB,OAAO,CAAC,mBAAmB,CAAC,EACpCxB,OAAO,CAACwB,OAAO,CAAC,aAAa,CAAC,EAC9BxB,OAAO,CAACwB,OAAO,CAAC,cAAc,CAAC,EAC/BxB,OAAO,CAACwB,OAAO,CAACxB,OAAO,CAACjG,EAAE,CAACoH,UAAU,CAAC,YAAY,CAAC,CAAG,GAAGnB,OAAO,CAACjG,EAAE,EAAE,CAAG,MAAM,CAAC,EAC/EiG,OAAO,CAACwB,OAAO,CAAC,UAAU,CAAC,EAC3BxB,OAAO,CAACwB,OAAO,CAAC,kBAAkB,CAAC,EACnCxB,OAAO,CAACwB,OAAO,CAAC,2BAA2B,CAAC,EAC5CxB,OAAO,CAACwB,OAAO,CAAC,oBAAoB,CAAC,CAEvC,CAAC,CAED,GAAI,CAAAuC,OAAY,CAChB,KAAM,CAAAC,sBAAsB,CAAGA,CAAA,GAAM,CACpC,KAAM,CAAAC,mBAAmB,CAAGxK,QAAQ,CAAC8H,aAAa,CAAC,uBAAuB,CAAgB,CAC1F,GAAI0C,mBAAmB,CAAE,CACxBC,UAAU,CAAC,IAAM,CAChBD,mBAAmB,CAACtK,KAAK,CAACwK,OAAO,CAAG,MAAM,CAC1C;AACAF,mBAAmB,CAACtK,KAAK,CAACqC,SAAS,CAAG,EAAE,CACxCiI,mBAAmB,CAACtK,KAAK,CAACyK,aAAa,CAAG,EAAE,CAC5CH,mBAAmB,CAACI,eAAe,CAAC,UAAU,CAAC,CAC/CJ,mBAAmB,CAACN,SAAS,CAACW,MAAM,CAAC,sBAAsB,CAAC,CAC7D,CAAC,CAAE,EAAE,CAAC,CACP,CACD,CAAC,CAED,KAAM,CAAAC,0BAA0B,CAAGA,CAAA,GAAM,CACxC;AACA,KAAM,CAAAC,mBAAmB,CAAG/K,QAAQ,CAACiH,gBAAgB,CAAC,uBAAuB,CAAC,CAC9E8D,mBAAmB,CAACC,OAAO,CAAEzE,OAAgB,EAAK,CACjD,KAAM,CAAA0E,WAAW,CAAG1E,OAAsB,CAC1C0E,WAAW,CAAC/K,KAAK,CAACwK,OAAO,CAAG,EAAE,CAC9B;AACAO,WAAW,CAAC/K,KAAK,CAACqC,SAAS,CAAG,EAAE,CAChC0I,WAAW,CAAC/K,KAAK,CAACyK,aAAa,CAAG,EAAE,CACpCM,WAAW,CAACL,eAAe,CAAC,UAAU,CAAC,CACvCK,WAAW,CAACf,SAAS,CAACW,MAAM,CAAC,sBAAsB,CAAC,CACrD,CAAC,CAAC,CAEF;AACA,KAAM,CAAAK,cAAc,CAAGlL,QAAQ,CAACiH,gBAAgB,CAAC,8BAA8B,CAAC,CAChFiE,cAAc,CAACF,OAAO,CAAEzE,OAAgB,EAAK,CAC5C,KAAM,CAAA0E,WAAW,CAAG1E,OAAsB,CAC1C0E,WAAW,CAAC/K,KAAK,CAACwK,OAAO,CAAG,EAAE,CAC9B;AACAO,WAAW,CAAC/K,KAAK,CAACqC,SAAS,CAAG,EAAE,CAChC0I,WAAW,CAAC/K,KAAK,CAACyK,aAAa,CAAG,EAAE,CACpCM,WAAW,CAACL,eAAe,CAAC,UAAU,CAAC,CACvCK,WAAW,CAACL,eAAe,CAAC,qBAAqB,CAAC,CACnD,CAAC,CAAC,CAEF;AACA,KAAM,CAAAO,kBAAkB,CAAGnL,QAAQ,CAACiH,gBAAgB,CAAC,sCAAsC,CAAC,CAC5FkE,kBAAkB,CAACH,OAAO,CAAEzE,OAAgB,EAAK,CAChD,KAAM,CAAA0E,WAAW,CAAG1E,OAAsB,CAC1C0E,WAAW,CAAC/K,KAAK,CAACwK,OAAO,CAAG,EAAE,CAC9BO,WAAW,CAAC/K,KAAK,CAACkL,aAAa,CAAG,EAAE,CACpC;AACAH,WAAW,CAAC/K,KAAK,CAACqC,SAAS,CAAG,EAAE,CAChC0I,WAAW,CAAC/K,KAAK,CAACyK,aAAa,CAAG,EAAE,CACpCM,WAAW,CAACL,eAAe,CAAC,UAAU,CAAC,CACvCK,WAAW,CAACL,eAAe,CAAC,6BAA6B,CAAC,CAC3D,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAS,uBAAuB,CAAI9E,OAAoB,EAAK,CACzDA,OAAO,CAACqE,eAAe,CAAC,UAAU,CAAC,CACnCrE,OAAO,CAACrG,KAAK,CAACwK,OAAO,CAAG,EAAE,CAC1BnE,OAAO,CAACrG,KAAK,CAACyK,aAAa,CAAG,OAAO,CACtC,CAAC,CAED;AACA,KAAM,CAAAW,yBAAyB,CAAGA,CAAA,GAAM,CACvC;AACA,KAAM,CAAAC,SAAS,CAAG3M,YAAY,CAC9B,KAAM,CAAA4M,UAAU,CAAGC,kBAAkB,CAACF,SAAS,CAAC,CAChD,MAAO,sBAAsBC,UAAU,EAAE,CAC1C,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,aAAa,CAAGL,yBAAyB,CAAC,CAAC,CACjD;AACA;AACAtL,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC0L,MAAM,CAAG,QAAQD,aAAa,qBAAqB,CACvE3L,QAAQ,CAACqI,eAAe,CAACnI,KAAK,CAAC0L,MAAM,CAAG,QAAQD,aAAa,qBAAqB,CAElF;AACA,KAAM,CAAAzL,KAAK,CAAGF,QAAQ,CAAC6L,aAAa,CAAC,OAAO,CAAC,CAC7C3L,KAAK,CAACI,EAAE,CAAG,gCAAgC,CAC3CJ,KAAK,CAAC4L,WAAW,CAAG;AACtB;AACA,mBAAmBH,aAAa;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,CACD3L,QAAQ,CAAC+L,IAAI,CAACC,WAAW,CAAC9L,KAAK,CAAC,CACjC,CAAC,CAED,KAAM,CAAA+L,kBAAkB,CAAGA,CAAA,GAAM,CAChC;AACAjM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC0L,MAAM,CAAG,EAAE,CAC/B5L,QAAQ,CAACqI,eAAe,CAACnI,KAAK,CAAC0L,MAAM,CAAG,EAAE,CAE1C;AACA,KAAM,CAAAM,aAAa,CAAGlM,QAAQ,CAACmM,cAAc,CAAC,gCAAgC,CAAC,CAC/E,GAAID,aAAa,CAAE,CAClBA,aAAa,CAACrB,MAAM,CAAC,CAAC,CACvB,CACD,CAAC,CAEDzM,SAAS,CAAC,IAAM,CACf,GAAI,CAAAgO,cAAc,CAAG,KAAK,CAE1B,KAAM,CAAAC,eAAe,CAAIC,KAAiB,EAAK,CAChD,GAAIjH,mBAAmB,EAAIE,qBAAqB,EAAI,CAAC3C,uBAAuB,EAAI,CAAC8C,WAAW,CAAE,CAC7F,KAAM,CAAAa,OAAO,CAAG+F,KAAK,CAACC,MAAqB,CAC3C/G,wBAAwB,CAACe,OAAO,CAAC,CAEjC,GAAI,CAAC0D,qBAAqB,CAAC1D,OAAO,CAAC,CAAE,CACpCgE,sBAAsB,CAAC,CAAC,CAExB,KAAM,CAAAiC,IAAI,CAAGjG,OAAO,CAACkG,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAAC,OAAO,CAAG1M,QAAQ,CAAC6L,aAAa,CAAC,KAAK,CAAC,CAC7Ca,OAAO,CAACjN,SAAS,CAAG,oBAAoB,CACxCkN,MAAM,CAACC,MAAM,CAACF,OAAO,CAACxM,KAAK,CAAE,CAC5BmC,QAAQ,CAAE,OAAO,CACjB1B,GAAG,CAAE,GAAG6L,IAAI,CAAC7L,GAAG,CAAGkM,MAAM,CAACC,OAAO,IAAI,CACrCC,IAAI,CAAE,GAAGP,IAAI,CAACO,IAAI,CAAGF,MAAM,CAACG,OAAO,IAAI,CACvClL,KAAK,CAAE,GAAG0K,IAAI,CAAC1K,KAAK,IAAI,CACxBmL,MAAM,CAAE,GAAGT,IAAI,CAACS,MAAM,IAAI,CAC1BhL,eAAe,CAAE,0BAA0B,CAC3CyI,OAAO,CAAE,oBAAoB,CAC7BnI,SAAS,CAAE,uBAAuB,CAClCoI,aAAa,CAAE,MAAM,CACrB/J,MAAM,CAAE,MACT,CAAC,CAAC,CAEF8L,OAAO,CAACQ,YAAY,CAAC,cAAc,CAAE,MAAM,CAAC,CAE5ClN,QAAQ,CAACC,IAAI,CAAC+L,WAAW,CAACU,OAAO,CAAC,CAElC;AACAnG,OAAO,CAAC4G,gBAAgB,CAAC,UAAU,CAAE,IAAM,CAC1CnN,QAAQ,CAACiH,gBAAgB,CAAC,uBAAuB,CAAC,CAAC+D,OAAO,CAAC5D,EAAE,EAAIA,EAAE,CAACyD,MAAM,CAAC,CAAC,CAAC,CAC9E,CAAC,CAAE,CAAEuC,IAAI,CAAE,IAAK,CAAC,CAAC,CACnB,CACD,CACD,CAAC,CACC,KAAM,CAAAC,cAAc,CAAIf,KAAiB,EAAK,CAC7C,KAAM,CAAA/F,OAAO,CAAG+F,KAAK,CAACC,MAAqB,CAC3C/G,wBAAwB,CAACe,OAAsB,CAAC,CAChD,GAAI,CAAC0D,qBAAqB,CAAC1D,OAAO,CAAC,CAAE,CACpC,GAAI,CAAC,OAAO,CAAE,QAAQ,CAAE,UAAU,CAAE,QAAQ,CAAE,GAAG,CAAE,QAAQ,CAAC,CAAC6C,QAAQ,CAAC7C,OAAO,CAACI,OAAO,CAACU,WAAW,CAAC,CAAC,CAAC,CAAE,CACrGd,OAAO,CAACrG,KAAK,CAACyK,aAAa,CAAG,EAAE,CACjC,CACApE,OAAO,CAACqE,eAAe,CAAC,UAAU,CAAC,CACnCrE,OAAO,CAACrG,KAAK,CAACwK,OAAO,CAAG,EAAE,CAC1B;AACAnE,OAAO,CAACrG,KAAK,CAACqC,SAAS,CAAG,EAAE,CAC5B6J,cAAc,CAAG,KAAK,CACvB,CACD,CAAC,CACD,KAAM,CAAAkB,eAAe,CAAGtN,QAAQ,CAACmM,cAAc,CAAC,sBAAsB,CAAC,CAEvE,KAAM,CAAAoB,WAAW,CAAIjB,KAAiB,EAAK,CAC1C,GAAI,CAAC7G,iBAAiB,EAAI,CAAC7C,uBAAuB,EAAI,CAAC8C,WAAW,CAAE,CACnE,KAAM,CAAAa,OAAO,CAAG+F,KAAK,CAACC,MAAqB,CAC3CD,KAAK,CAACkB,eAAe,CAAC,CAAC,CACvBlB,KAAK,CAACmB,wBAAwB,CAAC,CAAC,CAChCnB,KAAK,CAACoB,cAAc,CAAC,CAAC,CACtBpB,KAAK,CAACmB,wBAAwB,CAAC,CAAC,CAChCtK,kBAAkB,CAAC,IAAI,CAAC,CACxB,GAAIkC,mBAAmB,CAAE,CACxB,GAAI,CAAC4E,qBAAqB,CAAC1D,OAAO,CAAC,EAAI,CAAC8D,kBAAkB,CAAC9D,OAAO,CAAC,CAAE,CACpE,GAAI,CAACA,OAAO,CAACoH,YAAY,CAAC,qBAAqB,CAAC,CAAE,CACjDpH,OAAO,CAAC2G,YAAY,CAAC,qBAAqB,CAAE,MAAM,CAAC,CACpD,CACA,KAAM,CAAAtE,KAAK,CAAGtC,QAAQ,CAACC,OAAO,CAAC,CAC/B;AACAA,OAAO,CAACrG,KAAK,CAACwK,OAAO,CAAG,EAAE,CAC1B,KAAM,CAAAkD,gBAAgB,CAAGjF,gBAAgB,CAACpC,OAAO,CAAC,CAClD;AACA;AACAA,OAAO,CAACrG,KAAK,CAACwK,OAAO,CAAG,oBAAoB,CAC5CnE,OAAO,CAACrG,KAAK,CAACqC,SAAS,CAAG,uBAAuB,CACjDsL,OAAO,CAACC,GAAG,CAACF,gBAAgB,CAAC,CAC7B,KAAM,CAAApB,IAAI,CAAGjG,OAAO,CAACkG,qBAAqB,CAAC,CAAC,CAC5CpG,gBAAgB,CAAC,CAChB0G,IAAI,CAAE,GAAGP,IAAI,CAACO,IAAI,IAAI,CACtBpM,GAAG,CAAE,GAAG4F,OAAO,CAACwH,YAAY,CAAGlB,MAAM,CAACC,OAAO,IAC9C,CAAC,CAAC,CAEF7G,WAAW,CAACuG,IAAI,CAAC,CACjBtH,WAAW,CAACsH,IAAI,CAAC,CAEjB,GAAI,CAAC,CAAC,MAAM,CAAE,MAAM,CAAC,CAACpD,QAAQ,CAAC7C,OAAO,CAACI,OAAO,CAAC,CAAE,CAChD,KAAM,CAAArC,eAAe,CAAG,CAAE0J,CAAC,CAAExB,IAAI,CAACO,IAAI,CAAG,CAAC,CAAEkB,CAAC,CAAEzB,IAAI,CAAC7L,GAAI,CAAC,CACzDuC,UAAU,CAAC,CACVgL,OAAO,CAAE,IAAI,CACbC,IAAI,CAAEvF,KAAK,CACXvG,QAAQ,CAAE,CAAE2L,CAAC,CAAExB,IAAI,CAACO,IAAI,CAAG,CAAC,CAAEkB,CAAC,CAAEzB,IAAI,CAAC7L,GAAI,CAC3C,CAAC,CAAC,CACF,GAAIiI,KAAK,CAAE,CACVrF,yBAAyB,CAAC,CACzBmD,KAAK,CAAEkC,KAAK,CACZwF,mBAAmB,CAAER,gBAAgB,CACrCvL,QAAQ,CAAEiC,eACX,CAAC,CAAC,CAEF;AACA,GAAIqB,YAAY,EAAIjG,gBAAgB,GAAK,SAAS,CAAE,CACnD;AACA+K,UAAU,CAAC,IAAM,CAChB7E,0BAA0B,CAAC,CAAC,CAC7B,CAAC,CAAE,CAAC,CAAC,CACN,CACD,CACAhB,cAAc,CAAC,IAAI,CAAC,CACpB,GAAI,CAAC0I,eAAe,CAAE,CACrBe,sBAAsB,CAAC7B,IAAI,CAAC,CAC7B,CACD,CACD,CACD,CACD,CACD,CAAC,CAED,KAAM,CAAA8B,mBAAmB,CAAIhC,KAAiB,EAAK,CAClD,KAAM,CAAAC,MAAM,CAAGD,KAAK,CAACC,MAAqB,CAE1C,GAAIlH,mBAAmB,EAAI,CAACI,iBAAiB,CAAE,CAC9C,GAAI,CAACwE,qBAAqB,CAACsC,MAAM,CAAC,EAAI,CAAClC,kBAAkB,CAACkC,MAAM,CAAC,CAAE,CAClEgB,WAAW,CAACjB,KAAK,CAAC,CACnB,CACD,CACD,CAAC,CAED,GAAItG,QAAQ,EAAIsH,eAAe,CAAE,CAChCe,sBAAsB,CAACrI,QAAQ,CAAC,CACjC,CAEA,GAAI,CAACX,mBAAmB,EAAIE,qBAAqB,CAAE,CAClD;AACAuF,0BAA0B,CAAC,CAAC,CAC5B;AACAmB,kBAAkB,CAAC,CAAC,CACpBjM,QAAQ,CAACuO,mBAAmB,CAAC,WAAW,CAAElC,eAAe,CAAC,CAC1DrM,QAAQ,CAACuO,mBAAmB,CAAC,UAAU,CAAElB,cAAc,CAAC,CACxDrN,QAAQ,CAACuO,mBAAmB,CAAC,OAAO,CAAED,mBAAmB,CAAE,IAAI,CAAC,CAChE,OACD,CAAC,IAAM,IAAIvO,eAAe,GAAK,KAAK,GAAKL,gBAAgB,GAAK,SAAS,EAAIC,oBAAoB,GAAK,SAAS,CAAC,CAAE,CAC/G;AACA,GAAI0F,mBAAmB,CAAE,CACxBqG,iBAAiB,CAAC,CAAC,CACpB,CACA1L,QAAQ,CAACmN,gBAAgB,CAAC,WAAW,CAAEd,eAAe,CAAC,CACvDrM,QAAQ,CAACmN,gBAAgB,CAAC,UAAU,CAAEE,cAAc,CAAC,CACrDrN,QAAQ,CAACmN,gBAAgB,CAAC,OAAO,CAAEmB,mBAAmB,CAAE,IAAI,CAAC,CAC9D,CAAC,IAAM,IACN,CAACvO,eAAe,GACfL,gBAAgB,GAAK,SAAS,EAAIC,oBAAoB,GAAK,SAAS,CAAC,EACtE0F,mBAAmB,CAClB,CACD;AACAqG,iBAAiB,CAAC,CAAC,CACnB1L,QAAQ,CAACmN,gBAAgB,CAAC,WAAW,CAAEd,eAAe,CAAC,CACvDrM,QAAQ,CAACmN,gBAAgB,CAAC,UAAU,CAAEE,cAAc,CAAC,CACrDrN,QAAQ,CAACmN,gBAAgB,CAAC,OAAO,CAAEmB,mBAAmB,CAAE,IAAI,CAAC,CAC7DnL,kBAAkB,CAAC,KAAK,CAAC,CAC1B,CAEA,MAAO,IAAM,CACZnD,QAAQ,CAACuO,mBAAmB,CAAC,WAAW,CAAElC,eAAe,CAAC,CAC1DrM,QAAQ,CAACuO,mBAAmB,CAAC,UAAU,CAAElB,cAAc,CAAC,CACxDrN,QAAQ,CAACuO,mBAAmB,CAAC,OAAO,CAAED,mBAAmB,CAAE,IAAI,CAAC,CAChE;AACArC,kBAAkB,CAAC,CAAC,CACrB,CAAC,CACF,CAAC,CAAE,CAACpM,oBAAoB,CAAEE,eAAe,CAAEuD,UAAU,CAAE+B,mBAAmB,CAAEE,qBAAqB,CAAC,CAAC,CAEnG;AACAnH,SAAS,CAAC,IAAM,CACf,GAAIiH,mBAAmB,EAAI,CAACtF,eAAe,GACzCL,gBAAgB,GAAK,SAAS,EAAIA,gBAAgB,GAAK,SAAS,EAChEC,oBAAoB,GAAK,SAAS,EAAIA,oBAAoB,GAAK,SAAS,CAAC,CAAE,CAC5E;AACA+L,iBAAiB,CAAC,CAAC,CACpB,CAAC,IAAM,CACN;AACAO,kBAAkB,CAAC,CAAC,CACrB,CAEA;AACA,MAAO,IAAM,CACZA,kBAAkB,CAAC,CAAC,CACrB,CAAC,CACF,CAAC,CAAE,CAAC5G,mBAAmB,CAAEtF,eAAe,CAAEL,gBAAgB,CAAEC,oBAAoB,CAAC,CAAC,CAElF,KAAM,CAAA6O,iBAAiB,CAAGA,CAAA,GAAM,CAC/B,MAAO,sBAAsB,CAC9B,CAAC,CAED,KAAM,CAAAH,sBAAsB,CAAI7B,IAAS,EAAK,KAAAiC,uBAAA,CAC7C,KAAM,CAAAC,eAAe,CAAG7O,oBAAoB,IAAA4O,uBAAA,CAAI5O,oBAAoB,CAAC,CAAC,CAAC,UAAA4O,uBAAA,iBAAvBA,uBAAA,CAAyBE,QAAQ,EACjF;AACA;AACA,KAAM,CAAAC,IAAI,CAAGF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEG,IAAI,CAClC;AACA;AACA,KAAM,CAAA9B,IAAI,CAAG,KAAK,CAClB,KAAM,CAAApM,GAAG,CAAG,KAAK,CACjB,GAAIjB,gBAAgB,GAAK,SAAS,EAAIC,oBAAoB,GAAK,SAAS,CAAE,CACzE2K,OAAO,CAAGtK,QAAQ,CAACmM,cAAc,CAAC,sBAAsB,CAAC,CACzD,GAAI,CAAC7B,OAAO,CAAE,CACbA,OAAO,CAAGtK,QAAQ,CAAC6L,aAAa,CAAC,KAAK,CAAC,CACvCvB,OAAO,CAAChK,EAAE,CAAGkO,iBAAiB,CAAC,CAAC,CAChCxO,QAAQ,CAACC,IAAI,CAAC+L,WAAW,CAAC1B,OAAO,CAAC,CACnC,CAEAA,OAAO,CAACpK,KAAK,CAACmC,QAAQ,CAAG,UAAU,CACnCiI,OAAO,CAACpK,KAAK,CAAC6M,IAAI,CAAG,GAAGA,IAAI,IAAI,CAChCzC,OAAO,CAACpK,KAAK,CAACS,GAAG,CAAG,GAAGA,GAAG,IAAI,CAE9B2J,OAAO,CAACpK,KAAK,CAAC4B,KAAK,CAAG,GAAG8M,IAAI,IAAI,CACjCtE,OAAO,CAACpK,KAAK,CAAC+M,MAAM,CAAG,GAAG2B,IAAI,IAAI,CAClCtE,OAAO,CAACpK,KAAK,CAAC+B,eAAe,CAAGyM,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEI,KAAK,CAAGJ,eAAe,CAACI,KAAK,CAAG,QAAQ,CACzFxE,OAAO,CAACpK,KAAK,CAACkC,YAAY,CAAG,KAAK,CAClCkI,OAAO,CAACpK,KAAK,CAACU,MAAM,CAAG,MAAM,CAC7B0J,OAAO,CAACpK,KAAK,CAAC6O,UAAU,CAAG,MAAM,CACjCzE,OAAO,CAAC0E,SAAS,CAAG,EAAE,CACtB,GAAI,CAAAN,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEO,IAAI,IAAK,MAAM,EAAI,CAAAP,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEO,IAAI,IAAK,UAAU,CAAE,CAC7E,KAAM,CAAAC,QAAQ,CAAGlP,QAAQ,CAAC6L,aAAa,CAAC,MAAM,CAAC,CAC/CqD,QAAQ,CAACC,SAAS,CAAGT,eAAe,CAACO,IAAI,GAAK,MAAM,CAAG,GAAG,CAAG,GAAG,CAChEC,QAAQ,CAAChP,KAAK,CAACgC,KAAK,CAAG,OAAO,CAC9BgN,QAAQ,CAAChP,KAAK,CAACiC,QAAQ,CAAG,MAAM,CAChC+M,QAAQ,CAAChP,KAAK,CAACkP,UAAU,CAAG,MAAM,CAClCF,QAAQ,CAAChP,KAAK,CAACmP,SAAS,CAAGX,eAAe,CAACO,IAAI,GAAK,MAAM,CAAG,QAAQ,CAAG,QAAQ,CAChFC,QAAQ,CAAChP,KAAK,CAACoP,OAAO,CAAG,MAAM,CAC/BJ,QAAQ,CAAChP,KAAK,CAACqP,UAAU,CAAG,QAAQ,CACpCL,QAAQ,CAAChP,KAAK,CAACsP,cAAc,CAAG,QAAQ,CACxCN,QAAQ,CAAChP,KAAK,CAAC4B,KAAK,CAAG,MAAM,CAC7BoN,QAAQ,CAAChP,KAAK,CAAC+M,MAAM,CAAG,MAAM,CAC9BiC,QAAQ,CAAChP,KAAK,CAACyK,aAAa,CAAG,MAAM,CACrCL,OAAO,CAAC0B,WAAW,CAACkD,QAAQ,CAAC,CAC9B,CAEA,GAAIR,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEe,cAAc,CAAE,CACpCnF,OAAO,CAACJ,SAAS,CAACwF,GAAG,CAAC,iBAAiB,CAAC,CAExC,GAAIhB,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEiB,4BAA4B,CAAE,CAClD,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC3B,GAAItF,OAAO,CAAE,CACZA,OAAO,CAACJ,SAAS,CAACW,MAAM,CAAC,iBAAiB,CAAC,CAC3CP,OAAO,CAACJ,SAAS,CAACwF,GAAG,CAAC,yBAAyB,CAAC,CAChD;AACApF,OAAO,CAACpK,KAAK,CAACoP,OAAO,CAAG,MAAM,CAC9BhF,OAAO,CAACpK,KAAK,CAAC2P,OAAO,CAAG,GAAG,CAC3BvF,OAAO,CAACpK,KAAK,CAAC4P,SAAS,CAAG,UAAU,CACpC;AACD,CACD,CAAC,CAED;AACAxF,OAAO,CAACyF,OAAO,CAAG,IAAI,CACtBzF,OAAO,CAAC0F,WAAW,CAAG,IAAI,CAE1B;AACA,GAAI,CAAAtB,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEuB,QAAQ,IAAK,kBAAkB,CAAE,CACrD3F,OAAO,CAAC6C,gBAAgB,CAAC,WAAW,CAAEyC,aAAa,CAAC,CACrD,CAAC,IAAM,IAAI,CAAAlB,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEuB,QAAQ,IAAK,kBAAkB,CAAE,CAC5D3F,OAAO,CAAC6C,gBAAgB,CAAC,OAAO,CAAEyC,aAAa,CAAC,CACjD,CAAC,IAAM,CACN;AACAtF,OAAO,CAAC6C,gBAAgB,CAAC,OAAO,CAAEyC,aAAa,CAAC,CACjD,CACD,CACD,CAAC,IAAM,CACNtF,OAAO,CAACJ,SAAS,CAACW,MAAM,CAAC,iBAAiB,CAAC,CAC5C,CACD,CACD,CAAC,CAED;AAEA,KAAM,CAAAqF,gBAAgB,EAAAvN,uBAAA,CAAG9C,oBAAoB,CAACC,WAAW,CAAG,CAAC,CAAC,UAAA6C,uBAAA,iBAArCA,uBAAA,CAAuCZ,MAAM,CACtE,mBACC7C,KAAA,CAAAF,SAAA,EAAA4K,QAAA,EACE,CAAClK,gBAAgB,GAAK,SAAS,EAC/BA,gBAAgB,GAAK,SAAS,EAC9BC,oBAAoB,GAAK,SAAS,EAClCA,oBAAoB,GAAK,SAAS,GAClCI,eAAe,GAAK,IAAI,eACvBjB,IAAA,QACCW,SAAS,CAAC,UAAU,CACpBS,KAAK,CAAE,CACNmC,QAAQ,CAAE,OAAO,CACjB1B,GAAG,CAAE,CAAC,CACNoM,IAAI,CAAE,CAAC,CACPoD,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTnO,eAAe,CAAE,oBAAoB,CACrCrB,MAAM,CAAE,KACT,CAAE,CACF,CACD,cAEF9B,IAAA,CAACS,kBAAkB,EAClBG,gBAAgB,CAAEA,gBAAiB,CACnCC,oBAAoB,CAAEA,oBAAqB,CAC3C0Q,KAAK,cACJvR,IAAA,CAAAE,SAAA,EAAA4K,QAAA,cACC9K,IAAA,CAACH,WAAW,EACXqG,aAAa,CAAEA,aAAc,CAC7BC,gBAAgB,CAAEA,gBAAiB,CACnCmB,aAAa,CAAEA,aAAc,CAC7BvD,gBAAgB,CAAEA,gBAAiB,CACnCC,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BC,WAAW,CAAEA,WAAY,CACzBC,gBAAgB,CAAEA,gBAClB;AAAA,CACA,CAAC,CA0BD,CACF,CACDqN,IAAI,CACH,CAAC5Q,gBAAgB,GAAK,SAAS,EAC9BA,gBAAgB,GAAK,SAAS,EAC9BC,oBAAoB,GAAK,SAAS,EAClCA,oBAAoB,GAAK,SAAS,GACnCI,eAAe,GAAK,IAAI,EACxB4E,WACA,CACD4L,SAAS,CAAC,QAAQ,CAClBC,SAAS,CAAE,CACV5O,OAAO,CAAE,CACRlB,EAAE,CAAE,CACH0B,YAAY,CAAE,CAAA8N,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE9N,YAAY,GAAI8B,mBAAmB,CACnEuM,MAAM,CAAE,GAAG,CAAAP,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE5M,UAAU,GAAI,KAAK,UAAU,CAAA4M,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE7M,WAAW,GAAIe,kBAAkB,EAAE,CAC/GnC,eAAe,CAAE,CAAAiO,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEjO,eAAe,GAAIoC,sBAAsB,CAC5E/B,OAAO,CAAE,CAAA4N,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE5N,OAAO,GAAI2B,cAAc,CACpDyM,KAAK,CAAE,GAAG,CAAAR,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEpO,KAAK,GAAI,OAAO,aAAa,CACzDlB,MAAM,CAAE,IAAM;AACf,CACD,CACD,CAAE,CAAAgJ,QAAA,cAEF9K,IAAA,SAAU,CAAC,CACQ,CAAC,EACpB,CAAC,CAEL,CAAC,CACD,KAAM,CAAA6R,WAAW,CAAG,QAAAA,CAACjK,KAAS,CAAuB,IAArB,CAAAkK,QAAQ,CAAAC,SAAA,CAAA3J,MAAA,IAAA2J,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,KAAK,CAC/C,GAAI,CAACnK,KAAK,CAAE,MAAO,CAAAkK,QAAQ,CAC3B,KAAM,CAAAG,GAAG,CAAG,MAAO,CAAArK,KAAK,GAAK,QAAQ,CAAGA,KAAK,CAAC4C,IAAI,CAAC,CAAC,CAAG,GAAG5C,KAAK,EAAE,CACjE,MAAO,CAAAqK,GAAG,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAGD,GAAG,CAAG,GAAGA,GAAG,IAAI,CAC7C,CAAC,CACD,cAAe,CAAAtO,aAAa,CAE5B,KAAM,CAAAwO,WAAW,CAAGC,KAAA,EAAkE,IAAjE,CAAEzM,KAAK,CAAE0M,UAAkD,CAAC,CAAAD,KAAA,CAChF,mBACCpS,IAAA,CAACL,aAAa,EACb2S,OAAO,CAAC,MAAM,CACd3M,KAAK,CAAEA,KAAM,CACbpC,QAAQ,CAAC,QAAQ,CACjB8O,UAAU,CAAEA,UAAU,CAAG,CAAE,CAC3BzQ,EAAE,CAAE,CAAEsB,QAAQ,CAAE,GAAG,CAAEqP,QAAQ,CAAE,CAAC,CAAE/B,OAAO,CAAE,MAAM,CAAEE,cAAc,CAAE,QAAS,CAAE,CAC9E8B,UAAU,cAAExS,IAAA,CAAAE,SAAA,GAAI,CAAE,CAClBuS,UAAU,cAAEzS,IAAA,CAAAE,SAAA,GAAI,CAAE,CAClB,CAAC,CAEJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}