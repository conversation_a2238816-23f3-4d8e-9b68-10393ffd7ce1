{"ast": null, "code": "import React,{useMemo,useState,useEffect,useRef}from\"react\";import{Dialog,DialogContent,useMediaQ<PERSON>y,useTheme,Box,IconButton,Popover,Typography,Button,FormControl,Select,MenuItem,TextField,ToggleButton,ToggleButtonGroup,Tooltip,LinearProgress,MobileStepper,Breadcrumbs}from\"@mui/material\";import ImageSection from\"./PopupSections/Imagesection\";import RTEsection from\"./PopupSections/RTEsection\";import ButtonSection from\"./PopupSections/Button\";import AddIcon from\"@mui/icons-material/Add\";import DragIndicatorIcon from\"@mui/icons-material/DragIndicator\";import{Image,TextFormat,Code,VideoLibrary,Link}from\"@mui/icons-material\";import HtmlSection from\"./PopupSections/HtmlSection\";import VideoSection from\"./PopupSections/VideoSection\";import useDrawerStore from\"../../store/drawerStore\";import CloseIcon from\"@mui/icons-material/Close\";import\"../guideDesign/Canvas.module.css\";import PerfectScrollbar from\"react-perfect-scrollbar\";import\"react-perfect-scrollbar/dist/css/styles.css\";import AlertPopup from\"../drawer/AlertPopup\";import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";export const TOOLTIP_MN_WIDTH=500;// Helper function to convert hex color to rgba with opacity\nconst hexToRgba=(hex,opacity)=>{// Remove # if present\nhex=hex.replace('#','');// Parse hex values\nconst r=parseInt(hex.substring(0,2),16);const g=parseInt(hex.substring(2,4),16);const b=parseInt(hex.substring(4,6),16);return`rgba(${r}, ${g}, ${b}, ${opacity})`;};// Only text sections have IDs\nconst GuidePopup=_ref=>{var _announcementJson$Gui,_style$Radius,_style$Radius2,_style$Radius3,_style$Radius4,_style$Radius5;let{selectedStepType,guideStep,setImageSrc,imageSrc,textBoxRef,htmlContent,setHtmlContent,buttonColor,setButtonColor,setImageName,imageName,openStepDropdown,openWarning,setopenWarning,isUnSavedChanges,handleLeave}=_ref;const{t:translate}=useTranslation();const{addNewButton,updateButton,guideListByOrg,getGuildeListByOrg,updateButtonInteraction,addNewImageContainer,dismissData,setSelectActions,currentButtonName,setCurrentButtonName,targetURL,setTargetURL,selectedInteraction,setSelectedInteraction,openInteractionList,setOpenInteractionList,selectedTab,setSelectedTab,loading,setLoading,addNewRTEContainer,dismiss,currentStepIndex,selectedOption,progress,steps,setProgress,selectedTemplate,updateTooltipButtonAction,updateTooltipButtonInteraction,selectedTemplateTour,setIsUnSavedChanges,ProgressColor,setProgressColor,createWithAI}=useDrawerStore(state=>state);const[open,setOpen]=useState(true);const[anchorEl,setAnchorEl]=useState(null);const[sections,setSections]=useState([{type:\"image\"},{type:\"text\"},{type:\"button\"}]);const[draggingIndex,setDraggingIndex]=useState(null);const[sectionCounts,setSectionCounts]=useState({image:1,// Start with one of each as per initial sections state\ntext:1,button:1,video:0,gif:0,html:0});// Maximum allowed sections of each type\nconst MAX_SECTIONS={image:3,text:3,// RTE sections\nbutton:3,video:3,gif:3,html:3};// Helper function to check if a section type has reached its limit\nconst hasReachedLimit=type=>{// Map \"text\" to \"text\" for the check\nconst checkType=type===\"text\"?\"text\":type;return sectionCounts[checkType]>=MAX_SECTIONS[checkType];};//const [imageSrc, setImageSrc] = useState<string>(\"\");\n//const [imageName, setImageName] = useState<string>(\"\");\n//const [selectedActions, setSelectActions] = useState<string>(\"close\");\n//const [selectedTab, setSelectedTab] = useState<string>(\"new-tab\");\n//const [targetURL, setTargetURL] = useState<string>(\"\");\n//const [selectedInteraction, setSelectedInteraction] = useState(null);\n//const [currentButtonName, setCurrentButtonName] = useState(\"\");\n//const [openInteractionList, setOpenInteractionList] = useState(false);\n//const [loading, setLoading] = useState(false);\nconst[action,setAction]=useState(\"close\");const userInfo=localStorage.getItem(\"userInfo\");const userInfoObj=JSON.parse(userInfo||\"{}\");const orgDetails=JSON.parse(userInfoObj.orgDetails||\"{}\");const organizationId=orgDetails.OrganizationId;const overlayEnabled=useDrawerStore(state=>state.overlayEnabled);const guidePopUpRef=useRef(null);//Added Zustand here\nconst{designPopup,announcementJson,currentStep,setSettingAnchorEl,settingAnchorEl,updateButtonAction,getCurrentButtonInfo,buttonsContainer,buttonId,setButtonId,cuntainerId,setCuntainerId,btnname,setBtnName,rtesContainer,imagesContainer}=useDrawerStore(state=>state);const theme=useTheme();const isFullScreen=useMediaQuery(theme.breakpoints.down(\"sm\"));// Synchronize local sectionCounts with actual store state\nuseEffect(()=>{setSectionCounts({image:imagesContainer.length,text:rtesContainer.length,button:buttonsContainer.length,video:0,gif:0,html:0});},[buttonsContainer.length,rtesContainer.length,imagesContainer.length]);const handleCloseInteraction=()=>{setOpenInteractionList(false);};const handleOpenInteraction=()=>{setOpenInteractionList(true);if(organizationId&&!guideListByOrg.length){(async()=>{setLoading(true);await getGuildeListByOrg(organizationId);setLoading(false);})();}};const handleClose=(_event,reason)=>{if(reason===\"backdropClick\"||reason===\"escapeKeyDown\"){return;}setOpen(false);};const handleAddIconClick=event=>{if(hasReachedLimit(\"text\")&&hasReachedLimit(\"button\")&&hasReachedLimit(\"image\")){return;}setAnchorEl(event.currentTarget);};const handlePopoverClose=()=>{setAnchorEl(null);};const handleAddSection=type=>{// Check if we've reached the limit for this section type\nif(hasReachedLimit(type)){// Don't add more sections if limit is reached\nsetAnchorEl(null);return;}if(type===\"button\"){// Create and add a new button with default values\naddNewButton({id:crypto.randomUUID(),name:\"Button 1\",position:\"center\",type:\"primary\",isEditing:false,index:0,style:{...defaultButtonColors},actions:{value:\"close\",// Default action is \"close\"\ntargetURL:targetURL,// Default empty target URL\ntab:\"same-tab\"// Default tab behavior\n}},\"\");// Set the temporary colors (this might be for styling the new button)\nsetTempColors(defaultButtonColors);// Optionally, set the selected actions (if needed outside of the button creation)\nsetSelectedActions({value:\"close\",// Default action is \"close\"\ntargetURL:targetURL,// Default empty target URL\ntab:\"same-tab\"// Default tab behavior\n});}else if(type===\"image\"){addNewImageContainer();}else if(type===\"text\"){addNewRTEContainer();}else{// For other section types\nsetSections(prevSections=>[...prevSections,{type}]);}setAnchorEl(null);};const handleDragStart=index=>{setDraggingIndex(index);};const handleDragEnter=index=>{if(draggingIndex!==null&&draggingIndex!==index){const reorderedSections=[...sections];const[removed]=reorderedSections.splice(draggingIndex,1);reorderedSections.splice(index,0,removed);setSections(reorderedSections);setDraggingIndex(index);}};const handleDragEnd=()=>{setDraggingIndex(null);};const handleDeleteRTESection=index=>{// RTE section deletion is handled by the store\n// Section counts will be automatically updated via useEffect\n};// Function to handle deletion of image sections\nconst handleDeleteImageSection=()=>{// Image section deletion is handled by the store\n// Section counts will be automatically updated via useEffect\n};// Function to handle deletion of button sections\nconst handleDeleteButtonSection=()=>{// Button section deletion is handled by the store\n// Section counts will be automatically updated via useEffect\n};// Function to handle cloning of RTE sections\nconst handleCloneRTESection=()=>{// Check if we've reached the limit for RTE sections\nif(hasReachedLimit(\"text\")){return;// Don't clone if limit is reached\n}// RTE section cloning is handled by the store\n// Section counts will be automatically updated via useEffect\n};// Function to handle cloning of image sections\nconst handleCloneImageSection=()=>{// Check if we've reached the limit for image sections\nif(hasReachedLimit(\"image\")){return;// Don't clone if limit is reached\n}// Image section cloning is handled by the store\n// Section counts will be automatically updated via useEffect\n};// Function to handle cloning of button sections\nconst handleCloneButtonSection=()=>{// Check if we've reached the limit for button sections\nif(hasReachedLimit(\"button\")){return;// Don't clone if limit is reached\n}// Button section cloning is handled by the store\n// Section counts will be automatically updated via useEffect\n};// State to track if scrolling is needed\nconst[needsScrolling,setNeedsScrolling]=useState(false);const scrollbarRef=useRef(null);const renderSection=(section,index)=>{switch(section.type){case\"image\":return/*#__PURE__*/_jsx(ImageSection,{setImageSrc:setImageSrc,imageSrc:imageSrc,setImageName:setImageName,imageName:imageName,onDelete:handleDeleteImageSection,onClone:handleCloneImageSection,isCloneDisabled:hasReachedLimit(\"image\")},index);case\"text\":return/*#__PURE__*/_jsx(RTEsection,{// Use unique ID as the key for RTESection\ntextBoxRef:textBoxRef,isBanner:false,handleDeleteRTESection:()=>handleDeleteRTESection(index),index:index// @ts-ignore\n,ref:textBoxRef,guidePopUpRef:guidePopUpRef,onClone:handleCloneRTESection,isCloneDisabled:hasReachedLimit(\"text\")},index);case\"button\":return/*#__PURE__*/_jsx(ButtonSection,{buttonColor:buttonColor,setButtonColor:setButtonColor,isBanner:false,onDelete:handleDeleteButtonSection,onClone:handleCloneButtonSection,isCloneDisabled:hasReachedLimit(\"button\")},index);case\"video\":return/*#__PURE__*/_jsx(VideoSection,{},index);case\"html\":return/*#__PURE__*/_jsx(HtmlSection,{htmlContent:htmlContent,setHtmlContent:setHtmlContent,isBanner:false},index);default:return null;}};const style=(_announcementJson$Gui=announcementJson.GuideStep.find(step=>step.stepName===currentStep))===null||_announcementJson$Gui===void 0?void 0:_announcementJson$Gui.Canvas;const popupStyle={//maxWidth: \"533px\",\n//\tminWidth: TOOLTIP_MN_WIDTH,\nmaxWidth:`${style===null||style===void 0?void 0:style.Width} !important`||\"500px !important\",// maxHeight: \"400px\",\nwidth:`${(style===null||style===void 0?void 0:style.Width)||500}px`,borderRadius:`${(_style$Radius=style===null||style===void 0?void 0:style.Radius)!==null&&_style$Radius!==void 0?_style$Radius:8}px`,borderWidth:`${(style===null||style===void 0?void 0:style.BorderWidth)||0}px`,display:\"flex\",flexDirection:\"column\",borderColor:`${(style===null||style===void 0?void 0:style.BorderColor)||\"transparent\"}`,backgroundColor:`${(style===null||style===void 0?void 0:style.BackgroundColor)||\"#fff\"}`,border:`${(style===null||style===void 0?void 0:style.BorderSize)||\"0\"}px solid ${(style===null||style===void 0?void 0:style.BorderColor)||\"none\"}`,overflow:\"visible\"};const sectionStyle={width:\"100%\",display:\"flex\",flexDirection:\"column\",position:\"relative\",\"&:hover .add-icon\":{display:\"flex\"},\"&:hover .side-add-icon\":{display:\"flex\"},\"&:hover .add-section\":{opacity:\"1\"}};const dragButtonStyle={position:\"absolute\",left:\"-60px\",top:\"50%\",transform:\"translateY(-50%)\",cursor:\"move\",zIndex:1000};const sideAddButtonStyle={position:\"absolute\",right:\"-38px\",top:\"50%\",transform:\"translateY(-50%)\",width:\"18px\",height:\"100%\",borderRadius:\"6px\",display:\"none\",alignItems:\"center\",justifyContent:\"center\",backgroundColor:\"#5F9EA0\",cursor:\"pointer\",zIndex:1000,\"&:hover\":{backgroundColor:\"#70afaf\"}};const handleChangeTabs=event=>{setSelectedTab(event.target.value);};const handleCloseSettingPopup=(containerId,buttonId)=>{updateButtonAction(containerId,buttonId,selectedActions);updateButtonInteraction(containerId,buttonId,selectedInteraction);setSettingAnchorEl({containerId:\"\",buttonId:\"\",value:null});};const[targetURLError,setTargetURLError]=useState(\"\");const validateTargetURL=url=>{if(selectedActions.value===\"open-url\"){if(!url){return\"URL is required\";}try{new URL(url);return\"\";}catch(error){return\"Invalid URL\";}}return\"\";};const handleApplyChanges=(containerId,buttonId)=>{const error=validateTargetURL(targetURL);setTargetURLError(error);// Set the error message for display\nif(error){return;// Prevent applying changes if there's a validation error\n}const buttonNameToUpdate=!currentButtonName||!currentButtonName.trim()?curronButtonInfo.title// Retain the previously saved button name\n:currentButtonName;setCurrentButtonName(buttonNameToUpdate);updateButton(containerId,buttonId,\"style\",tempColors);updateButtonAction(containerId,buttonId,selectedActions);// Update the selected actions\nupdateButtonInteraction(containerId,buttonId,selectedInteraction);updateButton(containerId,buttonId,\"name\",buttonNameToUpdate);updateButton(containerId,buttonId,\"actions\",selectedActions);// Update button actions\nsetSettingAnchorEl({containerId:\"\",buttonId:\"\",value:null});setIsUnSavedChanges(true);};const handleURLChange=e=>{const newURL=e.target.value;setTargetURL(newURL);// Validate the URL and update the error state\nconst error=validateTargetURL(newURL);setTargetURLError(error);setSelectedActions({value:selectedActions.value,targetURL:newURL,tab:selectedTab});};const curronButtonInfo=useMemo(()=>{const result=getCurrentButtonInfo(settingAnchorEl.containerId,settingAnchorEl.buttonId);setCurrentButtonName(result.title);setBtnName(result.title);setAction(result.value);return result;},[settingAnchorEl.containerId,settingAnchorEl.buttonId]);const defaultButtonColors={backgroundColor:\"#5F9EA0\",borderColor:\"#70afaf\",color:\"#ffffff\"};const[selectedActions,setSelectedActions]=useState({value:\"close\",// Default action\ntargetURL:\"\",// Default empty target URL\ntab:\"same-tab\"// Default tab (same-tab)\n});const[tempColors,setTempColors]=useState(defaultButtonColors);const selectedButton=getCurrentButtonInfo(settingAnchorEl.containerId,settingAnchorEl.buttonId);useEffect(()=>{const handleSelectButton=(containerId,buttonId)=>{const selectedButton=getCurrentButtonInfo(containerId,buttonId);if(selectedButton){setTargetURL(selectedButton.targetURL||\"\");setTempColors({backgroundColor:selectedButton.bgColor||defaultButtonColors.backgroundColor,borderColor:selectedButton.borderColor||defaultButtonColors.borderColor,color:selectedButton.textColor||defaultButtonColors.color});setSelectedActions({value:selectedButton.selectedActions||\"close\",// Default to \"close\" if no action is set\ntargetURL:selectedButton.targetURL||targetURL,// Can be updated later if needed\ntab:\"same-tab\"// Default tab behavior\n});}};handleSelectButton(settingAnchorEl.containerId,settingAnchorEl.buttonId);},[settingAnchorEl.containerId,settingAnchorEl.buttonId]);// Function to handle color changes in the color picker\nconst handleColorChange=(e,targetName)=>{const value=e.target.value;setTempColors(prev=>({...prev,[targetName]:value}));};useEffect(()=>{setSelectedActions({value:selectedActions.value,// Default action\ntargetURL:targetURL,// Default empty target URL\ntab:\"same-tab\"// Default tab (same-tab)\n});},[]);// Check if content needs scrolling with improved detection\nuseEffect(()=>{const checkScrollNeeded=()=>{if(guidePopUpRef.current){// Force a reflow to get accurate measurements\nguidePopUpRef.current.style.height='auto';const contentHeight=guidePopUpRef.current.scrollHeight;const containerHeight=320;// max-height value\nconst shouldScroll=contentHeight>containerHeight;setNeedsScrolling(shouldScroll);// Force update scrollbar\nif(scrollbarRef.current){// Try multiple methods to update the scrollbar\nif(scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}// Force re-initialization if needed\nsetTimeout(()=>{if(scrollbarRef.current&&scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}},10);}}};checkScrollNeeded();const timeouts=[setTimeout(checkScrollNeeded,50),setTimeout(checkScrollNeeded,100),setTimeout(checkScrollNeeded,200),setTimeout(checkScrollNeeded,500)];let resizeObserver=null;let mutationObserver=null;if(guidePopUpRef.current&&window.ResizeObserver){resizeObserver=new ResizeObserver(()=>{setTimeout(checkScrollNeeded,10);});resizeObserver.observe(guidePopUpRef.current);}if(guidePopUpRef.current&&window.MutationObserver){mutationObserver=new MutationObserver(()=>{setTimeout(checkScrollNeeded,10);});mutationObserver.observe(guidePopUpRef.current,{childList:true,subtree:true,attributes:true,attributeFilter:['style','class']});}return()=>{timeouts.forEach(clearTimeout);if(resizeObserver){resizeObserver.disconnect();}if(mutationObserver){mutationObserver.disconnect();}};},[currentStep]);const handleChangeActions=e=>{const v=e.target.value;// Casting to TInteractionValue\nsetSelectedActions({value:v,// Ensure that selectedActions.value is of type TInteractionValue\ntargetURL:targetURL,tab:selectedTab// Ensure tab is a valid value\n});};// useEffect(() => {\n// \tif (selectedButton) {\n// \t  selectedButton.targetURL = targetURL;  // Update selectedButton's targetURL whenever targetURL state changes\n// \t}\n//   }, [targetURL]);  // Dependency on `targetURL`\n// Dependency on `targetURL`\nreturn/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-annpopop\",children:[overlayEnabled&&/*#__PURE__*/_jsx(Box,{sx:{position:\"fixed\",top:0,left:0,width:\"100vw\",height:\"100vh\",backgroundColor:\"rgba(0, 0, 0, 0.5)\",zIndex:9}}),isUnSavedChanges&&openWarning&&/*#__PURE__*/_jsx(AlertPopup,{openWarning:openWarning,setopenWarning:setopenWarning,handleLeave:handleLeave}),/*#__PURE__*/_jsxs(Dialog,{className:\"qadpt-guide-popup\",open:open,onClose:handleClose,fullScreen:isFullScreen,PaperProps:{style:popupStyle},maxWidth:false,disableEnforceFocus:true,children:[/*#__PURE__*/_jsx(\"div\",{style:{placeContent:\"end\",display:\"flex\"},children:dismiss&&/*#__PURE__*/_jsx(IconButton,{className:\"qadpt-dismiss\"//onClick={handleCloseBanner}\n,children:/*#__PURE__*/_jsx(CloseIcon,{sx:{zoom:1,color:\"#000\"}})})}),/*#__PURE__*/_jsx(PerfectScrollbar,{ref:scrollbarRef,style:{maxHeight:\"400px\",...(progress?{borderTopLeftRadius:`${(_style$Radius2=style===null||style===void 0?void 0:style.Radius)!==null&&_style$Radius2!==void 0?_style$Radius2:8}px`,borderTopRightRadius:`${(_style$Radius3=style===null||style===void 0?void 0:style.Radius)!==null&&_style$Radius3!==void 0?_style$Radius3:8}px`,borderBottomLeftRadius:\"0px\",borderBottomRightRadius:\"0px\"}:{borderRadius:`${(_style$Radius4=style===null||style===void 0?void 0:style.Radius)!==null&&_style$Radius4!==void 0?_style$Radius4:8}px`})},options:{suppressScrollY:false,suppressScrollX:true,wheelPropagation:false,swipeEasing:true,minScrollbarLength:20,scrollingThreshold:1000,scrollYMarginOffset:0},children:/*#__PURE__*/_jsx(DialogContent,{sx:{padding:0,overflow:\"hidden\",display:\"flex\",flexDirection:\"column\",position:\"relative\"},ref:guidePopUpRef,id:\"guide-popup\",children:/*#__PURE__*/_jsxs(Box,{sx:{padding:`${(style===null||style===void 0?void 0:style.Padding)||12}px`},children:[sections.map((section,index)=>/*#__PURE__*/_jsxs(Box,{sx:{...sectionStyle,height:\"auto\",\"&:hover\":{borderTopWidth:index!==0?\"1px\":\"0px\",borderTopColor:index!==0?\"var(--primarycolor)\":\"transparent\",borderTopStyle:index!==0?\"dotted\":\"none\"}},draggable:true,onDragStart:()=>handleDragStart(index),onDragEnter:()=>handleDragEnter(index),onDragEnd:handleDragEnd,children:[/*#__PURE__*/_jsx(IconButton,{className:\"drag-icon\",sx:dragButtonStyle,children:/*#__PURE__*/_jsx(DragIndicatorIcon,{fontSize:\"small\",sx:{color:\"#5F9EA0\"}})}),renderSection(section,index),index!==0&&/*#__PURE__*/_jsx(Tooltip,{title:hasReachedLimit(\"text\")&&hasReachedLimit(\"button\")&&hasReachedLimit(\"image\")?translate(\"Maximum limit reached for all section types\"):translate(\"Add Section\"),children:/*#__PURE__*/_jsx(IconButton,{className:\"add-section\",onClick:handleAddIconClick,sx:{backgroundColor:\"#5F9EA0\",\"&:hover\":{backgroundColor:\"#70afaf\"},borderRadius:\"4px\",padding:\"5px !important\",position:\"absolute\",top:\"auto\",left:\"50%\",transform:\"translate(-50%, -50%)\",opacity:\"0\",cursor:hasReachedLimit(\"text\")&&hasReachedLimit(\"button\")&&hasReachedLimit(\"image\")?\"not-allowed\":\"pointer\"},disabled:hasReachedLimit(\"text\")&&hasReachedLimit(\"button\")&&hasReachedLimit(\"image\"),children:/*#__PURE__*/_jsx(AddIcon,{fontSize:\"small\",sx:{color:\"#fff\"}})})})]},index)),Boolean(settingAnchorEl.value)?/*#__PURE__*/_jsx(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup qadpt-btnprop\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:translate(\"Properties\")}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":\"close\",onClick:()=>handleCloseSettingPopup(settingAnchorEl.containerId,settingAnchorEl.buttonId),children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-canblock qadpt-btnpro\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\",children:[/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,sx:{marginBottom:\"16px\"},children:[/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"14px\",fontWeight:\"bold\",my:\"5px\"},children:translate(\"Button Name\")}),/*#__PURE__*/_jsx(TextField,{value:currentButtonName,size:\"small\",sx:{mb:\"5px\",border:\"1px solid #ccc\",borderRadius:\"4px\",\"& .MuiOutlinedInput-root\":{height:\"35px\",\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"}},\"& .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"}},placeholder:translate(\"Button Name\"),onChange:e=>setCurrentButtonName(e.target.value)}),/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"14px\",fontWeight:\"bold\",mb:\"5px\"},children:translate(\"Button Action\")}),/*#__PURE__*/_jsxs(Select,{value:selectedActions.value,defaultValue:\"close\",onChange:handleChangeActions,sx:{mb:\"5px\",border:\"1px solid #ccc\",borderRadius:\"4px\",textAlign:\"left\",\"& .MuiSelect-select\":{padding:\"8px\"},\"& .MuiOutlinedInput-root\":{height:\"35px\",\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"}},\"& .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"}},children:[/*#__PURE__*/_jsx(MenuItem,{value:\"close\",children:translate(\"Close\")}),/*#__PURE__*/_jsx(MenuItem,{value:\"open-url\",children:translate(\"Open URL\")}),/*#__PURE__*/_jsx(MenuItem,{value:\"Previous\",children:translate(\"Previous\")}),/*#__PURE__*/_jsx(MenuItem,{value:\"Next\",children:translate(\"Next\")}),/*#__PURE__*/_jsx(MenuItem,{value:\"Restart\",children:translate(\"Restart\")})]}),selectedActions.value===\"open-url\"?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"14px\",fontWeight:\"bold\",my:\"5px\"},children:translate(\"Enter URL\")}),/*#__PURE__*/_jsx(TextField,{value:targetURL,size:\"small\",placeholder:\"https://quixy.com\",onChange:e=>{const newURL=e.target.value;setTargetURL(newURL);// Update the `targetURL` state with the new value\nhandleURLChange(e);// Update the selectedButton.targetURL with the new value\n},error:!!targetURLError,helperText:targetURLError}),/*#__PURE__*/_jsx(ToggleButtonGroup,{value:selectedTab,onChange:handleChangeTabs,exclusive:true,\"aria-label\":translate(\"open in tab\"),sx:{gap:\"5px\",marginY:\"5px\",height:\"35px\"},children:[\"new-tab\",\"same-tab\"].map(tab=>{return/*#__PURE__*/_jsx(ToggleButton,{value:tab,\"aria-label\":\"new tab\",sx:{border:\"1px solid #7EA8A5\",textTransform:\"capitalize\",color:\"#000\",borderRadius:\"4px\",flex:1,padding:\"0 !important\",\"&.Mui-selected\":{backgroundColor:\"var(--border-color)\",color:\"#000\",border:\"2px solid #7EA8A5\"},\"&:hover\":{backgroundColor:\"#f5f5f5\"},\"&:last-child\":{borderLeft:\"1px solid var(--primarycolor) !important\"// Remove left border for the last button\n}},children:tab});})})]}):null]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{borderRadius:\"5px\"},children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate(\"Background\")}),/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:tempColors.backgroundColor,onChange:e=>handleColorChange(e,\"backgroundColor\"),className:\"qadpt-color-input\"})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{borderRadius:\"5px\"},children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate(\"Border\")}),/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:tempColors.borderColor,onChange:e=>handleColorChange(e,\"borderColor\"),className:\"qadpt-color-input\"})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{borderRadius:\"5px\"},children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate(\"Text\")}),/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:tempColors.color,onChange:e=>handleColorChange(e,\"color\"),className:\"qadpt-color-input\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-drawerFooter\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:()=>handleApplyChanges(settingAnchorEl.containerId,settingAnchorEl.buttonId),className:\"qadpt-btn\",children:translate(\"Apply\")})})]})}):null]})})},`scrollbar-${needsScrolling}`),/*#__PURE__*/_jsx(Box,{sx:{borderRadius:`${(_style$Radius5=style===null||style===void 0?void 0:style.Radius)!==null&&_style$Radius5!==void 0?_style$Radius5:8}px`},children:progress&&(selectedOption===1||selectedOption===\"\"?/*#__PURE__*/_jsx(DotsStepper,{activeStep:currentStep,steps:steps.length,ProgressColor:ProgressColor}):selectedOption===2?/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",sx:{height:\"6px\",borderRadius:\"20px\",margin:\"6px 10px\",backgroundColor:hexToRgba(ProgressColor,0.45),'& .MuiLinearProgress-bar':{backgroundColor:ProgressColor// progress bar color\n}},value:currentStep/steps.length*100})}):selectedOption===3?/*#__PURE__*/_jsx(\"div\",{style:{padding:\"8px\"},children:/*#__PURE__*/_jsx(BreadCrumpStepper,{activeStep:currentStep,steps:steps.length,ProgressColor:ProgressColor})}):selectedOption===4?/*#__PURE__*/_jsx(Breadcrumbs,{\"aria-label\":\"breadcrumb\",sx:{padding:\"8px\"},children:/*#__PURE__*/_jsxs(Typography,{children:[\"Step \",currentStep,\" of \",steps.length]})}):null)}),anchorEl&&/*#__PURE__*/_jsx(Popover,{className:\"qadpt-secprop\",open:Boolean(anchorEl),anchorEl:anchorEl,onClose:handlePopoverClose,anchorOrigin:{vertical:\"bottom\",horizontal:\"center\"},transformOrigin:{vertical:\"top\",horizontal:\"center\"},slotProps:{paper:{sx:{padding:\"12px\",display:\"flex\",gap:\"16px\",width:\"auto\",zIndex:1302}}},sx:{position:\"absolute\",// top: `${anchorEl.getBoundingClientRect().bottom + 8}px`,\n// left: `${anchorEl.getBoundingClientRect().left - 150}px`,\ntransform:\"none\"},children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"row\",gap:\"16px\",children:[/*#__PURE__*/_jsx(Tooltip,{title:hasReachedLimit(\"text\")?translate(\"Maximum limit of 3 Rich Text sections reached\"):\"\",PopperProps:{sx:{zIndex:9999}},children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"column\",alignItems:\"center\",sx:{cursor:hasReachedLimit(\"text\")?\"not-allowed\":\"pointer\",opacity:hasReachedLimit(\"text\")?0.5:1,svg:{fontSize:\"24px !important\"}},onClick:()=>!hasReachedLimit(\"text\")&&handleAddSection(\"text\"),children:[/*#__PURE__*/_jsx(TextFormat,{}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{fontSize:\"11px !important\"},children:translate(\"Rich Text\")})]})}),/*#__PURE__*/_jsx(Tooltip,{title:hasReachedLimit(\"button\")?translate(\"Maximum limit of 3 Button sections reached\"):\"\",PopperProps:{sx:{zIndex:9999}},children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"column\",alignItems:\"center\",sx:{cursor:hasReachedLimit(\"button\")?\"not-allowed\":\"pointer\",opacity:hasReachedLimit(\"button\")?0.5:1,svg:{fontSize:\"24px !important\"}},onClick:()=>!hasReachedLimit(\"button\")&&handleAddSection(\"button\"),children:[/*#__PURE__*/_jsx(Link,{}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{fontSize:\"11px !important\"},children:translate(\"Button\")})]})}),/*#__PURE__*/_jsx(Tooltip,{title:hasReachedLimit(\"image\")?translate(\"Maximum limit of 3 Image sections reached\"):\"\",PopperProps:{sx:{zIndex:9999}},children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"column\",alignItems:\"center\",sx:{cursor:hasReachedLimit(\"image\")?\"not-allowed\":\"pointer\",opacity:hasReachedLimit(\"image\")?0.5:1,svg:{fontSize:\"24px !important\"}},onClick:()=>!hasReachedLimit(\"image\")&&handleAddSection(\"image\"),children:[/*#__PURE__*/_jsx(Image,{}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{fontSize:\"11px !important\"},children:translate(\"Image\")})]})}),/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Coming Soon\"),PopperProps:{sx:{zIndex:9999}},children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"column\",alignItems:\"center\",sx:{cursor:\"pointer\",opacity:0.5,svg:{fontSize:\"24px !important\"}}// onClick={() => handleAddSection(\"video\")}\n,children:[/*#__PURE__*/_jsx(VideoLibrary,{}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{fontSize:\"11px !important\"},children:translate(\"Video\")})]})})}),/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Coming Soon\"),PopperProps:{sx:{zIndex:9999}},children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"column\",alignItems:\"center\",sx:{cursor:\"pointer\",opacity:0.5,svg:{fontSize:\"24px !important\"}},onClick:()=>handleAddSection(\"html\"),children:[/*#__PURE__*/_jsx(Code,{}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{fontSize:\"11px !important\"},children:translate(\"HTML\")})]})})})]})})]})]});};export default GuidePopup;const DotsStepper=_ref2=>{let{steps,activeStep,ProgressColor}=_ref2;return/*#__PURE__*/_jsx(MobileStepper,{variant:\"dots\",steps:steps,position:\"static\",activeStep:activeStep-1,sx:{flexGrow:1,display:\"flex\",justifyContent:\"center\",background:\"inherit\",\"& .MuiMobileStepper-dotActive\":{backgroundColor:ProgressColor// active dot color\n}},nextButton:/*#__PURE__*/_jsx(_Fragment,{}),backButton:/*#__PURE__*/_jsx(_Fragment,{})});};const BreadCrumpStepper=_ref3=>{let{steps,activeStep,ProgressColor}=_ref3;return/*#__PURE__*/_jsx(Box,{sx:{flexGrow:1},children:/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',gap:\"4px\"// Adjust space between steps\n//   paddingTop: '15px',\n},children:Array.from({length:steps}).map((_,index)=>/*#__PURE__*/_jsx(\"div\",{style:{width:'14px',height:'4px',backgroundColor:index===activeStep-1?ProgressColor:hexToRgba(ProgressColor,0.45),// Active color and inactive color\nborderRadius:'100px'}},index))})});};", "map": {"version": 3, "names": ["React", "useMemo", "useState", "useEffect", "useRef", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useMediaQuery", "useTheme", "Box", "IconButton", "Popover", "Typography", "<PERSON><PERSON>", "FormControl", "Select", "MenuItem", "TextField", "ToggleButton", "ToggleButtonGroup", "<PERSON><PERSON><PERSON>", "LinearProgress", "MobileStepper", "Breadcrumbs", "ImageSection", "RTEsection", "ButtonSection", "AddIcon", "DragIndicatorIcon", "Image", "TextFormat", "Code", "VideoLibrary", "Link", "HtmlSection", "VideoSection", "useDrawerStore", "CloseIcon", "PerfectScrollbar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "TOOLTIP_MN_WIDTH", "hexToRgba", "hex", "opacity", "replace", "r", "parseInt", "substring", "g", "b", "GuidePopup", "_ref", "_announcementJson$Gui", "_style$Radius", "_style$Radius2", "_style$Radius3", "_style$Radius4", "_style$Radius5", "selectedStepType", "guideStep", "setImageSrc", "imageSrc", "textBoxRef", "htmlContent", "setHtmlContent", "buttonColor", "setButtonColor", "setImageName", "imageName", "openStepDropdown", "openWarning", "setopenWarning", "isUnSavedChanges", "handleLeave", "t", "translate", "addNewButton", "updateButton", "guideListByOrg", "getGuildeListByOrg", "updateButtonInteraction", "addNewImageContainer", "dismissData", "setSelectActions", "currentButtonName", "setCurrentButtonName", "targetURL", "setTargetURL", "selectedInteraction", "setSelectedInteraction", "openInteractionList", "setOpenInteractionList", "selectedTab", "setSelectedTab", "loading", "setLoading", "addNewRTEContainer", "dismiss", "currentStepIndex", "selectedOption", "progress", "steps", "setProgress", "selectedTemplate", "updateTooltipButtonAction", "updateTooltipButtonInteraction", "selectedTemplateTour", "setIsUnSavedChanges", "ProgressColor", "setProgressColor", "createWithAI", "state", "open", "<PERSON><PERSON><PERSON>", "anchorEl", "setAnchorEl", "sections", "setSections", "type", "draggingIndex", "setDraggingIndex", "sectionCounts", "setSectionCounts", "image", "text", "button", "video", "gif", "html", "MAX_SECTIONS", "hasReachedLimit", "checkType", "action", "setAction", "userInfo", "localStorage", "getItem", "userInfoObj", "JSON", "parse", "orgDetails", "organizationId", "OrganizationId", "overlayEnabled", "guidePopUpRef", "designPopup", "announcement<PERSON><PERSON>", "currentStep", "setSettingAnchorEl", "settingAnchorEl", "updateButtonAction", "getCurrentButtonInfo", "buttonsContainer", "buttonId", "setButtonId", "cuntainerId", "setCuntainerId", "btnname", "setBtnName", "rtesContainer", "imagesContainer", "theme", "isFullScreen", "breakpoints", "down", "length", "handleCloseInteraction", "handleOpenInteraction", "handleClose", "_event", "reason", "handleAddIconClick", "event", "currentTarget", "handlePopoverClose", "handleAddSection", "id", "crypto", "randomUUID", "name", "position", "isEditing", "index", "style", "defaultButtonColors", "actions", "value", "tab", "setTempColors", "setSelectedActions", "prevSections", "handleDragStart", "handleDragEnter", "reorderedSections", "removed", "splice", "handleDragEnd", "handleDeleteRTESection", "handleDeleteImageSection", "handleDeleteButtonSection", "handleCloneRTESection", "handleCloneImageSection", "handleCloneButtonSection", "needsScrolling", "setNeedsScrolling", "scrollbarRef", "renderSection", "section", "onDelete", "onClone", "isCloneDisabled", "isBanner", "ref", "GuideStep", "find", "step", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "popupStyle", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "width", "borderRadius", "<PERSON><PERSON>", "borderWidth", "BorderWidth", "display", "flexDirection", "borderColor", "BorderColor", "backgroundColor", "BackgroundColor", "border", "BorderSize", "overflow", "sectionStyle", "dragButtonStyle", "left", "top", "transform", "cursor", "zIndex", "sideAddButtonStyle", "right", "height", "alignItems", "justifyContent", "handleChangeTabs", "target", "handleCloseSettingPopup", "containerId", "selectedActions", "targetURLError", "setTargetURLError", "validateTargetURL", "url", "URL", "error", "handleApplyChanges", "buttonNameToUpdate", "trim", "curronButtonInfo", "title", "tempColors", "handleURLChange", "e", "newURL", "result", "color", "<PERSON><PERSON><PERSON><PERSON>", "handleSelectButton", "bgColor", "textColor", "handleColorChange", "targetName", "prev", "checkScrollNeeded", "current", "contentHeight", "scrollHeight", "containerHeight", "shouldScroll", "updateScroll", "setTimeout", "timeouts", "resizeObserver", "mutationObserver", "window", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "for<PERSON>ach", "clearTimeout", "disconnect", "handleChangeActions", "v", "className", "children", "sx", "onClose", "fullScreen", "PaperProps", "disableEnforceFocus", "place<PERSON><PERSON>nt", "zoom", "maxHeight", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "options", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "padding", "Padding", "map", "borderTopWidth", "borderTopColor", "borderTopStyle", "draggable", "onDragStart", "onDragEnter", "onDragEnd", "fontSize", "onClick", "disabled", "Boolean", "size", "fullWidth", "marginBottom", "fontWeight", "my", "mb", "placeholder", "onChange", "defaultValue", "textAlign", "helperText", "exclusive", "gap", "marginY", "textTransform", "flex", "borderLeft", "variant", "DotsStepper", "activeStep", "margin", "BreadCrumpStepper", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "slotProps", "paper", "PopperProps", "svg", "_ref2", "flexGrow", "background", "nextButton", "backButton", "_ref3", "Array", "from", "_"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideSetting/GuidePopUp.tsx"], "sourcesContent": ["import React, { useMemo, useState, useEffect, useRef } from \"react\";\r\nimport { TB<PERSON>onAction, TInteractionValue, TSectionType } from \"../../store/drawerStore\";\r\nimport {\r\n\tDialog,\r\n\tDialogContent,\r\n\tuseMediaQuery,\r\n\tuseTheme,\r\n\tBox,\r\n\tIconButton,\r\n\tPopover,\r\n\tTypography,\r\n\tButton,\r\n\tFormControl,\r\n\tSelect,\r\n\tMenuItem,\r\n\tTextField,\r\n\tSelectChangeEvent,\r\n\tRadioGroup,\r\n\tRadio,\r\n\tFormControlLabel,\r\n\tInput,\r\n\tToggleButton,\r\n\tToggleButtonGroup,\r\n\tAutocomplete,\r\n\tCircularProgress,\r\n\tDialogTitle,\r\n\tTooltip,\r\n\tLinearProgress,\r\n\tMobileStepper,\r\n\tBreadcrumbs,\r\n\tDialogActions\r\n} from \"@mui/material\";\r\nimport ImageSection from \"./PopupSections/Imagesection\";\r\nimport RTEsection from \"./PopupSections/RTEsection\";\r\nimport ButtonSection from \"./PopupSections/Button\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport DragIndicatorIcon from \"@mui/icons-material/DragIndicator\";\r\nimport { Image, TextFormat, Code, VideoLibrary, GifBox, Link, Opacity } from \"@mui/icons-material\";\r\nimport HtmlSection from \"./PopupSections/HtmlSection\";\r\nimport VideoSection from \"./PopupSections/VideoSection\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport \"../guideDesign/Canvas.module.css\";\r\nimport PerfectScrollbar from \"react-perfect-scrollbar\";\r\nimport \"react-perfect-scrollbar/dist/css/styles.css\";\r\nimport WarningIcon from \"@mui/icons-material/Warning\";\r\nimport AlertPopup from \"../drawer/AlertPopup\";\r\nimport { color } from \"jodit/esm/plugins/color/color\";\r\nimport { useTranslation } from 'react-i18next';\r\nexport const TOOLTIP_MN_WIDTH = 500;\r\n\r\n// Helper function to convert hex color to rgba with opacity\r\nconst hexToRgba = (hex: string, opacity: number): string => {\r\n\t// Remove # if present\r\n\thex = hex.replace('#', '');\r\n\r\n\t// Parse hex values\r\n\tconst r = parseInt(hex.substring(0, 2), 16);\r\n\tconst g = parseInt(hex.substring(2, 4), 16);\r\n\tconst b = parseInt(hex.substring(4, 6), 16);\r\n\r\n\treturn `rgba(${r}, ${g}, ${b}, ${opacity})`;\r\n};\r\n\r\ntype SectionType = { type: \"image\" | \"button\" | \"video\" | \"gif\" | \"html\" } | { type: \"text\" }; // Only text sections have IDs\r\nconst GuidePopup = ({\r\n\tselectedStepType,\r\n\tguideStep,\r\n\tsetImageSrc,\r\n\timageSrc,\r\n\ttextBoxRef,\r\n\thtmlContent,\r\n\tsetHtmlContent,\r\n\tbuttonColor,\r\n\tsetButtonColor,\r\n\tsetImageName,\r\n\timageName,\r\n\topenStepDropdown,\r\n\topenWarning,\r\n\tsetopenWarning,\r\n\tisUnSavedChanges,\r\n\thandleLeave,\r\n}: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\taddNewButton,\r\n\t\tupdateButton,\r\n\t\tguideListByOrg,\r\n\t\tgetGuildeListByOrg,\r\n\t\tupdateButtonInteraction,\r\n\t\taddNewImageContainer,\r\n\t\tdismissData,\r\n\t\tsetSelectActions,\r\n\t\tcurrentButtonName,\r\n\t\tsetCurrentButtonName,\r\n\t\ttargetURL,\r\n\t\tsetTargetURL,\r\n\t\tselectedInteraction,\r\n\t\tsetSelectedInteraction,\r\n\t\topenInteractionList,\r\n\t\tsetOpenInteractionList,\r\n\t\tselectedTab,\r\n\t\tsetSelectedTab,\r\n\t\tloading,\r\n\t\tsetLoading,\r\n\t\taddNewRTEContainer,\r\n\t\tdismiss,\r\n\t\tcurrentStepIndex,\r\n\t\tselectedOption,\r\n\t\tprogress,\r\n\t\tsteps,\r\n\t\tsetProgress,\r\n\t\tselectedTemplate,\r\n\t\tupdateTooltipButtonAction,\r\n\t\tupdateTooltipButtonInteraction,\r\n\t\tselectedTemplateTour,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tProgressColor,\r\n\t\tsetProgressColor,\r\n\t\tcreateWithAI\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [open, setOpen] = useState(true);\r\n\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [sections, setSections] = useState<SectionType[]>([{ type: \"image\" }, { type: \"text\" }, { type: \"button\" }]);\r\n\tconst [draggingIndex, setDraggingIndex] = useState<number | null>(null);\r\n\r\n\tconst [sectionCounts, setSectionCounts] = useState({\r\n\t\timage: 1, // Start with one of each as per initial sections state\r\n\t\ttext: 1,\r\n\t\tbutton: 1,\r\n\t\tvideo: 0,\r\n\t\tgif: 0,\r\n\t\thtml: 0\r\n\t});\r\n\r\n\t// Maximum allowed sections of each type\r\n\tconst MAX_SECTIONS = {\r\n\t\timage: 3,\r\n\t\ttext: 3, // RTE sections\r\n\t\tbutton: 3,\r\n\t\tvideo: 3,\r\n\t\tgif: 3,\r\n\t\thtml: 3\r\n\t};\r\n\r\n\t// Helper function to check if a section type has reached its limit\r\n\tconst hasReachedLimit = (type: SectionType[\"type\"]): boolean => {\r\n\t\t// Map \"text\" to \"text\" for the check\r\n\t\tconst checkType = type === \"text\" ? \"text\" : type;\r\n\t\treturn sectionCounts[checkType] >= MAX_SECTIONS[checkType];\r\n\t};\r\n\r\n\t//const [imageSrc, setImageSrc] = useState<string>(\"\");\r\n\t//const [imageName, setImageName] = useState<string>(\"\");\r\n\t//const [selectedActions, setSelectActions] = useState<string>(\"close\");\r\n\t//const [selectedTab, setSelectedTab] = useState<string>(\"new-tab\");\r\n\t//const [targetURL, setTargetURL] = useState<string>(\"\");\r\n\t//const [selectedInteraction, setSelectedInteraction] = useState(null);\r\n\t//const [currentButtonName, setCurrentButtonName] = useState(\"\");\r\n\t//const [openInteractionList, setOpenInteractionList] = useState(false);\r\n\t//const [loading, setLoading] = useState(false);\r\n\tconst [action, setAction] = useState(\"close\");\r\n\tconst userInfo = localStorage.getItem(\"userInfo\");\r\n\tconst userInfoObj = JSON.parse(userInfo || \"{}\");\r\n\tconst orgDetails = JSON.parse(userInfoObj.orgDetails || \"{}\");\r\n\tconst organizationId = orgDetails.OrganizationId;\r\n\tconst overlayEnabled = useDrawerStore((state) => state.overlayEnabled);\r\n\tconst guidePopUpRef = useRef<HTMLDivElement | null>(null);\r\n\r\n\t//Added Zustand here\r\n\tconst {\r\n\t\tdesignPopup,\r\n\t\tannouncementJson,\r\n\t\tcurrentStep,\r\n\t\tsetSettingAnchorEl,\r\n\t\tsettingAnchorEl,\r\n\t\tupdateButtonAction,\r\n\t\tgetCurrentButtonInfo,\r\n\t\tbuttonsContainer,\r\n\t\tbuttonId,\r\n\t\tsetButtonId,\r\n\t\tcuntainerId,\r\n\t\tsetCuntainerId,\r\n\t\tbtnname,\r\n\t\tsetBtnName,\r\n\t\trtesContainer,\r\n\t\timagesContainer,\r\n\t} = useDrawerStore((state) => state);\r\n\r\n\tconst theme = useTheme();\r\n\tconst isFullScreen = useMediaQuery(theme.breakpoints.down(\"sm\"));\r\n\r\n\t// Synchronize local sectionCounts with actual store state\r\n\tuseEffect(() => {\r\n\t\tsetSectionCounts({\r\n\t\t\timage: imagesContainer.length,\r\n\t\t\ttext: rtesContainer.length,\r\n\t\t\tbutton: buttonsContainer.length,\r\n\t\t\tvideo: 0,\r\n\t\t\tgif: 0,\r\n\t\t\thtml: 0\r\n\t\t});\r\n\t}, [buttonsContainer.length, rtesContainer.length, imagesContainer.length]);\r\n\r\n\tconst handleCloseInteraction = () => {\r\n\t\tsetOpenInteractionList(false);\r\n\t};\r\n\r\n\tconst handleOpenInteraction = () => {\r\n\t\tsetOpenInteractionList(true);\r\n\t\tif (organizationId && !guideListByOrg.length) {\r\n\t\t\t(async () => {\r\n\t\t\t\tsetLoading(true);\r\n\t\t\t\tawait getGuildeListByOrg(organizationId);\r\n\t\t\t\tsetLoading(false);\r\n\t\t\t})();\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClose = (_event?: object, reason?: string) => {\r\n\t\tif (reason === \"backdropClick\" || reason === \"escapeKeyDown\") {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tsetOpen(false);\r\n\t};\r\n\r\n\tconst handleAddIconClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tif (hasReachedLimit(\"text\") && hasReachedLimit(\"button\") && hasReachedLimit(\"image\")) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tsetAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handlePopoverClose = () => {\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\tconst handleAddSection = (type: SectionType[\"type\"]) => {\r\n\t\t// Check if we've reached the limit for this section type\r\n\t\tif (hasReachedLimit(type)) {\r\n\t\t\t// Don't add more sections if limit is reached\r\n\t\t\tsetAnchorEl(null);\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tif (type === \"button\") {\r\n\t\t\t// Create and add a new button with default values\r\n\t\t\taddNewButton(\r\n\t\t\t\t{\r\n\t\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\t\tname: \"Button 1\",\r\n\t\t\t\t\tposition: \"center\",\r\n\t\t\t\t\ttype: \"primary\",\r\n\t\t\t\t\tisEditing: false,\r\n\t\t\t\t\tindex: 0,\r\n\t\t\t\t\tstyle: { ...defaultButtonColors },\r\n\t\t\t\t\tactions: {\r\n\t\t\t\t\t\tvalue: \"close\", // Default action is \"close\"\r\n\t\t\t\t\t\ttargetURL: targetURL, // Default empty target URL\r\n\t\t\t\t\t\ttab: \"same-tab\", // Default tab behavior\r\n\t\t\t\t\t},\r\n\t\t\t\t},\r\n\t\t\t\t\"\"\r\n\t\t\t);\r\n\r\n\t\t\t// Set the temporary colors (this might be for styling the new button)\r\n\t\t\tsetTempColors(defaultButtonColors);\r\n\r\n\t\t\t// Optionally, set the selected actions (if needed outside of the button creation)\r\n\t\t\tsetSelectedActions({\r\n\t\t\t\tvalue: \"close\", // Default action is \"close\"\r\n\t\t\t\ttargetURL: targetURL, // Default empty target URL\r\n\t\t\t\ttab: \"same-tab\", // Default tab behavior\r\n\t\t\t});\r\n\t\t} else if (type === \"image\") {\r\n\t\t\taddNewImageContainer();\r\n\t\t} else if (type === \"text\") {\r\n\t\t\taddNewRTEContainer();\r\n\t\t} else {\r\n\t\t\t// For other section types\r\n\t\t\tsetSections((prevSections) => [...prevSections, { type } as SectionType]);\r\n\t\t}\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleDragStart = (index: number) => {\r\n\t\tsetDraggingIndex(index);\r\n\t};\r\n\r\n\tconst handleDragEnter = (index: number) => {\r\n\t\tif (draggingIndex !== null && draggingIndex !== index) {\r\n\t\t\tconst reorderedSections = [...sections];\r\n\t\t\tconst [removed] = reorderedSections.splice(draggingIndex, 1);\r\n\t\t\treorderedSections.splice(index, 0, removed);\r\n\t\t\tsetSections(reorderedSections);\r\n\t\t\tsetDraggingIndex(index);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleDragEnd = () => {\r\n\t\tsetDraggingIndex(null);\r\n\t};\r\n\r\n\tconst handleDeleteRTESection = (index: number) => {\r\n\t\t// RTE section deletion is handled by the store\r\n\t\t// Section counts will be automatically updated via useEffect\r\n\t};\r\n\r\n\t// Function to handle deletion of image sections\r\n\tconst handleDeleteImageSection = () => {\r\n\t\t// Image section deletion is handled by the store\r\n\t\t// Section counts will be automatically updated via useEffect\r\n\t};\r\n\r\n\t// Function to handle deletion of button sections\r\n\tconst handleDeleteButtonSection = () => {\r\n\t\t// Button section deletion is handled by the store\r\n\t\t// Section counts will be automatically updated via useEffect\r\n\t};\r\n\r\n\t// Function to handle cloning of RTE sections\r\n\tconst handleCloneRTESection = () => {\r\n\t\t// Check if we've reached the limit for RTE sections\r\n\t\tif (hasReachedLimit(\"text\")) {\r\n\t\t\treturn; // Don't clone if limit is reached\r\n\t\t}\r\n\t\t// RTE section cloning is handled by the store\r\n\t\t// Section counts will be automatically updated via useEffect\r\n\t};\r\n\r\n\t// Function to handle cloning of image sections\r\n\tconst handleCloneImageSection = () => {\r\n\t\t// Check if we've reached the limit for image sections\r\n\t\tif (hasReachedLimit(\"image\")) {\r\n\t\t\treturn; // Don't clone if limit is reached\r\n\t\t}\r\n\t\t// Image section cloning is handled by the store\r\n\t\t// Section counts will be automatically updated via useEffect\r\n\t};\r\n\r\n\t// Function to handle cloning of button sections\r\n\tconst handleCloneButtonSection = () => {\r\n\t\t// Check if we've reached the limit for button sections\r\n\t\tif (hasReachedLimit(\"button\")) {\r\n\t\t\treturn; // Don't clone if limit is reached\r\n\t\t}\r\n\t\t// Button section cloning is handled by the store\r\n\t\t// Section counts will be automatically updated via useEffect\r\n\t};\r\n\t// State to track if scrolling is needed\r\n\tconst [needsScrolling, setNeedsScrolling] = useState(false);\r\n\tconst scrollbarRef = useRef<any>(null);\r\n\tconst renderSection = (section: SectionType, index: number) => {\r\n\t\tswitch (section.type) {\r\n\t\t\tcase \"image\":\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<ImageSection\r\n\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\tsetImageSrc={setImageSrc}\r\n\t\t\t\t\t\timageSrc={imageSrc}\r\n\t\t\t\t\t\tsetImageName={setImageName}\r\n\t\t\t\t\t\timageName={imageName}\r\n\t\t\t\t\t\tonDelete={handleDeleteImageSection}\r\n\t\t\t\t\t\tonClone={handleCloneImageSection}\r\n\t\t\t\t\t\tisCloneDisabled={hasReachedLimit(\"image\")}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\tcase \"text\":\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<RTEsection\r\n\t\t\t\t\t\tkey={index} // Use unique ID as the key for RTESection\r\n\t\t\t\t\t\ttextBoxRef={textBoxRef}\r\n\t\t\t\t\t\tisBanner={false}\r\n\t\t\t\t\t\thandleDeleteRTESection={() => handleDeleteRTESection(index)}\r\n\t\t\t\t\t\tindex={index}\r\n\t\t\t\t\t\t// @ts-ignore\r\n\t\t\t\t\t\tref={textBoxRef}\r\n\t\t\t\t\t\tguidePopUpRef={guidePopUpRef}\r\n\t\t\t\t\t\tonClone={handleCloneRTESection}\r\n\t\t\t\t\t\tisCloneDisabled={hasReachedLimit(\"text\")}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\tcase \"button\":\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<ButtonSection\r\n\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\tbuttonColor={buttonColor}\r\n\t\t\t\t\t\tsetButtonColor={setButtonColor}\r\n\t\t\t\t\t\tisBanner={false}\r\n\t\t\t\t\t\tonDelete={handleDeleteButtonSection}\r\n\t\t\t\t\t\tonClone={handleCloneButtonSection}\r\n\t\t\t\t\t\tisCloneDisabled={hasReachedLimit(\"button\")}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\tcase \"video\":\r\n\t\t\t\treturn <VideoSection key={index} />;\r\n\t\t\tcase \"html\":\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<HtmlSection\r\n\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\thtmlContent={htmlContent}\r\n\t\t\t\t\t\tsetHtmlContent={setHtmlContent}\r\n\t\t\t\t\t\tisBanner={false}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\tdefault:\r\n\t\t\t\treturn null;\r\n\t\t}\r\n\t};\r\n\r\n\tconst style = announcementJson.GuideStep.find((step) => step.stepName === currentStep)?.Canvas as\r\n\t\t| Record<string, unknown>\r\n\t\t| undefined;\r\n\r\n\tconst popupStyle: React.CSSProperties = {\r\n\t\t//maxWidth: \"533px\",\r\n\t\t//\tminWidth: TOOLTIP_MN_WIDTH,\r\n\t\tmaxWidth: `${style?.Width} !important` || \"500px !important\",\r\n\t\t// maxHeight: \"400px\",\r\n\t\twidth: `${style?.Width || 500}px`,\r\n\t\tborderRadius: `${style?.Radius ?? 8}px`,\r\n\t\tborderWidth: `${style?.BorderWidth || 0}px`,\r\n\t\tdisplay: \"flex\" as const,\r\n\t\tflexDirection: \"column\" as const,\r\n\t\tborderColor: `${style?.BorderColor || \"transparent\"}`,\r\n\t\tbackgroundColor: `${style?.BackgroundColor || \"#fff\"}`,\r\n\t\tborder: `${style?.BorderSize || \"0\"}px solid ${style?.BorderColor || \"none\"}`,\r\n\t\toverflow: \"visible\",\r\n\t};\r\n\r\n\tconst sectionStyle = {\r\n\t\twidth: \"100%\",\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"column\",\r\n\t\tposition: \"relative\" as const,\r\n\t\t\"&:hover .add-icon\": {\r\n\t\t\tdisplay: \"flex\",\r\n\t\t},\r\n\t\t\"&:hover .side-add-icon\": {\r\n\t\t\tdisplay: \"flex\",\r\n\t\t},\r\n\t\t\"&:hover .add-section\": {\r\n\t\t\topacity: \"1\",\r\n\t\t},\r\n\t};\r\n\r\n\tconst dragButtonStyle = {\r\n\t\tposition: \"absolute\" as const,\r\n\t\tleft: \"-60px\",\r\n\t\ttop: \"50%\",\r\n\t\ttransform: \"translateY(-50%)\",\r\n\t\tcursor: \"move\",\r\n\t\tzIndex: 1000,\r\n\t};\r\n\r\n\tconst sideAddButtonStyle = {\r\n\t\tposition: \"absolute\" as const,\r\n\t\tright: \"-38px\",\r\n\t\ttop: \"50%\",\r\n\t\ttransform: \"translateY(-50%)\",\r\n\t\twidth: \"18px\",\r\n\t\theight: \"100%\",\r\n\t\tborderRadius: \"6px\",\r\n\t\tdisplay: \"none\",\r\n\t\talignItems: \"center\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\tcursor: \"pointer\",\r\n\t\tzIndex: 1000,\r\n\t\t\"&:hover\": {\r\n\t\t\tbackgroundColor: \"#70afaf\",\r\n\t\t},\r\n\t};\r\n\r\n\tconst handleChangeTabs = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetSelectedTab((event.target as HTMLInputElement).value);\r\n\t};\r\n\r\n\tconst handleCloseSettingPopup = (containerId: string, buttonId: string) => {\r\n\t\tupdateButtonAction(containerId, buttonId, selectedActions);\r\n\t\tupdateButtonInteraction(containerId, buttonId, selectedInteraction);\r\n\r\n\t\tsetSettingAnchorEl({\r\n\t\t\tcontainerId: \"\",\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t};\r\n\tconst [targetURLError, setTargetURLError] = useState(\"\");\r\n\r\n\tconst validateTargetURL = (url: string) => {\r\n\t\tif (selectedActions.value === \"open-url\") {\r\n\t\t\tif (!url) {\r\n\t\t\t\treturn \"URL is required\";\r\n\t\t\t}\r\n\t\t\ttry {\r\n\t\t\t\tnew URL(url);\r\n\t\t\t\treturn \"\";\r\n\t\t\t} catch (error) {\r\n\t\t\t\treturn \"Invalid URL\";\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn \"\";\r\n\t};\r\n\r\n\tconst handleApplyChanges = (containerId: string, buttonId: string) => {\r\n\t\tconst error = validateTargetURL(targetURL);\r\n\t\tsetTargetURLError(error); // Set the error message for display\r\n\r\n\t\tif (error) {\r\n\t\t\treturn; // Prevent applying changes if there's a validation error\r\n\t\t}\r\n\r\n\t\tconst buttonNameToUpdate = !currentButtonName || !currentButtonName.trim()\r\n\t\t\t? curronButtonInfo.title // Retain the previously saved button name\r\n\t\t\t: currentButtonName;\r\n\t\tsetCurrentButtonName(buttonNameToUpdate);\r\n\r\n\t\tupdateButton(containerId, buttonId, \"style\", tempColors);\r\n\t\tupdateButtonAction(containerId, buttonId, selectedActions); // Update the selected actions\r\n\t\tupdateButtonInteraction(containerId, buttonId, selectedInteraction);\r\n\t\tupdateButton(containerId, buttonId, \"name\", buttonNameToUpdate);\r\n\t\tupdateButton(containerId, buttonId, \"actions\", selectedActions); // Update button actions\r\n\t\tsetSettingAnchorEl({ containerId: \"\", buttonId: \"\", value: null });\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\tconst handleURLChange = (e: any) => {\r\n\t\tconst newURL = e.target.value;\r\n\t\tsetTargetURL(newURL);\r\n\r\n\t\t// Validate the URL and update the error state\r\n\t\tconst error = validateTargetURL(newURL);\r\n\t\tsetTargetURLError(error);\r\n\r\n\t\tsetSelectedActions({\r\n\t\t\tvalue: selectedActions.value,\r\n\t\t\ttargetURL: newURL,\r\n\t\t\ttab: selectedTab as \"same-tab\" | \"new-tab\",\r\n\t\t});\r\n\t};\r\n\r\n\tconst curronButtonInfo = useMemo(() => {\r\n\t\tconst result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);\r\n\t\tsetCurrentButtonName(result.title);\r\n\t\tsetBtnName(result.title);\r\n\t\tsetAction(result.value);\r\n\t\treturn result;\r\n\t}, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);\r\n\r\n\tconst defaultButtonColors = {\r\n\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\tborderColor: \"#70afaf\",\r\n\t\tcolor: \"#ffffff\",\r\n\t};\r\n\r\n\tconst [selectedActions, setSelectedActions] = useState<TButtonAction>({\r\n\t\tvalue: \"close\", // Default action\r\n\t\ttargetURL: \"\", // Default empty target URL\r\n\t\ttab: \"same-tab\", // Default tab (same-tab)\r\n\t});\r\n\tconst [tempColors, setTempColors] = useState(defaultButtonColors);\r\n\tconst selectedButton = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst handleSelectButton = (containerId: any, buttonId: any) => {\r\n\t\t\tconst selectedButton = getCurrentButtonInfo(containerId, buttonId);\r\n\t\t\tif (selectedButton) {\r\n\t\t\t\tsetTargetURL(selectedButton.targetURL || \"\");\r\n\t\t\t\tsetTempColors({\r\n\t\t\t\t\tbackgroundColor: selectedButton.bgColor || defaultButtonColors.backgroundColor,\r\n\t\t\t\t\tborderColor: selectedButton.borderColor || defaultButtonColors.borderColor,\r\n\t\t\t\t\tcolor: selectedButton.textColor || defaultButtonColors.color,\r\n\t\t\t\t});\r\n\t\t\t\tsetSelectedActions({\r\n\t\t\t\t\tvalue: selectedButton.selectedActions || \"close\", // Default to \"close\" if no action is set\r\n\t\t\t\t\ttargetURL: selectedButton.targetURL || targetURL, // Can be updated later if needed\r\n\t\t\t\t\ttab: \"same-tab\", // Default tab behavior\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t};\r\n\t\thandleSelectButton(settingAnchorEl.containerId, settingAnchorEl.buttonId);\r\n\t}, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);\r\n\r\n\t// Function to handle color changes in the color picker\r\n\tconst handleColorChange = (e: any, targetName: any) => {\r\n\t\tconst value = e.target.value;\r\n\t\tsetTempColors((prev) => ({\r\n\t\t\t...prev,\r\n\t\t\t[targetName]: value,\r\n\t\t}));\r\n\t};\r\nuseEffect(() => {\r\n\r\n\t\tsetSelectedActions({\r\n\t\t\tvalue: selectedActions.value, // Default action\r\n\t\t\ttargetURL: targetURL, // Default empty target URL\r\n\t\t\ttab: \"same-tab\", // Default tab (same-tab)\r\n\t\t});\r\n}, []);\r\n// Check if content needs scrolling with improved detection\r\nuseEffect(() => {\r\n\tconst checkScrollNeeded = () => {\r\n\t\tif (guidePopUpRef.current) {\r\n\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\tguidePopUpRef.current.style.height = 'auto';\r\n\t\t\tconst contentHeight = guidePopUpRef.current.scrollHeight;\r\n\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t// Force update scrollbar\r\n\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t}\r\n\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 10);\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\t\r\n\tcheckScrollNeeded();\r\n\r\n\t\r\n\tconst timeouts = [\r\n\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t];\r\n\r\n\t\r\n\tlet resizeObserver: ResizeObserver | null = null;\r\n\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\tif (guidePopUpRef.current && window.ResizeObserver) {\r\n\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t});\r\n\t\tresizeObserver.observe(guidePopUpRef.current);\r\n\t}\r\n\r\n\t\r\n\tif (guidePopUpRef.current && window.MutationObserver) {\r\n\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t});\r\n\t\tmutationObserver.observe(guidePopUpRef.current, {\r\n\t\t\tchildList: true,\r\n\t\t\tsubtree: true,\r\n\t\t\tattributes: true,\r\n\t\t\tattributeFilter: ['style', 'class']\r\n\t\t});\r\n\t}\r\n\r\n\treturn () => {\r\n\t\ttimeouts.forEach(clearTimeout);\r\n\t\tif (resizeObserver) {\r\n\t\t\tresizeObserver.disconnect();\r\n\t\t}\r\n\t\tif (mutationObserver) {\r\n\t\t\tmutationObserver.disconnect();\r\n\t\t}\r\n\t};\r\n}, [ currentStep]);\r\n\tconst handleChangeActions = (e: SelectChangeEvent) => {\r\n\t\tconst v: TInteractionValue = e.target.value as TInteractionValue; // Casting to TInteractionValue\r\n\t\tsetSelectedActions({\r\n\t\t\tvalue: v, // Ensure that selectedActions.value is of type TInteractionValue\r\n\t\t\ttargetURL: targetURL,\r\n\t\t\ttab: selectedTab as \"same-tab\" | \"new-tab\", // Ensure tab is a valid value\r\n\t\t});\r\n\t};\r\n\t// useEffect(() => {\r\n\t// \tif (selectedButton) {\r\n\t// \t  selectedButton.targetURL = targetURL;  // Update selectedButton's targetURL whenever targetURL state changes\r\n\t// \t}\r\n\t//   }, [targetURL]);  // Dependency on `targetURL`\r\n\t// Dependency on `targetURL`\r\n\r\n\r\n\treturn (\r\n\t\t<div className=\"qadpt-annpopop\">\r\n\t\t\t{overlayEnabled && (\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\twidth: \"100vw\",\r\n\t\t\t\t\t\theight: \"100vh\",\r\n\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\tzIndex: 9,\r\n\t\t\t\t\t}}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t\t{isUnSavedChanges && openWarning &&(\r\n\t\t\t\t<AlertPopup\r\n\t\t\t\t\topenWarning={openWarning}\r\n\t\t\t\t\tsetopenWarning={setopenWarning}\r\n\t\t\t\t\thandleLeave={handleLeave}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\r\n\t\t\t<Dialog\r\n\t\t\t\tclassName=\"qadpt-guide-popup\"\r\n\t\t\t\topen={open}\r\n\t\t\t\tonClose={handleClose}\r\n\t\t\t\tfullScreen={isFullScreen}\r\n\t\t\t\tPaperProps={{\r\n\t\t\t\t\tstyle: popupStyle,\r\n\t\t\t\t}}\r\n\t\t\t\tmaxWidth={false}\r\n\t\t\t\tdisableEnforceFocus={true}\r\n\t\t\t>\r\n\t\t\t\t<div style={{placeContent:\"end\",display:\"flex\"}}>\r\n\t\t\t\t\t{dismiss && (\r\n\t\t\t\t\t\t<IconButton className=\"qadpt-dismiss\"\r\n\t\t\t\t\t\t\t//onClick={handleCloseBanner}\r\n\t\t\t\t\t\t>\r\n<CloseIcon sx={{ zoom: 1,color:\"#000\"}} />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\t\t\t\t<PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\tstyle={{\r\n  maxHeight: \"400px\",\r\n  ...(progress\r\n    ? {\r\n        borderTopLeftRadius: `${style?.Radius ?? 8}px`,\r\n        borderTopRightRadius: `${style?.Radius ?? 8}px`,\r\n        borderBottomLeftRadius: \"0px\",\r\n        borderBottomRightRadius: \"0px\",\r\n      }\r\n    : {\r\n        borderRadius: `${style?.Radius ?? 8}px`,\r\n      }),\r\n}}\r\n\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: false,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<DialogContent\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tpadding: 0,\r\n\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\t\tref={guidePopUpRef}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\tid=\"guide-popup\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{/* {dismissData?.dismisssel ? (\r\n\t\t\t\t\t\t<DialogTitle\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\tjustifyContent: \"end\",\r\n\t\t\t\t\t\t\t\tpadding: \"5px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t// sx={{ position: \"absolute\", right: 8, top: 8, color: `${dissmissIconColor}` }}\r\n\t\t\t\t\t\t\t\t//onClick={handleClose}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<CloseIcon\r\n\t\t\t\t\t\t\t\t\t// color={dismissData.Color}\r\n\t\t\t\t\t\t\t\t\thtmlColor={dismissData.Color}\r\n\t\t\t\t\t\t\t\t\t// fontSize=\"medium\"\r\n\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"1.5rem !important\" }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</DialogTitle>\r\n\t\t\t\t\t) : null} */}\r\n\r\n\r\n\t\t\t\t\t{/* <PerfectScrollbar> */}\r\n\t\t\t\t\t<Box sx={{padding: `${style?.Padding || 12}px`,\r\n}}>\r\n\t\t\t\t\t\t{sections.map((section, index) => (\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t...sectionStyle,\r\n\t\t\t\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tborderTopWidth: index !== 0 ? \"1px\" : \"0px\",\r\n\t\t\t\t\t\t\t\t\t\tborderTopColor: index !== 0 ? \"var(--primarycolor)\" : \"transparent\",\r\n\t\t\t\t\t\t\t\t\t\tborderTopStyle: index !== 0 ? \"dotted\" : \"none\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdraggable\r\n\t\t\t\t\t\t\t\tonDragStart={() => handleDragStart(index)}\r\n\t\t\t\t\t\t\t\tonDragEnter={() => handleDragEnter(index)}\r\n\t\t\t\t\t\t\t\tonDragEnd={handleDragEnd}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tclassName=\"drag-icon\"\r\n\t\t\t\t\t\t\t\t\tsx={dragButtonStyle}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<DragIndicatorIcon\r\n\t\t\t\t\t\t\t\t\t\tfontSize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ color: \"#5F9EA0\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\r\n\t\t\t\t\t\t\t\t{renderSection(section, index)}\r\n\t\t\t\t\t\t\t\t{index !== 0 && (\r\n\t\t\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\t\t\ttitle={\r\n\t\t\t\t\t\t\t\t\t\t\thasReachedLimit(\"text\") && hasReachedLimit(\"button\") && hasReachedLimit(\"image\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t? translate(\"Maximum limit reached for all section types\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t: translate(\"Add Section\")\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"add-section\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={handleAddIconClick}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#70afaf\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttop: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tleft: \"50%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttransform: \"translate(-50%, -50%)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\topacity: \"0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcursor: hasReachedLimit(\"text\") && hasReachedLimit(\"button\") && hasReachedLimit(\"image\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t? \"not-allowed\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled={hasReachedLimit(\"text\") && hasReachedLimit(\"button\") && hasReachedLimit(\"image\")}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<AddIcon\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx={{ color: \"#fff\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t))}\r\n\r\n\t\t\t\t\t\t{Boolean(settingAnchorEl.value) ? (\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-designpopup qadpt-btnprop\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Properties\")}</div>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleCloseSettingPopup(settingAnchorEl.containerId, settingAnchorEl.buttonId)}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-canblock qadpt-btnpro\">\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t\t\t\t\t<FormControl\r\n\t\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ marginBottom: \"16px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\" }}>{translate(\"Button Name\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={currentButtonName}\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tmb: \"5px\",\r\n\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\r\n  \"& .MuiOutlinedInput-root\": {\r\n    height: \"35px\",\r\n    \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n  },\r\n  \"& .MuiOutlinedInput-notchedOutline\": {\r\n    border: \"none !important\",\r\n  }\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Button Name\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => setCurrentButtonName(e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", mb: \"5px\" }}>{translate(\"Button Action\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedActions.value}\r\n\t\t\t\t\t\t\t\t\t\t\t\tdefaultValue=\"close\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleChangeActions}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tmb: \"5px\",\r\n\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\t\t\"& .MuiSelect-select\": {\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\r\n  \"& .MuiOutlinedInput-root\": {\r\n    height: \"35px\",\r\n    \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n  },\r\n  \"& .MuiOutlinedInput-notchedOutline\": {\r\n    border: \"none !important\",\r\n  }\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"close\">{translate(\"Close\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"open-url\">{translate(\"Open URL\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"Previous\">{translate(\"Previous\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"Next\">{translate(\"Next\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"Restart\">{translate(\"Restart\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t\t\t\t\t{selectedActions.value === \"open-url\" ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\" }}>{translate(\"Enter URL\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={targetURL}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"https://quixy.com\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst newURL = e.target.value;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetTargetURL(newURL); // Update the `targetURL` state with the new value\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thandleURLChange(e); // Update the selectedButton.targetURL with the new value\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\terror={!!targetURLError}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thelperText={targetURLError}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<ToggleButtonGroup\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedTab}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleChangeTabs}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\texclusive\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-label={translate(\"open in tab\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmarginY: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"35px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{[\"new-tab\", \"same-tab\"].map((tab) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ToggleButton\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={tab}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-label=\"new tab\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #7EA8A5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tflex: 1,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 !important\",\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-selected\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"var(--border-color)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"2px solid #7EA8A5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#f5f5f5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:last-child\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderLeft: \"1px solid var(--primarycolor) !important\", // Remove left border for the last button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{tab}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</ToggleButton>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</ToggleButtonGroup>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t\t\t\t\t\t{/* {selectedActions === \"start-interaction\" ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tChoose Interaction\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Autocomplete\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t// sx={{ width: 300 }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\topen={openInteractionList}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedInteraction}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(event, newValue) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetSelectedInteraction(newValue);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonOpen={handleOpenInteraction}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClose={handleCloseInteraction}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tisOptionEqualToValue={(option, value) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn option.guideId === value.guideId;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tgetOptionLabel={(option) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn option.title;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfreeSolo\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\toptions={guideListByOrg}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tloading={loading}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trenderInput={(params) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{...params}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Select Interaction\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tinputLabel: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tshrink: false,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t\t) : null} */}\r\n\t\t\t\t\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t\t\t\t\t{/* <Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", mb: \"5px\" }}>Button Color</Typography> */}\r\n\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Background\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={tempColors.backgroundColor}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => handleColorChange(e, \"backgroundColor\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={tempColors.borderColor}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => handleColorChange(e, \"borderColor\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Text\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={tempColors.color}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => handleColorChange(e, \"color\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleApplyChanges(settingAnchorEl.containerId, settingAnchorEl.buttonId)}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-btn\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t\r\n\t\t\t\t\t{/* </PerfectScrollbar> */}\r\n\t\t\t\t\t</DialogContent>\r\n\t\t\t\t</PerfectScrollbar>\r\n\t\t\t\t<Box sx={{\t\tborderRadius: `${style?.Radius ?? 8}px`,\r\n}}>\r\n\t\t\t\t{progress  &&\r\n\t\t\t\t\t(selectedOption === 1 || selectedOption === \"\" ? (\r\n\t\t\t\t\t\t<DotsStepper\r\n\t\t\t\t\t\t\tactiveStep={currentStep}\r\n\t\t\t\t\t\t\tsteps={steps.length}\r\n\t\t\t\t\t\t\tProgressColor = {ProgressColor}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)\r\n\t\t\t\t\t: selectedOption === 2 ? (\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: hexToRgba(ProgressColor, 0.45),\r\n\t\t\t\t\t\t\t\t\t\t'& .MuiLinearProgress-bar': {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // progress bar color\r\n\t\t\t\t\t\t\t\t  },}}\r\n\t\t\t\t\t\t\t\tvalue={(currentStep / steps.length) * 100}\r\n\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t) : selectedOption === 3 ? (\r\n\t\t\t\t\t\t<div style={{ padding: \"8px\" }}>\r\n\t\t\t\t\t\t\t<BreadCrumpStepper\r\n\t\t\t\t\t\t\tactiveStep={currentStep}\r\n\t\t\t\t\t\t\tsteps={steps.length}\r\n\t\t\t\t\t\t\tProgressColor={ProgressColor}\r\n\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t): selectedOption === 4 ? (\r\n\t\t\t\t\t\t<Breadcrumbs\r\n\t\t\t\t\t\t\taria-label=\"breadcrumb\"\r\n\t\t\t\t\t\t\tsx={{ padding: \"8px\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tStep {currentStep} of {steps.length}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t</Breadcrumbs>\r\n\t\t\t\t\t\t\t) : null)}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t{anchorEl && (\r\n\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-secprop\"\r\n\t\t\t\t\t\topen={Boolean(anchorEl)}\r\n\t\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\t\tonClose={handlePopoverClose}\r\n\t\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\tpaper: {\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\tpadding: \"12px\",\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\tgap: \"16px\",\r\n\t\t\t\t\t\t\t\t\twidth: \"auto\",\r\n\t\t\t\t\t\t\t\t\tzIndex: 1302,\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t// top: `${anchorEl.getBoundingClientRect().bottom + 8}px`,\r\n\t\t\t\t\t\t\t// left: `${anchorEl.getBoundingClientRect().left - 150}px`,\r\n\t\t\t\t\t\t\ttransform: \"none\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\tflexDirection=\"row\"\r\n\t\t\t\t\t\t\tgap=\"16px\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{/* Rich Text Section */}\r\n\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\ttitle={hasReachedLimit(\"text\") ? translate(\"Maximum limit of 3 Rich Text sections reached\") : \"\"}\r\n\t\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tcursor: hasReachedLimit(\"text\") ? \"not-allowed\" : \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\topacity: hasReachedLimit(\"text\") ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tonClick={() => !hasReachedLimit(\"text\") && handleAddSection(\"text\")}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<TextFormat />\r\n\t\t\t\t\t\t\t\t\t<Typography variant=\"caption\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize: \"11px !important\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>{translate(\"Rich Text\")}</Typography>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t\t\t{/* Button Section */}\r\n\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\ttitle={hasReachedLimit(\"button\") ? translate(\"Maximum limit of 3 Button sections reached\") : \"\"}\r\n\t\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tcursor: hasReachedLimit(\"button\") ? \"not-allowed\" : \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\topacity: hasReachedLimit(\"button\") ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tonClick={() => !hasReachedLimit(\"button\") && handleAddSection(\"button\")}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Link />\r\n\t\t\t\t\t\t\t\t\t<Typography variant=\"caption\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize: \"11px !important\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>{translate(\"Button\")}</Typography>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t\t\t{/* Image Section */}\r\n\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\ttitle={hasReachedLimit(\"image\") ? translate(\"Maximum limit of 3 Image sections reached\") : \"\"}\r\n\t\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tcursor: hasReachedLimit(\"image\") ? \"not-allowed\" : \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\topacity: hasReachedLimit(\"image\") ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tonClick={() => !hasReachedLimit(\"image\") && handleAddSection(\"image\")}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Image />\r\n\t\t\t\t\t\t\t\t\t<Typography variant=\"caption\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize: \"11px !important\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>{translate(\"Image\")}</Typography>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\ttitle={translate(\"Coming Soon\")}\r\n\t\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\topacity: 0.5,\r\n\t\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t// onClick={() => handleAddSection(\"video\")}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<VideoLibrary />\r\n\t\t\t\t\t\t\t\t\t\t<Typography variant=\"caption\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"11px !important\"\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>{translate(\"Video\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t{/* {<Box\r\n\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\tsx={{ cursor: \"pointer\", opacity: 0.5 }}\r\n\t\t\t\t\t\t\t\tonClick={() => handleAddSection(\"gif\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<GifBox />\r\n\t\t\t\t\t\t\t\t<Typography variant=\"caption\">Gif</Typography>\r\n\t\t\t\t\t\t\t</Box>*/}\r\n\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\ttitle={translate(\"Coming Soon\")}\r\n\t\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\topacity: 0.5,\r\n\t\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleAddSection(\"html\")}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Code />\r\n\t\t\t\t\t\t\t\t\t\t<Typography variant=\"caption\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"11px !important\"\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>{translate(\"HTML\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Popover>\r\n\t\t\t\t)}\r\n\r\n\t\t\t</Dialog>\r\n\t\t</div>\r\n\t);\r\n};\r\nexport default GuidePopup;\r\nconst DotsStepper = ({ steps, activeStep, ProgressColor }: { steps: number; activeStep: number; ProgressColor:any }) => {\r\n\treturn (\r\n\t\t<MobileStepper\r\n\t\t\tvariant=\"dots\"\r\n\t\t\tsteps={steps}\r\n\t\t\tposition=\"static\"\r\n\t\t\tactiveStep={activeStep - 1}\r\n\t\t\tsx={{ flexGrow: 1, display: \"flex\", justifyContent: \"center\",background:\"inherit\",\"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\tbackgroundColor: ProgressColor, // active dot color\r\n\t\t\t  } }}\r\n\t\t\tnextButton={<></>}\r\n\t\t\tbackButton={<></>}\r\n\r\n\t\t/>\r\n\t);\r\n};\r\nconst BreadCrumpStepper = ({ steps, activeStep,ProgressColor }: { steps: number; activeStep: number; ProgressColor:any }) => {\r\n\treturn (\r\n\t\t<Box sx={{ flexGrow: 1 }}>\r\n\t\t  {/* Custom Step Indicators */}\r\n\t\t  <Box\r\n\t\t\tsx={{\r\n\t\t\t  display: 'flex',\r\n\t\t\t  justifyContent: 'center',\r\n\t\t\t  gap: \"4px\", // Adjust space between steps\r\n\t\t\t//   paddingTop: '15px',\r\n\t\t\t}}\r\n\t\t  >\r\n\t\t\t{Array.from({ length: steps }).map((_, index) => (\r\n\t\t\t  <div\r\n\t\t\t\tkey={index}\r\n\t\t\t\tstyle={{\r\n\t\t\t\t  width: '14px',\r\n\t\t\t\t  height: '4px',\r\n\t\t\t\t  backgroundColor: index === activeStep - 1 ? ProgressColor : hexToRgba(ProgressColor, 0.45), // Active color and inactive color\r\n\t\t\t\t  borderRadius: '100px',\r\n\t\t\t\t}}\r\n\t\t\t  />\r\n\t\t\t))}\r\n\t\t  </Box>\r\n\t\t</Box>\r\n\t  );\r\n};\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,OAAO,CAAEC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAEnE,OACCC,MAAM,CACNC,aAAa,CACbC,aAAa,CACbC,QAAQ,CACRC,GAAG,CACHC,UAAU,CACVC,OAAO,CACPC,UAAU,CACVC,MAAM,CACNC,WAAW,CACXC,MAAM,CACNC,QAAQ,CACRC,SAAS,CAMTC,YAAY,CACZC,iBAAiB,CAIjBC,OAAO,CACPC,cAAc,CACdC,aAAa,CACbC,WAAW,KAEL,eAAe,CACtB,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CACvD,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,aAAa,KAAM,wBAAwB,CAClD,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,MAAO,CAAAC,iBAAiB,KAAM,mCAAmC,CACjE,OAASC,KAAK,CAAEC,UAAU,CAAEC,IAAI,CAAEC,YAAY,CAAUC,IAAI,KAAiB,qBAAqB,CAClG,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CACvD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,kCAAkC,CACzC,MAAO,CAAAC,gBAAgB,KAAM,yBAAyB,CACtD,MAAO,6CAA6C,CAEpD,MAAO,CAAAC,UAAU,KAAM,sBAAsB,CAE7C,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAC/C,MAAO,MAAM,CAAAC,gBAAgB,CAAG,GAAG,CAEnC;AACA,KAAM,CAAAC,SAAS,CAAGA,CAACC,GAAW,CAAEC,OAAe,GAAa,CAC3D;AACAD,GAAG,CAAGA,GAAG,CAACE,OAAO,CAAC,GAAG,CAAE,EAAE,CAAC,CAE1B;AACA,KAAM,CAAAC,CAAC,CAAGC,QAAQ,CAACJ,GAAG,CAACK,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,CAC3C,KAAM,CAAAC,CAAC,CAAGF,QAAQ,CAACJ,GAAG,CAACK,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,CAC3C,KAAM,CAAAE,CAAC,CAAGH,QAAQ,CAACJ,GAAG,CAACK,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,CAE3C,MAAO,QAAQF,CAAC,KAAKG,CAAC,KAAKC,CAAC,KAAKN,OAAO,GAAG,CAC5C,CAAC,CAE8F;AAC/F,KAAM,CAAAO,UAAU,CAAGC,IAAA,EAiBR,KAAAC,qBAAA,CAAAC,aAAA,CAAAC,cAAA,CAAAC,cAAA,CAAAC,cAAA,CAAAC,cAAA,IAjBS,CACnBC,gBAAgB,CAChBC,SAAS,CACTC,WAAW,CACXC,QAAQ,CACRC,UAAU,CACVC,WAAW,CACXC,cAAc,CACdC,WAAW,CACXC,cAAc,CACdC,YAAY,CACZC,SAAS,CACTC,gBAAgB,CAChBC,WAAW,CACXC,cAAc,CACdC,gBAAgB,CAChBC,WACI,CAAC,CAAAtB,IAAA,CACL,KAAM,CAAEuB,CAAC,CAAEC,SAAU,CAAC,CAAG1C,cAAc,CAAC,CAAC,CACzC,KAAM,CACL2C,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,kBAAkB,CAClBC,uBAAuB,CACvBC,oBAAoB,CACpBC,WAAW,CACXC,gBAAgB,CAChBC,iBAAiB,CACjBC,oBAAoB,CACpBC,SAAS,CACTC,YAAY,CACZC,mBAAmB,CACnBC,sBAAsB,CACtBC,mBAAmB,CACnBC,sBAAsB,CACtBC,WAAW,CACXC,cAAc,CACdC,OAAO,CACPC,UAAU,CACVC,kBAAkB,CAClBC,OAAO,CACPC,gBAAgB,CAChBC,cAAc,CACdC,QAAQ,CACRC,KAAK,CACLC,WAAW,CACXC,gBAAgB,CAChBC,yBAAyB,CACzBC,8BAA8B,CAC9BC,oBAAoB,CACpBC,mBAAmB,CACnBC,aAAa,CACbC,gBAAgB,CAChBC,YACD,CAAC,CAAGjF,cAAc,CAAEkF,KAAK,EAAKA,KAAK,CAAC,CACpC,KAAM,CAACC,IAAI,CAAEC,OAAO,CAAC,CAAGtH,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAACuH,QAAQ,CAAEC,WAAW,CAAC,CAAGxH,QAAQ,CAAqB,IAAI,CAAC,CAClE,KAAM,CAACyH,QAAQ,CAAEC,WAAW,CAAC,CAAG1H,QAAQ,CAAgB,CAAC,CAAE2H,IAAI,CAAE,OAAQ,CAAC,CAAE,CAAEA,IAAI,CAAE,MAAO,CAAC,CAAE,CAAEA,IAAI,CAAE,QAAS,CAAC,CAAC,CAAC,CAClH,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG7H,QAAQ,CAAgB,IAAI,CAAC,CAEvE,KAAM,CAAC8H,aAAa,CAAEC,gBAAgB,CAAC,CAAG/H,QAAQ,CAAC,CAClDgI,KAAK,CAAE,CAAC,CAAE;AACVC,IAAI,CAAE,CAAC,CACPC,MAAM,CAAE,CAAC,CACTC,KAAK,CAAE,CAAC,CACRC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CACP,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,YAAY,CAAG,CACpBN,KAAK,CAAE,CAAC,CACRC,IAAI,CAAE,CAAC,CAAE;AACTC,MAAM,CAAE,CAAC,CACTC,KAAK,CAAE,CAAC,CACRC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CACP,CAAC,CAED;AACA,KAAM,CAAAE,eAAe,CAAIZ,IAAyB,EAAc,CAC/D;AACA,KAAM,CAAAa,SAAS,CAAGb,IAAI,GAAK,MAAM,CAAG,MAAM,CAAGA,IAAI,CACjD,MAAO,CAAAG,aAAa,CAACU,SAAS,CAAC,EAAIF,YAAY,CAACE,SAAS,CAAC,CAC3D,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAG1I,QAAQ,CAAC,OAAO,CAAC,CAC7C,KAAM,CAAA2I,QAAQ,CAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CACjD,KAAM,CAAAC,WAAW,CAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,EAAI,IAAI,CAAC,CAChD,KAAM,CAAAM,UAAU,CAAGF,IAAI,CAACC,KAAK,CAACF,WAAW,CAACG,UAAU,EAAI,IAAI,CAAC,CAC7D,KAAM,CAAAC,cAAc,CAAGD,UAAU,CAACE,cAAc,CAChD,KAAM,CAAAC,cAAc,CAAGlH,cAAc,CAAEkF,KAAK,EAAKA,KAAK,CAACgC,cAAc,CAAC,CACtE,KAAM,CAAAC,aAAa,CAAGnJ,MAAM,CAAwB,IAAI,CAAC,CAEzD;AACA,KAAM,CACLoJ,WAAW,CACXC,gBAAgB,CAChBC,WAAW,CACXC,kBAAkB,CAClBC,eAAe,CACfC,kBAAkB,CAClBC,oBAAoB,CACpBC,gBAAgB,CAChBC,QAAQ,CACRC,WAAW,CACXC,WAAW,CACXC,cAAc,CACdC,OAAO,CACPC,UAAU,CACVC,aAAa,CACbC,eACD,CAAC,CAAGnI,cAAc,CAAEkF,KAAK,EAAKA,KAAK,CAAC,CAEpC,KAAM,CAAAkD,KAAK,CAAGhK,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAiK,YAAY,CAAGlK,aAAa,CAACiK,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAEhE;AACAxK,SAAS,CAAC,IAAM,CACf8H,gBAAgB,CAAC,CAChBC,KAAK,CAAEqC,eAAe,CAACK,MAAM,CAC7BzC,IAAI,CAAEmC,aAAa,CAACM,MAAM,CAC1BxC,MAAM,CAAE2B,gBAAgB,CAACa,MAAM,CAC/BvC,KAAK,CAAE,CAAC,CACRC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CACP,CAAC,CAAC,CACH,CAAC,CAAE,CAACwB,gBAAgB,CAACa,MAAM,CAAEN,aAAa,CAACM,MAAM,CAAEL,eAAe,CAACK,MAAM,CAAC,CAAC,CAE3E,KAAM,CAAAC,sBAAsB,CAAGA,CAAA,GAAM,CACpC3E,sBAAsB,CAAC,KAAK,CAAC,CAC9B,CAAC,CAED,KAAM,CAAA4E,qBAAqB,CAAGA,CAAA,GAAM,CACnC5E,sBAAsB,CAAC,IAAI,CAAC,CAC5B,GAAIkD,cAAc,EAAI,CAAC/D,cAAc,CAACuF,MAAM,CAAE,CAC7C,CAAC,SAAY,CACZtE,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAhB,kBAAkB,CAAC8D,cAAc,CAAC,CACxC9C,UAAU,CAAC,KAAK,CAAC,CAClB,CAAC,EAAE,CAAC,CACL,CACD,CAAC,CAED,KAAM,CAAAyE,WAAW,CAAGA,CAACC,MAAe,CAAEC,MAAe,GAAK,CACzD,GAAIA,MAAM,GAAK,eAAe,EAAIA,MAAM,GAAK,eAAe,CAAE,CAC7D,OACD,CACAzD,OAAO,CAAC,KAAK,CAAC,CACf,CAAC,CAED,KAAM,CAAA0D,kBAAkB,CAAIC,KAAoC,EAAK,CACpE,GAAI1C,eAAe,CAAC,MAAM,CAAC,EAAIA,eAAe,CAAC,QAAQ,CAAC,EAAIA,eAAe,CAAC,OAAO,CAAC,CAAE,CACrF,OACD,CACAf,WAAW,CAACyD,KAAK,CAACC,aAAa,CAAC,CACjC,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAChC3D,WAAW,CAAC,IAAI,CAAC,CAClB,CAAC,CACD,KAAM,CAAA4D,gBAAgB,CAAIzD,IAAyB,EAAK,CACvD;AACA,GAAIY,eAAe,CAACZ,IAAI,CAAC,CAAE,CAC1B;AACAH,WAAW,CAAC,IAAI,CAAC,CACjB,OACD,CAEA,GAAIG,IAAI,GAAK,QAAQ,CAAE,CACtB;AACA1C,YAAY,CACX,CACCoG,EAAE,CAAEC,MAAM,CAACC,UAAU,CAAC,CAAC,CACvBC,IAAI,CAAE,UAAU,CAChBC,QAAQ,CAAE,QAAQ,CAClB9D,IAAI,CAAE,SAAS,CACf+D,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,CAAE,GAAGC,mBAAoB,CAAC,CACjCC,OAAO,CAAE,CACRC,KAAK,CAAE,OAAO,CAAE;AAChBpG,SAAS,CAAEA,SAAS,CAAE;AACtBqG,GAAG,CAAE,UAAY;AAClB,CACD,CAAC,CACD,EACD,CAAC,CAED;AACAC,aAAa,CAACJ,mBAAmB,CAAC,CAElC;AACAK,kBAAkB,CAAC,CAClBH,KAAK,CAAE,OAAO,CAAE;AAChBpG,SAAS,CAAEA,SAAS,CAAE;AACtBqG,GAAG,CAAE,UAAY;AAClB,CAAC,CAAC,CACH,CAAC,IAAM,IAAIrE,IAAI,GAAK,OAAO,CAAE,CAC5BrC,oBAAoB,CAAC,CAAC,CACvB,CAAC,IAAM,IAAIqC,IAAI,GAAK,MAAM,CAAE,CAC3BtB,kBAAkB,CAAC,CAAC,CACrB,CAAC,IAAM,CACN;AACAqB,WAAW,CAAEyE,YAAY,EAAK,CAAC,GAAGA,YAAY,CAAE,CAAExE,IAAK,CAAC,CAAgB,CAAC,CAC1E,CACAH,WAAW,CAAC,IAAI,CAAC,CAClB,CAAC,CAED,KAAM,CAAA4E,eAAe,CAAIT,KAAa,EAAK,CAC1C9D,gBAAgB,CAAC8D,KAAK,CAAC,CACxB,CAAC,CAED,KAAM,CAAAU,eAAe,CAAIV,KAAa,EAAK,CAC1C,GAAI/D,aAAa,GAAK,IAAI,EAAIA,aAAa,GAAK+D,KAAK,CAAE,CACtD,KAAM,CAAAW,iBAAiB,CAAG,CAAC,GAAG7E,QAAQ,CAAC,CACvC,KAAM,CAAC8E,OAAO,CAAC,CAAGD,iBAAiB,CAACE,MAAM,CAAC5E,aAAa,CAAE,CAAC,CAAC,CAC5D0E,iBAAiB,CAACE,MAAM,CAACb,KAAK,CAAE,CAAC,CAAEY,OAAO,CAAC,CAC3C7E,WAAW,CAAC4E,iBAAiB,CAAC,CAC9BzE,gBAAgB,CAAC8D,KAAK,CAAC,CACxB,CACD,CAAC,CAED,KAAM,CAAAc,aAAa,CAAGA,CAAA,GAAM,CAC3B5E,gBAAgB,CAAC,IAAI,CAAC,CACvB,CAAC,CAED,KAAM,CAAA6E,sBAAsB,CAAIf,KAAa,EAAK,CACjD;AACA;AAAA,CACA,CAED;AACA,KAAM,CAAAgB,wBAAwB,CAAGA,CAAA,GAAM,CACtC;AACA;AAAA,CACA,CAED;AACA,KAAM,CAAAC,yBAAyB,CAAGA,CAAA,GAAM,CACvC;AACA;AAAA,CACA,CAED;AACA,KAAM,CAAAC,qBAAqB,CAAGA,CAAA,GAAM,CACnC;AACA,GAAItE,eAAe,CAAC,MAAM,CAAC,CAAE,CAC5B,OAAQ;AACT,CACA;AACA;AACD,CAAC,CAED;AACA,KAAM,CAAAuE,uBAAuB,CAAGA,CAAA,GAAM,CACrC;AACA,GAAIvE,eAAe,CAAC,OAAO,CAAC,CAAE,CAC7B,OAAQ;AACT,CACA;AACA;AACD,CAAC,CAED;AACA,KAAM,CAAAwE,wBAAwB,CAAGA,CAAA,GAAM,CACtC;AACA,GAAIxE,eAAe,CAAC,QAAQ,CAAC,CAAE,CAC9B,OAAQ;AACT,CACA;AACA;AACD,CAAC,CACD;AACA,KAAM,CAACyE,cAAc,CAAEC,iBAAiB,CAAC,CAAGjN,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAAkN,YAAY,CAAGhN,MAAM,CAAM,IAAI,CAAC,CACtC,KAAM,CAAAiN,aAAa,CAAGA,CAACC,OAAoB,CAAEzB,KAAa,GAAK,CAC9D,OAAQyB,OAAO,CAACzF,IAAI,EACnB,IAAK,OAAO,CACX,mBACCnF,IAAA,CAAClB,YAAY,EAEZ2C,WAAW,CAAEA,WAAY,CACzBC,QAAQ,CAAEA,QAAS,CACnBM,YAAY,CAAEA,YAAa,CAC3BC,SAAS,CAAEA,SAAU,CACrB4I,QAAQ,CAAEV,wBAAyB,CACnCW,OAAO,CAAER,uBAAwB,CACjCS,eAAe,CAAEhF,eAAe,CAAC,OAAO,CAAE,EAPrCoD,KAQL,CAAC,CAEJ,IAAK,MAAM,CACV,mBACCnJ,IAAA,CAACjB,UAAU,EACE;AACZ4C,UAAU,CAAEA,UAAW,CACvBqJ,QAAQ,CAAE,KAAM,CAChBd,sBAAsB,CAAEA,CAAA,GAAMA,sBAAsB,CAACf,KAAK,CAAE,CAC5DA,KAAK,CAAEA,KACP;AAAA,CACA8B,GAAG,CAAEtJ,UAAW,CAChBkF,aAAa,CAAEA,aAAc,CAC7BiE,OAAO,CAAET,qBAAsB,CAC/BU,eAAe,CAAEhF,eAAe,CAAC,MAAM,CAAE,EATpCoD,KAUL,CAAC,CAEJ,IAAK,QAAQ,CACZ,mBACCnJ,IAAA,CAAChB,aAAa,EAEb8C,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BiJ,QAAQ,CAAE,KAAM,CAChBH,QAAQ,CAAET,yBAA0B,CACpCU,OAAO,CAAEP,wBAAyB,CAClCQ,eAAe,CAAEhF,eAAe,CAAC,QAAQ,CAAE,EANtCoD,KAOL,CAAC,CAEJ,IAAK,OAAO,CACX,mBAAOnJ,IAAA,CAACP,YAAY,IAAM0J,KAAQ,CAAC,CACpC,IAAK,MAAM,CACV,mBACCnJ,IAAA,CAACR,WAAW,EAEXoC,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BmJ,QAAQ,CAAE,KAAM,EAHX7B,KAIL,CAAC,CAEJ,QACC,MAAO,KAAI,CACb,CACD,CAAC,CAED,KAAM,CAAAC,KAAK,EAAAnI,qBAAA,CAAG8F,gBAAgB,CAACmE,SAAS,CAACC,IAAI,CAAEC,IAAI,EAAKA,IAAI,CAACC,QAAQ,GAAKrE,WAAW,CAAC,UAAA/F,qBAAA,iBAAxEA,qBAAA,CAA0EqK,MAE5E,CAEZ,KAAM,CAAAC,UAA+B,CAAG,CACvC;AACA;AACAC,QAAQ,CAAE,GAAGpC,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEqC,KAAK,aAAa,EAAI,kBAAkB,CAC5D;AACAC,KAAK,CAAE,GAAG,CAAAtC,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEqC,KAAK,GAAI,GAAG,IAAI,CACjCE,YAAY,CAAE,IAAAzK,aAAA,CAAGkI,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEwC,MAAM,UAAA1K,aAAA,UAAAA,aAAA,CAAI,CAAC,IAAI,CACvC2K,WAAW,CAAE,GAAG,CAAAzC,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE0C,WAAW,GAAI,CAAC,IAAI,CAC3CC,OAAO,CAAE,MAAe,CACxBC,aAAa,CAAE,QAAiB,CAChCC,WAAW,CAAE,GAAG,CAAA7C,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE8C,WAAW,GAAI,aAAa,EAAE,CACrDC,eAAe,CAAE,GAAG,CAAA/C,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEgD,eAAe,GAAI,MAAM,EAAE,CACtDC,MAAM,CAAE,GAAG,CAAAjD,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEkD,UAAU,GAAI,GAAG,YAAY,CAAAlD,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE8C,WAAW,GAAI,MAAM,EAAE,CAC7EK,QAAQ,CAAE,SACX,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,CACpBd,KAAK,CAAE,MAAM,CACbK,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvB/C,QAAQ,CAAE,UAAmB,CAC7B,mBAAmB,CAAE,CACpB8C,OAAO,CAAE,MACV,CAAC,CACD,wBAAwB,CAAE,CACzBA,OAAO,CAAE,MACV,CAAC,CACD,sBAAsB,CAAE,CACvBvL,OAAO,CAAE,GACV,CACD,CAAC,CAED,KAAM,CAAAiM,eAAe,CAAG,CACvBxD,QAAQ,CAAE,UAAmB,CAC7ByD,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,KAAK,CACVC,SAAS,CAAE,kBAAkB,CAC7BC,MAAM,CAAE,MAAM,CACdC,MAAM,CAAE,IACT,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAG,CAC1B9D,QAAQ,CAAE,UAAmB,CAC7B+D,KAAK,CAAE,OAAO,CACdL,GAAG,CAAE,KAAK,CACVC,SAAS,CAAE,kBAAkB,CAC7BlB,KAAK,CAAE,MAAM,CACbuB,MAAM,CAAE,MAAM,CACdtB,YAAY,CAAE,KAAK,CACnBI,OAAO,CAAE,MAAM,CACfmB,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBhB,eAAe,CAAE,SAAS,CAC1BU,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,CACVX,eAAe,CAAE,SAClB,CACD,CAAC,CAED,KAAM,CAAAiB,gBAAgB,CAAI3E,KAAoC,EAAK,CAClE/E,cAAc,CAAE+E,KAAK,CAAC4E,MAAM,CAAsB9D,KAAK,CAAC,CACzD,CAAC,CAED,KAAM,CAAA+D,uBAAuB,CAAGA,CAACC,WAAmB,CAAEjG,QAAgB,GAAK,CAC1EH,kBAAkB,CAACoG,WAAW,CAAEjG,QAAQ,CAAEkG,eAAe,CAAC,CAC1D3K,uBAAuB,CAAC0K,WAAW,CAAEjG,QAAQ,CAAEjE,mBAAmB,CAAC,CAEnE4D,kBAAkB,CAAC,CAClBsG,WAAW,CAAE,EAAE,CACfjG,QAAQ,CAAE,EAAE,CACZiC,KAAK,CAAE,IACR,CAAC,CAAC,CACH,CAAC,CACD,KAAM,CAACkE,cAAc,CAAEC,iBAAiB,CAAC,CAAGlQ,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAAAmQ,iBAAiB,CAAIC,GAAW,EAAK,CAC1C,GAAIJ,eAAe,CAACjE,KAAK,GAAK,UAAU,CAAE,CACzC,GAAI,CAACqE,GAAG,CAAE,CACT,MAAO,iBAAiB,CACzB,CACA,GAAI,CACH,GAAI,CAAAC,GAAG,CAACD,GAAG,CAAC,CACZ,MAAO,EAAE,CACV,CAAE,MAAOE,KAAK,CAAE,CACf,MAAO,aAAa,CACrB,CACD,CACA,MAAO,EAAE,CACV,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAGA,CAACR,WAAmB,CAAEjG,QAAgB,GAAK,CACrE,KAAM,CAAAwG,KAAK,CAAGH,iBAAiB,CAACxK,SAAS,CAAC,CAC1CuK,iBAAiB,CAACI,KAAK,CAAC,CAAE;AAE1B,GAAIA,KAAK,CAAE,CACV,OAAQ;AACT,CAEA,KAAM,CAAAE,kBAAkB,CAAG,CAAC/K,iBAAiB,EAAI,CAACA,iBAAiB,CAACgL,IAAI,CAAC,CAAC,CACvEC,gBAAgB,CAACC,KAAM;AAAA,CACvBlL,iBAAiB,CACpBC,oBAAoB,CAAC8K,kBAAkB,CAAC,CAExCtL,YAAY,CAAC6K,WAAW,CAAEjG,QAAQ,CAAE,OAAO,CAAE8G,UAAU,CAAC,CACxDjH,kBAAkB,CAACoG,WAAW,CAAEjG,QAAQ,CAAEkG,eAAe,CAAC,CAAE;AAC5D3K,uBAAuB,CAAC0K,WAAW,CAAEjG,QAAQ,CAAEjE,mBAAmB,CAAC,CACnEX,YAAY,CAAC6K,WAAW,CAAEjG,QAAQ,CAAE,MAAM,CAAE0G,kBAAkB,CAAC,CAC/DtL,YAAY,CAAC6K,WAAW,CAAEjG,QAAQ,CAAE,SAAS,CAAEkG,eAAe,CAAC,CAAE;AACjEvG,kBAAkB,CAAC,CAAEsG,WAAW,CAAE,EAAE,CAAEjG,QAAQ,CAAE,EAAE,CAAEiC,KAAK,CAAE,IAAK,CAAC,CAAC,CAClE/E,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CACD,KAAM,CAAA6J,eAAe,CAAIC,CAAM,EAAK,CACnC,KAAM,CAAAC,MAAM,CAAGD,CAAC,CAACjB,MAAM,CAAC9D,KAAK,CAC7BnG,YAAY,CAACmL,MAAM,CAAC,CAEpB;AACA,KAAM,CAAAT,KAAK,CAAGH,iBAAiB,CAACY,MAAM,CAAC,CACvCb,iBAAiB,CAACI,KAAK,CAAC,CAExBpE,kBAAkB,CAAC,CAClBH,KAAK,CAAEiE,eAAe,CAACjE,KAAK,CAC5BpG,SAAS,CAAEoL,MAAM,CACjB/E,GAAG,CAAE/F,WACN,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAyK,gBAAgB,CAAG3Q,OAAO,CAAC,IAAM,CACtC,KAAM,CAAAiR,MAAM,CAAGpH,oBAAoB,CAACF,eAAe,CAACqG,WAAW,CAAErG,eAAe,CAACI,QAAQ,CAAC,CAC1FpE,oBAAoB,CAACsL,MAAM,CAACL,KAAK,CAAC,CAClCxG,UAAU,CAAC6G,MAAM,CAACL,KAAK,CAAC,CACxBjI,SAAS,CAACsI,MAAM,CAACjF,KAAK,CAAC,CACvB,MAAO,CAAAiF,MAAM,CACd,CAAC,CAAE,CAACtH,eAAe,CAACqG,WAAW,CAAErG,eAAe,CAACI,QAAQ,CAAC,CAAC,CAE3D,KAAM,CAAA+B,mBAAmB,CAAG,CAC3B8C,eAAe,CAAE,SAAS,CAC1BF,WAAW,CAAE,SAAS,CACtBwC,KAAK,CAAE,SACR,CAAC,CAED,KAAM,CAACjB,eAAe,CAAE9D,kBAAkB,CAAC,CAAGlM,QAAQ,CAAgB,CACrE+L,KAAK,CAAE,OAAO,CAAE;AAChBpG,SAAS,CAAE,EAAE,CAAE;AACfqG,GAAG,CAAE,UAAY;AAClB,CAAC,CAAC,CACF,KAAM,CAAC4E,UAAU,CAAE3E,aAAa,CAAC,CAAGjM,QAAQ,CAAC6L,mBAAmB,CAAC,CACjE,KAAM,CAAAqF,cAAc,CAAGtH,oBAAoB,CAACF,eAAe,CAACqG,WAAW,CAAErG,eAAe,CAACI,QAAQ,CAAC,CAElG7J,SAAS,CAAC,IAAM,CACf,KAAM,CAAAkR,kBAAkB,CAAGA,CAACpB,WAAgB,CAAEjG,QAAa,GAAK,CAC/D,KAAM,CAAAoH,cAAc,CAAGtH,oBAAoB,CAACmG,WAAW,CAAEjG,QAAQ,CAAC,CAClE,GAAIoH,cAAc,CAAE,CACnBtL,YAAY,CAACsL,cAAc,CAACvL,SAAS,EAAI,EAAE,CAAC,CAC5CsG,aAAa,CAAC,CACb0C,eAAe,CAAEuC,cAAc,CAACE,OAAO,EAAIvF,mBAAmB,CAAC8C,eAAe,CAC9EF,WAAW,CAAEyC,cAAc,CAACzC,WAAW,EAAI5C,mBAAmB,CAAC4C,WAAW,CAC1EwC,KAAK,CAAEC,cAAc,CAACG,SAAS,EAAIxF,mBAAmB,CAACoF,KACxD,CAAC,CAAC,CACF/E,kBAAkB,CAAC,CAClBH,KAAK,CAAEmF,cAAc,CAAClB,eAAe,EAAI,OAAO,CAAE;AAClDrK,SAAS,CAAEuL,cAAc,CAACvL,SAAS,EAAIA,SAAS,CAAE;AAClDqG,GAAG,CAAE,UAAY;AAClB,CAAC,CAAC,CACH,CACD,CAAC,CACDmF,kBAAkB,CAACzH,eAAe,CAACqG,WAAW,CAAErG,eAAe,CAACI,QAAQ,CAAC,CAC1E,CAAC,CAAE,CAACJ,eAAe,CAACqG,WAAW,CAAErG,eAAe,CAACI,QAAQ,CAAC,CAAC,CAE3D;AACA,KAAM,CAAAwH,iBAAiB,CAAGA,CAACR,CAAM,CAAES,UAAe,GAAK,CACtD,KAAM,CAAAxF,KAAK,CAAG+E,CAAC,CAACjB,MAAM,CAAC9D,KAAK,CAC5BE,aAAa,CAAEuF,IAAI,GAAM,CACxB,GAAGA,IAAI,CACP,CAACD,UAAU,EAAGxF,KACf,CAAC,CAAC,CAAC,CACJ,CAAC,CACF9L,SAAS,CAAC,IAAM,CAEdiM,kBAAkB,CAAC,CAClBH,KAAK,CAAEiE,eAAe,CAACjE,KAAK,CAAE;AAC9BpG,SAAS,CAAEA,SAAS,CAAE;AACtBqG,GAAG,CAAE,UAAY;AAClB,CAAC,CAAC,CACJ,CAAC,CAAE,EAAE,CAAC,CACN;AACA/L,SAAS,CAAC,IAAM,CACf,KAAM,CAAAwR,iBAAiB,CAAGA,CAAA,GAAM,CAC/B,GAAIpI,aAAa,CAACqI,OAAO,CAAE,CAC1B;AACArI,aAAa,CAACqI,OAAO,CAAC9F,KAAK,CAAC6D,MAAM,CAAG,MAAM,CAC3C,KAAM,CAAAkC,aAAa,CAAGtI,aAAa,CAACqI,OAAO,CAACE,YAAY,CACxD,KAAM,CAAAC,eAAe,CAAG,GAAG,CAAE;AAC7B,KAAM,CAAAC,YAAY,CAAGH,aAAa,CAAGE,eAAe,CAGpD5E,iBAAiB,CAAC6E,YAAY,CAAC,CAE/B;AACA,GAAI5E,YAAY,CAACwE,OAAO,CAAE,CACzB;AACA,GAAIxE,YAAY,CAACwE,OAAO,CAACK,YAAY,CAAE,CACtC7E,YAAY,CAACwE,OAAO,CAACK,YAAY,CAAC,CAAC,CACpC,CACA;AACAC,UAAU,CAAC,IAAM,CAChB,GAAI9E,YAAY,CAACwE,OAAO,EAAIxE,YAAY,CAACwE,OAAO,CAACK,YAAY,CAAE,CAC9D7E,YAAY,CAACwE,OAAO,CAACK,YAAY,CAAC,CAAC,CACpC,CACD,CAAC,CAAE,EAAE,CAAC,CACP,CACD,CACD,CAAC,CAGDN,iBAAiB,CAAC,CAAC,CAGnB,KAAM,CAAAQ,QAAQ,CAAG,CAChBD,UAAU,CAACP,iBAAiB,CAAE,EAAE,CAAC,CACjCO,UAAU,CAACP,iBAAiB,CAAE,GAAG,CAAC,CAClCO,UAAU,CAACP,iBAAiB,CAAE,GAAG,CAAC,CAClCO,UAAU,CAACP,iBAAiB,CAAE,GAAG,CAAC,CAClC,CAGD,GAAI,CAAAS,cAAqC,CAAG,IAAI,CAChD,GAAI,CAAAC,gBAAyC,CAAG,IAAI,CAEpD,GAAI9I,aAAa,CAACqI,OAAO,EAAIU,MAAM,CAACC,cAAc,CAAE,CACnDH,cAAc,CAAG,GAAI,CAAAG,cAAc,CAAC,IAAM,CACzCL,UAAU,CAACP,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFS,cAAc,CAACI,OAAO,CAACjJ,aAAa,CAACqI,OAAO,CAAC,CAC9C,CAGA,GAAIrI,aAAa,CAACqI,OAAO,EAAIU,MAAM,CAACG,gBAAgB,CAAE,CACrDJ,gBAAgB,CAAG,GAAI,CAAAI,gBAAgB,CAAC,IAAM,CAC7CP,UAAU,CAACP,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFU,gBAAgB,CAACG,OAAO,CAACjJ,aAAa,CAACqI,OAAO,CAAE,CAC/Cc,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IAAI,CACbC,UAAU,CAAE,IAAI,CAChBC,eAAe,CAAE,CAAC,OAAO,CAAE,OAAO,CACnC,CAAC,CAAC,CACH,CAEA,MAAO,IAAM,CACZV,QAAQ,CAACW,OAAO,CAACC,YAAY,CAAC,CAC9B,GAAIX,cAAc,CAAE,CACnBA,cAAc,CAACY,UAAU,CAAC,CAAC,CAC5B,CACA,GAAIX,gBAAgB,CAAE,CACrBA,gBAAgB,CAACW,UAAU,CAAC,CAAC,CAC9B,CACD,CAAC,CACF,CAAC,CAAE,CAAEtJ,WAAW,CAAC,CAAC,CACjB,KAAM,CAAAuJ,mBAAmB,CAAIjC,CAAoB,EAAK,CACrD,KAAM,CAAAkC,CAAoB,CAAGlC,CAAC,CAACjB,MAAM,CAAC9D,KAA0B,CAAE;AAClEG,kBAAkB,CAAC,CAClBH,KAAK,CAAEiH,CAAC,CAAE;AACVrN,SAAS,CAAEA,SAAS,CACpBqG,GAAG,CAAE/F,WAAuC;AAC7C,CAAC,CAAC,CACH,CAAC,CACD;AACA;AACA;AACA;AACA;AACA;AAGA,mBACCvD,KAAA,QAAKuQ,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAC7B9J,cAAc,eACd5G,IAAA,CAACjC,GAAG,EACH4S,EAAE,CAAE,CACH1H,QAAQ,CAAE,OAAO,CACjB0D,GAAG,CAAE,CAAC,CACND,IAAI,CAAE,CAAC,CACPhB,KAAK,CAAE,OAAO,CACduB,MAAM,CAAE,OAAO,CACfd,eAAe,CAAE,oBAAoB,CACrCW,MAAM,CAAE,CACT,CAAE,CACF,CACD,CACAzK,gBAAgB,EAAIF,WAAW,eAC/BnC,IAAA,CAACH,UAAU,EACVsC,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BE,WAAW,CAAEA,WAAY,CACzB,CACD,cAEDpC,KAAA,CAACvC,MAAM,EACN8S,SAAS,CAAC,mBAAmB,CAC7B5L,IAAI,CAAEA,IAAK,CACX+L,OAAO,CAAEvI,WAAY,CACrBwI,UAAU,CAAE9I,YAAa,CACzB+I,UAAU,CAAE,CACX1H,KAAK,CAAEmC,UACR,CAAE,CACFC,QAAQ,CAAE,KAAM,CAChBuF,mBAAmB,CAAE,IAAK,CAAAL,QAAA,eAE1B1Q,IAAA,QAAKoJ,KAAK,CAAE,CAAC4H,YAAY,CAAC,KAAK,CAACjF,OAAO,CAAC,MAAM,CAAE,CAAA2E,QAAA,CAC9C5M,OAAO,eACP9D,IAAA,CAAChC,UAAU,EAACyS,SAAS,CAAC,eACrB;AAAA,CAAAC,QAAA,cAEP1Q,IAAA,CAACL,SAAS,EAACgR,EAAE,CAAE,CAAEM,IAAI,CAAE,CAAC,CAACxC,KAAK,CAAC,MAAM,CAAE,CAAE,CAAC,CACxB,CACX,CACE,CAAC,cACNzO,IAAA,CAACJ,gBAAgB,EAEjBqL,GAAG,CAAEP,YAAa,CACrBtB,KAAK,CAAE,CACN8H,SAAS,CAAE,OAAO,CAClB,IAAIjN,QAAQ,CACR,CACEkN,mBAAmB,CAAE,IAAAhQ,cAAA,CAAGiI,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEwC,MAAM,UAAAzK,cAAA,UAAAA,cAAA,CAAI,CAAC,IAAI,CAC9CiQ,oBAAoB,CAAE,IAAAhQ,cAAA,CAAGgI,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEwC,MAAM,UAAAxK,cAAA,UAAAA,cAAA,CAAI,CAAC,IAAI,CAC/CiQ,sBAAsB,CAAE,KAAK,CAC7BC,uBAAuB,CAAE,KAC3B,CAAC,CACD,CACE3F,YAAY,CAAE,IAAAtK,cAAA,CAAG+H,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEwC,MAAM,UAAAvK,cAAA,UAAAA,cAAA,CAAI,CAAC,IACrC,CAAC,CACP,CAAE,CAEEkQ,OAAO,CAAE,CACRC,eAAe,CAAE,KAAK,CACtBC,eAAe,CAAE,IAAI,CACrBC,gBAAgB,CAAE,KAAK,CACvBC,WAAW,CAAE,IAAI,CACjBC,kBAAkB,CAAE,EAAE,CACtBC,kBAAkB,CAAE,IAAI,CACxBC,mBAAmB,CAAE,CACtB,CAAE,CAAApB,QAAA,cAEF1Q,IAAA,CAACpC,aAAa,EACb+S,EAAE,CAAE,CACHoB,OAAO,CAAE,CAAC,CACVxF,QAAQ,CAAE,QAAQ,CAClBR,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvB/C,QAAQ,CAAE,UACX,CAAE,CACDgC,GAAG,CAAEpE,aAAc,CAEpBgC,EAAE,CAAC,aAAa,CAAA6H,QAAA,cA0BhBxQ,KAAA,CAACnC,GAAG,EAAC4S,EAAE,CAAE,CAACoB,OAAO,CAAE,GAAG,CAAA3I,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE4I,OAAO,GAAI,EAAE,IAC/C,CAAE,CAAAtB,QAAA,EACKzL,QAAQ,CAACgN,GAAG,CAAC,CAACrH,OAAO,CAAEzB,KAAK,gBAC5BjJ,KAAA,CAACnC,GAAG,EAEH4S,EAAE,CAAE,CACH,GAAGnE,YAAY,CACfS,MAAM,CAAE,MAAM,CACd,SAAS,CAAE,CACViF,cAAc,CAAE/I,KAAK,GAAK,CAAC,CAAG,KAAK,CAAG,KAAK,CAC3CgJ,cAAc,CAAEhJ,KAAK,GAAK,CAAC,CAAG,qBAAqB,CAAG,aAAa,CACnEiJ,cAAc,CAAEjJ,KAAK,GAAK,CAAC,CAAG,QAAQ,CAAG,MAC1C,CACD,CAAE,CACFkJ,SAAS,MACTC,WAAW,CAAEA,CAAA,GAAM1I,eAAe,CAACT,KAAK,CAAE,CAC1CoJ,WAAW,CAAEA,CAAA,GAAM1I,eAAe,CAACV,KAAK,CAAE,CAC1CqJ,SAAS,CAAEvI,aAAc,CAAAyG,QAAA,eAEzB1Q,IAAA,CAAChC,UAAU,EACVyS,SAAS,CAAC,WAAW,CACrBE,EAAE,CAAElE,eAAgB,CAAAiE,QAAA,cAEpB1Q,IAAA,CAACd,iBAAiB,EACjBuT,QAAQ,CAAC,OAAO,CAChB9B,EAAE,CAAE,CAAElC,KAAK,CAAE,SAAU,CAAE,CACzB,CAAC,CACS,CAAC,CAEZ9D,aAAa,CAACC,OAAO,CAAEzB,KAAK,CAAC,CAC7BA,KAAK,GAAK,CAAC,eACXnJ,IAAA,CAACtB,OAAO,EACPyP,KAAK,CACJpI,eAAe,CAAC,MAAM,CAAC,EAAIA,eAAe,CAAC,QAAQ,CAAC,EAAIA,eAAe,CAAC,OAAO,CAAC,CAC7EvD,SAAS,CAAC,6CAA6C,CAAC,CACxDA,SAAS,CAAC,aAAa,CAC1B,CAAAkO,QAAA,cAED1Q,IAAA,CAAChC,UAAU,EACVyS,SAAS,CAAC,aAAa,CACvBiC,OAAO,CAAElK,kBAAmB,CAC5BmI,EAAE,CAAE,CACHxE,eAAe,CAAE,SAAS,CAC1B,SAAS,CAAE,CACVA,eAAe,CAAE,SAClB,CAAC,CACDR,YAAY,CAAE,KAAK,CACnBoG,OAAO,CAAE,gBAAgB,CACzB9I,QAAQ,CAAE,UAAU,CACpB0D,GAAG,CAAE,MAAM,CACXD,IAAI,CAAE,KAAK,CACXE,SAAS,CAAE,uBAAuB,CAClCpM,OAAO,CAAE,GAAG,CACZqM,MAAM,CAAE9G,eAAe,CAAC,MAAM,CAAC,EAAIA,eAAe,CAAC,QAAQ,CAAC,EAAIA,eAAe,CAAC,OAAO,CAAC,CACrF,aAAa,CACb,SACJ,CAAE,CACF4M,QAAQ,CAAE5M,eAAe,CAAC,MAAM,CAAC,EAAIA,eAAe,CAAC,QAAQ,CAAC,EAAIA,eAAe,CAAC,OAAO,CAAE,CAAA2K,QAAA,cAE3F1Q,IAAA,CAACf,OAAO,EACPwT,QAAQ,CAAC,OAAO,CAChB9B,EAAE,CAAE,CAAElC,KAAK,CAAE,MAAO,CAAE,CACtB,CAAC,CACS,CAAC,CACL,CACT,GA7DItF,KA8DD,CACL,CAAC,CAEDyJ,OAAO,CAAC1L,eAAe,CAACqC,KAAK,CAAC,cAC9BvJ,IAAA,QACC6I,EAAE,CAAC,mBAAmB,CACtB4H,SAAS,CAAC,iCAAiC,CAAAC,QAAA,cAE3CxQ,KAAA,QAAKuQ,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC7BxQ,KAAA,QAAKuQ,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC1Q,IAAA,QAAKyQ,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAElO,SAAS,CAAC,YAAY,CAAC,CAAM,CAAC,cAC7DxC,IAAA,CAAChC,UAAU,EACV6U,IAAI,CAAC,OAAO,CACZ,aAAW,OAAO,CAClBH,OAAO,CAAEA,CAAA,GAAMpF,uBAAuB,CAACpG,eAAe,CAACqG,WAAW,CAAErG,eAAe,CAACI,QAAQ,CAAE,CAAAoJ,QAAA,cAE9F1Q,IAAA,CAACL,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cACNK,IAAA,QAAKyQ,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC5CxQ,KAAA,QAAKuQ,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC9BxQ,KAAA,CAAC9B,WAAW,EACX0U,SAAS,MACTnC,EAAE,CAAE,CAAEoC,YAAY,CAAE,MAAO,CAAE,CAAArC,QAAA,eAE3B1Q,IAAA,CAAC9B,UAAU,EAACyS,EAAE,CAAE,CAAE8B,QAAQ,CAAE,MAAM,CAAEO,UAAU,CAAE,MAAM,CAAEC,EAAE,CAAE,KAAM,CAAE,CAAAvC,QAAA,CAAElO,SAAS,CAAC,aAAa,CAAC,CAAa,CAAC,cAC9GxC,IAAA,CAACzB,SAAS,EACTgL,KAAK,CAAEtG,iBAAkB,CACzB4P,IAAI,CAAC,OAAO,CACXlC,EAAE,CAAE,CACRuC,EAAE,CAAE,KAAK,CACT7G,MAAM,CAAE,gBAAgB,CACxBV,YAAY,CAAE,KAAK,CAE1B,0BAA0B,CAAE,CAC1BsB,MAAM,CAAE,MAAM,CACd,0CAA0C,CAAE,CAC1CZ,MAAM,CAAE,iBACV,CAAC,CACD,gDAAgD,CAAE,CAChDA,MAAM,CAAE,iBACV,CACF,CAAC,CACD,oCAAoC,CAAE,CACpCA,MAAM,CAAE,iBACV,CAEI,CAAE,CACM8G,WAAW,CAAE3Q,SAAS,CAAC,aAAa,CAAE,CACxC4Q,QAAQ,CAAG9E,CAAC,EAAKpL,oBAAoB,CAACoL,CAAC,CAACjB,MAAM,CAAC9D,KAAK,CAAE,CACtD,CAAC,cACAvJ,IAAA,CAAC9B,UAAU,EAACyS,EAAE,CAAE,CAAE8B,QAAQ,CAAE,MAAM,CAAEO,UAAU,CAAE,MAAM,CAAEE,EAAE,CAAE,KAAM,CAAE,CAAAxC,QAAA,CAAElO,SAAS,CAAC,eAAe,CAAC,CAAa,CAAC,cAChHtC,KAAA,CAAC7B,MAAM,EACNkL,KAAK,CAAEiE,eAAe,CAACjE,KAAM,CAC7B8J,YAAY,CAAC,OAAO,CACpBD,QAAQ,CAAE7C,mBAAoB,CAC5BI,EAAE,CAAE,CACTuC,EAAE,CAAE,KAAK,CACT7G,MAAM,CAAE,gBAAgB,CACxBV,YAAY,CAAE,KAAK,CACnB2H,SAAS,CAAE,MAAM,CACjB,qBAAqB,CAAE,CACxBvB,OAAO,CAAE,KACV,CAAC,CAEN,0BAA0B,CAAE,CAC1B9E,MAAM,CAAE,MAAM,CACd,0CAA0C,CAAE,CAC1CZ,MAAM,CAAE,iBACV,CAAC,CACD,gDAAgD,CAAE,CAChDA,MAAM,CAAE,iBACV,CACF,CAAC,CACD,oCAAoC,CAAE,CACpCA,MAAM,CAAE,iBACV,CAEI,CAAE,CAAAqE,QAAA,eAEM1Q,IAAA,CAAC1B,QAAQ,EAACiL,KAAK,CAAC,OAAO,CAAAmH,QAAA,CAAElO,SAAS,CAAC,OAAO,CAAC,CAAW,CAAC,cACvDxC,IAAA,CAAC1B,QAAQ,EAACiL,KAAK,CAAC,UAAU,CAAAmH,QAAA,CAAElO,SAAS,CAAC,UAAU,CAAC,CAAW,CAAC,cAC7DxC,IAAA,CAAC1B,QAAQ,EAACiL,KAAK,CAAC,UAAU,CAAAmH,QAAA,CAAElO,SAAS,CAAC,UAAU,CAAC,CAAW,CAAC,cAC7DxC,IAAA,CAAC1B,QAAQ,EAACiL,KAAK,CAAC,MAAM,CAAAmH,QAAA,CAAElO,SAAS,CAAC,MAAM,CAAC,CAAW,CAAC,cACrDxC,IAAA,CAAC1B,QAAQ,EAACiL,KAAK,CAAC,SAAS,CAAAmH,QAAA,CAAElO,SAAS,CAAC,SAAS,CAAC,CAAW,CAAC,EACtD,CAAC,CACRgL,eAAe,CAACjE,KAAK,GAAK,UAAU,cACpCrJ,KAAA,CAAAE,SAAA,EAAAsQ,QAAA,eACG1Q,IAAA,CAAC9B,UAAU,EAACyS,EAAE,CAAE,CAAE8B,QAAQ,CAAE,MAAM,CAAEO,UAAU,CAAE,MAAM,CAAEC,EAAE,CAAE,KAAM,CAAE,CAAAvC,QAAA,CAAElO,SAAS,CAAC,WAAW,CAAC,CAAa,CAAC,cAC5GxC,IAAA,CAACzB,SAAS,EACTgL,KAAK,CAAEpG,SAAU,CACjB0P,IAAI,CAAC,OAAO,CACZM,WAAW,CAAC,mBAAmB,CAC/BC,QAAQ,CAAG9E,CAAC,EAAK,CAChB,KAAM,CAAAC,MAAM,CAAGD,CAAC,CAACjB,MAAM,CAAC9D,KAAK,CAC7BnG,YAAY,CAACmL,MAAM,CAAC,CAAE;AACtBF,eAAe,CAACC,CAAC,CAAC,CAAE;AACpB,CAAE,CACFR,KAAK,CAAE,CAAC,CAACL,cAAe,CACxB8F,UAAU,CAAE9F,cAAe,CAC5B,CAAC,cAEFzN,IAAA,CAACvB,iBAAiB,EACjB8K,KAAK,CAAE9F,WAAY,CACnB2P,QAAQ,CAAEhG,gBAAiB,CAC3BoG,SAAS,MACP,aAAYhR,SAAS,CAAC,aAAa,CAAE,CACvCmO,EAAE,CAAE,CACH8C,GAAG,CAAE,KAAK,CACVC,OAAO,CAAE,KAAK,CACdzG,MAAM,CAAE,MACT,CAAE,CAAAyD,QAAA,CAED,CAAC,SAAS,CAAE,UAAU,CAAC,CAACuB,GAAG,CAAEzI,GAAG,EAAK,CACrC,mBACCxJ,IAAA,CAACxB,YAAY,EACZ+K,KAAK,CAAEC,GAAI,CACX,aAAW,SAAS,CACpBmH,EAAE,CAAE,CACHtE,MAAM,CAAE,mBAAmB,CAC3BsH,aAAa,CAAE,YAAY,CAC3BlF,KAAK,CAAE,MAAM,CACb9C,YAAY,CAAE,KAAK,CACnBiI,IAAI,CAAE,CAAC,CACP7B,OAAO,CAAE,cAAc,CAEvB,gBAAgB,CAAE,CACjB5F,eAAe,CAAE,qBAAqB,CACtCsC,KAAK,CAAE,MAAM,CACbpC,MAAM,CAAE,mBACT,CAAC,CACD,SAAS,CAAE,CACVF,eAAe,CAAE,SAClB,CAAC,CACD,cAAc,CAAE,CACf0H,UAAU,CAAE,0CAA4C;AACzD,CACD,CAAE,CAAAnD,QAAA,CAEDlH,GAAG,CACS,CAAC,CAEjB,CAAC,CAAC,CACgB,CAAC,EACnB,CAAC,CACA,IAAI,EAyCI,CAAC,cAGdtJ,KAAA,CAACnC,GAAG,EACH0S,SAAS,CAAC,mBAAmB,CAC7BE,EAAE,CAAE,CAAEhF,YAAY,CAAE,KAAM,CAAE,CAAA+E,QAAA,eAE1B1Q,IAAA,CAAC9B,UAAU,EAACuS,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAElO,SAAS,CAAC,YAAY,CAAC,CAAa,CAAC,cACpFxC,IAAA,UACCmF,IAAI,CAAC,OAAO,CACZoE,KAAK,CAAE6E,UAAU,CAACjC,eAAgB,CAClCiH,QAAQ,CAAG9E,CAAC,EAAKQ,iBAAiB,CAACR,CAAC,CAAE,iBAAiB,CAAE,CACzDmC,SAAS,CAAC,mBAAmB,CAC7B,CAAC,EACE,CAAC,cAENvQ,KAAA,CAACnC,GAAG,EACH0S,SAAS,CAAC,mBAAmB,CAC7BE,EAAE,CAAE,CAAEhF,YAAY,CAAE,KAAM,CAAE,CAAA+E,QAAA,eAE1B1Q,IAAA,CAAC9B,UAAU,EAACuS,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAElO,SAAS,CAAC,QAAQ,CAAC,CAAa,CAAC,cAChFxC,IAAA,UACCmF,IAAI,CAAC,OAAO,CACZoE,KAAK,CAAE6E,UAAU,CAACnC,WAAY,CAC9BmH,QAAQ,CAAG9E,CAAC,EAAKQ,iBAAiB,CAACR,CAAC,CAAE,aAAa,CAAE,CACrDmC,SAAS,CAAC,mBAAmB,CAC7B,CAAC,EACE,CAAC,cAENvQ,KAAA,CAACnC,GAAG,EACH0S,SAAS,CAAC,mBAAmB,CAC7BE,EAAE,CAAE,CAAEhF,YAAY,CAAE,KAAM,CAAE,CAAA+E,QAAA,eAE1B1Q,IAAA,CAAC9B,UAAU,EAACuS,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAElO,SAAS,CAAC,MAAM,CAAC,CAAa,CAAC,cAC9ExC,IAAA,UACCmF,IAAI,CAAC,OAAO,CACZoE,KAAK,CAAE6E,UAAU,CAACK,KAAM,CACxB2E,QAAQ,CAAG9E,CAAC,EAAKQ,iBAAiB,CAACR,CAAC,CAAE,OAAO,CAAE,CAC/CmC,SAAS,CAAC,mBAAmB,CAC7B,CAAC,EACE,CAAC,EACD,CAAC,CACD,CAAC,cAEPzQ,IAAA,QAAKyQ,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cAClC1Q,IAAA,CAAC7B,MAAM,EACN2V,OAAO,CAAC,WAAW,CACnBpB,OAAO,CAAEA,CAAA,GAAM3E,kBAAkB,CAAC7G,eAAe,CAACqG,WAAW,CAAErG,eAAe,CAACI,QAAQ,CAAE,CACzFmJ,SAAS,CAAC,WAAW,CAAAC,QAAA,CAEnBlO,SAAS,CAAC,OAAO,CAAC,CACb,CAAC,CACL,CAAC,EACF,CAAC,CACF,CAAC,CACH,IAAI,EACJ,CAAC,CAGS,CAAC,EArXZ,aAAagI,cAAc,EAsXd,CAAC,cACnBxK,IAAA,CAACjC,GAAG,EAAC4S,EAAE,CAAE,CAAGhF,YAAY,CAAE,IAAArK,cAAA,CAAG8H,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEwC,MAAM,UAAAtK,cAAA,UAAAA,cAAA,CAAI,CAAC,IACnD,CAAE,CAAAoP,QAAA,CACGzM,QAAQ,GACPD,cAAc,GAAK,CAAC,EAAIA,cAAc,GAAK,EAAE,cAC7ChE,IAAA,CAAC+T,WAAW,EACXC,UAAU,CAAEhN,WAAY,CACxB9C,KAAK,CAAEA,KAAK,CAACgE,MAAO,CACpBzD,aAAa,CAAIA,aAAc,CAC/B,CAAC,CAEDT,cAAc,GAAK,CAAC,cACrBhE,IAAA,QAAA0Q,QAAA,cACC1Q,IAAA,CAACrB,cAAc,EACdmV,OAAO,CAAC,aAAa,CACpBnD,EAAE,CAAE,CACH1D,MAAM,CAAE,KAAK,CACbtB,YAAY,CAAE,MAAM,CACpBsI,MAAM,CAAE,UAAU,CAClB9H,eAAe,CAAE7L,SAAS,CAACmE,aAAa,CAAE,IAAI,CAAC,CAC/C,0BAA0B,CAAE,CAC7B0H,eAAe,CAAE1H,aAAe;AAC/B,CAAE,CAAE,CACN8E,KAAK,CAAGvC,WAAW,CAAG9C,KAAK,CAACgE,MAAM,CAAI,GAAI,CAE1C,CAAC,CACE,CAAC,CACHlE,cAAc,GAAK,CAAC,cACvBhE,IAAA,QAAKoJ,KAAK,CAAE,CAAE2I,OAAO,CAAE,KAAM,CAAE,CAAArB,QAAA,cAC9B1Q,IAAA,CAACkU,iBAAiB,EAClBF,UAAU,CAAEhN,WAAY,CACxB9C,KAAK,CAAEA,KAAK,CAACgE,MAAO,CACpBzD,aAAa,CAAEA,aAAc,CAE7B,CAAC,CACG,CAAC,CACJT,cAAc,GAAK,CAAC,cACtBhE,IAAA,CAACnB,WAAW,EACX,aAAW,YAAY,CACvB8R,EAAE,CAAE,CAAEoB,OAAO,CAAE,KAAM,CAAE,CAAArB,QAAA,cAEvBxQ,KAAA,CAAChC,UAAU,EAAAwS,QAAA,EACV,OACK,CAAC1J,WAAW,CAAC,MAAI,CAAC9C,KAAK,CAACgE,MAAM,EACxB,CAAC,CACD,CAAC,CACT,IAAI,CAAC,CACN,CAAC,CACNnD,QAAQ,eACR/E,IAAA,CAAC/B,OAAO,EACDwS,SAAS,CAAC,eAAe,CAC/B5L,IAAI,CAAE+N,OAAO,CAAC7N,QAAQ,CAAE,CACxBA,QAAQ,CAAEA,QAAS,CACnB6L,OAAO,CAAEjI,kBAAmB,CAC5BwL,YAAY,CAAE,CACbC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACb,CAAE,CACFC,eAAe,CAAE,CAChBF,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,QACb,CAAE,CACFE,SAAS,CAAE,CACVC,KAAK,CAAE,CACN7D,EAAE,CAAE,CACHoB,OAAO,CAAE,MAAM,CACfhG,OAAO,CAAE,MAAM,CACf0H,GAAG,CAAE,MAAM,CACX/H,KAAK,CAAE,MAAM,CACboB,MAAM,CAAE,IACT,CACD,CACD,CAAE,CACF6D,EAAE,CAAE,CACH1H,QAAQ,CAAE,UAAU,CACpB;AACA;AACA2D,SAAS,CAAE,MACZ,CAAE,CAAA8D,QAAA,cAEFxQ,KAAA,CAACnC,GAAG,EACHgO,OAAO,CAAC,MAAM,CACdC,aAAa,CAAC,KAAK,CACnByH,GAAG,CAAC,MAAM,CAAA/C,QAAA,eAGV1Q,IAAA,CAACtB,OAAO,EACPyP,KAAK,CAAEpI,eAAe,CAAC,MAAM,CAAC,CAAGvD,SAAS,CAAC,+CAA+C,CAAC,CAAG,EAAG,CACjGiS,WAAW,CAAE,CACZ9D,EAAE,CAAE,CACH7D,MAAM,CAAE,IACT,CACD,CAAE,CAAA4D,QAAA,cAEFxQ,KAAA,CAACnC,GAAG,EACHgO,OAAO,CAAC,MAAM,CACdC,aAAa,CAAC,QAAQ,CACtBkB,UAAU,CAAC,QAAQ,CACnByD,EAAE,CAAE,CACH9D,MAAM,CAAE9G,eAAe,CAAC,MAAM,CAAC,CAAG,aAAa,CAAG,SAAS,CAC3DvF,OAAO,CAAEuF,eAAe,CAAC,MAAM,CAAC,CAAG,GAAG,CAAG,CAAC,CAC1C2O,GAAG,CAAE,CACJjC,QAAQ,CAAE,iBACX,CACD,CAAE,CACFC,OAAO,CAAEA,CAAA,GAAM,CAAC3M,eAAe,CAAC,MAAM,CAAC,EAAI6C,gBAAgB,CAAC,MAAM,CAAE,CAAA8H,QAAA,eAEpE1Q,IAAA,CAACZ,UAAU,GAAE,CAAC,cACdY,IAAA,CAAC9B,UAAU,EAAC4V,OAAO,CAAC,SAAS,CAC5BnD,EAAE,CAAE,CACH8B,QAAQ,CAAE,iBACX,CAAE,CAAA/B,QAAA,CACDlO,SAAS,CAAC,WAAW,CAAC,CAAa,CAAC,EAClC,CAAC,CACE,CAAC,cAGVxC,IAAA,CAACtB,OAAO,EACPyP,KAAK,CAAEpI,eAAe,CAAC,QAAQ,CAAC,CAAGvD,SAAS,CAAC,4CAA4C,CAAC,CAAG,EAAG,CAChGiS,WAAW,CAAE,CACZ9D,EAAE,CAAE,CACH7D,MAAM,CAAE,IACT,CACD,CAAE,CAAA4D,QAAA,cAEFxQ,KAAA,CAACnC,GAAG,EACHgO,OAAO,CAAC,MAAM,CACdC,aAAa,CAAC,QAAQ,CACtBkB,UAAU,CAAC,QAAQ,CACnByD,EAAE,CAAE,CACH9D,MAAM,CAAE9G,eAAe,CAAC,QAAQ,CAAC,CAAG,aAAa,CAAG,SAAS,CAC7DvF,OAAO,CAAEuF,eAAe,CAAC,QAAQ,CAAC,CAAG,GAAG,CAAG,CAAC,CAC5C2O,GAAG,CAAE,CACJjC,QAAQ,CAAE,iBACX,CACD,CAAE,CACFC,OAAO,CAAEA,CAAA,GAAM,CAAC3M,eAAe,CAAC,QAAQ,CAAC,EAAI6C,gBAAgB,CAAC,QAAQ,CAAE,CAAA8H,QAAA,eAExE1Q,IAAA,CAACT,IAAI,GAAE,CAAC,cACRS,IAAA,CAAC9B,UAAU,EAAC4V,OAAO,CAAC,SAAS,CAC5BnD,EAAE,CAAE,CACH8B,QAAQ,CAAE,iBACX,CAAE,CAAA/B,QAAA,CACDlO,SAAS,CAAC,QAAQ,CAAC,CAAa,CAAC,EAC/B,CAAC,CACE,CAAC,cAGVxC,IAAA,CAACtB,OAAO,EACPyP,KAAK,CAAEpI,eAAe,CAAC,OAAO,CAAC,CAAGvD,SAAS,CAAC,2CAA2C,CAAC,CAAG,EAAG,CAC9FiS,WAAW,CAAE,CACZ9D,EAAE,CAAE,CACH7D,MAAM,CAAE,IACT,CACD,CAAE,CAAA4D,QAAA,cAEFxQ,KAAA,CAACnC,GAAG,EACHgO,OAAO,CAAC,MAAM,CACdC,aAAa,CAAC,QAAQ,CACtBkB,UAAU,CAAC,QAAQ,CACnByD,EAAE,CAAE,CACH9D,MAAM,CAAE9G,eAAe,CAAC,OAAO,CAAC,CAAG,aAAa,CAAG,SAAS,CAC5DvF,OAAO,CAAEuF,eAAe,CAAC,OAAO,CAAC,CAAG,GAAG,CAAG,CAAC,CAC3C2O,GAAG,CAAE,CACJjC,QAAQ,CAAE,iBACX,CACD,CAAE,CACFC,OAAO,CAAEA,CAAA,GAAM,CAAC3M,eAAe,CAAC,OAAO,CAAC,EAAI6C,gBAAgB,CAAC,OAAO,CAAE,CAAA8H,QAAA,eAEtE1Q,IAAA,CAACb,KAAK,GAAE,CAAC,cACTa,IAAA,CAAC9B,UAAU,EAAC4V,OAAO,CAAC,SAAS,CAC5BnD,EAAE,CAAE,CACH8B,QAAQ,CAAE,iBACX,CAAE,CAAA/B,QAAA,CACDlO,SAAS,CAAC,OAAO,CAAC,CAAa,CAAC,EAC9B,CAAC,CACE,CAAC,cACVxC,IAAA,CAACtB,OAAO,EACPyP,KAAK,CAAE3L,SAAS,CAAC,aAAa,CAAE,CAChCiS,WAAW,CAAE,CACZ9D,EAAE,CAAE,CACH7D,MAAM,CAAE,IACT,CACD,CAAE,CAAA4D,QAAA,cAEF1Q,IAAA,SAAA0Q,QAAA,cACCxQ,KAAA,CAACnC,GAAG,EACHgO,OAAO,CAAC,MAAM,CACdC,aAAa,CAAC,QAAQ,CACtBkB,UAAU,CAAC,QAAQ,CACnByD,EAAE,CAAE,CACH9D,MAAM,CAAE,SAAS,CACjBrM,OAAO,CAAE,GAAG,CACZkU,GAAG,CAAE,CACJjC,QAAQ,CAAE,iBACX,CACD,CACA;AAAA,CAAA/B,QAAA,eAEA1Q,IAAA,CAACV,YAAY,GAAE,CAAC,cAChBU,IAAA,CAAC9B,UAAU,EAAC4V,OAAO,CAAC,SAAS,CAC5BnD,EAAE,CAAE,CACH8B,QAAQ,CAAE,iBACX,CAAE,CAAA/B,QAAA,CACDlO,SAAS,CAAC,OAAO,CAAC,CAAa,CAAC,EAC9B,CAAC,CACD,CAAC,CACC,CAAC,cAWVxC,IAAA,CAACtB,OAAO,EACPyP,KAAK,CAAE3L,SAAS,CAAC,aAAa,CAAE,CAChCiS,WAAW,CAAE,CACZ9D,EAAE,CAAE,CACH7D,MAAM,CAAE,IACT,CACD,CAAE,CAAA4D,QAAA,cAEF1Q,IAAA,SAAA0Q,QAAA,cACCxQ,KAAA,CAACnC,GAAG,EACHgO,OAAO,CAAC,MAAM,CACdC,aAAa,CAAC,QAAQ,CACtBkB,UAAU,CAAC,QAAQ,CACnByD,EAAE,CAAE,CACH9D,MAAM,CAAE,SAAS,CACjBrM,OAAO,CAAE,GAAG,CACZkU,GAAG,CAAE,CACJjC,QAAQ,CAAE,iBACX,CACD,CAAE,CACFC,OAAO,CAAEA,CAAA,GAAM9J,gBAAgB,CAAC,MAAM,CAAE,CAAA8H,QAAA,eAExC1Q,IAAA,CAACX,IAAI,GAAE,CAAC,cACRW,IAAA,CAAC9B,UAAU,EAAC4V,OAAO,CAAC,SAAS,CAC5BnD,EAAE,CAAE,CACH8B,QAAQ,CAAE,iBACX,CAAE,CAAA/B,QAAA,CACDlO,SAAS,CAAC,MAAM,CAAC,CAAa,CAAC,EAC7B,CAAC,CACD,CAAC,CACC,CAAC,EACN,CAAC,CACE,CACT,EAEM,CAAC,EACL,CAAC,CAER,CAAC,CACD,cAAe,CAAAzB,UAAU,CACzB,KAAM,CAAAgT,WAAW,CAAGY,KAAA,EAAoG,IAAnG,CAAEzQ,KAAK,CAAE8P,UAAU,CAAEvP,aAAwE,CAAC,CAAAkQ,KAAA,CAClH,mBACC3U,IAAA,CAACpB,aAAa,EACbkV,OAAO,CAAC,MAAM,CACd5P,KAAK,CAAEA,KAAM,CACb+E,QAAQ,CAAC,QAAQ,CACjB+K,UAAU,CAAEA,UAAU,CAAG,CAAE,CAC3BrD,EAAE,CAAE,CAAEiE,QAAQ,CAAE,CAAC,CAAE7I,OAAO,CAAE,MAAM,CAAEoB,cAAc,CAAE,QAAQ,CAAC0H,UAAU,CAAC,SAAS,CAAC,+BAA+B,CAAE,CAClH1I,eAAe,CAAE1H,aAAe;AAC/B,CAAE,CAAE,CACNqQ,UAAU,cAAE9U,IAAA,CAAAI,SAAA,GAAI,CAAE,CAClB2U,UAAU,cAAE/U,IAAA,CAAAI,SAAA,GAAI,CAAE,CAElB,CAAC,CAEJ,CAAC,CACD,KAAM,CAAA8T,iBAAiB,CAAGc,KAAA,EAAmG,IAAlG,CAAE9Q,KAAK,CAAE8P,UAAU,CAACvP,aAAwE,CAAC,CAAAuQ,KAAA,CACvH,mBACChV,IAAA,CAACjC,GAAG,EAAC4S,EAAE,CAAE,CAAEiE,QAAQ,CAAE,CAAE,CAAE,CAAAlE,QAAA,cAEvB1Q,IAAA,CAACjC,GAAG,EACL4S,EAAE,CAAE,CACF5E,OAAO,CAAE,MAAM,CACfoB,cAAc,CAAE,QAAQ,CACxBsG,GAAG,CAAE,KAAO;AACd;AACA,CAAE,CAAA/C,QAAA,CAEDuE,KAAK,CAACC,IAAI,CAAC,CAAEhN,MAAM,CAAEhE,KAAM,CAAC,CAAC,CAAC+N,GAAG,CAAC,CAACkD,CAAC,CAAEhM,KAAK,gBAC1CnJ,IAAA,QAEDoJ,KAAK,CAAE,CACLsC,KAAK,CAAE,MAAM,CACbuB,MAAM,CAAE,KAAK,CACbd,eAAe,CAAEhD,KAAK,GAAK6K,UAAU,CAAG,CAAC,CAAGvP,aAAa,CAAGnE,SAAS,CAACmE,aAAa,CAAE,IAAI,CAAC,CAAE;AAC5FkH,YAAY,CAAE,OAChB,CAAE,EANGxC,KAOH,CACF,CAAC,CACI,CAAC,CACH,CAAC,CAER,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}