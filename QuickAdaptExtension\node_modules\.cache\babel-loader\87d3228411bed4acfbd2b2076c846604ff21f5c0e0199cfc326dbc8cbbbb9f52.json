{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideDesign\\\\Design.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\n// import Draggable from \"react-draggable\";\nimport { Button, Box, Typography, IconButton, Select, MenuItem } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport DesignServicesIcon from \"@mui/icons-material/DesignServices\";\nimport CanvasSettings from \"./CanvasSettings\";\nimport Elementssettings from \"../guideSetting/ElementsSettings\";\nimport OverlaySettings from \"./Overlay\";\nimport CustomCSS from \"./CustomCss\";\nimport PageInteractions from \"../guideBanners/selectedpopupfields/PageInteraction\";\nimport AnimationSettings from \"./Animation\";\nimport \"./Canvas.module.css\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport HotspotSettings from \"../hotspot/HotspotSettings\";\nimport { animation, elements, Hotspoticon, overlay, Reselect } from \"../../assets/icons/icons\";\nimport TooltipCanvasSettings from \"../Tooltips/designFields/TooltipCanvasSettings\";\nimport { KeyboardTabSharp } from \"@mui/icons-material\";\nimport { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\nimport userSession from \"../../store/userSession\";\nimport ChecklistCanvasSettings from \"../checklist/ChecklistCanvasSettings\";\nimport LauncherSettings from \"../checklist/LauncherSettings\";\nimport Checkpoints from \"../checklist/Chekpoints\";\nimport TitleSubTitle from \"../checklist/TitleSubTitle\";\nimport '../../styles/rtl_styles.scss';\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DesignMenu = ({\n  width,\n  height,\n  overlays,\n  setOverLays,\n  backgroundC,\n  setBackgroundC,\n  Bposition,\n  setBposition,\n  bpadding,\n  setbPadding,\n  Bbordercolor,\n  setBBorderColor,\n  BborderSize,\n  setBBorderSize,\n  zindeex,\n  setZindeex,\n  setDesignPopup,\n  selectedTemplate,\n  designPopup,\n  initialGuideData,\n  updatedGuideData,\n  handleSaveGuide,\n  resetHeightofBanner\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  // State to control the visibility of CanvasSettings\n  const [showCanvasSettings, setShowCanvasSettings] = useState(false);\n  const [showChecklistCanvasSettings, setShowChecklistCanvasSettings] = useState(false);\n  //const [showTooltipCanvasSettings, setShowTooltipCanvasSettings] = useState(false);\n  const [showOverlay, setOverlaySettings] = useState(false);\n  const [showElementsSettings, setShowElementsSettings] = useState(false);\n  const [showAnimation, setshowAnimation] = useState(false);\n  const [showCustomCSS, setShowCustomCSS] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [reselectElement, setReselectElement] = useState(false);\n  const [goToNextElement, setGoToNextElement] = useState(false);\n  // const [elementClick, setElementClick] = useState(false);\n  //const [dropdownValue, setDropdownValue] = useState(\"\");\n  const [isOpen, setIsOpen] = useState(true);\n  const {\n    setCurrentGuideId,\n    currentGuideId,\n    getCurrentGuideId\n  } = userSession(state => state);\n  const {\n    //selectedTemplate,\n    padding,\n    setPadding,\n    position,\n    setPosition,\n    radius,\n    setRadius,\n    borderSize,\n    setBorderSize,\n    setBorderColor,\n    borderColor,\n    setBackgroundColor,\n    backgroundColor,\n    overlayEnabled,\n    setOverlayEnabled,\n    setZiindex,\n    setguidesSettingspopup,\n    setHotspotPopup,\n    setTitlePopup,\n    titlePopup,\n    hotspotPopup,\n    showTooltipCanvasSettings,\n    setShowTooltipCanvasSettings,\n    setTooltipBackgroundcolor,\n    setTooltipBordercolor,\n    setTooltipBorderradius,\n    setTooltipBordersize,\n    CANVAS_DEFAULT_VALUE,\n    savedGuideData,\n    ButtonsDropdown,\n    setButtonsDropdown,\n    elementSelected,\n    setElementSelected,\n    currentHoveredElement,\n    elementClick,\n    setElementClick,\n    elementButtonName,\n    setElementButtonName,\n    updateDesignelementInTooltip,\n    toolTipGuideMetaData,\n    elementbuttonClick,\n    SetElementButtonClick,\n    buttonClick,\n    setButtonClick,\n    currentStep,\n    highlightedButton,\n    setHighlightedButton,\n    setSelectActions,\n    updateTooltipButtonAction,\n    mapButtonSection,\n    btnidss,\n    selectedTemplateTour,\n    progress,\n    setProgress,\n    setSelectedOption,\n    dropdownValue,\n    setDropdownValue,\n    setIsUnSavedChanges,\n    showLauncherSettings,\n    setShowLauncherSettings,\n    checkpointsPopup,\n    setCheckPointsPopup,\n    createWithAI,\n    interactionData\n  } = useDrawerStore(state => state);\n  const setbtnidss = useDrawerStore(state => state.setbtnidss);\n  useEffect(() => {\n    setShowCanvasSettings(false);\n    setShowChecklistCanvasSettings(false);\n    setShowTooltipCanvasSettings(false);\n    setOverlaySettings(false);\n    setShowElementsSettings(false);\n    setshowAnimation(false);\n    setShowCustomCSS(false);\n    setHotspotPopup(false);\n    setTitlePopup(false);\n    setShowLauncherSettings(false);\n  }, [selectedTemplate, selectedTemplateTour]);\n  // const overlayEnabled = useDrawerStore((state) => state.overlayEnabled);\n  // const setOverlayEnabled = useDrawerStore((state) => state.setOverlayEnabled);\n  const toggleCanvasSettings = () => {\n    if (selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\") {\n      setShowTooltipCanvasSettings(!showTooltipCanvasSettings);\n    } else if (selectedTemplate === \"Checklist\") {\n      setShowChecklistCanvasSettings(!showChecklistCanvasSettings);\n    } else {\n      setShowCanvasSettings(!showCanvasSettings);\n    }\n  };\n  const toggleOverlaySettings = () => {\n    setMenuPopup(false);\n    setOverlaySettings(!showOverlay);\n  };\n  const handleHotspotClick = () => {\n    //setguidesSettingspopup(false); // Close any other popups\n    setHotspotPopup(true); // Open the hotspot popup\n    setTimeout(() => {\n      setHotspotPopup(true); // Ensure the popup is rendered\n    }, 0);\n  };\n  const handleTitlePopup = () => {\n    setTitlePopup(true);\n  };\n  const handleCheckPointPopup = () => {\n    setCheckPointsPopup(true);\n  };\n\n  // useEffect(() => {\n  // \tsetTimeout(() => {\n  // \t\tsetHotspotPopup(true); // Ensure the popup is rendered\n  // \t}, 0);\n  // }, [hotspotPopup]);\n\n  // Removed useEffect that was resetting dropdownValue on currentStep change\n  // This was causing the dropdown to show \"Select an option\" even after selection\n\n  const toggleReselectElement = () => {\n    //setReselectElement(!reselectElement);\n    //setTooltipXaxis(\"4\");\n    //setTooltipYaxis(\"4\");\n    //setTooltipPosition(\"middle-center\");\n    //setTooltipBackgroundcolor(\"\");\n    //setTooltipBordercolor(\"\");\n    setTooltipBorderradius(\"8\");\n    //setTooltipBordersize(\"1\");\n    //setTooltipPadding(\"4\");\n    //setTooltipWidth(\"400\");\n    //updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\n    setElementSelected(true);\n    setIsTooltipPopup(false);\n    setShowTooltipCanvasSettings(false);\n  };\n  useEffect(() => {\n    const fetchGuideDetails = async () => {\n      if (currentGuideId != \"\" && currentGuideId != null) {\n        var _tooltipMetadata$desi;\n        // First, check the current toolTipGuideMetaData for the most up-to-date state\n        const tooltipMetadata = toolTipGuideMetaData === null || toolTipGuideMetaData === void 0 ? void 0 : toolTipGuideMetaData[currentStep - 1];\n        if (tooltipMetadata !== null && tooltipMetadata !== void 0 && (_tooltipMetadata$desi = tooltipMetadata.design) !== null && _tooltipMetadata$desi !== void 0 && _tooltipMetadata$desi.gotoNext) {\n          // Use the current metadata as the source of truth\n          const hasButtonClick = tooltipMetadata.design.gotoNext.ButtonId && tooltipMetadata.design.gotoNext.ButtonId.trim() !== \"\";\n          console.log(\"useEffect: Has button click:\", hasButtonClick, \"ButtonId:\", tooltipMetadata.design.gotoNext.ButtonId);\n          if (hasButtonClick) {\n            setElementClick(\"button\");\n            setButtonClick(true);\n            SetElementButtonClick(true);\n            // Use ButtonId for dropdown value, not ButtonName\n            setDropdownValue(tooltipMetadata.design.gotoNext.ButtonId || \"\");\n            setElementButtonName(tooltipMetadata.design.gotoNext.ButtonName || tooltipMetadata.design.gotoNext.buttonName || \"\");\n            setbtnidss(tooltipMetadata.design.gotoNext.ButtonId || \"\");\n            console.log(\"useEffect: Set button click mode with values:\", {\n              elementClick: \"button\",\n              buttonClick: true,\n              dropdownValue: tooltipMetadata.design.gotoNext.ButtonId,\n              elementButtonName: tooltipMetadata.design.gotoNext.ButtonName,\n              btnidss: tooltipMetadata.design.gotoNext.ButtonId\n            });\n          } else {\n            setElementClick(\"element\");\n            setButtonClick(false);\n            SetElementButtonClick(false);\n            setDropdownValue(\"\");\n            setElementButtonName(\"\");\n            setbtnidss(\"\");\n            console.log(\"useEffect: Set element click mode\");\n          }\n        } else {\n          var _data$GuideDetails, _data$GuideDetails$Gu, _guideStep$Design;\n          // Fallback to fetching from database if metadata doesn't exist\n          const data = await GetGudeDetailsByGuideId(currentGuideId, createWithAI, interactionData);\n          const guideStep = data === null || data === void 0 ? void 0 : (_data$GuideDetails = data.GuideDetails) === null || _data$GuideDetails === void 0 ? void 0 : (_data$GuideDetails$Gu = _data$GuideDetails.GuideStep) === null || _data$GuideDetails$Gu === void 0 ? void 0 : _data$GuideDetails$Gu[currentStep - 1];\n          if (guideStep !== null && guideStep !== void 0 && (_guideStep$Design = guideStep.Design) !== null && _guideStep$Design !== void 0 && _guideStep$Design.GotoNext) {\n            const hasButtonClick = guideStep.Design.GotoNext.ButtonId && guideStep.Design.GotoNext.ButtonId.trim() !== \"\";\n            if (hasButtonClick) {\n              setElementClick(\"button\");\n              setButtonClick(true);\n              SetElementButtonClick(true);\n              // Use ButtonId for dropdown value, not ButtonName\n              setDropdownValue(guideStep.Design.GotoNext.ButtonId || \"\");\n              setElementButtonName(guideStep.Design.GotoNext.ButtonName || \"\");\n              setbtnidss(guideStep.Design.GotoNext.ButtonId || \"\");\n            } else {\n              setElementClick(\"element\");\n              setButtonClick(false);\n              SetElementButtonClick(false);\n              setDropdownValue(\"\");\n              setElementButtonName(\"\");\n              setbtnidss(\"\");\n            }\n          } else {\n            setElementClick(\"element\");\n            setButtonClick(false);\n            SetElementButtonClick(false);\n            setDropdownValue(\"\");\n            setElementButtonName(\"\");\n            setbtnidss(\"\");\n          }\n        }\n      }\n    };\n    fetchGuideDetails();\n  }, [currentStep, toolTipGuideMetaData]);\n  const removeAppliedStyleOfEle = element => {\n    element.removeAttribute(\"disabled\");\n    element.style.outline = \"\";\n    element.style.pointerEvents = \"unset\";\n  };\n  const [menuPopup, setMenuPopup] = useState(true);\n  const onReselectElement = () => {\n    // setTooltipXaxis(\"4\");\n    // setTooltipYaxis(\"4\");\n    // setTooltipPosition(\"middle-center\");\n    // setTooltipBackgroundcolor(\"\");\n    // setTooltipBordercolor(\"\");\n    // setTooltipBorderradius(\"4\");\n    // setTooltipBordersize(\"1\");\n    // setTooltipPadding(\"4\");\n    // setTooltipWidth(\"400\");\n    //updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\n    //setElementSelected(true);\n    // setElementSelected(false);\n    // setIsTooltipPopup(false);\n    // setShowTooltipCanvasSettings(false);\n    const existingHotspot = document.getElementById(\"hotspotBlinkCreation\");\n    const existingTooltip = document.getElementById(\"Tooltip-unique\");\n    setIsUnSavedChanges(false);\n\n    // existingTooltip.remove();\n    setDesignPopup(false);\n    setElementSelected(false);\n    currentHoveredElement && removeAppliedStyleOfEle(currentHoveredElement);\n    setIsUnSavedChanges(true);\n  };\n\n  // const [highlightedButton, setHighlightedButton] = useState(null);\n\n  // Function to handle button highlighting\n  const handleButtonClick = buttonId => {\n    console.log(\"Button clicked:\", buttonId);\n    setIsUnSavedChanges(true);\n    handleToggleClick(buttonId);\n    setHighlightedButton(prev => prev === buttonId ? null : buttonId);\n\n    // Remove immediate save to prevent state interference\n    // handleSaveGuide();\n  };\n  const handleLaunchSettings = () => {\n    setShowLauncherSettings(true);\n  };\n  const handleToggleClick = type => {\n    console.log(\"handleToggleClick called with type:\", type);\n    if (type === 1) {\n      // Switching to \"element click\"\n      console.log(\"Switching to element click mode\");\n      setElementClick(\"element\");\n      setElementButtonName(\"\");\n      setDropdownValue(\"\");\n      setbtnidss(\"\");\n      setButtonClick(false);\n      SetElementButtonClick(false);\n\n      // Update the gotoNext object to reflect \"element click\" state\n      const updatedCanvasSettings = {\n        NextStep: \"element\",\n        ButtonId: \"\",\n        ElementPath: \"\",\n        ButtonName: \"\",\n        Id: \"\"\n      };\n      updateDesignelementInTooltip(updatedCanvasSettings);\n    } else if (type === 2) {\n      var _tooltipMetadata$desi2, _tooltipMetadata$desi3;\n      // Switching to \"button click\"\n      console.log(\"Switching to button click mode\");\n      setElementClick(\"button\");\n      setButtonClick(true);\n      SetElementButtonClick(true);\n\n      // CRITICAL FIX: When switching to button click, restore the dropdown state from metadata\n      const tooltipMetadata = toolTipGuideMetaData === null || toolTipGuideMetaData === void 0 ? void 0 : toolTipGuideMetaData[currentStep - 1];\n      console.log(\"Current tooltip metadata:\", tooltipMetadata);\n      if (tooltipMetadata !== null && tooltipMetadata !== void 0 && (_tooltipMetadata$desi2 = tooltipMetadata.design) !== null && _tooltipMetadata$desi2 !== void 0 && (_tooltipMetadata$desi3 = _tooltipMetadata$desi2.gotoNext) !== null && _tooltipMetadata$desi3 !== void 0 && _tooltipMetadata$desi3.ButtonId) {\n        const buttonId = tooltipMetadata.design.gotoNext.ButtonId;\n        const buttonName = tooltipMetadata.design.gotoNext.ButtonName;\n\n        // Update all related state variables to ensure dropdown shows correctly\n        setDropdownValue(buttonId);\n        setElementButtonName(buttonName || \"\");\n        setbtnidss(buttonId);\n        console.log(\"Restored button click state when switching to button mode:\", {\n          buttonId,\n          buttonName,\n          dropdownValue: buttonId\n        });\n      } else {\n        console.log(\"No existing button click data found in metadata\");\n      }\n    }\n  };\n  const DesignelementInTooltip = (value, name) => {\n    const updatedCanvasSettings = {\n      NextStep: elementClick,\n      ButtonId: value,\n      ElementPath: \"\",\n      ButtonName: name === null || name === void 0 ? void 0 : name.name,\n      Id: value // Add the Id property to match what's expected in Tooltips.tsx\n    };\n    updateDesignelementInTooltip(updatedCanvasSettings);\n  };\n  const handleDropdownChange = event => {\n    var _toolTipGuideMetaData, _toolTipGuideMetaData2, _buttonContainer$butt;\n    const selectedValue = event.target.value;\n    console.log(\"Dropdown changed to:\", selectedValue);\n\n    // Find the button container dynamically instead of using hardcoded index\n    const buttonContainer = (_toolTipGuideMetaData = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.containers) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.find(container => container.type === \"button\");\n    const selectedButton = buttonContainer === null || buttonContainer === void 0 ? void 0 : (_buttonContainer$butt = buttonContainer.buttons) === null || _buttonContainer$butt === void 0 ? void 0 : _buttonContainer$butt.find(button => button.id === selectedValue);\n    console.log(\"Selected button:\", selectedButton);\n    console.log(\"Button container:\", buttonContainer);\n\n    // Update all relevant state variables\n    setDropdownValue(selectedValue);\n    setElementButtonName((selectedButton === null || selectedButton === void 0 ? void 0 : selectedButton.name) || selectedValue); // Use button name, fallback to ID\n    setbtnidss(selectedValue);\n    setElementClick(\"button\");\n\n    // Update the design metadata with both ID and name for proper persistence\n    DesignelementInTooltip(selectedValue, selectedButton);\n\n    // Mark as unsaved changes\n    setIsUnSavedChanges(true);\n    console.log(\"Updated state after dropdown change:\", {\n      dropdownValue: selectedValue,\n      elementButtonName: (selectedButton === null || selectedButton === void 0 ? void 0 : selectedButton.name) || selectedValue,\n      btnidss: selectedValue,\n      elementClick: \"button\"\n    });\n  };\n  const toggleElementsSettings = () => {\n    setMenuPopup(false);\n    setShowElementsSettings(true);\n    //setDesignPopup(false);\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [showElementsSettings && designPopup && /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(Elementssettings, {\n          setShowElementsSettings: setShowElementsSettings,\n          setDesignPopup: setDesignPopup\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 6\n      }, this), \";\"]\n    }, void 0, true);\n  };\n  const toggleCustomCSS = () => {\n    setShowCustomCSS(!showCustomCSS); // Toggle CustomCSS visibility\n  };\n  const toggleAnimation = () => {\n    setshowAnimation(!showAnimation); // Toggle CustomCSS visibility\n  };\n  const handleClose = () => {\n    setIsOpen(false); // Close the popup when close button is clicked\n    setDesignPopup(false);\n  };\n  const handleDismissDataChange = data => {};\n  if (!isOpen) return null;\n  const handleStatusChange = status => {\n    setOverlayEnabled(status);\n  };\n  return (\n    /*#__PURE__*/\n    //<Draggable>\n    _jsxDEV(\"div\", {\n      id: \"qadpt-designpopup\",\n      className: \"qadpt-designpopup\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-design-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title\",\n            children: translate(\"Design\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            \"aria-label\": translate(\"close\"),\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 5\n        }, this), titlePopup && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: /*#__PURE__*/_jsxDEV(TitleSubTitle, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 7\n          }, this)\n        }, void 0, false), showLauncherSettings && /*#__PURE__*/_jsxDEV(LauncherSettings, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 6\n        }, this), menuPopup && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-controls\",\n            children: [(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") && /*#__PURE__*/_jsxDEV(Box, {\n              className: \" qadpt-control-box\",\n              onClick: onReselectElement,\n              sx: {\n                cursor: \"pointer\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                sx: {\n                  color: \"#495e58\"\n                },\n                children: translate(\"Reselect Element\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"qadpt-reselect-icon\",\n                dangerouslySetInnerHTML: {\n                  __html: Reselect\n                },\n                style: {\n                  padding: \"5px\",\n                  marginRight: \"10px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 9\n            }, this), (selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") && /*#__PURE__*/_jsxDEV(\"div\", {\n              // className=\"qadpt-design-btn\"\n              style: {\n                marginBottom: \"8px\",\n                borderRadius: \"12px\",\n                padding: \"8px 12px\",\n                background: \"#eae2e2\",\n                textTransform: \"none\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  justifyContent: \"flex-start\",\n                  alignItems: \"center\"\n                },\n                className: \"qadpt-gtnext\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    background: \"rgba(95, 158, 160, 0.2)\",\n                    borderRadius: \"100px\",\n                    padding: \"4px 8px\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(KeyboardTabSharp, {\n                    style: {\n                      color: \"var(--primarycolor)\",\n                      height: \"21px\",\n                      width: \"21px\"\n                      // borderRadius: \"50px\",\n                      // background: \"rgba(95, 158, 160, 0.2)\",\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    color: \"#444444\",\n                    fontWeight: \"600\"\n                  },\n                  children: translate(\"Go to next step\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  gap: \"6px\",\n                  marginTop: \"10px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => handleButtonClick(1),\n                  style: {\n                    display: \"flex\",\n                    justifyContent: \"flex-start\",\n                    alignItems: \"center\",\n                    height: \"40px\",\n                    borderRadius: \"8px\",\n                    cursor: \"pointer\",\n                    backgroundColor: elementClick === \"element\" ? \"rgba(95, 158, 160, 0.2)\" : \"rgb(196, 193, 193, 0.3)\",\n                    border: elementClick === \"element\" ? \"1px solid var(--primarycolor)\" : \"1px solid transparent\",\n                    width: \"95px\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    sx: {\n                      color: \"#1c1b1f\",\n                      padding: \"0 6px\",\n                      fontSize: \"12px !important\"\n                    },\n                    children: translate(\"Element Click\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => handleButtonClick(2),\n                  style: {\n                    display: \"flex\",\n                    justifyContent: \"flex-start\",\n                    alignItems: \"center\",\n                    height: \"40px\",\n                    borderRadius: \"8px\",\n                    cursor: \"pointer\",\n                    backgroundColor: elementClick !== \"element\" ? \"rgba(95, 158, 160, 0.2)\" : \"rgb(196, 193, 193, 0.3)\",\n                    border: elementClick !== \"element\" ? \"1px solid var(--primarycolor)\" : \"1px solid transparent\",\n                    width: \"95px\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    sx: {\n                      color: \"#1c1b1f\",\n                      padding: \"0 11px\",\n                      fontSize: \"12px !important\"\n                    },\n                    children: translate(\"Button Click\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 650,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 10\n              }, this), buttonClick && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  className: \"qadpt-chos-btn\",\n                  sx: {\n                    margin: \"0 !important\",\n                    marginBottom: \"8px\",\n                    borderRadius: \"12px\",\n                    background: \"#eae2e2\",\n                    textTransform: \"none\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    sx: {\n                      padding: \"4px\",\n                      color: \"#495e58a\"\n                    },\n                    children: translate(\"Choose Button\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 13\n                  }, this), ((_toolTipGuideMetaData3, _toolTipGuideMetaData4) => {\n                    // Find the button container dynamically instead of using hardcoded index\n                    const buttonContainer = toolTipGuideMetaData === null || toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.containers) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.find(container => container.type === \"button\");\n                    return buttonContainer !== null && buttonContainer !== void 0 && buttonContainer.buttons ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: /*#__PURE__*/_jsxDEV(Select\n                      // The dropdown value is always the button's ID, matching MenuItem values\n                      , {\n                        value: ((_toolTipGuideMetaData5, _toolTipGuideMetaData6, _toolTipGuideMetaData7, _buttonContainer$butt2, _buttonContainer$butt3, _buttonContainer$butt4, _buttonContainer$butt5, _buttonContainer$butt6) => {\n                          // Primary: Use the ButtonId from metadata as the source of truth\n                          const designButtonId = (_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.design) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : (_toolTipGuideMetaData7 = _toolTipGuideMetaData6.gotoNext) === null || _toolTipGuideMetaData7 === void 0 ? void 0 : _toolTipGuideMetaData7.ButtonId;\n                          if (designButtonId && buttonContainer !== null && buttonContainer !== void 0 && (_buttonContainer$butt2 = buttonContainer.buttons) !== null && _buttonContainer$butt2 !== void 0 && _buttonContainer$butt2.some(button => button.id === designButtonId)) {\n                            return designButtonId;\n                          }\n\n                          // Secondary: Use btnidss if it matches a valid button\n                          if (btnidss && buttonContainer !== null && buttonContainer !== void 0 && (_buttonContainer$butt3 = buttonContainer.buttons) !== null && _buttonContainer$butt3 !== void 0 && _buttonContainer$butt3.some(button => button.id === btnidss)) {\n                            return btnidss;\n                          }\n\n                          // Tertiary: Use dropdownValue if it matches a valid button\n                          if (dropdownValue && buttonContainer !== null && buttonContainer !== void 0 && (_buttonContainer$butt4 = buttonContainer.buttons) !== null && _buttonContainer$butt4 !== void 0 && _buttonContainer$butt4.some(button => button.id === dropdownValue)) {\n                            return dropdownValue;\n                          }\n                          // Fallback: if elementButtonName matches a button name, use its id\n                          if (elementButtonName && buttonContainer !== null && buttonContainer !== void 0 && (_buttonContainer$butt5 = buttonContainer.buttons) !== null && _buttonContainer$butt5 !== void 0 && _buttonContainer$butt5.length) {\n                            const match = buttonContainer.buttons.find(button => button.name === elementButtonName);\n                            if (match) return match.id;\n                          }\n                          // Fallback: if ButtonId is present but ButtonName is missing, use ButtonId and display the name by lookup\n                          if (designButtonId && buttonContainer !== null && buttonContainer !== void 0 && (_buttonContainer$butt6 = buttonContainer.buttons) !== null && _buttonContainer$butt6 !== void 0 && _buttonContainer$butt6.length) {\n                            const match = buttonContainer.buttons.find(button => button.id === designButtonId);\n                            if (match) return designButtonId;\n                          }\n                          // Default to empty string\n                          return \"\";\n                        })(),\n                        onChange: handleDropdownChange,\n                        displayEmpty: true,\n                        style: {\n                          width: \"100%\"\n                        },\n                        sx: {\n                          \"& .MuiSvgIcon-root\": {\n                            height: \"20px\",\n                            width: \"20px\",\n                            top: \"10px\"\n                          }\n                        },\n                        renderValue: selected => {\n                          var _buttonContainer$butt7;\n                          // Always display the button name for the selected ID\n                          if (!selected) return translate(\"Select an option\");\n                          const btn = buttonContainer === null || buttonContainer === void 0 ? void 0 : (_buttonContainer$butt7 = buttonContainer.buttons) === null || _buttonContainer$butt7 === void 0 ? void 0 : _buttonContainer$butt7.find(button => button.id === selected);\n                          return (btn === null || btn === void 0 ? void 0 : btn.name) || translate(\"Select an option\");\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                          value: \"\",\n                          disabled: true,\n                          children: translate(\"Select an option\")\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 726,\n                          columnNumber: 17\n                        }, this), buttonContainer.buttons.map((button, buttonIndex) => /*#__PURE__*/_jsxDEV(MenuItem, {\n                          value: button.id,\n                          children: button.name\n                        }, buttonIndex, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 731,\n                          columnNumber: 18\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 677,\n                        columnNumber: 16\n                      }, this)\n                    }, void 0, false) : null;\n                  })(), \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 12\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 9\n            }, this), selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\" ? /*#__PURE__*/_jsxDEV(Button, {\n              className: \"qadpt-design-btn\",\n              onClick: handleHotspotClick // Trigger the hotspot popup\n              ,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"qadpt-hotsicon\",\n                dangerouslySetInnerHTML: {\n                  __html: Hotspoticon\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                sx: {\n                  fontWeight: \"600 !important\"\n                },\n                children: translate(\"Hotspot\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 9\n            }, this) : \"\", selectedTemplate === \"Checklist\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                className: \"qadpt-design-btn\",\n                onClick: handleCheckPointPopup,\n                startIcon: /*#__PURE__*/_jsxDEV(DesignServicesIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 20\n                }, this),\n                children: translate(\"Steps\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                className: \"qadpt-design-btn\",\n                onClick: handleTitlePopup,\n                startIcon: /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: elements\n                  },\n                  style: {\n                    height: \"23px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 779,\n                  columnNumber: 20\n                }, this),\n                children: translate(\"Title & SubTitle\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true), checkpointsPopup && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: /*#__PURE__*/_jsxDEV(Checkpoints, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 10\n              }, this)\n            }, void 0, false), /*#__PURE__*/_jsxDEV(Button, {\n              className: \"qadpt-design-btn\",\n              onClick: toggleCanvasSettings,\n              startIcon: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: overlay\n                },\n                style: {\n                  height: \"23px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 803,\n                columnNumber: 20\n              }, this),\n              children: translate(\"Canvas\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 800,\n              columnNumber: 8\n            }, this), selectedTemplate != \"Checklist\" && /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                className: \"qadpt-design-btn\",\n                onClick: toggleElementsSettings,\n                startIcon: /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: elements\n                  },\n                  style: {\n                    height: \"23px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 817,\n                  columnNumber: 12\n                }, this),\n                children: translate(\"Elements\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 10\n              }, this), ([\"Tooltip\", \"Announcement\"].includes(selectedTemplate) || selectedTemplate === \"Tour\" && [\"Tooltip\", \"Announcement\"].includes(selectedTemplateTour)) && /*#__PURE__*/_jsxDEV(Button, {\n                className: \"qadpt-design-btn\",\n                onClick: toggleOverlaySettings,\n                startIcon: /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: overlay\n                  },\n                  style: {\n                    height: \"23px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 832,\n                  columnNumber: 14\n                }, this)\n                // sx={{\n                // \topacity: selectedTemplate === \"Banner\" ? 0.5 : 1,\n                // }}\n                ,\n                children: translate(\"Overlay\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 12\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 9\n            }, this), selectedTemplate === \"Checklist\" && /*#__PURE__*/_jsxDEV(Button, {\n              className: \"qadpt-design-btn\",\n              onClick: handleLaunchSettings,\n              startIcon: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: animation\n                },\n                style: {\n                  height: \"23px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 879,\n                columnNumber: 21\n              }, this),\n              children: translate(\"Launcher\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 7\n          }, this)\n        }, void 0, false)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 4\n      }, this), hotspotPopup && /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(HotspotSettings, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 893,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 892,\n        columnNumber: 5\n      }, this), showCanvasSettings && (selectedTemplate === \"Banner\" || selectedTemplateTour === \"Banner\") ? /*#__PURE__*/_jsxDEV(PageInteractions, {\n        setShowCanvasSettings: setShowCanvasSettings,\n        backgroundC: backgroundC,\n        setBackgroundC: setBackgroundC,\n        Bposition: Bposition,\n        setBposition: setBposition,\n        bpadding: bpadding,\n        setbPadding: setbPadding,\n        Bbordercolor: Bbordercolor,\n        setBBorderColor: setBBorderColor,\n        BborderSize: BborderSize,\n        setBBorderSize: setBBorderSize,\n        zindeex: zindeex,\n        setZindeex: setZindeex,\n        resetHeightofBanner: resetHeightofBanner\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 898,\n        columnNumber: 5\n      }, this) : showCanvasSettings && /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(CanvasSettings, {\n          zindeex: zindeex,\n          setZindeex: setZindeex,\n          setShowCanvasSettings: setShowCanvasSettings\n          // width={width}\n          // height={height}\n          // padding={padding}\n          // borderRadius={borderRadius}\n          // borderColor={borderColor}\n          // backgroundColor={backgroundColor}\n          // selectedPosition={selectedPosition}\n          // setSelectedPosition={setSelectedPosition}\n          // setBorderColor={setBorderColor}\n          // setBackgroundColor={setBackgroundColor}\n          // setWidth={setWidth}\n          // setHeight={setHeight}\n          // setPadding={setPadding}\n          // setBorderRadius={setBorderRadius}\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 917,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 916,\n        columnNumber: 6\n      }, this), showTooltipCanvasSettings && (selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\") ? /*#__PURE__*/_jsxDEV(TooltipCanvasSettings, {\n        setShowTooltipCanvasSettings: setShowTooltipCanvasSettings,\n        backgroundC: backgroundC,\n        setBackgroundC: setBackgroundC,\n        Bposition: Bposition,\n        setBposition: setBposition,\n        bpadding: bpadding,\n        setbPadding: setbPadding,\n        Bbordercolor: Bbordercolor,\n        setBBorderColor: setBBorderColor,\n        BborderSize: BborderSize,\n        setBBorderSize: setBBorderSize,\n        zindeex: zindeex,\n        setZindeex: setZindeex\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 945,\n        columnNumber: 5\n      }, this) : \"\", showChecklistCanvasSettings && selectedTemplate === \"Checklist\" ? /*#__PURE__*/_jsxDEV(ChecklistCanvasSettings, {\n        setShowChecklistCanvasSettings: setShowChecklistCanvasSettings,\n        backgroundC: backgroundC,\n        setBackgroundC: setBackgroundC,\n        Bposition: Bposition,\n        setBposition: setBposition,\n        bpadding: bpadding,\n        setbPadding: setbPadding,\n        Bbordercolor: Bbordercolor,\n        setBBorderColor: setBBorderColor,\n        BborderSize: BborderSize,\n        setBBorderSize: setBBorderSize,\n        zindeex: zindeex,\n        setZindeex: setZindeex\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 969,\n        columnNumber: 5\n      }, this) : \"\", showOverlay && /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(OverlaySettings, {\n          setOverlaySettings: setOverlaySettings,\n          selectedTemplate: selectedTemplate,\n          onStatusChange: handleStatusChange,\n          setOverLays: setOverLays,\n          setDesignPopup: setDesignPopup,\n          anchorEl: anchorEl,\n          setMenuPopup: setMenuPopup\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 993,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 992,\n        columnNumber: 5\n      }, this), showElementsSettings && designPopup && /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(Elementssettings, {\n          setShowElementsSettings: setShowElementsSettings,\n          setDesignPopup: setDesignPopup,\n          setMenuPopup: setMenuPopup,\n          resetHeightofBanner: resetHeightofBanner\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1006,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1005,\n        columnNumber: 5\n      }, this), showCustomCSS && /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(CustomCSS, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1016,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1015,\n        columnNumber: 5\n      }, this), showAnimation && /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(AnimationSettings, {\n          selectedTemplate: selectedTemplate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1021,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1020,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 3\n    }, this)\n\n    //</Draggable>\n  );\n};\n_s(DesignMenu, \"cV89bWCupNcJ1We7LvhvAk+iouw=\", false, function () {\n  return [useTranslation, useDrawerStore, useDrawerStore];\n});\n_c = DesignMenu;\nexport default DesignMenu;\nfunction setTooltipXaxis(arg0) {\n  throw new Error(\"Function not implemented.\");\n}\nfunction setTooltipYaxis(arg0) {\n  throw new Error(\"Function not implemented.\");\n}\nfunction setTooltipPosition(arg0) {\n  throw new Error(\"Function not implemented.\");\n}\nfunction setTooltipBorderradius(arg0) {\n  throw new Error(\"Function not implemented.\");\n}\nfunction setTooltipPadding(arg0) {\n  throw new Error(\"Function not implemented.\");\n}\nfunction setTooltipWidth(arg0) {\n  throw new Error(\"Function not implemented.\");\n}\nfunction updateCanvasInTooltip(CANVAS_DEFAULT_VALUE) {\n  throw new Error(\"Function not implemented.\");\n}\nfunction setElementSelected(arg0) {\n  throw new Error(\"Function not implemented.\");\n}\nfunction setIsTooltipPopup(arg0) {\n  throw new Error(\"Function not implemented.\");\n}\nvar _c;\n$RefreshReg$(_c, \"DesignMenu\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "Box", "Typography", "IconButton", "Select", "MenuItem", "CloseIcon", "DesignServicesIcon", "CanvasSettings", "Elementssettings", "OverlaySettings", "CustomCSS", "PageInteractions", "AnimationSettings", "useDrawerStore", "HotspotSettings", "animation", "elements", "Hotspoticon", "overlay", "Reselect", "TooltipCanvasSettings", "KeyboardTabSharp", "GetGudeDetailsByGuideId", "userSession", "ChecklistCanvasSettings", "LauncherSettings", "Checkpoints", "TitleSubTitle", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DesignMenu", "width", "height", "overlays", "setOverLays", "backgroundC", "setBackgroundC", "Bposition", "setBposition", "bpadding", "setbPadding", "Bbordercolor", "setBBorderColor", "BborderSize", "setBBorderSize", "zindeex", "setZindeex", "setDesignPopup", "selectedTemplate", "designPopup", "initialGuideData", "updatedGuideData", "handleSaveGuide", "resetHeightofBanner", "_s", "t", "translate", "showCanvasSettings", "setShowCanvasSettings", "showChecklistCanvasSettings", "setShowChecklistCanvasSettings", "showOverlay", "setOverlaySettings", "showElementsSettings", "setShowElementsSettings", "showAnimation", "setshowAnimation", "showCustomCSS", "setShowCustomCSS", "anchorEl", "setAnchorEl", "reselectElement", "setReselectElement", "goToNextElement", "setGoToNextElement", "isOpen", "setIsOpen", "setCurrentGuideId", "currentGuideId", "getCurrentGuideId", "state", "padding", "setPadding", "position", "setPosition", "radius", "setRadius", "borderSize", "setBorderSize", "setBorderColor", "borderColor", "setBackgroundColor", "backgroundColor", "overlayEnabled", "setOverlayEnabled", "setZiindex", "setguidesSettingspopup", "setHotspotPopup", "setTitlePopup", "titlePopup", "hotspotPopup", "showTooltipCanvasSettings", "setShowTooltipCanvasSettings", "setTooltipBackgroundcolor", "setTooltipBordercolor", "setTooltipBorderradius", "setTooltipBordersize", "CANVAS_DEFAULT_VALUE", "savedGuideData", "ButtonsDropdown", "setButtonsDropdown", "elementSelected", "setElementSelected", "currentHoveredElement", "elementClick", "setElementClick", "elementButtonName", "setElementButtonName", "updateDesignelementInTooltip", "toolTipGuideMetaData", "elementbuttonClick", "SetElementButtonClick", "buttonClick", "setButtonClick", "currentStep", "highlighted<PERSON><PERSON><PERSON>", "setHighlightedButton", "setSelectActions", "updateTooltipButtonAction", "mapButtonSection", "btnidss", "selectedTemplateTour", "progress", "setProgress", "setSelectedOption", "dropdownValue", "setDropdownValue", "setIsUnSavedChanges", "showLauncherSettings", "setShowLauncherSettings", "checkpointsPopup", "setCheckPointsPopup", "createWithAI", "interactionData", "setbtnidss", "toggleCanvasSettings", "toggleOverlaySettings", "setMenuPopup", "handleHotspotClick", "setTimeout", "handleTitlePopup", "handleCheckPointPopup", "toggleReselectElement", "setIsTooltipPopup", "fetchGuideDetails", "_tooltipMetadata$desi", "tooltipMetadata", "design", "gotoNext", "hasButtonClick", "ButtonId", "trim", "console", "log", "ButtonName", "buttonName", "_data$GuideDetails", "_data$GuideDetails$Gu", "_guideStep$Design", "data", "guideStep", "GuideDetails", "GuideStep", "Design", "GotoNext", "removeAppliedStyleOfEle", "element", "removeAttribute", "style", "outline", "pointerEvents", "menuPopup", "onReselectElement", "existingHotspot", "document", "getElementById", "existingTooltip", "handleButtonClick", "buttonId", "handleToggleClick", "prev", "handleLaunchSettings", "type", "updatedCanvasSettings", "NextStep", "<PERSON>ement<PERSON><PERSON>", "Id", "_tooltipMetadata$desi2", "_tooltipMetadata$desi3", "DesignelementInTooltip", "value", "name", "handleDropdownChange", "event", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "_buttonContainer$butt", "selected<PERSON><PERSON><PERSON>", "target", "buttonContainer", "containers", "find", "container", "<PERSON><PERSON><PERSON><PERSON>", "buttons", "button", "id", "toggleElementsSettings", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toggleCustomCSS", "toggleAnimation", "handleClose", "handleDismissDataChange", "handleStatusChange", "status", "className", "size", "onClick", "sx", "cursor", "color", "dangerouslySetInnerHTML", "__html", "marginRight", "marginBottom", "borderRadius", "background", "textTransform", "display", "justifyContent", "alignItems", "fontWeight", "gap", "marginTop", "border", "fontSize", "margin", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "_toolTipGuideMetaData7", "_buttonContainer$butt2", "_buttonContainer$butt3", "_buttonContainer$butt4", "_buttonContainer$butt5", "_buttonContainer$butt6", "designButtonId", "some", "length", "match", "onChange", "displayEmpty", "top", "renderValue", "selected", "_buttonContainer$butt7", "btn", "disabled", "map", "buttonIndex", "startIcon", "includes", "onStatusChange", "_c", "setTooltipXaxis", "arg0", "Error", "setTooltipYaxis", "setTooltipPosition", "setTooltipPadding", "setTooltipWidth", "updateCanvasInTooltip", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideDesign/Design.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\n// import Draggable from \"react-draggable\";\r\nimport { Button, Box, Typography, IconButton, Select, MenuItem, Step } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport DesignServicesIcon from \"@mui/icons-material/DesignServices\";\r\nimport ViewModuleIcon from \"@mui/icons-material/ViewModule\";\r\nimport CodeIcon from \"@mui/icons-material/Code\";\r\nimport CanvasSettings from \"./CanvasSettings\";\r\nimport Elementssettings from \"../guideSetting/ElementsSettings\";\r\nimport OverlaySettings from \"./Overlay\";\r\nimport CustomCSS from \"./CustomCss\";\r\nimport PageInteractions from \"../guideBanners/selectedpopupfields/PageInteraction\";\r\nimport AnimationSettings from \"./Animation\";\r\nimport \"./Canvas.module.css\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport Tooltip from \"@mui/material/Tooltip\";\r\nimport HotspotSettings from \"../hotspot/HotspotSettings\";\r\nimport { animation, elements, Hotspoticon, overlay, Reselect } from \"../../assets/icons/icons\";\r\nimport TooltipCanvasSettings from \"../Tooltips/designFields/TooltipCanvasSettings\";\r\nimport { TouchAppSharp, KeyboardTabSharp } from \"@mui/icons-material\";\r\nimport { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\r\nimport userSession from \"../../store/userSession\";\r\nimport ChecklistCanvasSettings from \"../checklist/ChecklistCanvasSettings\";\r\nimport LauncherSettings from \"../checklist/LauncherSettings\";\r\nimport Checkpoints from \"../checklist/Chekpoints\";\r\nimport TitleSubTitle from \"../checklist/TitleSubTitle\";\r\nimport '../../styles/rtl_styles.scss';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst DesignMenu = ({\r\n\twidth,\r\n\theight,\r\n\toverlays,\r\n\tsetOverLays,\r\n\tbackgroundC,\r\n\tsetBackgroundC,\r\n\tBposition,\r\n\tsetBposition,\r\n\tbpadding,\r\n\tsetbPadding,\r\n\tBbordercolor,\r\n\tsetBBorderColor,\r\n\tBborderSize,\r\n\tsetBBorderSize,\r\n\tzindeex,\r\n\tsetZindeex,\r\n\tsetDesignPopup,\r\n\tselectedTemplate,\r\n\tdesignPopup,\r\n\tinitialGuideData,\r\n\tupdatedGuideData,\r\n\thandleSaveGuide,\r\n\tresetHeightofBanner,\r\n}: // hotspotPopup\r\n// padding,\r\n// borderRadius,\r\n// borderColor,\r\n// backgroundColor,\r\n// selectedPosition,\r\n// setSelectedPosition,\r\n// setBorderColor,\r\n// setBackgroundColor,\r\n// setWidth,\r\n// setHeight,\r\n// setPadding,\r\n// setBorderRadius,\r\n// //selectedTemplate,\r\n// position,\r\n// setPosition,\r\n// radius,\r\n// setRadius,\r\n// borderSize,\r\n// setBorderSize,\r\nany) => {\r\n\tconst { t: translate } = useTranslation();\r\n\t// State to control the visibility of CanvasSettings\r\n\tconst [showCanvasSettings, setShowCanvasSettings] = useState(false);\r\n\tconst [showChecklistCanvasSettings, setShowChecklistCanvasSettings] = useState(false);\r\n\t//const [showTooltipCanvasSettings, setShowTooltipCanvasSettings] = useState(false);\r\n\tconst [showOverlay, setOverlaySettings] = useState(false);\r\n\tconst [showElementsSettings, setShowElementsSettings] = useState(false);\r\n\tconst [showAnimation, setshowAnimation] = useState(false);\r\n\tconst [showCustomCSS, setShowCustomCSS] = useState(false);\r\n\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [reselectElement, setReselectElement] = useState(false);\r\n\tconst [goToNextElement, setGoToNextElement] = useState(false);\r\n\t// const [elementClick, setElementClick] = useState(false);\r\n\t//const [dropdownValue, setDropdownValue] = useState(\"\");\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst { setCurrentGuideId, currentGuideId, getCurrentGuideId } = userSession((state: any) => state);\r\n\tconst {\r\n\t\t//selectedTemplate,\r\n\t\tpadding,\r\n\t\tsetPadding,\r\n\t\tposition,\r\n\t\tsetPosition,\r\n\t\tradius,\r\n\t\tsetRadius,\r\n\t\tborderSize,\r\n\t\tsetBorderSize,\r\n\t\tsetBorderColor,\r\n\t\tborderColor,\r\n\t\tsetBackgroundColor,\r\n\t\tbackgroundColor,\r\n\t\toverlayEnabled,\r\n\t\tsetOverlayEnabled,\r\n\t\tsetZiindex,\r\n\t\tsetguidesSettingspopup,\r\n\t\tsetHotspotPopup,\r\n\t\tsetTitlePopup,\r\n\t\ttitlePopup,\r\n\t\thotspotPopup,\r\n\t\tshowTooltipCanvasSettings,\r\n\t\tsetShowTooltipCanvasSettings,\r\n\t\tsetTooltipBackgroundcolor,\r\n\t\tsetTooltipBordercolor,\r\n\t\tsetTooltipBorderradius,\r\n\t\tsetTooltipBordersize,\r\n\t\tCANVAS_DEFAULT_VALUE,\r\n\t\tsavedGuideData,\r\n\t\tButtonsDropdown,\r\n\t\tsetButtonsDropdown,\r\n\t\telementSelected,\r\n\t\tsetElementSelected,\r\n\t\tcurrentHoveredElement,\r\n\t\telementClick,\r\n\t\tsetElementClick,\r\n\t\telementButtonName,\r\n\t\tsetElementButtonName,\r\n\t\tupdateDesignelementInTooltip,\r\n\t\ttoolTipGuideMetaData,\r\n\t\telementbuttonClick,\r\n\t\tSetElementButtonClick,\r\n\t\tbuttonClick,\r\n\t\tsetButtonClick,\r\n\t\tcurrentStep,\r\n\t\thighlightedButton,\r\n\t\tsetHighlightedButton,\r\n\t\tsetSelectActions,\r\n\t\tupdateTooltipButtonAction,\r\n\t\tmapButtonSection,\r\n\t\tbtnidss,\r\n\t\tselectedTemplateTour,\r\n\t\tprogress,\r\n\t\tsetProgress,\r\n\t\tsetSelectedOption,\r\n\t\tdropdownValue,\r\n\t\tsetDropdownValue,\r\nsetIsUnSavedChanges,\r\n\t\tshowLauncherSettings,\r\n\t\tsetShowLauncherSettings,\r\n\t\tcheckpointsPopup,\r\n\t\tsetCheckPointsPopup,\r\n\t\tcreateWithAI,\r\n\t\tinteractionData\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst setbtnidss = useDrawerStore((state) => state.setbtnidss);\r\n\r\n\tuseEffect(() => {\r\n\t\tsetShowCanvasSettings(false);\r\n\t\tsetShowChecklistCanvasSettings(false);\r\n\t\tsetShowTooltipCanvasSettings(false);\r\n\t\tsetOverlaySettings(false);\r\n\t\tsetShowElementsSettings(false);\r\n\t\tsetshowAnimation(false);\r\n\t\tsetShowCustomCSS(false);\r\n\t\tsetHotspotPopup(false);\r\n\t\tsetTitlePopup(false);\r\n\t\tsetShowLauncherSettings(false);\r\n\t}, [selectedTemplate, selectedTemplateTour]);\r\n\t// const overlayEnabled = useDrawerStore((state) => state.overlayEnabled);\r\n\t// const setOverlayEnabled = useDrawerStore((state) => state.setOverlayEnabled);\r\n\tconst toggleCanvasSettings = () => {\r\n\t\tif (\r\n\t\t\tselectedTemplate === \"Tooltip\" ||\r\n\t\t\tselectedTemplate === \"Hotspot\" ||\r\n\t\t\tselectedTemplateTour === \"Tooltip\" ||\r\n\t\t\tselectedTemplateTour === \"Hotspot\"\r\n\t\t) {\r\n\t\t\tsetShowTooltipCanvasSettings(!showTooltipCanvasSettings);\r\n\t\t}\r\n\t\telse if (selectedTemplate === \"Checklist\")\r\n\t\t{\r\n\t\t\tsetShowChecklistCanvasSettings(!showChecklistCanvasSettings);\r\n\t\t}\r\n\t\telse {\r\n\t\t\tsetShowCanvasSettings(!showCanvasSettings);\r\n\t\t}\r\n\t};\r\n\tconst toggleOverlaySettings = () => {\r\n\t\tsetMenuPopup(false);\r\n\t\tsetOverlaySettings(!showOverlay);\r\n\t};\r\n\tconst handleHotspotClick = () => {\r\n\t\t//setguidesSettingspopup(false); // Close any other popups\r\n\t\tsetHotspotPopup(true); // Open the hotspot popup\r\n\t\tsetTimeout(() => {\r\n\t\t\tsetHotspotPopup(true); // Ensure the popup is rendered\r\n\t\t}, 0);\r\n\t};\r\n\r\n\tconst handleTitlePopup = () =>\r\n\t{\r\n\t\tsetTitlePopup(true);\r\n\t}\r\n\tconst handleCheckPointPopup = () =>\r\n\t{\r\n\t\tsetCheckPointsPopup(true);\r\n\t}\r\n\r\n\t// useEffect(() => {\r\n\t// \tsetTimeout(() => {\r\n\t// \t\tsetHotspotPopup(true); // Ensure the popup is rendered\r\n\t// \t}, 0);\r\n\t// }, [hotspotPopup]);\r\n\r\n\t// Removed useEffect that was resetting dropdownValue on currentStep change\r\n\t// This was causing the dropdown to show \"Select an option\" even after selection\r\n\r\n\r\n\tconst toggleReselectElement = () => {\r\n\t\t//setReselectElement(!reselectElement);\r\n\t\t//setTooltipXaxis(\"4\");\r\n\t\t//setTooltipYaxis(\"4\");\r\n\t\t//setTooltipPosition(\"middle-center\");\r\n\t\t//setTooltipBackgroundcolor(\"\");\r\n\t\t//setTooltipBordercolor(\"\");\r\n\t\tsetTooltipBorderradius(\"8\");\r\n\t\t//setTooltipBordersize(\"1\");\r\n\t\t//setTooltipPadding(\"4\");\r\n\t\t//setTooltipWidth(\"400\");\r\n\t\t//updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\r\n\t\tsetElementSelected(true);\r\n\t\tsetIsTooltipPopup(false);\r\n\t\tsetShowTooltipCanvasSettings(false);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\r\n\t\tconst fetchGuideDetails = async () => {\r\n\t\t  if (currentGuideId != \"\" && currentGuideId != null) {\r\n\t\t\t// First, check the current toolTipGuideMetaData for the most up-to-date state\r\n\t\t\tconst tooltipMetadata = toolTipGuideMetaData?.[currentStep - 1];\r\n\r\n\t\t\tif (tooltipMetadata?.design?.gotoNext) {\r\n\t\t\t  // Use the current metadata as the source of truth\r\n\t\t\t  const hasButtonClick = tooltipMetadata.design.gotoNext.ButtonId &&\r\n\t\t\t\ttooltipMetadata.design.gotoNext.ButtonId.trim() !== \"\";\r\n\r\n\t\t\t  console.log(\"useEffect: Has button click:\", hasButtonClick, \"ButtonId:\", tooltipMetadata.design.gotoNext.ButtonId);\r\n\r\n\t\t\t  if (hasButtonClick) {\r\n\t\t\t\tsetElementClick(\"button\");\r\n\t\t\t\tsetButtonClick(true);\r\n\t\t\t\tSetElementButtonClick(true);\r\n\t\t\t\t// Use ButtonId for dropdown value, not ButtonName\r\n\t\t\t\tsetDropdownValue(tooltipMetadata.design.gotoNext.ButtonId || \"\");\r\n\t\t\t\tsetElementButtonName(tooltipMetadata.design.gotoNext.ButtonName || tooltipMetadata.design.gotoNext.buttonName || \"\");\r\n\t\t\t\tsetbtnidss(tooltipMetadata.design.gotoNext.ButtonId || \"\");\r\n\r\n\t\t\t\tconsole.log(\"useEffect: Set button click mode with values:\", {\r\n\t\t\t\t\telementClick: \"button\",\r\n\t\t\t\t\tbuttonClick: true,\r\n\t\t\t\t\tdropdownValue: tooltipMetadata.design.gotoNext.ButtonId,\r\n\t\t\t\t\telementButtonName: tooltipMetadata.design.gotoNext.ButtonName,\r\n\t\t\t\t\tbtnidss: tooltipMetadata.design.gotoNext.ButtonId\r\n\t\t\t\t});\r\n\t\t\t  } else {\r\n\t\t\t\tsetElementClick(\"element\");\r\n\t\t\t\tsetButtonClick(false);\r\n\t\t\t\tSetElementButtonClick(false);\r\n\t\t\t\tsetDropdownValue(\"\");\r\n\t\t\t\tsetElementButtonName(\"\");\r\n\t\t\t\tsetbtnidss(\"\");\r\n\r\n\t\t\t\tconsole.log(\"useEffect: Set element click mode\");\r\n\t\t\t  }\r\n\t\t\t} else {\r\n\t\t\t  // Fallback to fetching from database if metadata doesn't exist\r\n\t\t\t  const data = await GetGudeDetailsByGuideId(currentGuideId, createWithAI, interactionData);\r\n\t\t\t  const guideStep = data?.GuideDetails?.GuideStep?.[currentStep - 1];\r\n\r\n\t\t\t  if (guideStep?.Design?.GotoNext) {\r\n\t\t\t\tconst hasButtonClick = guideStep.Design.GotoNext.ButtonId &&\r\n\t\t\t\t  guideStep.Design.GotoNext.ButtonId.trim() !== \"\";\r\n\r\n\t\t\t\tif (hasButtonClick) {\r\n\t\t\t\t  setElementClick(\"button\");\r\n\t\t\t\t  setButtonClick(true);\r\n\t\t\t\t  SetElementButtonClick(true);\r\n\t\t\t\t  // Use ButtonId for dropdown value, not ButtonName\r\n\t\t\t\t  setDropdownValue(guideStep.Design.GotoNext.ButtonId || \"\");\r\n\t\t\t\t  setElementButtonName(guideStep.Design.GotoNext.ButtonName || \"\");\r\n\t\t\t\t  setbtnidss(guideStep.Design.GotoNext.ButtonId || \"\");\r\n\t\t\t\t} else {\r\n\t\t\t\t  setElementClick(\"element\");\r\n\t\t\t\t  setButtonClick(false);\r\n\t\t\t\t  SetElementButtonClick(false);\r\n\t\t\t\t  setDropdownValue(\"\");\r\n\t\t\t\t  setElementButtonName(\"\");\r\n\t\t\t\t  setbtnidss(\"\");\r\n\t\t\t\t}\r\n\t\t\t  } else {\r\n\t\t\t\tsetElementClick(\"element\");\r\n\t\t\t\tsetButtonClick(false);\r\n\t\t\t\tSetElementButtonClick(false);\r\n\t\t\t\tsetDropdownValue(\"\");\r\n\t\t\t\tsetElementButtonName(\"\");\r\n\t\t\t\tsetbtnidss(\"\");\r\n\t\t\t  }\r\n\t\t\t}\r\n\t\t  }\r\n\t\t};\r\n\t\tfetchGuideDetails();\r\n\t  }, [currentStep, toolTipGuideMetaData]);\r\n\r\n\r\n\tconst removeAppliedStyleOfEle = (element: HTMLElement) => {\r\n\t\telement.removeAttribute(\"disabled\");\r\n\t\telement.style.outline = \"\";\r\n\t\telement.style.pointerEvents = \"unset\";\r\n\t};\r\n\tconst [menuPopup, setMenuPopup] = useState(true);\r\n\tconst onReselectElement = () => {\r\n\t\t// setTooltipXaxis(\"4\");\r\n\t\t// setTooltipYaxis(\"4\");\r\n\t\t// setTooltipPosition(\"middle-center\");\r\n\t\t// setTooltipBackgroundcolor(\"\");\r\n\t\t// setTooltipBordercolor(\"\");\r\n\t\t// setTooltipBorderradius(\"4\");\r\n\t\t// setTooltipBordersize(\"1\");\r\n\t\t// setTooltipPadding(\"4\");\r\n\t\t// setTooltipWidth(\"400\");\r\n\t\t//updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\r\n\t\t//setElementSelected(true);\r\n\t\t// setElementSelected(false);\r\n\t\t// setIsTooltipPopup(false);\r\n\t\t// setShowTooltipCanvasSettings(false);\r\n\t\tconst existingHotspot = document.getElementById(\"hotspotBlinkCreation\");\r\n\t\tconst existingTooltip = document.getElementById(\"Tooltip-unique\");\r\n\t\tsetIsUnSavedChanges(false);\r\n\r\n\t\t\t// existingTooltip.remove();\r\n\t\t\tsetDesignPopup(false);\r\n\t\t\tsetElementSelected(false);\r\n\t\t\tcurrentHoveredElement && removeAppliedStyleOfEle(currentHoveredElement);\r\n\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\t// const [highlightedButton, setHighlightedButton] = useState(null);\r\n\r\n\t// Function to handle button highlighting\r\n\tconst handleButtonClick = (buttonId: any) => {\r\n\t\tconsole.log(\"Button clicked:\", buttonId);\r\n\t\tsetIsUnSavedChanges(true);\r\n\r\n\t\thandleToggleClick(buttonId);\r\n\t\tsetHighlightedButton((prev: any) => (prev === buttonId ? null : buttonId));\r\n\r\n\t\t// Remove immediate save to prevent state interference\r\n\t\t// handleSaveGuide();\r\n\t};\r\n\r\n\tconst handleLaunchSettings = () =>\r\n\t{\r\n\t\tsetShowLauncherSettings(true);\r\n\t\t}\r\n\tconst handleToggleClick = (type: any) => {\r\n\t\tconsole.log(\"handleToggleClick called with type:\", type);\r\n\r\n\t\tif (type === 1) {\r\n\t\t\t// Switching to \"element click\"\r\n\t\t\tconsole.log(\"Switching to element click mode\");\r\n\t\t\tsetElementClick(\"element\");\r\n\t\t\tsetElementButtonName(\"\");\r\n\t\t\tsetDropdownValue(\"\");\r\n\t\t\tsetbtnidss(\"\");\r\n\t\t\tsetButtonClick(false);\r\n\t\t\tSetElementButtonClick(false);\r\n\r\n\t\t\t// Update the gotoNext object to reflect \"element click\" state\r\n\t\t\tconst updatedCanvasSettings = {\r\n\t\t\t\tNextStep: \"element\",\r\n\t\t\t\tButtonId: \"\",\r\n\t\t\t\tElementPath: \"\",\r\n\t\t\t\tButtonName: \"\",\r\n\t\t\t\tId: \"\"\r\n\t\t\t};\r\n\t\t\tupdateDesignelementInTooltip(updatedCanvasSettings);\r\n\r\n\t\t} else if (type === 2) {\r\n\t\t\t// Switching to \"button click\"\r\n\t\t\tconsole.log(\"Switching to button click mode\");\r\n\t\t\tsetElementClick(\"button\");\r\n\t\t\tsetButtonClick(true);\r\n\t\t\tSetElementButtonClick(true);\r\n\r\n\t\t\t// CRITICAL FIX: When switching to button click, restore the dropdown state from metadata\r\n\t\t\tconst tooltipMetadata = toolTipGuideMetaData?.[currentStep - 1];\r\n\t\t\tconsole.log(\"Current tooltip metadata:\", tooltipMetadata);\r\n\r\n\t\t\tif (tooltipMetadata?.design?.gotoNext?.ButtonId) {\r\n\t\t\t\tconst buttonId = tooltipMetadata.design.gotoNext.ButtonId;\r\n\t\t\t\tconst buttonName = tooltipMetadata.design.gotoNext.ButtonName;\r\n\r\n\t\t\t\t// Update all related state variables to ensure dropdown shows correctly\r\n\t\t\t\tsetDropdownValue(buttonId);\r\n\t\t\t\tsetElementButtonName(buttonName || \"\");\r\n\t\t\t\tsetbtnidss(buttonId);\r\n\r\n\t\t\t\tconsole.log(\"Restored button click state when switching to button mode:\", {\r\n\t\t\t\t\tbuttonId,\r\n\t\t\t\t\tbuttonName,\r\n\t\t\t\t\tdropdownValue: buttonId\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log(\"No existing button click data found in metadata\");\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\tconst DesignelementInTooltip = (value: any, name: any) => {\r\n\t\tconst updatedCanvasSettings = {\r\n\t\t\tNextStep: elementClick,\r\n\t\t\tButtonId: value,\r\n\t\t\tElementPath: \"\",\r\n\t\t\tButtonName: name?.name,\r\n\t\t\tId: value, // Add the Id property to match what's expected in Tooltips.tsx\r\n\t\t};\r\n\r\n\t\tupdateDesignelementInTooltip(updatedCanvasSettings);\r\n\t};\r\n\r\n\tconst handleDropdownChange = (event: any) => {\r\n\t\tconst selectedValue = event.target.value;\r\n\t\tconsole.log(\"Dropdown changed to:\", selectedValue);\r\n\r\n\t\t// Find the button container dynamically instead of using hardcoded index\r\n\t\tconst buttonContainer = toolTipGuideMetaData[currentStep - 1]?.containers?.find(\r\n\t\t\t(container: any) => container.type === \"button\"\r\n\t\t);\r\n\t\tconst selectedButton = buttonContainer?.buttons?.find(\r\n\t\t\t(button: any) => button.id === selectedValue\r\n\t\t);\r\n\r\n\t\tconsole.log(\"Selected button:\", selectedButton);\r\n\t\tconsole.log(\"Button container:\", buttonContainer);\r\n\r\n\t\t// Update all relevant state variables\r\n\t\tsetDropdownValue(selectedValue);\r\n\t\tsetElementButtonName(selectedButton?.name || selectedValue); // Use button name, fallback to ID\r\n\t\tsetbtnidss(selectedValue);\r\n\t\tsetElementClick(\"button\");\r\n\r\n\t\t// Update the design metadata with both ID and name for proper persistence\r\n\t\tDesignelementInTooltip(selectedValue, selectedButton);\r\n\r\n\t\t// Mark as unsaved changes\r\n\t\tsetIsUnSavedChanges(true);\r\n\r\n\t\tconsole.log(\"Updated state after dropdown change:\", {\r\n\t\t\tdropdownValue: selectedValue,\r\n\t\t\telementButtonName: selectedButton?.name || selectedValue,\r\n\t\t\tbtnidss: selectedValue,\r\n\t\t\telementClick: \"button\"\r\n\t\t});\r\n\t};\r\n\r\n\tconst toggleElementsSettings = () => {\r\n\t\tsetMenuPopup(false);\r\n\t\tsetShowElementsSettings(true);\r\n\t\t//setDesignPopup(false);\r\n\t\treturn (\r\n\t\t\t<>\r\n\t\t\t\t{showElementsSettings && designPopup && (\r\n\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t<Elementssettings\r\n\t\t\t\t\t\t\tsetShowElementsSettings={setShowElementsSettings}\r\n\t\t\t\t\t\t\tsetDesignPopup={setDesignPopup}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t)}\r\n\t\t\t\t;\r\n\t\t\t</>\r\n\t\t);\r\n\t};\r\n\r\n\tconst toggleCustomCSS = () => {\r\n\t\tsetShowCustomCSS(!showCustomCSS); // Toggle CustomCSS visibility\r\n\t};\r\n\tconst toggleAnimation = () => {\r\n\t\tsetshowAnimation(!showAnimation); // Toggle CustomCSS visibility\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false); // Close the popup when close button is clicked\r\n\t\tsetDesignPopup(false);\r\n\t};\r\n\r\n\tconst handleDismissDataChange = (data: any) => {};\r\n\tif (!isOpen) return null;\r\n\tconst handleStatusChange = (status: boolean) => {\r\n\t\tsetOverlayEnabled(status);\r\n\t};\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Design\")}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label={translate(\"close\")}\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\r\n\t\t\t\t{titlePopup && (\r\n\t\t\t\t\t<>\r\n\t\t\t\t\t\t<TitleSubTitle/>\r\n\t\t\t\t\t</>\r\n\t\t\t\t)}\r\n\r\n\t\t\t\t{showLauncherSettings && (\r\n\t\t\t\t\t<LauncherSettings/>\r\n\r\n\t\t\t\t)}\r\n\t\t\t\t{menuPopup && (\r\n\t\t\t\t\t<>\r\n\t\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t\t{(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") && (\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\" qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tonClick={onReselectElement}\r\n\t\t\t\t\t\t\t\t\tsx={{cursor:\"pointer\"}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ color: \"#495e58\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Reselect Element\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-reselect-icon\"\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Reselect }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ padding: \"5px\", marginRight: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t{(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") && (\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t// className=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tmarginBottom: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"8px 12px\",\r\n\t\t\t\t\t\t\t\t\t\tbackground: \"#eae2e2\",\r\n\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{/* <Button\r\n\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\tonClick={toggleGoToNextElement}\r\n\t\t\t\t\t\tstartIcon={<KeyboardTabIcon />}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tGo to next step\r\n\t\t\t\t\t</Button> */}\r\n\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", justifyContent: \"flex-start\", alignItems: \"center\" }} className=\"qadpt-gtnext\">\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"rgba(95, 158, 160, 0.2)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"100px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<KeyboardTabSharp\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"var(--primarycolor)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"21px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"21px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t// borderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t// background: \"rgba(95, 158, 160, 0.2)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"#444444\", fontWeight: \"600\" }}>{translate(\"Go to next step\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", gap: \"6px\", marginTop: \"10px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t{/* Button 1 */}\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonClick(1)}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\theight: \"40px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\telementClick === \"element\" ? \"rgba(95, 158, 160, 0.2)\" : \"rgb(196, 193, 193, 0.3)\",\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: elementClick === \"element\" ? \"1px solid var(--primarycolor)\" : \"1px solid transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"95px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{/* <KeyboardTabSharp\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: \"var(--primarycolor)\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\tmarginRight: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t/> */}\r\n\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"#1c1b1f\", padding: \"0 6px\", fontSize: \"12px !important\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Element Click\")}\r\n\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t{/* Button 2 */}\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonClick(2)}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\theight: \"40px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\telementClick !== \"element\" ? \"rgba(95, 158, 160, 0.2)\" : \"rgb(196, 193, 193, 0.3)\",\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: elementClick !== \"element\" ? \"1px solid var(--primarycolor)\" : \"1px solid transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"95px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{/* <KeyboardTabSharp\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: \"var(--primarycolor)\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\tmarginRight: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t/> */}\r\n\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"#1c1b1f\", padding: \"0 11px\", fontSize: \"12px !important\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Button Click\")}\r\n\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t{buttonClick && (\r\n\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chos-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"0 !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tmarginBottom: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#eae2e2\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{  padding: \"4px\", color: \"#495e58a\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Choose Button\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{(() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t// Find the button container dynamically instead of using hardcoded index\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tconst buttonContainer = toolTipGuideMetaData?.[currentStep - 1]?.containers?.find(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t(container: any) => container.type === \"button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\treturn buttonContainer?.buttons ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// The dropdown value is always the button's ID, matching MenuItem values\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={(() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  // Primary: Use the ButtonId from metadata as the source of truth\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  const designButtonId = toolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonId;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  if (designButtonId && buttonContainer?.buttons?.some((button: any) => button.id === designButtonId)) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn designButtonId;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  }\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  // Secondary: Use btnidss if it matches a valid button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  if (btnidss && buttonContainer?.buttons?.some((button: any) => button.id === btnidss)) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn btnidss;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  }\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  // Tertiary: Use dropdownValue if it matches a valid button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  if (dropdownValue && buttonContainer?.buttons?.some((button: any) => button.id === dropdownValue)) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn dropdownValue;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  // Fallback: if elementButtonName matches a button name, use its id\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  if (elementButtonName && buttonContainer?.buttons?.length) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst match = buttonContainer.buttons.find((button: any) => button.name === elementButtonName);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (match) return match.id;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  // Fallback: if ButtonId is present but ButtonName is missing, use ButtonId and display the name by lookup\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  if (designButtonId && buttonContainer?.buttons?.length) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst match = buttonContainer.buttons.find((button: any) => button.id === designButtonId);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (match) return designButtonId;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  // Default to empty string\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  return \"\";\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})()}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleDropdownChange}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplayEmpty\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  \"& .MuiSvgIcon-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttop: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\trenderValue={(selected) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// Always display the button name for the selected ID\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (!selected) return translate(\"Select an option\");\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst btn = buttonContainer?.buttons?.find((button: any) => button.id === selected);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn btn?.name || translate(\"Select an option\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"\" disabled>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Select an option\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{buttonContainer.buttons.map(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  (button: any, buttonIndex: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem key={buttonIndex} value={button.id}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  {button.name}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  )\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{/* The selected button name is shown in the dropdown, so no need to display it separately below. */}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t) : null;\r\n\t\t\t\t\t\t\t\t\t\t\t\t})()}{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t{/* Prevent rendering until data exists */}\r\n\t\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t{selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\" ? (\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\tonClick={handleHotspotClick} // Trigger the hotspot popup\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-hotsicon\"\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Hotspoticon }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tsx={{fontWeight: \"600 !important\"}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Hotspot\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\"\"\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t{(selectedTemplate === \"Checklist\") && (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={handleCheckPointPopup}\r\n\r\n\t\t\t\t\t\t\t\tstartIcon={<DesignServicesIcon />}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Steps\")}\r\n\t\t\t\t\t\t\t\t</Button>\r\n\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={handleTitlePopup}\r\n\t\t\t\t\t\t\t\tstartIcon={<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: elements }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ height: \"23px\" }}\r\n\t\t\t\t\t\t\t\t/>}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Title & SubTitle\")}\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t</>\r\n\r\n\t\t\t\t\t\t\t)}\r\n\r\n\r\n\t\t\t\t\t\t\t{checkpointsPopup && (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<Checkpoints/>\r\n\t\t\t\t\t\t\t\t</>\r\n\r\n\t\t\t\t\t\t\t)}\r\n\r\n\r\n\t\t\t\t\t\t\t{/* Buttons with icons */}\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\tonClick={toggleCanvasSettings}\r\n\t\t\t\t\t\t\t\tstartIcon={<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: overlay }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ height: \"23px\" }}\r\n\t\t\t\t\t\t\t\t/>}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{translate(\"Canvas\")}\r\n\t\t\t\t\t\t\t</Button>\r\n\r\n\t\t\t\t\t\t\t{selectedTemplate != \"Checklist\" && (\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={toggleElementsSettings}\r\n\t\t\t\t\t\t\t\t\t\tstartIcon={\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: elements }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ height: \"23px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Elements\")}\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\r\n\t\t\t\t\t\t\t\t{([\"Tooltip\", \"Announcement\"].includes(selectedTemplate) ||\r\n\t\t\t\t\t\t\t\t\t(selectedTemplate === \"Tour\" && [\"Tooltip\", \"Announcement\"].includes(selectedTemplateTour))) && (\r\n\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={toggleOverlaySettings}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstartIcon={\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: overlay }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ height: \"23px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t// sx={{\r\n\t\t\t\t\t\t\t\t\t\t\t// \topacity: selectedTemplate === \"Banner\" ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\t// }}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Overlay\")}\r\n\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t{/* {selectedTemplate != \"Checklist\" && (\r\n\t\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\t\ttitle={translate(\"Coming soon\")}\r\n\t\t\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\tstartIcon={\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: animation }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ height: \"23px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\topacity: 0.5,\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Animation\")}\r\n\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t)} */}\r\n\t\t\t\t\t\t\t{selectedTemplate === \"Checklist\" && (\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\tonClick={handleLaunchSettings}\r\n\t\t\t\t\t\t\t\t\tstartIcon={<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: animation }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ height: \"23px\" }}\r\n\t\t\t\t\t\t\t\t\t/>}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Launcher\")}\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</>\r\n\t\t\t\t)}\r\n\t\t\t</div>\r\n\t\t\t{hotspotPopup && (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<HotspotSettings />\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\r\n\t\t\t{showCanvasSettings && (selectedTemplate === \"Banner\" || selectedTemplateTour === \"Banner\") ? (\r\n\t\t\t\t<PageInteractions\r\n\t\t\t\t\tsetShowCanvasSettings={setShowCanvasSettings}\r\n\t\t\t\t\tbackgroundC={backgroundC}\r\n\t\t\t\t\tsetBackgroundC={setBackgroundC}\r\n\t\t\t\t\tBposition={Bposition}\r\n\t\t\t\t\tsetBposition={setBposition}\r\n\t\t\t\t\tbpadding={bpadding}\r\n\t\t\t\t\tsetbPadding={setbPadding}\r\n\t\t\t\t\tBbordercolor={Bbordercolor}\r\n\t\t\t\t\tsetBBorderColor={setBBorderColor}\r\n\t\t\t\t\tBborderSize={BborderSize}\r\n\t\t\t\t\tsetBBorderSize={setBBorderSize}\r\n\t\t\t\t\tzindeex={zindeex}\r\n\t\t\t\t\tsetZindeex={setZindeex}\r\n\t\t\t\t\tresetHeightofBanner={resetHeightofBanner}\r\n\t\t\t\t/>\r\n\t\t\t) : (\r\n\t\t\t\tshowCanvasSettings && (\r\n\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t<CanvasSettings\r\n\t\t\t\t\t\t\tzindeex={zindeex}\r\n\t\t\t\t\t\t\tsetZindeex={setZindeex}\r\n\t\t\t\t\t\t\tsetShowCanvasSettings={setShowCanvasSettings}\r\n\t\t\t\t\t\t\t// width={width}\r\n\t\t\t\t\t\t\t// height={height}\r\n\t\t\t\t\t\t\t// padding={padding}\r\n\t\t\t\t\t\t\t// borderRadius={borderRadius}\r\n\t\t\t\t\t\t\t// borderColor={borderColor}\r\n\t\t\t\t\t\t\t// backgroundColor={backgroundColor}\r\n\t\t\t\t\t\t\t// selectedPosition={selectedPosition}\r\n\t\t\t\t\t\t\t// setSelectedPosition={setSelectedPosition}\r\n\t\t\t\t\t\t\t// setBorderColor={setBorderColor}\r\n\t\t\t\t\t\t\t// setBackgroundColor={setBackgroundColor}\r\n\t\t\t\t\t\t\t// setWidth={setWidth}\r\n\t\t\t\t\t\t\t// setHeight={setHeight}\r\n\t\t\t\t\t\t\t// setPadding={setPadding}\r\n\t\t\t\t\t\t\t// setBorderRadius={setBorderRadius}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t)\r\n\t\t\t)}\r\n\r\n\t\t\t{showTooltipCanvasSettings &&\r\n\t\t\t(selectedTemplate === \"Tooltip\" ||\r\n\t\t\t\tselectedTemplate === \"Hotspot\" ||\r\n\t\t\t\tselectedTemplateTour === \"Tooltip\" ||\r\n\t\t\t\tselectedTemplateTour === \"Hotspot\") ? (\r\n\t\t\t\t<TooltipCanvasSettings\r\n\t\t\t\t\tsetShowTooltipCanvasSettings={setShowTooltipCanvasSettings}\r\n\t\t\t\t\tbackgroundC={backgroundC}\r\n\t\t\t\t\tsetBackgroundC={setBackgroundC}\r\n\t\t\t\t\tBposition={Bposition}\r\n\t\t\t\t\tsetBposition={setBposition}\r\n\t\t\t\t\tbpadding={bpadding}\r\n\t\t\t\t\tsetbPadding={setbPadding}\r\n\t\t\t\t\tBbordercolor={Bbordercolor}\r\n\t\t\t\t\tsetBBorderColor={setBBorderColor}\r\n\t\t\t\t\tBborderSize={BborderSize}\r\n\t\t\t\t\tsetBBorderSize={setBBorderSize}\r\n\t\t\t\t\tzindeex={zindeex}\r\n\t\t\t\t\tsetZindeex={setZindeex}\r\n\t\t\t\t/>\r\n\t\t\t) : (\r\n\t\t\t\t\"\"\r\n\t\t\t)}\r\n\r\n\r\n\r\n{showChecklistCanvasSettings &&\r\n\t\t\t(selectedTemplate === \"Checklist\"\r\n\t\t\t) ? (\r\n\t\t\t\t<ChecklistCanvasSettings\r\n\t\t\t\t\tsetShowChecklistCanvasSettings={setShowChecklistCanvasSettings}\r\n\t\t\t\t\tbackgroundC={backgroundC}\r\n\t\t\t\t\tsetBackgroundC={setBackgroundC}\r\n\t\t\t\t\tBposition={Bposition}\r\n\t\t\t\t\tsetBposition={setBposition}\r\n\t\t\t\t\tbpadding={bpadding}\r\n\t\t\t\t\tsetbPadding={setbPadding}\r\n\t\t\t\t\tBbordercolor={Bbordercolor}\r\n\t\t\t\t\tsetBBorderColor={setBBorderColor}\r\n\t\t\t\t\tBborderSize={BborderSize}\r\n\t\t\t\t\tsetBBorderSize={setBBorderSize}\r\n\t\t\t\t\tzindeex={zindeex}\r\n\t\t\t\t\tsetZindeex={setZindeex}\r\n\t\t\t\t/>\r\n\t\t\t) : (\r\n\t\t\t\t\"\"\r\n\t\t\t)}\r\n\r\n\r\n\r\n\r\n\t\t\t{showOverlay && (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<OverlaySettings\r\n\t\t\t\t\t\tsetOverlaySettings={setOverlaySettings}\r\n\t\t\t\t\t\tselectedTemplate={selectedTemplate}\r\n\t\t\t\t\t\tonStatusChange={handleStatusChange}\r\n\t\t\t\t\t\tsetOverLays={setOverLays}\r\n\t\t\t\t\t\tsetDesignPopup={setDesignPopup}\r\n\t\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\t\tsetMenuPopup={setMenuPopup}\r\n\t\t\t\t\t/>\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\t\t\t{showElementsSettings && designPopup && (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Elementssettings\r\n\t\t\t\t\t\tsetShowElementsSettings={setShowElementsSettings}\r\n\t\t\t\t\t\tsetDesignPopup={setDesignPopup}\r\n\t\t\t\t\t\tsetMenuPopup={setMenuPopup}\r\n\t\t\t\t\t\tresetHeightofBanner={resetHeightofBanner}\r\n\t\t\t\t\t/>\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\t\t\t{showCustomCSS && (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<CustomCSS />\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\t\t\t{showAnimation && (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<AnimationSettings selectedTemplate={selectedTemplate} />\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\t\t</div>\r\n\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default DesignMenu;\r\nfunction setTooltipXaxis(arg0: string) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction setTooltipYaxis(arg0: string) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction setTooltipPosition(arg0: string) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction setTooltipBorderradius(arg0: string) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction setTooltipPadding(arg0: string) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction setTooltipWidth(arg0: string) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction updateCanvasInTooltip(CANVAS_DEFAULT_VALUE: any) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction setElementSelected(arg0: boolean) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction setIsTooltipPopup(arg0: boolean) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD;AACA,SAASC,MAAM,EAAEC,GAAG,EAAEC,UAAU,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,QAAc,eAAe;AAC3F,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,kBAAkB,MAAM,oCAAoC;AAGnE,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,eAAe,MAAM,WAAW;AACvC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,gBAAgB,MAAM,qDAAqD;AAClF,OAAOC,iBAAiB,MAAM,aAAa;AAC3C,OAAO,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,yBAAyB;AAEpD,OAAOC,eAAe,MAAM,4BAA4B;AACxD,SAASC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,0BAA0B;AAC9F,OAAOC,qBAAqB,MAAM,gDAAgD;AAClF,SAAwBC,gBAAgB,QAAQ,qBAAqB;AACrE,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,uBAAuB,MAAM,sCAAsC;AAC1E,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAO,8BAA8B;AACrC,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,UAAU,GAAGA,CAAC;EACnBC,KAAK;EACLC,MAAM;EACNC,QAAQ;EACRC,WAAW;EACXC,WAAW;EACXC,cAAc;EACdC,SAAS;EACTC,YAAY;EACZC,QAAQ;EACRC,WAAW;EACXC,YAAY;EACZC,eAAe;EACfC,WAAW;EACXC,cAAc;EACdC,OAAO;EACPC,UAAU;EACVC,cAAc;EACdC,gBAAgB;EAChBC,WAAW;EACXC,gBAAgB;EAChBC,gBAAgB;EAChBC,eAAe;EACfC;AAqBC,CAAC,KAAK;EAAAC,EAAA;EACP,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAG/B,cAAc,CAAC,CAAC;EACzC;EACA,MAAM,CAACgC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACgE,2BAA2B,EAAEC,8BAA8B,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACrF;EACA,MAAM,CAACkE,WAAW,EAAEC,kBAAkB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACsE,aAAa,EAAEC,gBAAgB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACwE,aAAa,EAAEC,gBAAgB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0E,QAAQ,EAAEC,WAAW,CAAC,GAAG3E,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAAC4E,eAAe,EAAEC,kBAAkB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8E,eAAe,EAAEC,kBAAkB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAC7D;EACA;EACA,MAAM,CAACgF,MAAM,EAAEC,SAAS,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM;IAAEkF,iBAAiB;IAAEC,cAAc;IAAEC;EAAkB,CAAC,GAAG3D,WAAW,CAAE4D,KAAU,IAAKA,KAAK,CAAC;EACnG,MAAM;IACL;IACAC,OAAO;IACPC,UAAU;IACVC,QAAQ;IACRC,WAAW;IACXC,MAAM;IACNC,SAAS;IACTC,UAAU;IACVC,aAAa;IACbC,cAAc;IACdC,WAAW;IACXC,kBAAkB;IAClBC,eAAe;IACfC,cAAc;IACdC,iBAAiB;IACjBC,UAAU;IACVC,sBAAsB;IACtBC,eAAe;IACfC,aAAa;IACbC,UAAU;IACVC,YAAY;IACZC,yBAAyB;IACzBC,4BAA4B;IAC5BC,yBAAyB;IACzBC,qBAAqB;IACrBC,sBAAsB;IACtBC,oBAAoB;IACpBC,oBAAoB;IACpBC,cAAc;IACdC,eAAe;IACfC,kBAAkB;IAClBC,eAAe;IACfC,kBAAkB;IAClBC,qBAAqB;IACrBC,YAAY;IACZC,eAAe;IACfC,iBAAiB;IACjBC,oBAAoB;IACpBC,4BAA4B;IAC5BC,oBAAoB;IACpBC,kBAAkB;IAClBC,qBAAqB;IACrBC,WAAW;IACXC,cAAc;IACdC,WAAW;IACXC,iBAAiB;IACjBC,oBAAoB;IACpBC,gBAAgB;IAChBC,yBAAyB;IACzBC,gBAAgB;IAChBC,OAAO;IACPC,oBAAoB;IACpBC,QAAQ;IACRC,WAAW;IACXC,iBAAiB;IACjBC,aAAa;IACbC,gBAAgB;IAClBC,mBAAmB;IACjBC,oBAAoB;IACpBC,uBAAuB;IACvBC,gBAAgB;IAChBC,mBAAmB;IACnBC,YAAY;IACZC;EACD,CAAC,GAAGrI,cAAc,CAAEsE,KAAU,IAAKA,KAAK,CAAC;EACzC,MAAMgE,UAAU,GAAGtI,cAAc,CAAEsE,KAAK,IAAKA,KAAK,CAACgE,UAAU,CAAC;EAE9DtJ,SAAS,CAAC,MAAM;IACfgE,qBAAqB,CAAC,KAAK,CAAC;IAC5BE,8BAA8B,CAAC,KAAK,CAAC;IACrC0C,4BAA4B,CAAC,KAAK,CAAC;IACnCxC,kBAAkB,CAAC,KAAK,CAAC;IACzBE,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,gBAAgB,CAAC,KAAK,CAAC;IACvBE,gBAAgB,CAAC,KAAK,CAAC;IACvB6B,eAAe,CAAC,KAAK,CAAC;IACtBC,aAAa,CAAC,KAAK,CAAC;IACpByC,uBAAuB,CAAC,KAAK,CAAC;EAC/B,CAAC,EAAE,CAAC3F,gBAAgB,EAAEmF,oBAAoB,CAAC,CAAC;EAC5C;EACA;EACA,MAAMc,oBAAoB,GAAGA,CAAA,KAAM;IAClC,IACCjG,gBAAgB,KAAK,SAAS,IAC9BA,gBAAgB,KAAK,SAAS,IAC9BmF,oBAAoB,KAAK,SAAS,IAClCA,oBAAoB,KAAK,SAAS,EACjC;MACD7B,4BAA4B,CAAC,CAACD,yBAAyB,CAAC;IACzD,CAAC,MACI,IAAIrD,gBAAgB,KAAK,WAAW,EACzC;MACCY,8BAA8B,CAAC,CAACD,2BAA2B,CAAC;IAC7D,CAAC,MACI;MACJD,qBAAqB,CAAC,CAACD,kBAAkB,CAAC;IAC3C;EACD,CAAC;EACD,MAAMyF,qBAAqB,GAAGA,CAAA,KAAM;IACnCC,YAAY,CAAC,KAAK,CAAC;IACnBrF,kBAAkB,CAAC,CAACD,WAAW,CAAC;EACjC,CAAC;EACD,MAAMuF,kBAAkB,GAAGA,CAAA,KAAM;IAChC;IACAnD,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACvBoD,UAAU,CAAC,MAAM;MAChBpD,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACxB,CAAC,EAAE,CAAC,CAAC;EACN,CAAC;EAED,MAAMqD,gBAAgB,GAAGA,CAAA,KACzB;IACCpD,aAAa,CAAC,IAAI,CAAC;EACpB,CAAC;EACD,MAAMqD,qBAAqB,GAAGA,CAAA,KAC9B;IACCV,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAGA,MAAMW,qBAAqB,GAAGA,CAAA,KAAM;IACnC;IACA;IACA;IACA;IACA;IACA;IACA/C,sBAAsB,CAAC,GAAG,CAAC;IAC3B;IACA;IACA;IACA;IACAO,kBAAkB,CAAC,IAAI,CAAC;IACxByC,iBAAiB,CAAC,KAAK,CAAC;IACxBnD,4BAA4B,CAAC,KAAK,CAAC;EACpC,CAAC;EAED5G,SAAS,CAAC,MAAM;IAEf,MAAMgK,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI5E,cAAc,IAAI,EAAE,IAAIA,cAAc,IAAI,IAAI,EAAE;QAAA,IAAA6E,qBAAA;QACrD;QACA,MAAMC,eAAe,GAAGrC,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAGK,WAAW,GAAG,CAAC,CAAC;QAE/D,IAAIgC,eAAe,aAAfA,eAAe,gBAAAD,qBAAA,GAAfC,eAAe,CAAEC,MAAM,cAAAF,qBAAA,eAAvBA,qBAAA,CAAyBG,QAAQ,EAAE;UACrC;UACA,MAAMC,cAAc,GAAGH,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE,QAAQ,IAChEJ,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE,QAAQ,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;UAErDC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEJ,cAAc,EAAE,WAAW,EAAEH,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE,QAAQ,CAAC;UAElH,IAAID,cAAc,EAAE;YACrB5C,eAAe,CAAC,QAAQ,CAAC;YACzBQ,cAAc,CAAC,IAAI,CAAC;YACpBF,qBAAqB,CAAC,IAAI,CAAC;YAC3B;YACAe,gBAAgB,CAACoB,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE,QAAQ,IAAI,EAAE,CAAC;YAChE3C,oBAAoB,CAACuC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACM,UAAU,IAAIR,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACO,UAAU,IAAI,EAAE,CAAC;YACpHrB,UAAU,CAACY,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE,QAAQ,IAAI,EAAE,CAAC;YAE1DE,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE;cAC5DjD,YAAY,EAAE,QAAQ;cACtBQ,WAAW,EAAE,IAAI;cACjBa,aAAa,EAAEqB,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE,QAAQ;cACvD5C,iBAAiB,EAAEwC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACM,UAAU;cAC7DlC,OAAO,EAAE0B,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE;YAC1C,CAAC,CAAC;UACD,CAAC,MAAM;YACR7C,eAAe,CAAC,SAAS,CAAC;YAC1BQ,cAAc,CAAC,KAAK,CAAC;YACrBF,qBAAqB,CAAC,KAAK,CAAC;YAC5Be,gBAAgB,CAAC,EAAE,CAAC;YACpBnB,oBAAoB,CAAC,EAAE,CAAC;YACxB2B,UAAU,CAAC,EAAE,CAAC;YAEdkB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;UAC/C;QACF,CAAC,MAAM;UAAA,IAAAG,kBAAA,EAAAC,qBAAA,EAAAC,iBAAA;UACL;UACA,MAAMC,IAAI,GAAG,MAAMtJ,uBAAuB,CAAC2D,cAAc,EAAEgE,YAAY,EAAEC,eAAe,CAAC;UACzF,MAAM2B,SAAS,GAAGD,IAAI,aAAJA,IAAI,wBAAAH,kBAAA,GAAJG,IAAI,CAAEE,YAAY,cAAAL,kBAAA,wBAAAC,qBAAA,GAAlBD,kBAAA,CAAoBM,SAAS,cAAAL,qBAAA,uBAA7BA,qBAAA,CAAgC3C,WAAW,GAAG,CAAC,CAAC;UAElE,IAAI8C,SAAS,aAATA,SAAS,gBAAAF,iBAAA,GAATE,SAAS,CAAEG,MAAM,cAAAL,iBAAA,eAAjBA,iBAAA,CAAmBM,QAAQ,EAAE;YAClC,MAAMf,cAAc,GAAGW,SAAS,CAACG,MAAM,CAACC,QAAQ,CAACd,QAAQ,IACvDU,SAAS,CAACG,MAAM,CAACC,QAAQ,CAACd,QAAQ,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;YAElD,IAAIF,cAAc,EAAE;cAClB5C,eAAe,CAAC,QAAQ,CAAC;cACzBQ,cAAc,CAAC,IAAI,CAAC;cACpBF,qBAAqB,CAAC,IAAI,CAAC;cAC3B;cACAe,gBAAgB,CAACkC,SAAS,CAACG,MAAM,CAACC,QAAQ,CAACd,QAAQ,IAAI,EAAE,CAAC;cAC1D3C,oBAAoB,CAACqD,SAAS,CAACG,MAAM,CAACC,QAAQ,CAACV,UAAU,IAAI,EAAE,CAAC;cAChEpB,UAAU,CAAC0B,SAAS,CAACG,MAAM,CAACC,QAAQ,CAACd,QAAQ,IAAI,EAAE,CAAC;YACtD,CAAC,MAAM;cACL7C,eAAe,CAAC,SAAS,CAAC;cAC1BQ,cAAc,CAAC,KAAK,CAAC;cACrBF,qBAAqB,CAAC,KAAK,CAAC;cAC5Be,gBAAgB,CAAC,EAAE,CAAC;cACpBnB,oBAAoB,CAAC,EAAE,CAAC;cACxB2B,UAAU,CAAC,EAAE,CAAC;YAChB;UACC,CAAC,MAAM;YACR7B,eAAe,CAAC,SAAS,CAAC;YAC1BQ,cAAc,CAAC,KAAK,CAAC;YACrBF,qBAAqB,CAAC,KAAK,CAAC;YAC5Be,gBAAgB,CAAC,EAAE,CAAC;YACpBnB,oBAAoB,CAAC,EAAE,CAAC;YACxB2B,UAAU,CAAC,EAAE,CAAC;UACb;QACF;MACC;IACF,CAAC;IACDU,iBAAiB,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC9B,WAAW,EAAEL,oBAAoB,CAAC,CAAC;EAGzC,MAAMwD,uBAAuB,GAAIC,OAAoB,IAAK;IACzDA,OAAO,CAACC,eAAe,CAAC,UAAU,CAAC;IACnCD,OAAO,CAACE,KAAK,CAACC,OAAO,GAAG,EAAE;IAC1BH,OAAO,CAACE,KAAK,CAACE,aAAa,GAAG,OAAO;EACtC,CAAC;EACD,MAAM,CAACC,SAAS,EAAElC,YAAY,CAAC,GAAGxJ,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM2L,iBAAiB,GAAGA,CAAA,KAAM;IAC/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,eAAe,GAAGC,QAAQ,CAACC,cAAc,CAAC,sBAAsB,CAAC;IACvE,MAAMC,eAAe,GAAGF,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;IACjEhD,mBAAmB,CAAC,KAAK,CAAC;;IAEzB;IACA1F,cAAc,CAAC,KAAK,CAAC;IACrBiE,kBAAkB,CAAC,KAAK,CAAC;IACzBC,qBAAqB,IAAI8D,uBAAuB,CAAC9D,qBAAqB,CAAC;IAExEwB,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;;EAEA;EACA,MAAMkD,iBAAiB,GAAIC,QAAa,IAAK;IAC5C1B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEyB,QAAQ,CAAC;IACxCnD,mBAAmB,CAAC,IAAI,CAAC;IAEzBoD,iBAAiB,CAACD,QAAQ,CAAC;IAC3B9D,oBAAoB,CAAEgE,IAAS,IAAMA,IAAI,KAAKF,QAAQ,GAAG,IAAI,GAAGA,QAAS,CAAC;;IAE1E;IACA;EACD,CAAC;EAED,MAAMG,oBAAoB,GAAGA,CAAA,KAC7B;IACCpD,uBAAuB,CAAC,IAAI,CAAC;EAC7B,CAAC;EACF,MAAMkD,iBAAiB,GAAIG,IAAS,IAAK;IACxC9B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE6B,IAAI,CAAC;IAExD,IAAIA,IAAI,KAAK,CAAC,EAAE;MACf;MACA9B,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAC9ChD,eAAe,CAAC,SAAS,CAAC;MAC1BE,oBAAoB,CAAC,EAAE,CAAC;MACxBmB,gBAAgB,CAAC,EAAE,CAAC;MACpBQ,UAAU,CAAC,EAAE,CAAC;MACdrB,cAAc,CAAC,KAAK,CAAC;MACrBF,qBAAqB,CAAC,KAAK,CAAC;;MAE5B;MACA,MAAMwE,qBAAqB,GAAG;QAC7BC,QAAQ,EAAE,SAAS;QACnBlC,QAAQ,EAAE,EAAE;QACZmC,WAAW,EAAE,EAAE;QACf/B,UAAU,EAAE,EAAE;QACdgC,EAAE,EAAE;MACL,CAAC;MACD9E,4BAA4B,CAAC2E,qBAAqB,CAAC;IAEpD,CAAC,MAAM,IAAID,IAAI,KAAK,CAAC,EAAE;MAAA,IAAAK,sBAAA,EAAAC,sBAAA;MACtB;MACApC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7ChD,eAAe,CAAC,QAAQ,CAAC;MACzBQ,cAAc,CAAC,IAAI,CAAC;MACpBF,qBAAqB,CAAC,IAAI,CAAC;;MAE3B;MACA,MAAMmC,eAAe,GAAGrC,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAGK,WAAW,GAAG,CAAC,CAAC;MAC/DsC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEP,eAAe,CAAC;MAEzD,IAAIA,eAAe,aAAfA,eAAe,gBAAAyC,sBAAA,GAAfzC,eAAe,CAAEC,MAAM,cAAAwC,sBAAA,gBAAAC,sBAAA,GAAvBD,sBAAA,CAAyBvC,QAAQ,cAAAwC,sBAAA,eAAjCA,sBAAA,CAAmCtC,QAAQ,EAAE;QAChD,MAAM4B,QAAQ,GAAGhC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE,QAAQ;QACzD,MAAMK,UAAU,GAAGT,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACM,UAAU;;QAE7D;QACA5B,gBAAgB,CAACoD,QAAQ,CAAC;QAC1BvE,oBAAoB,CAACgD,UAAU,IAAI,EAAE,CAAC;QACtCrB,UAAU,CAAC4C,QAAQ,CAAC;QAEpB1B,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAE;UACzEyB,QAAQ;UACRvB,UAAU;UACV9B,aAAa,EAAEqD;QAChB,CAAC,CAAC;MACH,CAAC,MAAM;QACN1B,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC/D;IACD;EACD,CAAC;EACD,MAAMoC,sBAAsB,GAAGA,CAACC,KAAU,EAAEC,IAAS,KAAK;IACzD,MAAMR,qBAAqB,GAAG;MAC7BC,QAAQ,EAAEhF,YAAY;MACtB8C,QAAQ,EAAEwC,KAAK;MACfL,WAAW,EAAE,EAAE;MACf/B,UAAU,EAAEqC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEA,IAAI;MACtBL,EAAE,EAAEI,KAAK,CAAE;IACZ,CAAC;IAEDlF,4BAA4B,CAAC2E,qBAAqB,CAAC;EACpD,CAAC;EAED,MAAMS,oBAAoB,GAAIC,KAAU,IAAK;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;IAC5C,MAAMC,aAAa,GAAGJ,KAAK,CAACK,MAAM,CAACR,KAAK;IACxCtC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE4C,aAAa,CAAC;;IAElD;IACA,MAAME,eAAe,IAAAL,qBAAA,GAAGrF,oBAAoB,CAACK,WAAW,GAAG,CAAC,CAAC,cAAAgF,qBAAA,wBAAAC,sBAAA,GAArCD,qBAAA,CAAuCM,UAAU,cAAAL,sBAAA,uBAAjDA,sBAAA,CAAmDM,IAAI,CAC7EC,SAAc,IAAKA,SAAS,CAACpB,IAAI,KAAK,QACxC,CAAC;IACD,MAAMqB,cAAc,GAAGJ,eAAe,aAAfA,eAAe,wBAAAH,qBAAA,GAAfG,eAAe,CAAEK,OAAO,cAAAR,qBAAA,uBAAxBA,qBAAA,CAA0BK,IAAI,CACnDI,MAAW,IAAKA,MAAM,CAACC,EAAE,KAAKT,aAChC,CAAC;IAED7C,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkD,cAAc,CAAC;IAC/CnD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE8C,eAAe,CAAC;;IAEjD;IACAzE,gBAAgB,CAACuE,aAAa,CAAC;IAC/B1F,oBAAoB,CAAC,CAAAgG,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEZ,IAAI,KAAIM,aAAa,CAAC,CAAC,CAAC;IAC7D/D,UAAU,CAAC+D,aAAa,CAAC;IACzB5F,eAAe,CAAC,QAAQ,CAAC;;IAEzB;IACAoF,sBAAsB,CAACQ,aAAa,EAAEM,cAAc,CAAC;;IAErD;IACA5E,mBAAmB,CAAC,IAAI,CAAC;IAEzByB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;MACnD5B,aAAa,EAAEwE,aAAa;MAC5B3F,iBAAiB,EAAE,CAAAiG,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEZ,IAAI,KAAIM,aAAa;MACxD7E,OAAO,EAAE6E,aAAa;MACtB7F,YAAY,EAAE;IACf,CAAC,CAAC;EACH,CAAC;EAED,MAAMuG,sBAAsB,GAAGA,CAAA,KAAM;IACpCtE,YAAY,CAAC,KAAK,CAAC;IACnBnF,uBAAuB,CAAC,IAAI,CAAC;IAC7B;IACA,oBACCrC,OAAA,CAAAE,SAAA;MAAA6L,QAAA,GACE3J,oBAAoB,IAAId,WAAW,iBACnCtB,OAAA,CAAC9B,GAAG;QAAA6N,QAAA,eACH/L,OAAA,CAACtB,gBAAgB;UAChB2D,uBAAuB,EAAEA,uBAAwB;UACjDjB,cAAc,EAAEA;QAAe;UAAA4K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACL,EAAC,GAEH;IAAA,eAAE,CAAC;EAEL,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC7B3J,gBAAgB,CAAC,CAACD,aAAa,CAAC,CAAC,CAAC;EACnC,CAAC;EACD,MAAM6J,eAAe,GAAGA,CAAA,KAAM;IAC7B9J,gBAAgB,CAAC,CAACD,aAAa,CAAC,CAAC,CAAC;EACnC,CAAC;EAED,MAAMgK,WAAW,GAAGA,CAAA,KAAM;IACzBrJ,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;IAClB7B,cAAc,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMmL,uBAAuB,GAAIzD,IAAS,IAAK,CAAC,CAAC;EACjD,IAAI,CAAC9F,MAAM,EAAE,OAAO,IAAI;EACxB,MAAMwJ,kBAAkB,GAAIC,MAAe,IAAK;IAC/CtI,iBAAiB,CAACsI,MAAM,CAAC;EAC1B,CAAC;EAED;IAAA;IACC;IACAzM,OAAA;MACC6L,EAAE,EAAC,mBAAmB;MACtBa,SAAS,EAAC,mBAAmB;MAAAX,QAAA,gBAE7B/L,OAAA;QAAK0M,SAAS,EAAC,eAAe;QAAAX,QAAA,gBAC7B/L,OAAA;UAAK0M,SAAS,EAAC,qBAAqB;UAAAX,QAAA,gBACnC/L,OAAA;YAAK0M,SAAS,EAAC,aAAa;YAAAX,QAAA,EAAElK,SAAS,CAAC,QAAQ;UAAC;YAAAmK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxDnM,OAAA,CAAC5B,UAAU;YACVuO,IAAI,EAAC,OAAO;YACZ,cAAY9K,SAAS,CAAC,OAAO,CAAE;YAC/B+K,OAAO,EAAEN,WAAY;YAAAP,QAAA,eAErB/L,OAAA,CAACzB,SAAS;cAAAyN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,EAGL3H,UAAU,iBACVxE,OAAA,CAAAE,SAAA;UAAA6L,QAAA,eACC/L,OAAA,CAACH,aAAa;YAAAmM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC,gBACf,CACF,EAEApF,oBAAoB,iBACpB/G,OAAA,CAACL,gBAAgB;UAAAqM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAElB,EACAzC,SAAS,iBACT1J,OAAA,CAAAE,SAAA;UAAA6L,QAAA,eACC/L,OAAA;YAAK0M,SAAS,EAAC,gBAAgB;YAAAX,QAAA,GAC7B,CAAC1K,gBAAgB,KAAK,SAAS,IAAImF,oBAAoB,KAAK,SAAS,kBACrExG,OAAA,CAAC9B,GAAG;cACHwO,SAAS,EAAC,oBAAoB;cAC9BE,OAAO,EAAEjD,iBAAkB;cAC3BkD,EAAE,EAAE;gBAACC,MAAM,EAAC;cAAS,CAAE;cAAAf,QAAA,gBAEvB/L,OAAA,CAAC7B,UAAU;gBACVuO,SAAS,EAAC,qBAAqB;gBAC/BG,EAAE,EAAE;kBAAEE,KAAK,EAAE;gBAAU,CAAE;gBAAAhB,QAAA,EAExBlK,SAAS,CAAC,kBAAkB;cAAC;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACbnM,OAAA;gBACC0M,SAAS,EAAC,qBAAqB;gBAC/BM,uBAAuB,EAAE;kBAAEC,MAAM,EAAE5N;gBAAS,CAAE;gBAC9CkK,KAAK,EAAE;kBAAEjG,OAAO,EAAE,KAAK;kBAAE4J,WAAW,EAAE;gBAAO;cAAE;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACL,EACA,CAAC9K,gBAAgB,KAAK,SAAS,IAAImF,oBAAoB,KAAK,SAAS,kBACrExG,OAAA;cACC;cACAuJ,KAAK,EAAE;gBACN4D,YAAY,EAAE,KAAK;gBACnBC,YAAY,EAAE,MAAM;gBACpB9J,OAAO,EAAE,UAAU;gBACnB+J,UAAU,EAAE,SAAS;gBACrBC,aAAa,EAAE;cAChB,CAAE;cAAAvB,QAAA,gBASF/L,OAAA;gBAAKuJ,KAAK,EAAE;kBAAEgE,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,YAAY;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAACf,SAAS,EAAC,cAAc;gBAAAX,QAAA,gBAC5G/L,OAAA;kBACCuJ,KAAK,EAAE;oBACN8D,UAAU,EAAE,yBAAyB;oBACrCD,YAAY,EAAE,OAAO;oBACrB9J,OAAO,EAAE;kBACV,CAAE;kBAAAyI,QAAA,eAEF/L,OAAA,CAACT,gBAAgB;oBAChBgK,KAAK,EAAE;sBACNwD,KAAK,EAAE,qBAAqB;sBAC5B1M,MAAM,EAAE,MAAM;sBACdD,KAAK,EAAE;sBACP;sBACA;oBACD;kBAAE;oBAAA4L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACPnM,OAAA,CAAC7B,UAAU;kBAAC0O,EAAE,EAAE;oBAAEE,KAAK,EAAE,SAAS;oBAAEW,UAAU,EAAE;kBAAM,CAAE;kBAAA3B,QAAA,EAAElK,SAAS,CAAC,iBAAiB;gBAAC;kBAAAmK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC,eAENnM,OAAA;gBAAKuJ,KAAK,EAAE;kBAAEgE,OAAO,EAAE,MAAM;kBAAEI,GAAG,EAAE,KAAK;kBAAEC,SAAS,EAAE;gBAAO,CAAE;gBAAA7B,QAAA,gBAE9D/L,OAAA;kBACC4M,OAAO,EAAEA,CAAA,KAAM5C,iBAAiB,CAAC,CAAC,CAAE;kBACpCT,KAAK,EAAE;oBACNgE,OAAO,EAAE,MAAM;oBACfC,cAAc,EAAE,YAAY;oBAC5BC,UAAU,EAAE,QAAQ;oBACpBpN,MAAM,EAAE,MAAM;oBACd+M,YAAY,EAAE,KAAK;oBACnBN,MAAM,EAAE,SAAS;oBACjB7I,eAAe,EACdsB,YAAY,KAAK,SAAS,GAAG,yBAAyB,GAAG,yBAAyB;oBAEnFsI,MAAM,EAAEtI,YAAY,KAAK,SAAS,GAAG,+BAA+B,GAAG,uBAAuB;oBAC9FnF,KAAK,EAAE;kBACR,CAAE;kBAAA2L,QAAA,eASF/L,OAAA,CAAC7B,UAAU;oBAAC0O,EAAE,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAEzJ,OAAO,EAAE,OAAO;sBAAEwK,QAAQ,EAAE;oBAAkB,CAAE;oBAAA/B,QAAA,EAClFlK,SAAS,CAAC,eAAe;kBAAC;oBAAAmK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eAGNnM,OAAA;kBACC4M,OAAO,EAAEA,CAAA,KAAM5C,iBAAiB,CAAC,CAAC,CAAE;kBACpCT,KAAK,EAAE;oBACNgE,OAAO,EAAE,MAAM;oBACfC,cAAc,EAAE,YAAY;oBAC5BC,UAAU,EAAE,QAAQ;oBACpBpN,MAAM,EAAE,MAAM;oBACd+M,YAAY,EAAE,KAAK;oBACnBN,MAAM,EAAE,SAAS;oBACjB7I,eAAe,EACdsB,YAAY,KAAK,SAAS,GAAG,yBAAyB,GAAG,yBAAyB;oBAEnFsI,MAAM,EAAEtI,YAAY,KAAK,SAAS,GAAG,+BAA+B,GAAG,uBAAuB;oBAC9FnF,KAAK,EAAE;kBACR,CAAE;kBAAA2L,QAAA,eASF/L,OAAA,CAAC7B,UAAU;oBAAC0O,EAAE,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAEzJ,OAAO,EAAE,QAAQ;sBAAEwK,QAAQ,EAAE;oBAAkB,CAAE;oBAAA/B,QAAA,EACnFlK,SAAS,CAAC,cAAc;kBAAC;oBAAAmK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,EACLpG,WAAW,iBACX/F,OAAA;gBAAA+L,QAAA,eACC/L,OAAA,CAAC9B,GAAG;kBACHwO,SAAS,EAAC,gBAAgB;kBAC1BG,EAAE,EAAE;oBACHkB,MAAM,EAAE,cAAc;oBACtBZ,YAAY,EAAE,KAAK;oBACnBC,YAAY,EAAE,MAAM;oBACpBC,UAAU,EAAE,SAAS;oBACrBC,aAAa,EAAE;kBAChB,CAAE;kBAAAvB,QAAA,gBAEF/L,OAAA,CAAC7B,UAAU;oBAAC0O,EAAE,EAAE;sBAAGvJ,OAAO,EAAE,KAAK;sBAAEyJ,KAAK,EAAE;oBAAW,CAAE;oBAAAhB,QAAA,EACrDlK,SAAS,CAAC,eAAe;kBAAC;oBAAAmK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,EACZ,CAAC,CAAA6B,sBAAA,EAAAC,sBAAA,KAAM;oBACP;oBACA,MAAM3C,eAAe,GAAG1F,oBAAoB,aAApBA,oBAAoB,wBAAAoI,sBAAA,GAApBpI,oBAAoB,CAAGK,WAAW,GAAG,CAAC,CAAC,cAAA+H,sBAAA,wBAAAC,sBAAA,GAAvCD,sBAAA,CAAyCzC,UAAU,cAAA0C,sBAAA,uBAAnDA,sBAAA,CAAqDzC,IAAI,CAC/EC,SAAc,IAAKA,SAAS,CAACpB,IAAI,KAAK,QACxC,CAAC;oBACD,OAAOiB,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEK,OAAO,gBAC9B3L,OAAA,CAAAE,SAAA;sBAAA6L,QAAA,eACC/L,OAAA,CAAC3B;sBACA;sBAAA;wBACAwM,KAAK,EAAE,CAAC,CAAAqD,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,KAAM;0BACZ;0BACA,MAAMC,cAAc,IAAAR,sBAAA,GAAGtI,oBAAoB,CAACK,WAAW,GAAG,CAAC,CAAC,cAAAiI,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuChG,MAAM,cAAAiG,sBAAA,wBAAAC,sBAAA,GAA7CD,sBAAA,CAA+ChG,QAAQ,cAAAiG,sBAAA,uBAAvDA,sBAAA,CAAyD/F,QAAQ;0BACxF,IAAIqG,cAAc,IAAIpD,eAAe,aAAfA,eAAe,gBAAA+C,sBAAA,GAAf/C,eAAe,CAAEK,OAAO,cAAA0C,sBAAA,eAAxBA,sBAAA,CAA0BM,IAAI,CAAE/C,MAAW,IAAKA,MAAM,CAACC,EAAE,KAAK6C,cAAc,CAAC,EAAE;4BACtG,OAAOA,cAAc;0BACpB;;0BAEA;0BACA,IAAInI,OAAO,IAAI+E,eAAe,aAAfA,eAAe,gBAAAgD,sBAAA,GAAfhD,eAAe,CAAEK,OAAO,cAAA2C,sBAAA,eAAxBA,sBAAA,CAA0BK,IAAI,CAAE/C,MAAW,IAAKA,MAAM,CAACC,EAAE,KAAKtF,OAAO,CAAC,EAAE;4BACxF,OAAOA,OAAO;0BACb;;0BAEA;0BACA,IAAIK,aAAa,IAAI0E,eAAe,aAAfA,eAAe,gBAAAiD,sBAAA,GAAfjD,eAAe,CAAEK,OAAO,cAAA4C,sBAAA,eAAxBA,sBAAA,CAA0BI,IAAI,CAAE/C,MAAW,IAAKA,MAAM,CAACC,EAAE,KAAKjF,aAAa,CAAC,EAAE;4BACpG,OAAOA,aAAa;0BACnB;0BACA;0BACA,IAAInB,iBAAiB,IAAI6F,eAAe,aAAfA,eAAe,gBAAAkD,sBAAA,GAAflD,eAAe,CAAEK,OAAO,cAAA6C,sBAAA,eAAxBA,sBAAA,CAA0BI,MAAM,EAAE;4BAC5D,MAAMC,KAAK,GAAGvD,eAAe,CAACK,OAAO,CAACH,IAAI,CAAEI,MAAW,IAAKA,MAAM,CAACd,IAAI,KAAKrF,iBAAiB,CAAC;4BAC9F,IAAIoJ,KAAK,EAAE,OAAOA,KAAK,CAAChD,EAAE;0BACzB;0BACA;0BACA,IAAI6C,cAAc,IAAIpD,eAAe,aAAfA,eAAe,gBAAAmD,sBAAA,GAAfnD,eAAe,CAAEK,OAAO,cAAA8C,sBAAA,eAAxBA,sBAAA,CAA0BG,MAAM,EAAE;4BACzD,MAAMC,KAAK,GAAGvD,eAAe,CAACK,OAAO,CAACH,IAAI,CAAEI,MAAW,IAAKA,MAAM,CAACC,EAAE,KAAK6C,cAAc,CAAC;4BACzF,IAAIG,KAAK,EAAE,OAAOH,cAAc;0BAC/B;0BACA;0BACA,OAAO,EAAE;wBACX,CAAC,EAAE,CAAE;wBACLI,QAAQ,EAAE/D,oBAAqB;wBAC/BgE,YAAY;wBACZxF,KAAK,EAAE;0BAAEnJ,KAAK,EAAE;wBAAO,CAAE;wBACzByM,EAAE,EAAE;0BACF,oBAAoB,EAAE;4BACvBxM,MAAM,EAAE,MAAM;4BACdD,KAAK,EAAE,MAAM;4BACb4O,GAAG,EAAE;0BACJ;wBACF,CAAE;wBACFC,WAAW,EAAGC,QAAQ,IAAK;0BAAA,IAAAC,sBAAA;0BAC1B;0BACA,IAAI,CAACD,QAAQ,EAAE,OAAOrN,SAAS,CAAC,kBAAkB,CAAC;0BAEnD,MAAMuN,GAAG,GAAG9D,eAAe,aAAfA,eAAe,wBAAA6D,sBAAA,GAAf7D,eAAe,CAAEK,OAAO,cAAAwD,sBAAA,uBAAxBA,sBAAA,CAA0B3D,IAAI,CAAEI,MAAW,IAAKA,MAAM,CAACC,EAAE,KAAKqD,QAAQ,CAAC;0BACnF,OAAO,CAAAE,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEtE,IAAI,KAAIjJ,SAAS,CAAC,kBAAkB,CAAC;wBAClD,CAAE;wBAAAkK,QAAA,gBAEF/L,OAAA,CAAC1B,QAAQ;0BAACuM,KAAK,EAAC,EAAE;0BAACwE,QAAQ;0BAAAtD,QAAA,EACzBlK,SAAS,CAAC,kBAAkB;wBAAC;0BAAAmK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrB,CAAC,EACVb,eAAe,CAACK,OAAO,CAAC2D,GAAG,CAC1B,CAAC1D,MAAW,EAAE2D,WAAmB,kBAClCvP,OAAA,CAAC1B,QAAQ;0BAAmBuM,KAAK,EAAEe,MAAM,CAACC,EAAG;0BAAAE,QAAA,EAC1CH,MAAM,CAACd;wBAAI,GADCyE,WAAW;0BAAAvD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEhB,CAEX,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACM;oBAAC,gBAER,CAAC,GACA,IAAI;kBACT,CAAC,EAAE,CAAC,EAAE,GAAG;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACL;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CACL,EACA9K,gBAAgB,KAAK,SAAS,IAAImF,oBAAoB,KAAK,SAAS,gBACpExG,OAAA,CAAC/B,MAAM;cACNyO,SAAS,EAAC,kBAAkB;cAC5BE,OAAO,EAAEnF,kBAAmB,CAAC;cAAA;cAAAsE,QAAA,gBAE7B/L,OAAA;gBACC0M,SAAS,EAAC,gBAAgB;gBAC1BM,uBAAuB,EAAE;kBAAEC,MAAM,EAAE9N;gBAAY;cAAE;gBAAA6M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACFnM,OAAA,CAAC7B,UAAU;gBACV0O,EAAE,EAAE;kBAACa,UAAU,EAAE;gBAAgB,CAAE;gBAAA3B,QAAA,EAElClK,SAAS,CAAC,SAAS;cAAC;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,GAET,EACA,EACC9K,gBAAgB,KAAK,WAAW,iBACjCrB,OAAA,CAAAE,SAAA;cAAA6L,QAAA,gBACA/L,OAAA,CAAC/B,MAAM;gBACLyO,SAAS,EAAC,kBAAkB;gBAC5BE,OAAO,EAAEhF,qBAAsB;gBAEjC4H,SAAS,eAAExP,OAAA,CAACxB,kBAAkB;kBAAAwN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,EAE/BlK,SAAS,CAAC,OAAO;cAAC;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eAETnM,OAAA,CAAC/B,MAAM;gBACLyO,SAAS,EAAC,kBAAkB;gBAC5BE,OAAO,EAAEjF,gBAAiB;gBAC5B6H,SAAS,eAAExP,OAAA;kBACVgN,uBAAuB,EAAE;oBAAEC,MAAM,EAAE/N;kBAAS,CAAE;kBAC9CqK,KAAK,EAAE;oBAAElJ,MAAM,EAAE;kBAAO;gBAAE;kBAAA2L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAE;gBAAAJ,QAAA,EAEAlK,SAAS,CAAC,kBAAkB;cAAC;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA,eACP,CAEH,EAGAlF,gBAAgB,iBAChBjH,OAAA,CAAAE,SAAA;cAAA6L,QAAA,eACC/L,OAAA,CAACJ,WAAW;gBAAAoM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC,gBACb,CAEF,eAIDnM,OAAA,CAAC/B,MAAM;cACNyO,SAAS,EAAC,kBAAkB;cAC5BE,OAAO,EAAEtF,oBAAqB;cAC9BkI,SAAS,eAAExP,OAAA;gBACVgN,uBAAuB,EAAE;kBAAEC,MAAM,EAAE7N;gBAAQ,CAAE;gBAC7CmK,KAAK,EAAE;kBAAElJ,MAAM,EAAE;gBAAO;cAAE;gBAAA2L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAE;cAAAJ,QAAA,EAEFlK,SAAS,CAAC,QAAQ;YAAC;cAAAmK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EAER9K,gBAAgB,IAAI,WAAW,iBAC/BrB,OAAA;cAAA+L,QAAA,gBACC/L,OAAA,CAAC/B,MAAM;gBACNyO,SAAS,EAAC,kBAAkB;gBAC5BE,OAAO,EAAEd,sBAAuB;gBAChC0D,SAAS,eACRxP,OAAA;kBACCgN,uBAAuB,EAAE;oBAAEC,MAAM,EAAE/N;kBAAS,CAAE;kBAC9CqK,KAAK,EAAE;oBAAElJ,MAAM,EAAE;kBAAO;gBAAE;kBAAA2L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CACD;gBAAAJ,QAAA,EAEAlK,SAAS,CAAC,UAAU;cAAC;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,EAET,CAAC,CAAC,SAAS,EAAE,cAAc,CAAC,CAACsD,QAAQ,CAACpO,gBAAgB,CAAC,IACtDA,gBAAgB,KAAK,MAAM,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAACoO,QAAQ,CAACjJ,oBAAoB,CAAE,kBACzFxG,OAAA,CAAC/B,MAAM;gBACNyO,SAAS,EAAC,kBAAkB;gBAC5BE,OAAO,EAAErF,qBAAsB;gBAC/BiI,SAAS,eACRxP,OAAA;kBACCgN,uBAAuB,EAAE;oBAAEC,MAAM,EAAE7N;kBAAQ,CAAE;kBAC7CmK,KAAK,EAAE;oBAAElJ,MAAM,EAAE;kBAAO;gBAAE;kBAAA2L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;gBAEH;gBACA;gBACA;gBAAA;gBAAAJ,QAAA,EAEClK,SAAS,CAAC,SAAS;cAAC;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CACN,EA8BA9K,gBAAgB,KAAK,WAAW,iBAChCrB,OAAA,CAAC/B,MAAM;cACNyO,SAAS,EAAC,kBAAkB;cAC5BE,OAAO,EAAExC,oBAAqB;cAC9BoF,SAAS,eAAExP,OAAA;gBACVgN,uBAAuB,EAAE;kBAAEC,MAAM,EAAEhO;gBAAU,CAAE;gBAC/CsK,KAAK,EAAE;kBAAElJ,MAAM,EAAE;gBAAO;cAAE;gBAAA2L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAE;cAAAJ,QAAA,EAEFlK,SAAS,CAAC,UAAU;YAAC;cAAAmK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC,gBACL,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,EACL1H,YAAY,iBACZzE,OAAA,CAAC9B,GAAG;QAAA6N,QAAA,eACH/L,OAAA,CAAChB,eAAe;UAAAgN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CACL,EAEArK,kBAAkB,KAAKT,gBAAgB,KAAK,QAAQ,IAAImF,oBAAoB,KAAK,QAAQ,CAAC,gBAC1FxG,OAAA,CAACnB,gBAAgB;QAChBkD,qBAAqB,EAAEA,qBAAsB;QAC7CvB,WAAW,EAAEA,WAAY;QACzBC,cAAc,EAAEA,cAAe;QAC/BC,SAAS,EAAEA,SAAU;QACrBC,YAAY,EAAEA,YAAa;QAC3BC,QAAQ,EAAEA,QAAS;QACnBC,WAAW,EAAEA,WAAY;QACzBC,YAAY,EAAEA,YAAa;QAC3BC,eAAe,EAAEA,eAAgB;QACjCC,WAAW,EAAEA,WAAY;QACzBC,cAAc,EAAEA,cAAe;QAC/BC,OAAO,EAAEA,OAAQ;QACjBC,UAAU,EAAEA,UAAW;QACvBO,mBAAmB,EAAEA;MAAoB;QAAAsK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,GAEFrK,kBAAkB,iBACjB9B,OAAA,CAAC9B,GAAG;QAAA6N,QAAA,eACH/L,OAAA,CAACvB,cAAc;UACdyC,OAAO,EAAEA,OAAQ;UACjBC,UAAU,EAAEA,UAAW;UACvBY,qBAAqB,EAAEA;UACvB;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QAAA;UAAAiK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAEN,EAEAzH,yBAAyB,KACzBrD,gBAAgB,KAAK,SAAS,IAC9BA,gBAAgB,KAAK,SAAS,IAC9BmF,oBAAoB,KAAK,SAAS,IAClCA,oBAAoB,KAAK,SAAS,CAAC,gBACnCxG,OAAA,CAACV,qBAAqB;QACrBqF,4BAA4B,EAAEA,4BAA6B;QAC3DnE,WAAW,EAAEA,WAAY;QACzBC,cAAc,EAAEA,cAAe;QAC/BC,SAAS,EAAEA,SAAU;QACrBC,YAAY,EAAEA,YAAa;QAC3BC,QAAQ,EAAEA,QAAS;QACnBC,WAAW,EAAEA,WAAY;QACzBC,YAAY,EAAEA,YAAa;QAC3BC,eAAe,EAAEA,eAAgB;QACjCC,WAAW,EAAEA,WAAY;QACzBC,cAAc,EAAEA,cAAe;QAC/BC,OAAO,EAAEA,OAAQ;QACjBC,UAAU,EAAEA;MAAW;QAAA6K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,GAEF,EACA,EAIHnK,2BAA2B,IACxBX,gBAAgB,KAAK,WACrB,gBACArB,OAAA,CAACN,uBAAuB;QACvBuC,8BAA8B,EAAEA,8BAA+B;QAC/DzB,WAAW,EAAEA,WAAY;QACzBC,cAAc,EAAEA,cAAe;QAC/BC,SAAS,EAAEA,SAAU;QACrBC,YAAY,EAAEA,YAAa;QAC3BC,QAAQ,EAAEA,QAAS;QACnBC,WAAW,EAAEA,WAAY;QACzBC,YAAY,EAAEA,YAAa;QAC3BC,eAAe,EAAEA,eAAgB;QACjCC,WAAW,EAAEA,WAAY;QACzBC,cAAc,EAAEA,cAAe;QAC/BC,OAAO,EAAEA,OAAQ;QACjBC,UAAU,EAAEA;MAAW;QAAA6K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,GAEF,EACA,EAKAjK,WAAW,iBACXlC,OAAA,CAAC9B,GAAG;QAAA6N,QAAA,eACH/L,OAAA,CAACrB,eAAe;UACfwD,kBAAkB,EAAEA,kBAAmB;UACvCd,gBAAgB,EAAEA,gBAAiB;UACnCqO,cAAc,EAAElD,kBAAmB;UACnCjM,WAAW,EAAEA,WAAY;UACzBa,cAAc,EAAEA,cAAe;UAC/BsB,QAAQ,EAAEA,QAAS;UACnB8E,YAAY,EAAEA;QAAa;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACL,EACA/J,oBAAoB,IAAId,WAAW,iBACnCtB,OAAA,CAAC9B,GAAG;QAAA6N,QAAA,eACH/L,OAAA,CAACtB,gBAAgB;UAChB2D,uBAAuB,EAAEA,uBAAwB;UACjDjB,cAAc,EAAEA,cAAe;UAC/BoG,YAAY,EAAEA,YAAa;UAC3B9F,mBAAmB,EAAEA;QAAoB;UAAAsK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACL,EACA3J,aAAa,iBACbxC,OAAA,CAAC9B,GAAG;QAAA6N,QAAA,eACH/L,OAAA,CAACpB,SAAS;UAAAoN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACL,EACA7J,aAAa,iBACbtC,OAAA,CAAC9B,GAAG;QAAA6N,QAAA,eACH/L,OAAA,CAAClB,iBAAiB;UAACuC,gBAAgB,EAAEA;QAAiB;UAAA2K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CACL;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;;IAEL;EAAA;AAEF,CAAC;AAACxK,EAAA,CAt+BIxB,UAAU;EAAA,QA6CUL,cAAc,EAiFnCf,cAAc,EACCA,cAAc;AAAA;AAAA4Q,EAAA,GA/H5BxP,UAAU;AAw+BhB,eAAeA,UAAU;AACzB,SAASyP,eAAeA,CAACC,IAAY,EAAE;EACtC,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;AAC7C;AAEA,SAASC,eAAeA,CAACF,IAAY,EAAE;EACtC,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;AAC7C;AAEA,SAASE,kBAAkBA,CAACH,IAAY,EAAE;EACzC,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;AAC7C;AAEA,SAAShL,sBAAsBA,CAAC+K,IAAY,EAAE;EAC7C,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;AAC7C;AAEA,SAASG,iBAAiBA,CAACJ,IAAY,EAAE;EACxC,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;AAC7C;AAEA,SAASI,eAAeA,CAACL,IAAY,EAAE;EACtC,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;AAC7C;AAEA,SAASK,qBAAqBA,CAACnL,oBAAyB,EAAE;EACzD,MAAM,IAAI8K,KAAK,CAAC,2BAA2B,CAAC;AAC7C;AAEA,SAASzK,kBAAkBA,CAACwK,IAAa,EAAE;EAC1C,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;AAC7C;AAEA,SAAShI,iBAAiBA,CAAC+H,IAAa,EAAE;EACzC,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;AAC7C;AAAC,IAAAH,EAAA;AAAAS,YAAA,CAAAT,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}