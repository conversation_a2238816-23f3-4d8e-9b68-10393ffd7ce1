{"ast": null, "code": "import React,{useState,useEffect,useRef}from\"react\";import{Popover,Button,Typography,Box,LinearProgress,IconButton,MobileStepper}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import useDrawerStore from\"../../store/drawerStore\";import PerfectScrollbar from'react-perfect-scrollbar';import'react-perfect-scrollbar/dist/css/styles.css';// Helper function to get an element by XPath\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const getElementByXPath=xpath=>{try{const result=document.evaluate(xpath,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null);return result.singleNodeValue;}catch(error){console.error(\"Error evaluating XPath:\",error);return null;}};// Helper function to convert hex color to rgba with opacity\nconst hexToRgba=(hex,opacity)=>{// Remove # if present\nhex=hex.replace('#','');// Parse hex values\nconst r=parseInt(hex.substring(0,2),16);const g=parseInt(hex.substring(2,4),16);const b=parseInt(hex.substring(4,6),16);return`rgba(${r}, ${g}, ${b}, ${opacity})`;};const AnnouncementPopup=_ref=>{var _imageProperties,_imageProperties$Cust,_imageProperties$Cust2,_savedGuideData$Guide2,_savedGuideData$Guide3,_savedGuideData$Guide4,_textFieldProperties$,_textFieldProperties$2,_textFieldProperties$3,_textFieldProperties$4,_imageProperties2,_imageProperties2$Cus,_imageProperties2$Cus2,_imageProperties3,_imageProperties3$Cus,_imageProperties3$Cus2,_imageProperties4,_imageProperties4$Cus,_imageProperties4$Cus2,_imageProperties5,_imageProperties5$Cus,_imageProperties5$Cus2,_imageProperties$,_imageProperties$$Cus,_imageProperties$$Cus2;let{selectedTemplate,handlecloseBannerPopup,backgroundC,Bposition,bpadding,Bbordercolor,BborderSize,guideStep,anchorEl,onClose,onPrevious,onContinue,title,text,imageUrl,videoUrl,previousButtonLabel,continueButtonLabel,currentStep,totalSteps,onDontShowAgain,progress,textFieldProperties,imageProperties,customButton,modalProperties,canvasProperties,htmlSnippet,previousButtonStyles,continueButtonStyles,OverlayValue,savedGuideData}=_ref;const{setCurrentStep,selectedOption,steps,setBannerPreview,bannerPreview,announcementPreview,setAnnouncementPreview,ProgressColor,setProgressColor}=useDrawerStore(state=>state);const[Overlayvalue,setOverlayValue]=useState(false);const handleContinue=()=>{if(selectedTemplate!==\"Tour\"){if(currentStep<totalSteps){setCurrentStep(currentStep+1);onContinue();}}else{var _savedGuideData$Guide;if(currentStep!==(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide=savedGuideData.GuideStep)===null||_savedGuideData$Guide===void 0?void 0:_savedGuideData$Guide.length)){setCurrentStep(currentStep+1);}}};const handlePrevious=()=>{if(currentStep>1){setCurrentStep(currentStep-1);onPrevious();}};// Initialize Overlayvalue state from props only once\nuseEffect(()=>{setOverlayValue(!!OverlayValue);},[]);const imageFit=((_imageProperties=imageProperties[currentStep-1])===null||_imageProperties===void 0?void 0:(_imageProperties$Cust=_imageProperties.CustomImage)===null||_imageProperties$Cust===void 0?void 0:(_imageProperties$Cust2=_imageProperties$Cust[currentStep-1])===null||_imageProperties$Cust2===void 0?void 0:_imageProperties$Cust2.Fit)||'contain';const getAnchorAndTransformOrigins=position=>{switch(position){case\"top-left\":return{anchorOrigin:{vertical:\"top\",horizontal:\"left\"},transformOrigin:{vertical:\"bottom\",horizontal:\"right\"}};case\"top-right\":return{anchorOrigin:{vertical:\"top\",horizontal:\"right\"},transformOrigin:{vertical:\"bottom\",horizontal:\"left\"}};case\"bottom-left\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"left\"},transformOrigin:{vertical:\"top\",horizontal:\"right\"}};case\"bottom-right\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"right\"},transformOrigin:{vertical:\"center\",horizontal:\"left\"}};case\"center-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};case\"top-center\":return{anchorOrigin:{vertical:\"top\",horizontal:\"center\"},transformOrigin:{vertical:\"bottom\",horizontal:\"center\"}};case\"left-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"left\"},transformOrigin:{vertical:\"center\",horizontal:\"right\"}};case\"bottom-center\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};case\"right-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"right\"},transformOrigin:{vertical:\"center\",horizontal:\"left\"}};default:return{anchorOrigin:{vertical:\"center\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};}};const getPopoverPositionStyle=function(){let position=arguments.length>0&&arguments[0]!==undefined?arguments[0]:\"center-center\";// Constants\nconst EDGE_PADDING=12;// Padding from screen edges (in px)\n// Basic reset for all positioning properties\nconst baseStyle={position:'fixed',top:'auto !important',right:'auto',bottom:'auto',left:'auto !important',transform:'none'};// Apply specific positioning based on selected position\nswitch(position){case\"top-left\":return{...baseStyle,top:`${EDGE_PADDING+10}px !important`,left:`${EDGE_PADDING}px !important`};case\"top-center\":return{...baseStyle,top:`${EDGE_PADDING+10}px !important`,left:'50% !important',transform:'translateX(-50%)'};case\"top-right\":return{...baseStyle,top:`${EDGE_PADDING+10}px !important`,right:`${EDGE_PADDING+5}px`};// case \"left-center\":\ncase\"left-center\":return{...baseStyle,top:'54% !important',left:`${EDGE_PADDING}px !important`,transform:'translateY(-50%)'};//case \"center-center\":\ncase\"center-center\":return{...baseStyle,top:'54% !important',left:'50% !important',transform:'translate(-50%, -50%)'};// case \"right-center\":\ncase\"right-center\":return{...baseStyle,top:'54% !important',right:`${EDGE_PADDING+5}px`,transform:'translateY(-50%)'};case\"bottom-left\":return{...baseStyle,bottom:`${EDGE_PADDING}px !important`,left:`${EDGE_PADDING}px !important`};case\"bottom-center\":return{...baseStyle,bottom:`${EDGE_PADDING}px`,left:'50% !important',transform:'translateX(-50%)'};case\"bottom-right\":return{...baseStyle,bottom:`${EDGE_PADDING}px`,right:`${EDGE_PADDING+5}px`};default:return{...baseStyle,top:'50% !important',left:'50% !important',transform:'translate(-50%, -50%)'};}};const interactWithPage=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide2=savedGuideData.GuideStep)===null||_savedGuideData$Guide2===void 0?void 0:(_savedGuideData$Guide3=_savedGuideData$Guide2[currentStep-1])===null||_savedGuideData$Guide3===void 0?void 0:(_savedGuideData$Guide4=_savedGuideData$Guide3.Tooltip)===null||_savedGuideData$Guide4===void 0?void 0:_savedGuideData$Guide4.InteractWithPage;const{anchorOrigin,transformOrigin}=getAnchorAndTransformOrigins((canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center center\");const textStyle={fontWeight:textFieldProperties!==null&&textFieldProperties!==void 0&&(_textFieldProperties$=textFieldProperties.TextProperties)!==null&&_textFieldProperties$!==void 0&&_textFieldProperties$.Bold?\"bold\":\"normal\",fontStyle:textFieldProperties!==null&&textFieldProperties!==void 0&&(_textFieldProperties$2=textFieldProperties.TextProperties)!==null&&_textFieldProperties$2!==void 0&&_textFieldProperties$2.Italic?\"italic\":\"normal\",color:(textFieldProperties===null||textFieldProperties===void 0?void 0:(_textFieldProperties$3=textFieldProperties.TextProperties)===null||_textFieldProperties$3===void 0?void 0:_textFieldProperties$3.TextColor)||\"#000000\",textAlign:(textFieldProperties===null||textFieldProperties===void 0?void 0:textFieldProperties.Alignment)||\"left\"};const isRTL=document.body.classList.contains(\"rtl\");const baseAlign=(textFieldProperties===null||textFieldProperties===void 0?void 0:(_textFieldProperties$4=textFieldProperties.TextProperties)===null||_textFieldProperties$4===void 0?void 0:_textFieldProperties$4.TextFormat)||textStyle.textAlign;const textAlign=isRTL?baseAlign===\"left\"?\"right\":baseAlign===\"right\"?\"left\":baseAlign:baseAlign;const imageStyle={maxHeight:((_imageProperties2=imageProperties[currentStep-1])===null||_imageProperties2===void 0?void 0:(_imageProperties2$Cus=_imageProperties2.CustomImage)===null||_imageProperties2$Cus===void 0?void 0:(_imageProperties2$Cus2=_imageProperties2$Cus[currentStep-1])===null||_imageProperties2$Cus2===void 0?void 0:_imageProperties2$Cus2.MaxImageHeight)||\"500px\",textAlign:((_imageProperties3=imageProperties[currentStep-1])===null||_imageProperties3===void 0?void 0:(_imageProperties3$Cus=_imageProperties3.CustomImage)===null||_imageProperties3$Cus===void 0?void 0:(_imageProperties3$Cus2=_imageProperties3$Cus[currentStep-1])===null||_imageProperties3$Cus2===void 0?void 0:_imageProperties3$Cus2.Alignment)||\"center\",objectFit:imageFit||\"contain\",width:\"100%\",height:`${((_imageProperties4=imageProperties[currentStep-1])===null||_imageProperties4===void 0?void 0:(_imageProperties4$Cus=_imageProperties4.CustomImage)===null||_imageProperties4$Cus===void 0?void 0:(_imageProperties4$Cus2=_imageProperties4$Cus[currentStep-1])===null||_imageProperties4$Cus2===void 0?void 0:_imageProperties4$Cus2.SectionHeight)||250}px`,background:((_imageProperties5=imageProperties[currentStep-1])===null||_imageProperties5===void 0?void 0:(_imageProperties5$Cus=_imageProperties5.CustomImage)===null||_imageProperties5$Cus===void 0?void 0:(_imageProperties5$Cus2=_imageProperties5$Cus[currentStep-1])===null||_imageProperties5$Cus2===void 0?void 0:_imageProperties5$Cus2.BackgroundColor)||\"#ffffff\"};const renderHtmlSnippet=snippet=>{// Return the raw HTML snippet for rendering\nreturn{__html:snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g,(match,p1,p2,p3)=>{return`${p1}${p2}\" target=\"_blank\"${p3}`;})};};// Safely group buttons, handling potential null/undefined values\nconst groupedButtons=React.useMemo(()=>{if(!customButton||!Array.isArray(customButton)||customButton.length===0){return{};}return customButton.reduce((acc,button)=>{if(!button)return acc;const containerId=button.ContainerId||\"default\";// Use a ContainerId or fallback\nif(!acc[containerId]){acc[containerId]=[];}acc[containerId].push(button);return acc;},{});},[customButton]);const canvasStyle={position:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center-center\",borderRadius:`${canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.Radius?canvasProperties.Radius:8}px !important`,borderWidth:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BorderSize)||\"0px\",borderColor:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BorderColor)||\"transparent\",borderStyle:\"solid\",backgroundColor:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BackgroundColor)||\"white\",width:canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.Width?`${canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Width}px`:\"500px\"};const dissmissIconColor=\"red\";const ActionButtonBackgroundcolor=\"#f0f0f0\";const overlay=Overlayvalue;const sectionHeight=((_imageProperties$=imageProperties[0])===null||_imageProperties$===void 0?void 0:(_imageProperties$$Cus=_imageProperties$.CustomImage)===null||_imageProperties$$Cus===void 0?void 0:(_imageProperties$$Cus2=_imageProperties$$Cus[0])===null||_imageProperties$$Cus2===void 0?void 0:_imageProperties$$Cus2.SectionHeight)||\"auto\";const openInNewTab=true;// Determine progress bar state based on guide type and current step\nconst enableProgress=((_savedGuideData$Guide5,_savedGuideData$Guide6,_currentStepData$Tool,_firstStepData$Toolti)=>{// For AI-created announcements, check the current step's data\nconst currentStepData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide5=savedGuideData.GuideStep)===null||_savedGuideData$Guide5===void 0?void 0:_savedGuideData$Guide5[currentStep-1];const firstStepData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide6=savedGuideData.GuideStep)===null||_savedGuideData$Guide6===void 0?void 0:_savedGuideData$Guide6[0];if((currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$Tool=currentStepData.Tooltip)===null||_currentStepData$Tool===void 0?void 0:_currentStepData$Tool.EnableProgress)!==undefined){return currentStepData.Tooltip.EnableProgress;}// Fallback to first step for backward compatibility\nreturn(firstStepData===null||firstStepData===void 0?void 0:(_firstStepData$Toolti=firstStepData.Tooltip)===null||_firstStepData$Toolti===void 0?void 0:_firstStepData$Toolti.EnableProgress)||false;})();function getProgressTemplate(selectedOption){var _savedGuideData$Guide7,_savedGuideData$Guide8,_savedGuideData$Guide9;if(selectedOption===1){return\"dots\";}else if(selectedOption===2){return\"linear\";}else if(selectedOption===3){return\"BreadCrumbs\";}else if(selectedOption===4){return\"breadcrumbs\";}return(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide7=savedGuideData.GuideStep)===null||_savedGuideData$Guide7===void 0?void 0:(_savedGuideData$Guide8=_savedGuideData$Guide7[0])===null||_savedGuideData$Guide8===void 0?void 0:(_savedGuideData$Guide9=_savedGuideData$Guide8.Tooltip)===null||_savedGuideData$Guide9===void 0?void 0:_savedGuideData$Guide9.ProgressTemplate)||\"dots\";}const progressTemplate=getProgressTemplate(selectedOption);const renderProgress=()=>{if(!enableProgress)return null;if(progressTemplate===\"dots\"){return/*#__PURE__*/_jsx(MobileStepper,{variant:\"dots\",steps:steps.length,position:\"static\",activeStep:currentStep-1,sx:{backgroundColor:\"transparent\",\"& .MuiMobileStepper-dotActive\":{backgroundColor:ProgressColor// Active dot\n}},backButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}}),nextButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}})});}if(progressTemplate===\"BreadCrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",placeContent:\"center\",gap:\"4px\",padding:\"8px\"},children:Array.from({length:steps.length}).map((_,index)=>/*#__PURE__*/_jsx(\"div\",{style:{width:'14px',height:'4px',backgroundColor:index===currentStep-1?ProgressColor:hexToRgba(ProgressColor,0.45),// Active color and inactive color\nborderRadius:'100px'}},index))});}if(progressTemplate===\"breadcrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",placeContent:\"flex-start\",padding:\"8px\"},children:/*#__PURE__*/_jsxs(Typography,{sx:{color:ProgressColor},children:[\"Step \",currentStep,\" of \",steps.length]})});}if(progressTemplate===\"linear\"){return/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:progress,sx:{height:\"6px\",borderRadius:\"20px\",margin:\"6px 10px\",backgroundColor:hexToRgba(ProgressColor,0.45),'& .MuiLinearProgress-bar':{backgroundColor:ProgressColor// progress bar color\n}}})})});}return null;};// State to track if scrolling is needed\nconst[needsScrolling,setNeedsScrolling]=useState(false);const contentRef=useRef(null);const scrollbarRef=useRef(null);const handleButtonAction=action=>{if(action.Action===\"open-url\"||action.Action===\"open\"||action.Action===\"openurl\"){const targetUrl=action.TargetUrl;if(action.ActionValue===\"same-tab\"){// Open the URL in the same tab\nwindow.location.href=targetUrl;}else{// Open the URL in a new tab\nwindow.open(targetUrl,\"_blank\",\"noopener noreferrer\");}}else{if(action.Action==\"Previous\"||action.Action==\"previous\"||action.ActionValue==\"Previous\"||action.ActionValue==\"previous\"){handlePrevious();}else if(action.Action==\"Next\"||action.Action==\"next\"||action.ActionValue==\"Next\"||action.ActionValue==\"next\"){handleContinue();}else if(action.Action==\"Restart\"||action.ActionValue==\"Restart\"){var _savedGuideData$Guide10,_savedGuideData$Guide11;// Reset to the first step\nsetCurrentStep(1);// If there's a specific URL for the first step, navigate to it\nif(savedGuideData!==null&&savedGuideData!==void 0&&(_savedGuideData$Guide10=savedGuideData.GuideStep)!==null&&_savedGuideData$Guide10!==void 0&&(_savedGuideData$Guide11=_savedGuideData$Guide10[0])!==null&&_savedGuideData$Guide11!==void 0&&_savedGuideData$Guide11.ElementPath){const firstStepElement=getElementByXPath(savedGuideData.GuideStep[0].ElementPath);if(firstStepElement){firstStepElement.scrollIntoView({behavior:'smooth'});}}}}};function getAlignment(alignment){switch(alignment){case\"start\":return\"flex-start\";case\"end\":return\"flex-end\";case\"center\":default:return\"center\";}}const position=(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center-center\";const positionStyle=getPopoverPositionStyle(position);// Check if content needs scrolling with improved detection\nuseEffect(()=>{const checkScrollNeeded=()=>{if(contentRef.current){// Force a reflow to get accurate measurements\ncontentRef.current.style.height='auto';const contentHeight=contentRef.current.scrollHeight;const containerHeight=320;// max-height value\nconst shouldScroll=contentHeight>containerHeight;setNeedsScrolling(shouldScroll);// Force update scrollbar\nif(scrollbarRef.current){// Try multiple methods to update the scrollbar\nif(scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}// Force re-initialization if needed\nsetTimeout(()=>{if(scrollbarRef.current&&scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}},10);}}};checkScrollNeeded();const timeouts=[setTimeout(checkScrollNeeded,50),setTimeout(checkScrollNeeded,100),setTimeout(checkScrollNeeded,200),setTimeout(checkScrollNeeded,500)];let resizeObserver=null;let mutationObserver=null;if(contentRef.current&&window.ResizeObserver){resizeObserver=new ResizeObserver(()=>{setTimeout(checkScrollNeeded,10);});resizeObserver.observe(contentRef.current);}if(contentRef.current&&window.MutationObserver){mutationObserver=new MutationObserver(()=>{setTimeout(checkScrollNeeded,10);});mutationObserver.observe(contentRef.current,{childList:true,subtree:true,attributes:true,attributeFilter:['style','class']});}return()=>{timeouts.forEach(clearTimeout);if(resizeObserver){resizeObserver.disconnect();}if(mutationObserver){mutationObserver.disconnect();}};},[currentStep]);return/*#__PURE__*/_jsxs(\"div\",{children:[OverlayValue&&selectedTemplate!==\"Tour\"&&/*#__PURE__*/_jsx(\"div\",{style:{position:\"fixed\",top:0,left:0,right:0,bottom:0,backgroundColor:\"rgba(0, 0, 0, 0.5)\",zIndex:998,pointerEvents:selectedTemplate===\"Tour\"?\"auto\":\"none\"}}),/*#__PURE__*/_jsxs(Popover,{className:\"previewdata qadpt-index\",open:Boolean(anchorEl),anchorEl:anchorEl,onClose:undefined,anchorOrigin:anchorOrigin,transformOrigin:transformOrigin,sx:{position:selectedTemplate===\"Tour\"?\"absolute !important\":interactWithPage&&OverlayValue===false?\"absolute !important\":\"\",zIndex:selectedTemplate===\"Tour\"?\"auto !important\":interactWithPage&&OverlayValue===false?\"auto !important\":\"\",\"pointer-events\":anchorEl?\"auto\":\"auto\",\"& .MuiPaper-root:not(.MuiMobileStepper-root)\":{zIndex:\"999 !important\",// zIndex: 999,\n// borderRadius: \"1px\",\n...canvasStyle,...positionStyle,margin:\"0 !important\",transform:`${positionStyle.transform} !important`,overflow:\"visible\"}},disableScrollLock:true,children:[/*#__PURE__*/_jsx(\"div\",{style:{placeContent:\"end\",display:\"flex\"},children:(modalProperties===null||modalProperties===void 0?void 0:modalProperties.DismissOption)&&/*#__PURE__*/_jsx(IconButton,{sx:{position:\"fixed\",boxShadow:\"rgba(0, 0, 0, 0.06) 0px 4px 8px\",left:\"auto\",right:\"auto\",margin:\"-15px\",background:\"#fff !important\",border:\"1px solid #ccc\",zIndex:\"999999\",borderRadius:\"50px\",padding:\"5px !important\"},children:/*#__PURE__*/_jsx(CloseIcon,{sx:{zoom:\"0.7\",color:\"#000\"}})})}),/*#__PURE__*/_jsx(PerfectScrollbar,{ref:scrollbarRef,style:{maxHeight:\"550px\",...(enableProgress?{borderTopLeftRadius:`${canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.Radius?canvasProperties.Radius:8}px`,borderTopRightRadius:`${canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.Radius?canvasProperties.Radius:8}px`,borderBottomLeftRadius:\"0px\",borderBottomRightRadius:\"0px\"}:{borderRadius:`${canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.Radius?canvasProperties.Radius:8}px`})},options:{suppressScrollY:!needsScrolling,suppressScrollX:true,wheelPropagation:false,swipeEasing:true,minScrollbarLength:20,scrollingThreshold:1000,scrollYMarginOffset:0},children:/*#__PURE__*/_jsxs(Box,{style:{padding:canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.Padding?`${canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Padding}px`:\"4px\",height:sectionHeight},ref:contentRef,children:[/*#__PURE__*/_jsx(Box,{display:\"flex\",flexDirection:\"column\",flexWrap:\"wrap\",justifyContent:\"center\",children:imageProperties===null||imageProperties===void 0?void 0:imageProperties.map((imageProp,propIndex)=>imageProp.CustomImage.map((customImg,imgIndex)=>/*#__PURE__*/_jsx(Box,{component:\"img\",src:customImg.Url,alt:customImg.AltText||\"Image\",sx:{maxHeight:imageProp.MaxImageHeight||customImg.MaxImageHeight||\"500px\",textAlign:imageProp.Alignment||\"center\",objectFit:customImg.Fit||\"contain\",background:customImg.BackgroundColor||\"#ffffff\",width:\"100%\",height:`${customImg.SectionHeight||250}px`,//height: \"100%\",\nmargin:0,padding:0,borderRadius:\"0\"},onClick:()=>{if(imageProp.Hyperlink){const targetUrl=imageProp.Hyperlink;window.open(targetUrl,\"_blank\",\"noopener noreferrer\");}},style:{cursor:imageProp.Hyperlink?\"pointer\":\"default\"}},`${imageProp.Id}-${imgIndex}`)))}),textFieldProperties===null||textFieldProperties===void 0?void 0:textFieldProperties.map((textField,index)=>{var _textField$TextProper;return textField.Text&&/*#__PURE__*/_jsx(Typography,{// Use a unique key, either Id or index\nclassName:\"qadpt-preview\",sx:{whiteSpace:\"pre-wrap\",wordBreak:\"break-word\",textAlign,marginTop:1,color:((_textField$TextProper=textField.TextProperties)===null||_textField$TextProper===void 0?void 0:_textField$TextProper.TextColor)||textStyle.color,padding:\"5px\"},dangerouslySetInnerHTML:renderHtmlSnippet(textField.Text)// Render the raw HTML\n},textField.Id||index);}),Object.keys(groupedButtons).map(containerId=>{var _groupedButtons$conta,_groupedButtons$conta2;return/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",justifyContent:getAlignment((_groupedButtons$conta=groupedButtons[containerId][0])===null||_groupedButtons$conta===void 0?void 0:_groupedButtons$conta.Alignment),flexWrap:\"wrap\",margin:0,backgroundColor:(_groupedButtons$conta2=groupedButtons[containerId][0])===null||_groupedButtons$conta2===void 0?void 0:_groupedButtons$conta2.BackgroundColor,padding:\"10px\"},children:groupedButtons[containerId].map((button,index)=>{var _button$ButtonPropert,_button$ButtonPropert2,_button$ButtonPropert3,_button$ButtonPropert4,_button$ButtonPropert5,_button$ButtonPropert6;return/*#__PURE__*/_jsx(Button,{onClick:()=>handleButtonAction(button.ButtonAction),variant:\"contained\",sx:{marginRight:\"13px\",margin:\"0 5px\",backgroundColor:((_button$ButtonPropert=button.ButtonProperties)===null||_button$ButtonPropert===void 0?void 0:_button$ButtonPropert.ButtonBackgroundColor)||\"#007bff\",color:((_button$ButtonPropert2=button.ButtonProperties)===null||_button$ButtonPropert2===void 0?void 0:_button$ButtonPropert2.ButtonTextColor)||\"#fff\",border:`2px solid ${(_button$ButtonPropert3=button.ButtonProperties)===null||_button$ButtonPropert3===void 0?void 0:_button$ButtonPropert3.ButtonBorderColor}`||\"transparent\",fontSize:((_button$ButtonPropert4=button.ButtonProperties)===null||_button$ButtonPropert4===void 0?void 0:_button$ButtonPropert4.FontSize)||\"14px\",width:((_button$ButtonPropert5=button.ButtonProperties)===null||_button$ButtonPropert5===void 0?void 0:_button$ButtonPropert5.Width)||\"auto\",// paddingTop: \"3px\",\n//                         paddingRight: \"16px\",\n//                         paddingBottom: \"3x\",\n//                         paddingLeft:\"16px\",\ntextTransform:\"none\",borderRadius:((_button$ButtonPropert6=button.ButtonProperties)===null||_button$ButtonPropert6===void 0?void 0:_button$ButtonPropert6.BorderRadius)||\"8px\",padding:\"var(--button-padding) !important\",lineHeight:\"var(--button-lineheight) !important\",boxShadow:\"none !important\"},children:button.ButtonName},index);})},containerId);})]})},`scrollbar-${needsScrolling}`),/*#__PURE__*/_jsx(\"div\",{children:totalSteps>=1&&enableProgress?/*#__PURE__*/_jsx(_Fragment,{children:renderProgress()}):null})]})]});};export default AnnouncementPopup;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Popover", "<PERSON><PERSON>", "Typography", "Box", "LinearProgress", "IconButton", "MobileStepper", "CloseIcon", "useDrawerStore", "PerfectScrollbar", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "getElementByXPath", "xpath", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "singleNodeValue", "error", "console", "hexToRgba", "hex", "opacity", "replace", "r", "parseInt", "substring", "g", "b", "AnnouncementPopup", "_ref", "_imageProperties", "_imageProperties$Cust", "_imageProperties$Cust2", "_savedGuideData$Guide2", "_savedGuideData$Guide3", "_savedGuideData$Guide4", "_textFieldProperties$", "_textFieldProperties$2", "_textFieldProperties$3", "_textFieldProperties$4", "_imageProperties2", "_imageProperties2$Cus", "_imageProperties2$Cus2", "_imageProperties3", "_imageProperties3$Cus", "_imageProperties3$Cus2", "_imageProperties4", "_imageProperties4$Cus", "_imageProperties4$Cus2", "_imageProperties5", "_imageProperties5$Cus", "_imageProperties5$Cus2", "_imageProperties$", "_imageProperties$$Cus", "_imageProperties$$Cus2", "selectedTemplate", "handlecloseBannerPopup", "backgroundC", "Bposition", "bpadding", "Bbordercolor", "BborderSize", "guideStep", "anchorEl", "onClose", "onPrevious", "onContinue", "title", "text", "imageUrl", "videoUrl", "previousButtonLabel", "continueButtonLabel", "currentStep", "totalSteps", "onDontShowAgain", "progress", "textFieldProperties", "imageProperties", "customButton", "modalProperties", "canvasProperties", "htmlSnippet", "previousButtonStyles", "continueButtonStyles", "OverlayValue", "savedGuideData", "setCurrentStep", "selectedOption", "steps", "setBannerPreview", "bannerPreview", "announcementPreview", "setAnnouncementPreview", "ProgressColor", "setProgressColor", "state", "Overlayvalue", "setOverlayValue", "handleContinue", "_savedGuideData$Guide", "GuideStep", "length", "handlePrevious", "imageFit", "CustomImage", "Fit", "getAnchorAndTransformOrigins", "position", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "getPopoverPositionStyle", "arguments", "undefined", "EDGE_PADDING", "baseStyle", "top", "right", "bottom", "left", "transform", "interactWithPage", "<PERSON><PERSON><PERSON>", "InteractWithPage", "Position", "textStyle", "fontWeight", "TextProperties", "Bold", "fontStyle", "Italic", "color", "TextColor", "textAlign", "Alignment", "isRTL", "body", "classList", "contains", "baseAlign", "TextFormat", "imageStyle", "maxHeight", "MaxImageHeight", "objectFit", "width", "height", "SectionHeight", "background", "BackgroundColor", "renderHtmlSnippet", "snippet", "__html", "match", "p1", "p2", "p3", "groupedButtons", "useMemo", "Array", "isArray", "reduce", "acc", "button", "containerId", "ContainerId", "push", "canvasStyle", "borderRadius", "<PERSON><PERSON>", "borderWidth", "BorderSize", "borderColor", "BorderColor", "borderStyle", "backgroundColor", "<PERSON><PERSON><PERSON>", "dissmissIconColor", "ActionButtonBackgroundcolor", "overlay", "sectionHeight", "openInNewTab", "enableProgress", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "_currentStepData$Tool", "_firstStepData$Toolti", "currentStepData", "firstStepData", "EnableProgress", "getProgressTemplate", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "ProgressTemplate", "progressTemplate", "renderProgress", "variant", "activeStep", "sx", "backButton", "style", "visibility", "nextButton", "display", "alignItems", "place<PERSON><PERSON>nt", "gap", "padding", "children", "from", "map", "_", "index", "value", "margin", "needsScrolling", "setNeedsScrolling", "contentRef", "scrollbarRef", "handleButtonAction", "action", "Action", "targetUrl", "TargetUrl", "ActionValue", "window", "location", "href", "open", "_savedGuideData$Guide10", "_savedGuideData$Guide11", "<PERSON>ement<PERSON><PERSON>", "firstStepElement", "scrollIntoView", "behavior", "getAlignment", "alignment", "positionStyle", "checkScrollNeeded", "current", "contentHeight", "scrollHeight", "containerHeight", "shouldScroll", "updateScroll", "setTimeout", "timeouts", "resizeObserver", "mutationObserver", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "for<PERSON>ach", "clearTimeout", "disconnect", "zIndex", "pointerEvents", "className", "Boolean", "overflow", "disableScrollLock", "DismissOption", "boxShadow", "border", "zoom", "ref", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "options", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "Padding", "flexDirection", "flexWrap", "justifyContent", "imageProp", "propIndex", "customImg", "imgIndex", "component", "src", "Url", "alt", "AltText", "onClick", "Hyperlink", "cursor", "Id", "textField", "_textField$TextProper", "Text", "whiteSpace", "wordBreak", "marginTop", "dangerouslySetInnerHTML", "Object", "keys", "_groupedButtons$conta", "_groupedButtons$conta2", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "_button$ButtonPropert6", "ButtonAction", "marginRight", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "ButtonBorderColor", "fontSize", "FontSize", "textTransform", "BorderRadius", "lineHeight", "ButtonName"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/GuidesPreview/AnnouncementPreview.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport { <PERSON>over, Button, Typography, Box, LinearProgress, DialogActions,IconButton, MobileStepper } from \"@mui/material\";\r\nimport { CustomIconButton } from \"./Button\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { PopoverOrigin } from \"@mui/material\";\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nimport BannerEndUser from \"../Bannerspreview/Banner\";\r\nimport BannerStepPreview from \"../tours/BannerStepPreview\"\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\n\r\n// Helper function to get an element by XPath\r\nconst getElementByXPath = (xpath: string): HTMLElement | null => {\r\n    try {\r\n        const result = document.evaluate(\r\n            xpath,\r\n            document,\r\n            null,\r\n            XPathResult.FIRST_ORDERED_NODE_TYPE,\r\n            null\r\n        );\r\n        return result.singleNodeValue as HTMLElement;\r\n    } catch (error) {\r\n        console.error(\"Error evaluating XPath:\", error);\r\n        return null;\r\n    }\r\n};\r\n\r\n// Helper function to convert hex color to rgba with opacity\r\nconst hexToRgba = (hex: string, opacity: number): string => {\r\n\t// Remove # if present\r\n\thex = hex.replace('#', '');\r\n\r\n\t// Parse hex values\r\n\tconst r = parseInt(hex.substring(0, 2), 16);\r\n\tconst g = parseInt(hex.substring(2, 4), 16);\r\n\tconst b = parseInt(hex.substring(4, 6), 16);\r\n\r\n\treturn `rgba(${r}, ${g}, ${b}, ${opacity})`;\r\n};\r\ninterface PopupProps {\r\n    handlecloseBannerPopup: any;\r\n    guideStep: any[];\r\n    anchorEl: null | HTMLElement;\r\n    onClose: () => void;\r\n    onPrevious: () => void;\r\n    onContinue: () => void;\r\n    title: string;\r\n    text: string;\r\n    imageUrl?: string;\r\n    videoUrl?: string;\r\n    previousButtonLabel: string;\r\n    continueButtonLabel: string;\r\n    previousButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    continueButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    currentStep: number;\r\n    totalSteps: number;\r\n    onDontShowAgain: () => void;\r\n    progress: number;\r\n    textFieldProperties?: any;\r\n    imageProperties?: any;\r\n    customButton?: any;\r\n    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };\r\n    canvasProperties?: {\r\n        Position?: string;\r\n        Padding?: string;\r\n        Radius?: string;\r\n        BorderSize?: string;\r\n        BorderColor?: string;\r\n        BackgroundColor?: string;\r\n        Width?: string;\r\n    };\r\n    htmlSnippet: string;\r\n    OverlayValue: boolean;\r\n    backgroundC: any;\r\n    Bposition: any;\r\n    bpadding: any;\r\n    Bbordercolor: any;\r\n    BborderSize: any;\r\n    savedGuideData: GuideData | null;\r\n    selectedTemplate:any\r\n    ProgressColor:any}\r\n\r\nconst AnnouncementPopup: React.FC<PopupProps> = ({\r\n    selectedTemplate,\r\n    handlecloseBannerPopup,\r\n    backgroundC,\r\n    Bposition,\r\n    bpadding,\r\n    Bbordercolor,\r\n    BborderSize,\r\n    guideStep,\r\n    anchorEl,\r\n    onClose,\r\n    onPrevious,\r\n    onContinue,\r\n    title,\r\n    text,\r\n    imageUrl,\r\n    videoUrl,\r\n    previousButtonLabel,\r\n    continueButtonLabel,\r\n    currentStep,\r\n    totalSteps,\r\n    onDontShowAgain,\r\n    progress,\r\n    textFieldProperties,\r\n    imageProperties,\r\n    customButton,\r\n    modalProperties,\r\n    canvasProperties,\r\n    htmlSnippet,\r\n    previousButtonStyles,\r\n    continueButtonStyles,\r\n    OverlayValue,\r\n    savedGuideData\r\n}) => {\r\n\r\n    const {\r\n        setCurrentStep,\r\n        selectedOption,\r\n        steps,\r\n        setBannerPreview,\r\n        bannerPreview,\r\n        announcementPreview, setAnnouncementPreview,\r\n        ProgressColor,\r\n\t\tsetProgressColor\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n    const [Overlayvalue, setOverlayValue] = useState(false);\r\n    const handleContinue = () => {\r\n        if (selectedTemplate !== \"Tour\") {\r\n            if (currentStep < totalSteps) {\r\n                setCurrentStep(currentStep + 1);\r\n                onContinue();\r\n            }\r\n        }\r\n        else {\r\n            if (currentStep !== savedGuideData?.GuideStep?.length) {\r\n                setCurrentStep(currentStep + 1);\r\n            }\r\n        }\r\n    };\r\n\r\n    const handlePrevious = () => {\r\n        if (currentStep > 1) {\r\n            setCurrentStep(currentStep - 1);\r\n            onPrevious();\r\n        }\r\n    };\r\n    // Initialize Overlayvalue state from props only once\r\n    useEffect(() => {\r\n        setOverlayValue(!!OverlayValue);\r\n    }, []);\r\n    const imageFit = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.Fit || 'contain';\r\n    const getAnchorAndTransformOrigins = (position: string): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {\r\n        switch (position) {\r\n            case \"top-left\":\r\n                return { anchorOrigin: { vertical: \"top\", horizontal: \"left\" }, transformOrigin: { vertical: \"bottom\", horizontal: \"right\" } };\r\n            case \"top-right\":\r\n                return { anchorOrigin: { vertical: \"top\", horizontal: \"right\" }, transformOrigin: { vertical: \"bottom\", horizontal: \"left\" } };\r\n            case \"bottom-left\":\r\n                return { anchorOrigin: { vertical: \"bottom\", horizontal: \"left\" }, transformOrigin: { vertical: \"top\", horizontal: \"right\" } };\r\n            case \"bottom-right\":\r\n                return { anchorOrigin: { vertical: \"bottom\", horizontal: \"right\" }, transformOrigin: { vertical: \"center\", horizontal: \"left\" } };\r\n            case \"center-center\":\r\n                return { anchorOrigin: { vertical: \"center\", horizontal: \"center\" }, transformOrigin: { vertical: \"center\", horizontal: \"center\" } };\r\n            case \"top-center\":\r\n                return { anchorOrigin: { vertical: \"top\", horizontal: \"center\" }, transformOrigin: { vertical: \"bottom\", horizontal: \"center\" } };\r\n            case \"left-center\":\r\n                return { anchorOrigin: { vertical: \"center\", horizontal: \"left\" }, transformOrigin: { vertical: \"center\", horizontal: \"right\" } };\r\n            case \"bottom-center\":\r\n                return { anchorOrigin: { vertical: \"bottom\", horizontal: \"center\" }, transformOrigin: { vertical: \"center\", horizontal: \"center\" } };\r\n            case \"right-center\":\r\n                return { anchorOrigin: { vertical: \"center\", horizontal: \"right\" }, transformOrigin: { vertical: \"center\", horizontal: \"left\" } };\r\n            default:\r\n                return { anchorOrigin: { vertical: \"center\", horizontal: \"center\" }, transformOrigin: { vertical: \"center\", horizontal: \"center\" } };\r\n        }\r\n    };\r\n    const getPopoverPositionStyle = (position: string = \"center-center\") => {\r\n        // Constants\r\n        const EDGE_PADDING = 12; // Padding from screen edges (in px)\r\n\r\n        // Basic reset for all positioning properties\r\n        const baseStyle = {\r\n            position: 'fixed',\r\n            top: 'auto !important',\r\n            right: 'auto',\r\n            bottom: 'auto',\r\n            left: 'auto !important',\r\n            transform: 'none'\r\n        };\r\n\r\n         // Apply specific positioning based on selected position\r\n         switch (position) {\r\n            case \"top-left\":\r\n                return {\r\n                    ...baseStyle,\r\n                    top: `${EDGE_PADDING + 10 }px !important`,\r\n                    left: `${EDGE_PADDING}px !important`\r\n                };\r\n            case \"top-center\":\r\n                return {\r\n                    ...baseStyle,\r\n                    top: `${EDGE_PADDING + 10}px !important`,\r\n                    left: '50% !important',\r\n                    transform: 'translateX(-50%)'\r\n                };\r\n            case \"top-right\":\r\n                return {\r\n                    ...baseStyle,\r\n                    top: `${EDGE_PADDING + 10 }px !important`,\r\n                    right: `${EDGE_PADDING + 5}px`\r\n                };\r\n           // case \"left-center\":\r\n            case \"left-center\":\r\n                return {\r\n                    ...baseStyle,\r\n                    top: '54% !important',\r\n                    left: `${EDGE_PADDING}px !important`,\r\n                    transform: 'translateY(-50%)'\r\n                };\r\n            //case \"center-center\":\r\n            case \"center-center\":\r\n                return {\r\n                    ...baseStyle,\r\n                    top: '54% !important',\r\n                    left: '50% !important',\r\n                    transform: 'translate(-50%, -50%)'\r\n                };\r\n           // case \"right-center\":\r\n            case \"right-center\":\r\n                return {\r\n                    ...baseStyle,\r\n                    top: '54% !important',\r\n                    right: `${EDGE_PADDING + 5}px`,\r\n                    transform: 'translateY(-50%)'\r\n                };\r\n            case \"bottom-left\":\r\n                return {\r\n                    ...baseStyle,\r\n                    bottom: `${EDGE_PADDING }px !important`,\r\n                    left: `${EDGE_PADDING}px !important`\r\n                };\r\n            case \"bottom-center\":\r\n                return {\r\n                    ...baseStyle,\r\n                    bottom: `${EDGE_PADDING}px`,\r\n                    left: '50% !important',\r\n                    transform: 'translateX(-50%)'\r\n                };\r\n            case \"bottom-right\":\r\n                return {\r\n                    ...baseStyle,\r\n                    bottom: `${EDGE_PADDING }px`,\r\n                    right: `${EDGE_PADDING + 5}px`\r\n                };\r\n            default:\r\n                return {\r\n                    ...baseStyle,\r\n                    top: '50% !important',\r\n                    left: '50% !important',\r\n                    transform: 'translate(-50%, -50%)'\r\n                };\r\n        }\r\n    };\r\n\r\n    const interactWithPage = savedGuideData?.GuideStep?.[currentStep - 1]?.Tooltip?.InteractWithPage;\r\n    const { anchorOrigin, transformOrigin } = getAnchorAndTransformOrigins(canvasProperties?.Position || \"center center\");\r\n\r\n    const textStyle = {\r\n        fontWeight: textFieldProperties?.TextProperties?.Bold ? \"bold\" : \"normal\",\r\n        fontStyle: textFieldProperties?.TextProperties?.Italic ? \"italic\" : \"normal\",\r\n        color: textFieldProperties?.TextProperties?.TextColor || \"#000000\",\r\n        textAlign: textFieldProperties?.Alignment || \"left\",\r\n\t};\r\n\tconst isRTL = document.body.classList.contains(\"rtl\");\r\n\r\nconst baseAlign = textFieldProperties?.TextProperties?.TextFormat || textStyle.textAlign;\r\n\r\nconst textAlign = isRTL\r\n  ? baseAlign === \"left\"\r\n    ? \"right\"\r\n    : baseAlign === \"right\"\r\n    ? \"left\"\r\n    : baseAlign\r\n  : baseAlign;\r\n\r\n\r\n    const imageStyle = {\r\n        maxHeight: imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.MaxImageHeight || \"500px\",\r\n        textAlign: imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.Alignment || \"center\",\r\n        objectFit: imageFit || \"contain\",\r\n        width: \"100%\",\r\n        height: `${imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || 250}px`,\r\n        background: imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.BackgroundColor || \"#ffffff\",\r\n    };\r\n\r\n    const renderHtmlSnippet = (snippet: string) => {\r\n        // Return the raw HTML snippet for rendering\r\n        return { __html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (match, p1, p2, p3) => {\r\n            return `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n        }) };\r\n    };\r\n\r\n    // Safely group buttons, handling potential null/undefined values\r\n    const groupedButtons = React.useMemo(() => {\r\n        if (!customButton || !Array.isArray(customButton) || customButton.length === 0) {\r\n            return {};\r\n        }\r\n\r\n        return customButton.reduce((acc: any, button: any) => {\r\n            if (!button) return acc;\r\n\r\n        const containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\r\n        if (!acc[containerId]) {\r\n          acc[containerId] = [];\r\n        }\r\n        acc[containerId].push(button);\r\n        return acc;\r\n      }, {});\r\n    }, [customButton]);\r\n\r\n\tconst canvasStyle = {\r\n\t\tposition: canvasProperties?.Position || \"center-center\",\r\n\t\tborderRadius: `${canvasProperties?.Radius ? canvasProperties.Radius : 8}px !important`,\r\n\t\tborderWidth: canvasProperties?.BorderSize || \"0px\",\r\n\t\tborderColor: canvasProperties?.BorderColor || \"transparent\",\r\n\t\tborderStyle: \"solid\",\r\n\t\tbackgroundColor: canvasProperties?.BackgroundColor || \"white\",\r\n\t\twidth: canvasProperties?.Width ? `${canvasProperties?.Width}px` : \"500px\",\r\n\t};\r\n\tconst dissmissIconColor = \"red\";\r\n\tconst ActionButtonBackgroundcolor = \"#f0f0f0\";\r\n\tconst overlay: boolean = Overlayvalue;\r\n\tconst sectionHeight = imageProperties[0]?.CustomImage?.[0]?.SectionHeight || \"auto\";\r\n\tconst openInNewTab = true;\r\n\t// Determine progress bar state based on guide type and current step\r\n\tconst enableProgress = (() => {\r\n\t\t// For AI-created announcements, check the current step's data\r\n\t\tconst currentStepData = savedGuideData?.GuideStep?.[currentStep - 1];\r\n\t\tconst firstStepData = savedGuideData?.GuideStep?.[0];\r\n\r\n\t\tif (currentStepData?.Tooltip?.EnableProgress !== undefined) {\r\n\t\t\treturn currentStepData.Tooltip.EnableProgress;\r\n\t\t}\r\n\t\t// Fallback to first step for backward compatibility\r\n\t\treturn firstStepData?.Tooltip?.EnableProgress || false;\r\n\t})();\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t}\r\n        else if (selectedOption === 4) {\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n        if (!enableProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={steps.length}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{ backgroundColor: \"transparent\",  \"& .MuiMobileStepper-dotActive\": {\r\n                        backgroundColor: ProgressColor, // Active dot\r\n                      }, }}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n        if (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n                <Box sx={{display: \"flex\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\tgap: \"4px\",padding:\"8px\"}}>\r\n                  {/* Custom Step Indicators */}\r\n\r\n                    {Array.from({ length: steps.length }).map((_, index) => (\r\n                      <div\r\n                        key={index}\r\n                        style={{\r\n                          width: '14px',\r\n                          height: '4px',\r\n                          backgroundColor: index === currentStep - 1 ? ProgressColor : hexToRgba(ProgressColor, 0.45), // Active color and inactive color\r\n                          borderRadius: '100px',\r\n                        }}\r\n                      />\r\n                    ))}\r\n\r\n                </Box>\r\n              );\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{display: \"flex\",\r\n\t\t\t\talignItems: \"center\",\r\n\t\t\t\tplaceContent: \"flex-start\",padding:\"8px\"\r\n\t\t\t\t}}>\r\n\t\t\t\t\t<Typography sx={{color: ProgressColor}}>\r\n                    Step {currentStep} of {steps.length}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box >\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n                            sx={{\r\n                                height: \"6px\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: hexToRgba(ProgressColor, 0.45),\r\n                                '& .MuiLinearProgress-bar': {\r\n                                backgroundColor: ProgressColor, // progress bar color\r\n                              },}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n    };\r\n    // State to track if scrolling is needed\r\n    const [needsScrolling, setNeedsScrolling] = useState(false);\r\n    const contentRef = useRef<HTMLDivElement>(null);\r\n  const scrollbarRef = useRef<any>(null);\r\n    const handleButtonAction = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"open\" || action.Action===\"openurl\") {\r\n\t\t\tconst targetUrl = action.TargetUrl;\r\n\t\t\tif (action.ActionValue === \"same-tab\") {\r\n\t\t\t\t// Open the URL in the same tab\r\n\t\t\t\twindow.location.href = targetUrl;\r\n\t\t\t} else {\r\n\t\t\t\t// Open the URL in a new tab\r\n\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (action.Action == \"Previous\" || action.Action == \"previous\" || action.ActionValue == \"Previous\" || action.ActionValue == \"previous\") {\r\n\t\t\t\thandlePrevious();\r\n\t\t\t} else if (action.Action == \"Next\" || action.Action == \"next\" || action.ActionValue == \"Next\" || action.ActionValue == \"next\") {\r\n\t\t\t\thandleContinue();\r\n\t\t\t} else if (action.Action == \"Restart\" || action.ActionValue == \"Restart\") {\r\n\t\t\t\t// Reset to the first step\r\n\t\t\t\tsetCurrentStep(1);\r\n\t\t\t\t// If there's a specific URL for the first step, navigate to it\r\n\t\t\t\tif (savedGuideData?.GuideStep?.[0]?.ElementPath) {\r\n\t\t\t\t\tconst firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\r\n\t\t\t\t\tif (firstStepElement) {\r\n\t\t\t\t\t\tfirstStepElement.scrollIntoView({ behavior: 'smooth' });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\tfunction getAlignment(alignment: string) {\r\n\t\tswitch (alignment) {\r\n\t\t\tcase \"start\":\r\n\t\t\t\treturn \"flex-start\";\r\n\t\t\tcase \"end\":\r\n\t\t\t\treturn \"flex-end\";\r\n\t\t\tcase \"center\":\r\n\t\t\tdefault:\r\n\t\t\t\treturn \"center\";\r\n\t\t}\r\n\t}\r\n\r\n    const position = canvasProperties?.Position || \"center-center\";\r\n    const positionStyle = getPopoverPositionStyle(position);\r\n    // Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [currentStep]);\r\n\treturn (\r\n\t\t<div>\r\n\t\t\t{OverlayValue && selectedTemplate!==\"Tour\"&&(\r\n\t\t\t\t<div\r\n\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\tzIndex: 998,\r\n\t\t\t\t\t\tpointerEvents: selectedTemplate === \"Tour\" ? \"auto\" : \"none\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t\t<Popover\r\n\t\t\t\tclassName=\"previewdata qadpt-index\"\r\n\t\t\t\topen={Boolean(anchorEl)}\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\tonClose={undefined}\r\n\t\t\t\tanchorOrigin={anchorOrigin}\r\n\t\t\t\ttransformOrigin={transformOrigin}\r\n\t\t\t\tsx={{\r\n\t\t\t\t\tposition:selectedTemplate===\"Tour\"?\"absolute !important\": interactWithPage && OverlayValue === false ? \"absolute !important\" : \"\",\r\n\t\t\t\t\tzIndex:selectedTemplate===\"Tour\"?\"auto !important\": interactWithPage && OverlayValue === false ? \"auto !important\" : \"\",\r\n\t\t\t\t\t\"pointer-events\": anchorEl ? \"auto\" : \"auto\",\r\n\t\t\t\t\t\"& .MuiPaper-root:not(.MuiMobileStepper-root)\": {\r\n\t\t\t\t\t\tzIndex: \"999 !important\",\r\n\t\t\t\t\t\t// zIndex: 999,\r\n\t\t\t\t\t\t// borderRadius: \"1px\",\r\n\t\t\t\t\t\t...canvasStyle,\r\n\t\t\t\t\t\t...positionStyle,\r\n                        margin: \"0 !important\",\r\n\t\t\t\t\t\ttransform: `${positionStyle.transform} !important`,\r\n\r\n\t\t\t\t\t\toverflow: \"visible\",\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t\tdisableScrollLock={true}\r\n\t\t\t>\r\n\t\t\t\t<div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n\t\t\t\t\t{modalProperties?.DismissOption && (\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\r\n\t\t\t\t\t\t\t\tleft: \"auto\",\r\n\t\t\t\t\t\t\t\tright: \"auto\",\r\n\t\t\t\t\t\t\t\tmargin: \"-15px\",\r\n\t\t\t\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\tzIndex: \"999999\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon sx={{ zoom: \"0.7\", color: \"#000\" }} />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n                <PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{\r\n  maxHeight: \"550px\",\r\n  ...(enableProgress\r\n    ? {\r\n        borderTopLeftRadius: `${canvasProperties?.Radius ? canvasProperties.Radius : 8}px`,\r\n        borderTopRightRadius: `${canvasProperties?.Radius ? canvasProperties.Radius : 8}px`,\r\n        borderBottomLeftRadius: \"0px\",\r\n        borderBottomRightRadius: \"0px\",\r\n      }\r\n    : {\r\n        borderRadius: `${canvasProperties?.Radius ? canvasProperties.Radius : 8}px`,\r\n      }),\r\n}}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tpadding: canvasProperties?.Padding ? `${canvasProperties?.Padding}px` : \"4px\",\r\n\t\t\t\t\t\t\theight: sectionHeight,\r\n                        }}\r\n                        ref={contentRef}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\tflexWrap=\"wrap\"\r\n\t\t\t\t\t\t\tjustifyContent=\"center\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{imageProperties?.map((imageProp: any, propIndex: number) =>\r\n\t\t\t\t\t\t\t\timageProp.CustomImage.map((customImg: any, imgIndex: number) => (\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tkey={`${imageProp.Id}-${imgIndex}`}\r\n\t\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\t\tsrc={customImg.Url}\r\n\t\t\t\t\t\t\t\t\t\talt={customImg.AltText || \"Image\"}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tmaxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\ttextAlign: imageProp.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\tobjectFit: customImg.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackground: customImg.BackgroundColor || \"#ffffff\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\theight: `${customImg.SectionHeight || 250}px`,\r\n\t\t\t\t\t\t\t\t\t\t\t//height: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tmargin: 0,\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: 0,\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"0\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (imageProp.Hyperlink) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst targetUrl = imageProp.Hyperlink;\r\n\t\t\t\t\t\t\t\t\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ cursor: imageProp.Hyperlink ? \"pointer\" : \"default\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t\t{textFieldProperties?.map(\r\n\t\t\t\t\t\t\t(textField: any, index: any) =>\r\n\t\t\t\t\t\t\t\ttextField.Text && (\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tkey={textField.Id || index} // Use a unique key, either Id or index\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-preview\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\ttextAlign,\r\n\t\t\t\t\t\t\t\t\t\t\tmarginTop: 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: textField.TextProperties?.TextColor || textStyle.color,\r\n\t\t\t\t\t\t\t\t\t\t\tpadding:\"5px\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={renderHtmlSnippet(textField.Text)} // Render the raw HTML\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t{Object.keys(groupedButtons).map((containerId) => (\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tkey={containerId}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\tjustifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),\r\n\t\t\t\t\t\t\t\t\tflexWrap: \"wrap\",\r\n\t\t\t\t\t\t\t\t\tmargin: 0,\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: groupedButtons[containerId][0]?.BackgroundColor,\r\n           padding: \"10px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{groupedButtons[containerId].map((button: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonAction(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tmarginRight: \"13px\",\r\n\t\t\t\t\t\t\t\t\t\t\tmargin: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\",\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor || \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\tborder: `2px solid ${button.ButtonProperties?.ButtonBorderColor}` || \"transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || \"14px\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t// paddingTop: \"3px\",\r\n\t\t\t\t\t\t\t\t\t\t\t//                         paddingRight: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t//                         paddingBottom: \"3x\",\r\n\t\t\t\t\t\t\t\t\t\t\t//                         paddingLeft:\"16px\",\r\n\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: button.ButtonProperties?.BorderRadius || \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"var(--button-padding) !important\",\r\n                                            lineHeight: \"var(--button-lineheight) !important\",\r\n                                            boxShadow: \"none !important\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t))}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t\r\n               </PerfectScrollbar>\r\n                <div>\r\n\t\t\t\t\t\t{/* Render Step Progress */}\r\n                        {totalSteps >= 1 && enableProgress ? <>{renderProgress()}</> : null}\r\n                    </div>\r\n\t\t\t</Popover>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default AnnouncementPopup;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OAASC,OAAO,CAAEC,MAAM,CAAEC,UAAU,CAAEC,GAAG,CAAEC,cAAc,CAAgBC,UAAU,CAAEC,aAAa,KAAQ,eAAe,CAEzH,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CAEjD,MAAO,CAAAC,cAAc,KAAuB,yBAAyB,CAIrE,MAAO,CAAAC,gBAAgB,KAAM,yBAAyB,CACtD,MAAO,6CAA6C,CAGpD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,iBAAiB,CAAIC,KAAa,EAAyB,CAC7D,GAAI,CACA,KAAM,CAAAC,MAAM,CAAGC,QAAQ,CAACC,QAAQ,CAC5BH,KAAK,CACLE,QAAQ,CACR,IAAI,CACJE,WAAW,CAACC,uBAAuB,CACnC,IACJ,CAAC,CACD,MAAO,CAAAJ,MAAM,CAACK,eAAe,CACjC,CAAE,MAAOC,KAAK,CAAE,CACZC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,MAAO,KAAI,CACf,CACJ,CAAC,CAED;AACA,KAAM,CAAAE,SAAS,CAAGA,CAACC,GAAW,CAAEC,OAAe,GAAa,CAC3D;AACAD,GAAG,CAAGA,GAAG,CAACE,OAAO,CAAC,GAAG,CAAE,EAAE,CAAC,CAE1B;AACA,KAAM,CAAAC,CAAC,CAAGC,QAAQ,CAACJ,GAAG,CAACK,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,CAC3C,KAAM,CAAAC,CAAC,CAAGF,QAAQ,CAACJ,GAAG,CAACK,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,CAC3C,KAAM,CAAAE,CAAC,CAAGH,QAAQ,CAACJ,GAAG,CAACK,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,CAE3C,MAAO,QAAQF,CAAC,KAAKG,CAAC,KAAKC,CAAC,KAAKN,OAAO,GAAG,CAC5C,CAAC,CAoDD,KAAM,CAAAO,iBAAuC,CAAGC,IAAA,EAiC1C,KAAAC,gBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAAAC,sBAAA,IAjC2C,CAC7CC,gBAAgB,CAChBC,sBAAsB,CACtBC,WAAW,CACXC,SAAS,CACTC,QAAQ,CACRC,YAAY,CACZC,WAAW,CACXC,SAAS,CACTC,QAAQ,CACRC,OAAO,CACPC,UAAU,CACVC,UAAU,CACVC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,QAAQ,CACRC,mBAAmB,CACnBC,mBAAmB,CACnBC,WAAW,CACXC,UAAU,CACVC,eAAe,CACfC,QAAQ,CACRC,mBAAmB,CACnBC,eAAe,CACfC,YAAY,CACZC,eAAe,CACfC,gBAAgB,CAChBC,WAAW,CACXC,oBAAoB,CACpBC,oBAAoB,CACpBC,YAAY,CACZC,cACJ,CAAC,CAAAzD,IAAA,CAEG,KAAM,CACF0D,cAAc,CACdC,cAAc,CACdC,KAAK,CACLC,gBAAgB,CAChBC,aAAa,CACbC,mBAAmB,CAAEC,sBAAsB,CAC3CC,aAAa,CACnBC,gBACD,CAAC,CAAG9F,cAAc,CAAE+F,KAAkB,EAAKA,KAAK,CAAC,CAC9C,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG5G,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAA6G,cAAc,CAAGA,CAAA,GAAM,CACzB,GAAI5C,gBAAgB,GAAK,MAAM,CAAE,CAC7B,GAAIkB,WAAW,CAAGC,UAAU,CAAE,CAC1Ba,cAAc,CAACd,WAAW,CAAG,CAAC,CAAC,CAC/BP,UAAU,CAAC,CAAC,CAChB,CACJ,CAAC,IACI,KAAAkC,qBAAA,CACD,GAAI3B,WAAW,IAAKa,cAAc,SAAdA,cAAc,kBAAAc,qBAAA,CAAdd,cAAc,CAAEe,SAAS,UAAAD,qBAAA,iBAAzBA,qBAAA,CAA2BE,MAAM,EAAE,CACnDf,cAAc,CAACd,WAAW,CAAG,CAAC,CAAC,CACnC,CACJ,CACJ,CAAC,CAED,KAAM,CAAA8B,cAAc,CAAGA,CAAA,GAAM,CACzB,GAAI9B,WAAW,CAAG,CAAC,CAAE,CACjBc,cAAc,CAACd,WAAW,CAAG,CAAC,CAAC,CAC/BR,UAAU,CAAC,CAAC,CAChB,CACJ,CAAC,CACD;AACA1E,SAAS,CAAC,IAAM,CACZ2G,eAAe,CAAC,CAAC,CAACb,YAAY,CAAC,CACnC,CAAC,CAAE,EAAE,CAAC,CACN,KAAM,CAAAmB,QAAQ,CAAG,EAAA1E,gBAAA,CAAAgD,eAAe,CAACL,WAAW,CAAG,CAAC,CAAC,UAAA3C,gBAAA,kBAAAC,qBAAA,CAAhCD,gBAAA,CAAkC2E,WAAW,UAAA1E,qBAAA,kBAAAC,sBAAA,CAA7CD,qBAAA,CAAgD0C,WAAW,CAAG,CAAC,CAAC,UAAAzC,sBAAA,iBAAhEA,sBAAA,CAAkE0E,GAAG,GAAI,SAAS,CACnG,KAAM,CAAAC,4BAA4B,CAAIC,QAAgB,EAAsE,CACxH,OAAQA,QAAQ,EACZ,IAAK,UAAU,CACX,MAAO,CAAEC,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,MAAO,CAAC,CAAEC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAE,CAAC,CAClI,IAAK,WAAW,CACZ,MAAO,CAAEF,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,OAAQ,CAAC,CAAEC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAE,CAAC,CAClI,IAAK,aAAa,CACd,MAAO,CAAEF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAC,CAAEC,eAAe,CAAE,CAAEF,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,OAAQ,CAAE,CAAC,CAClI,IAAK,cAAc,CACf,MAAO,CAAEF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAC,CAAEC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAE,CAAC,CACrI,IAAK,eAAe,CAChB,MAAO,CAAEF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAAEC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAC,CACxI,IAAK,YAAY,CACb,MAAO,CAAEF,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,QAAS,CAAC,CAAEC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAC,CACrI,IAAK,aAAa,CACd,MAAO,CAAEF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAC,CAAEC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAE,CAAC,CACrI,IAAK,eAAe,CAChB,MAAO,CAAEF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAAEC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAC,CACxI,IAAK,cAAc,CACf,MAAO,CAAEF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAC,CAAEC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAE,CAAC,CACrI,QACI,MAAO,CAAEF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAAEC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAC,CAC5I,CACJ,CAAC,CACD,KAAM,CAAAE,uBAAuB,CAAG,QAAAA,CAAA,CAAwC,IAAvC,CAAAL,QAAgB,CAAAM,SAAA,CAAAZ,MAAA,IAAAY,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,eAAe,CAC/D;AACA,KAAM,CAAAE,YAAY,CAAG,EAAE,CAAE;AAEzB;AACA,KAAM,CAAAC,SAAS,CAAG,CACdT,QAAQ,CAAE,OAAO,CACjBU,GAAG,CAAE,iBAAiB,CACtBC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,iBAAiB,CACvBC,SAAS,CAAE,MACf,CAAC,CAEA;AACA,OAAQd,QAAQ,EACb,IAAK,UAAU,CACX,MAAO,CACH,GAAGS,SAAS,CACZC,GAAG,CAAE,GAAGF,YAAY,CAAG,EAAE,eAAgB,CACzCK,IAAI,CAAE,GAAGL,YAAY,eACzB,CAAC,CACL,IAAK,YAAY,CACb,MAAO,CACH,GAAGC,SAAS,CACZC,GAAG,CAAE,GAAGF,YAAY,CAAG,EAAE,eAAe,CACxCK,IAAI,CAAE,gBAAgB,CACtBC,SAAS,CAAE,kBACf,CAAC,CACL,IAAK,WAAW,CACZ,MAAO,CACH,GAAGL,SAAS,CACZC,GAAG,CAAE,GAAGF,YAAY,CAAG,EAAE,eAAgB,CACzCG,KAAK,CAAE,GAAGH,YAAY,CAAG,CAAC,IAC9B,CAAC,CACN;AACC,IAAK,aAAa,CACd,MAAO,CACH,GAAGC,SAAS,CACZC,GAAG,CAAE,gBAAgB,CACrBG,IAAI,CAAE,GAAGL,YAAY,eAAe,CACpCM,SAAS,CAAE,kBACf,CAAC,CACL;AACA,IAAK,eAAe,CAChB,MAAO,CACH,GAAGL,SAAS,CACZC,GAAG,CAAE,gBAAgB,CACrBG,IAAI,CAAE,gBAAgB,CACtBC,SAAS,CAAE,uBACf,CAAC,CACN;AACC,IAAK,cAAc,CACf,MAAO,CACH,GAAGL,SAAS,CACZC,GAAG,CAAE,gBAAgB,CACrBC,KAAK,CAAE,GAAGH,YAAY,CAAG,CAAC,IAAI,CAC9BM,SAAS,CAAE,kBACf,CAAC,CACL,IAAK,aAAa,CACd,MAAO,CACH,GAAGL,SAAS,CACZG,MAAM,CAAE,GAAGJ,YAAY,eAAgB,CACvCK,IAAI,CAAE,GAAGL,YAAY,eACzB,CAAC,CACL,IAAK,eAAe,CAChB,MAAO,CACH,GAAGC,SAAS,CACZG,MAAM,CAAE,GAAGJ,YAAY,IAAI,CAC3BK,IAAI,CAAE,gBAAgB,CACtBC,SAAS,CAAE,kBACf,CAAC,CACL,IAAK,cAAc,CACf,MAAO,CACH,GAAGL,SAAS,CACZG,MAAM,CAAE,GAAGJ,YAAY,IAAK,CAC5BG,KAAK,CAAE,GAAGH,YAAY,CAAG,CAAC,IAC9B,CAAC,CACL,QACI,MAAO,CACH,GAAGC,SAAS,CACZC,GAAG,CAAE,gBAAgB,CACrBG,IAAI,CAAE,gBAAgB,CACtBC,SAAS,CAAE,uBACf,CAAC,CACT,CACJ,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGrC,cAAc,SAAdA,cAAc,kBAAArD,sBAAA,CAAdqD,cAAc,CAAEe,SAAS,UAAApE,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4BwC,WAAW,CAAG,CAAC,CAAC,UAAAvC,sBAAA,kBAAAC,sBAAA,CAA5CD,sBAAA,CAA8C0F,OAAO,UAAAzF,sBAAA,iBAArDA,sBAAA,CAAuD0F,gBAAgB,CAChG,KAAM,CAAEhB,YAAY,CAAEG,eAAgB,CAAC,CAAGL,4BAA4B,CAAC,CAAA1B,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE6C,QAAQ,GAAI,eAAe,CAAC,CAErH,KAAM,CAAAC,SAAS,CAAG,CACdC,UAAU,CAAEnD,mBAAmB,SAAnBA,mBAAmB,YAAAzC,qBAAA,CAAnByC,mBAAmB,CAAEoD,cAAc,UAAA7F,qBAAA,WAAnCA,qBAAA,CAAqC8F,IAAI,CAAG,MAAM,CAAG,QAAQ,CACzEC,SAAS,CAAEtD,mBAAmB,SAAnBA,mBAAmB,YAAAxC,sBAAA,CAAnBwC,mBAAmB,CAAEoD,cAAc,UAAA5F,sBAAA,WAAnCA,sBAAA,CAAqC+F,MAAM,CAAG,QAAQ,CAAG,QAAQ,CAC5EC,KAAK,CAAE,CAAAxD,mBAAmB,SAAnBA,mBAAmB,kBAAAvC,sBAAA,CAAnBuC,mBAAmB,CAAEoD,cAAc,UAAA3F,sBAAA,iBAAnCA,sBAAA,CAAqCgG,SAAS,GAAI,SAAS,CAClEC,SAAS,CAAE,CAAA1D,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAE2D,SAAS,GAAI,MACpD,CAAC,CACD,KAAM,CAAAC,KAAK,CAAG7H,QAAQ,CAAC8H,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,KAAK,CAAC,CAEtD,KAAM,CAAAC,SAAS,CAAG,CAAAhE,mBAAmB,SAAnBA,mBAAmB,kBAAAtC,sBAAA,CAAnBsC,mBAAmB,CAAEoD,cAAc,UAAA1F,sBAAA,iBAAnCA,sBAAA,CAAqCuG,UAAU,GAAIf,SAAS,CAACQ,SAAS,CAExF,KAAM,CAAAA,SAAS,CAAGE,KAAK,CACnBI,SAAS,GAAK,MAAM,CAClB,OAAO,CACPA,SAAS,GAAK,OAAO,CACrB,MAAM,CACNA,SAAS,CACXA,SAAS,CAGT,KAAM,CAAAE,UAAU,CAAG,CACfC,SAAS,CAAE,EAAAxG,iBAAA,CAAAsC,eAAe,CAACL,WAAW,CAAG,CAAC,CAAC,UAAAjC,iBAAA,kBAAAC,qBAAA,CAAhCD,iBAAA,CAAkCiE,WAAW,UAAAhE,qBAAA,kBAAAC,sBAAA,CAA7CD,qBAAA,CAAgDgC,WAAW,CAAG,CAAC,CAAC,UAAA/B,sBAAA,iBAAhEA,sBAAA,CAAkEuG,cAAc,GAAI,OAAO,CACtGV,SAAS,CAAE,EAAA5F,iBAAA,CAAAmC,eAAe,CAACL,WAAW,CAAG,CAAC,CAAC,UAAA9B,iBAAA,kBAAAC,qBAAA,CAAhCD,iBAAA,CAAkC8D,WAAW,UAAA7D,qBAAA,kBAAAC,sBAAA,CAA7CD,qBAAA,CAAgD6B,WAAW,CAAG,CAAC,CAAC,UAAA5B,sBAAA,iBAAhEA,sBAAA,CAAkE2F,SAAS,GAAI,QAAQ,CAClGU,SAAS,CAAE1C,QAAQ,EAAI,SAAS,CAChC2C,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,GAAG,EAAAtG,iBAAA,CAAAgC,eAAe,CAACL,WAAW,CAAG,CAAC,CAAC,UAAA3B,iBAAA,kBAAAC,qBAAA,CAAhCD,iBAAA,CAAkC2D,WAAW,UAAA1D,qBAAA,kBAAAC,sBAAA,CAA7CD,qBAAA,CAAgD0B,WAAW,CAAG,CAAC,CAAC,UAAAzB,sBAAA,iBAAhEA,sBAAA,CAAkEqG,aAAa,GAAI,GAAG,IAAI,CACrGC,UAAU,CAAE,EAAArG,iBAAA,CAAA6B,eAAe,CAACL,WAAW,CAAG,CAAC,CAAC,UAAAxB,iBAAA,kBAAAC,qBAAA,CAAhCD,iBAAA,CAAkCwD,WAAW,UAAAvD,qBAAA,kBAAAC,sBAAA,CAA7CD,qBAAA,CAAgDuB,WAAW,CAAG,CAAC,CAAC,UAAAtB,sBAAA,iBAAhEA,sBAAA,CAAkEoG,eAAe,GAAI,SACrG,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAIC,OAAe,EAAK,CAC3C;AACA,MAAO,CAAEC,MAAM,CAAED,OAAO,CAACnI,OAAO,CAAC,qCAAqC,CAAE,CAACqI,KAAK,CAAEC,EAAE,CAAEC,EAAE,CAAEC,EAAE,GAAK,CAC3F,MAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE,CAC7C,CAAC,CAAE,CAAC,CACR,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAG1K,KAAK,CAAC2K,OAAO,CAAC,IAAM,CACvC,GAAI,CAACjF,YAAY,EAAI,CAACkF,KAAK,CAACC,OAAO,CAACnF,YAAY,CAAC,EAAIA,YAAY,CAACuB,MAAM,GAAK,CAAC,CAAE,CAC5E,MAAO,CAAC,CAAC,CACb,CAEA,MAAO,CAAAvB,YAAY,CAACoF,MAAM,CAAC,CAACC,GAAQ,CAAEC,MAAW,GAAK,CAClD,GAAI,CAACA,MAAM,CAAE,MAAO,CAAAD,GAAG,CAE3B,KAAM,CAAAE,WAAW,CAAGD,MAAM,CAACE,WAAW,EAAI,SAAS,CAAE;AACrD,GAAI,CAACH,GAAG,CAACE,WAAW,CAAC,CAAE,CACrBF,GAAG,CAACE,WAAW,CAAC,CAAG,EAAE,CACvB,CACAF,GAAG,CAACE,WAAW,CAAC,CAACE,IAAI,CAACH,MAAM,CAAC,CAC7B,MAAO,CAAAD,GAAG,CACZ,CAAC,CAAE,CAAC,CAAC,CAAC,CACR,CAAC,CAAE,CAACrF,YAAY,CAAC,CAAC,CAErB,KAAM,CAAA0F,WAAW,CAAG,CACnB7D,QAAQ,CAAE,CAAA3B,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE6C,QAAQ,GAAI,eAAe,CACvD4C,YAAY,CAAE,GAAGzF,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAE0F,MAAM,CAAG1F,gBAAgB,CAAC0F,MAAM,CAAG,CAAC,eAAe,CACtFC,WAAW,CAAE,CAAA3F,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE4F,UAAU,GAAI,KAAK,CAClDC,WAAW,CAAE,CAAA7F,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE8F,WAAW,GAAI,aAAa,CAC3DC,WAAW,CAAE,OAAO,CACpBC,eAAe,CAAE,CAAAhG,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEsE,eAAe,GAAI,OAAO,CAC7DJ,KAAK,CAAElE,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAEiG,KAAK,CAAG,GAAGjG,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEiG,KAAK,IAAI,CAAG,OACnE,CAAC,CACD,KAAM,CAAAC,iBAAiB,CAAG,KAAK,CAC/B,KAAM,CAAAC,2BAA2B,CAAG,SAAS,CAC7C,KAAM,CAAAC,OAAgB,CAAGpF,YAAY,CACrC,KAAM,CAAAqF,aAAa,CAAG,EAAAlI,iBAAA,CAAA0B,eAAe,CAAC,CAAC,CAAC,UAAA1B,iBAAA,kBAAAC,qBAAA,CAAlBD,iBAAA,CAAoBqD,WAAW,UAAApD,qBAAA,kBAAAC,sBAAA,CAA/BD,qBAAA,CAAkC,CAAC,CAAC,UAAAC,sBAAA,iBAApCA,sBAAA,CAAsC+F,aAAa,GAAI,MAAM,CACnF,KAAM,CAAAkC,YAAY,CAAG,IAAI,CACzB;AACA,KAAM,CAAAC,cAAc,CAAG,CAAC,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,qBAAA,GAAM,CAC7B;AACA,KAAM,CAAAC,eAAe,CAAGvG,cAAc,SAAdA,cAAc,kBAAAmG,sBAAA,CAAdnG,cAAc,CAAEe,SAAS,UAAAoF,sBAAA,iBAAzBA,sBAAA,CAA4BhH,WAAW,CAAG,CAAC,CAAC,CACpE,KAAM,CAAAqH,aAAa,CAAGxG,cAAc,SAAdA,cAAc,kBAAAoG,sBAAA,CAAdpG,cAAc,CAAEe,SAAS,UAAAqF,sBAAA,iBAAzBA,sBAAA,CAA4B,CAAC,CAAC,CAEpD,GAAI,CAAAG,eAAe,SAAfA,eAAe,kBAAAF,qBAAA,CAAfE,eAAe,CAAEjE,OAAO,UAAA+D,qBAAA,iBAAxBA,qBAAA,CAA0BI,cAAc,IAAK5E,SAAS,CAAE,CAC3D,MAAO,CAAA0E,eAAe,CAACjE,OAAO,CAACmE,cAAc,CAC9C,CACA;AACA,MAAO,CAAAD,aAAa,SAAbA,aAAa,kBAAAF,qBAAA,CAAbE,aAAa,CAAElE,OAAO,UAAAgE,qBAAA,iBAAtBA,qBAAA,CAAwBG,cAAc,GAAI,KAAK,CACvD,CAAC,EAAE,CAAC,CAAE,QAAS,CAAAC,mBAAmBA,CAACxG,cAAmB,CAAE,KAAAyG,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACvD,GAAI3G,cAAc,GAAK,CAAC,CAAE,CACzB,MAAO,MAAM,CACd,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,QAAQ,CAChB,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,aAAa,CACrB,CAAC,IACU,IAAIA,cAAc,GAAK,CAAC,CAAE,CACpC,MAAO,aAAa,CACrB,CAEA,MAAO,CAAAF,cAAc,SAAdA,cAAc,kBAAA2G,sBAAA,CAAd3G,cAAc,CAAEe,SAAS,UAAA4F,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4B,CAAC,CAAC,UAAAC,sBAAA,kBAAAC,sBAAA,CAA9BD,sBAAA,CAAgCtE,OAAO,UAAAuE,sBAAA,iBAAvCA,sBAAA,CAAyCC,gBAAgB,GAAI,MAAM,CAC3E,CACA,KAAM,CAAAC,gBAAgB,CAAGL,mBAAmB,CAACxG,cAAc,CAAC,CAC5D,KAAM,CAAA8G,cAAc,CAAGA,CAAA,GAAM,CACtB,GAAI,CAACd,cAAc,CAAE,MAAO,KAAI,CAEtC,GAAIa,gBAAgB,GAAK,MAAM,CAAE,CAChC,mBACCjM,IAAA,CAACL,aAAa,EACbwM,OAAO,CAAC,MAAM,CACd9G,KAAK,CAAEA,KAAK,CAACa,MAAO,CACpBM,QAAQ,CAAC,QAAQ,CACjB4F,UAAU,CAAE/H,WAAW,CAAG,CAAE,CAC5BgI,EAAE,CAAE,CAAExB,eAAe,CAAE,aAAa,CAAG,+BAA+B,CAAE,CACrDA,eAAe,CAAEnF,aAAe;AAClC,CAAG,CAAE,CACtB4G,UAAU,cAAEtM,IAAA,CAACV,MAAM,EAACiN,KAAK,CAAE,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxDC,UAAU,cAAEzM,IAAA,CAACV,MAAM,EAACiN,KAAK,CAAE,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxD,CAAC,CAEJ,CACM,GAAIP,gBAAgB,GAAK,aAAa,CAAE,CAC7C,mBACajM,IAAA,CAACR,GAAG,EAAC6M,EAAE,CAAE,CAACK,OAAO,CAAE,MAAM,CACpCC,UAAU,CAAE,QAAQ,CACpBC,YAAY,CAAE,QAAQ,CACtBC,GAAG,CAAE,KAAK,CAACC,OAAO,CAAC,KAAK,CAAE,CAAAC,QAAA,CAGVlD,KAAK,CAACmD,IAAI,CAAC,CAAE9G,MAAM,CAAEb,KAAK,CAACa,MAAO,CAAC,CAAC,CAAC+G,GAAG,CAAC,CAACC,CAAC,CAAEC,KAAK,gBACjDnN,IAAA,QAEEuM,KAAK,CAAE,CACLxD,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,KAAK,CACb6B,eAAe,CAAEsC,KAAK,GAAK9I,WAAW,CAAG,CAAC,CAAGqB,aAAa,CAAG3E,SAAS,CAAC2E,aAAa,CAAE,IAAI,CAAC,CAAE;AAC7F4E,YAAY,CAAE,OAChB,CAAE,EANG6C,KAON,CACF,CAAC,CAED,CAAC,CAEpB,CACA,GAAIlB,gBAAgB,GAAK,aAAa,CAAE,CACvC,mBACCjM,IAAA,CAACR,GAAG,EAAC6M,EAAE,CAAE,CAACK,OAAO,CAAE,MAAM,CACzBC,UAAU,CAAE,QAAQ,CACpBC,YAAY,CAAE,YAAY,CAACE,OAAO,CAAC,KACnC,CAAE,CAAAC,QAAA,cACD7M,KAAA,CAACX,UAAU,EAAC8M,EAAE,CAAE,CAACpE,KAAK,CAAEvC,aAAa,CAAE,CAAAqH,QAAA,EAAC,OACpB,CAAC1I,WAAW,CAAC,MAAI,CAACgB,KAAK,CAACa,MAAM,EACtC,CAAC,CACT,CAAC,CAER,CAEA,GAAI+F,gBAAgB,GAAK,QAAQ,CAAE,CAClC,mBACCjM,IAAA,CAACR,GAAG,EAAAuN,QAAA,cACH/M,IAAA,CAACT,UAAU,EAAC4M,OAAO,CAAC,OAAO,CAAAY,QAAA,cAC1B/M,IAAA,CAACP,cAAc,EACd0M,OAAO,CAAC,aAAa,CACrBiB,KAAK,CAAE5I,QAAS,CACK6H,EAAE,CAAE,CACArD,MAAM,CAAE,KAAK,CACrCsB,YAAY,CAAE,MAAM,CACpB+C,MAAM,CAAE,UAAU,CAClBxC,eAAe,CAAE9J,SAAS,CAAC2E,aAAa,CAAE,IAAI,CAAC,CACvB,0BAA0B,CAAE,CAC5BmF,eAAe,CAAEnF,aAAe;AAClC,CAAE,CAAE,CAC3B,CAAC,CACS,CAAC,CACT,CAAC,CAER,CAEA,MAAO,KAAI,CACT,CAAC,CACD;AACA,KAAM,CAAC4H,cAAc,CAAEC,iBAAiB,CAAC,CAAGrO,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAAsO,UAAU,CAAGpO,MAAM,CAAiB,IAAI,CAAC,CACjD,KAAM,CAAAqO,YAAY,CAAGrO,MAAM,CAAM,IAAI,CAAC,CACpC,KAAM,CAAAsO,kBAAkB,CAAIC,MAAW,EAAK,CAC9C,GAAIA,MAAM,CAACC,MAAM,GAAK,UAAU,EAAID,MAAM,CAACC,MAAM,GAAK,MAAM,EAAID,MAAM,CAACC,MAAM,GAAG,SAAS,CAAE,CAC1F,KAAM,CAAAC,SAAS,CAAGF,MAAM,CAACG,SAAS,CAClC,GAAIH,MAAM,CAACI,WAAW,GAAK,UAAU,CAAE,CACtC;AACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAGL,SAAS,CACjC,CAAC,IAAM,CACN;AACAG,MAAM,CAACG,IAAI,CAACN,SAAS,CAAE,QAAQ,CAAE,qBAAqB,CAAC,CACxD,CACD,CAAC,IAAM,CACN,GAAIF,MAAM,CAACC,MAAM,EAAI,UAAU,EAAID,MAAM,CAACC,MAAM,EAAI,UAAU,EAAID,MAAM,CAACI,WAAW,EAAI,UAAU,EAAIJ,MAAM,CAACI,WAAW,EAAI,UAAU,CAAE,CACvI5H,cAAc,CAAC,CAAC,CACjB,CAAC,IAAM,IAAIwH,MAAM,CAACC,MAAM,EAAI,MAAM,EAAID,MAAM,CAACC,MAAM,EAAI,MAAM,EAAID,MAAM,CAACI,WAAW,EAAI,MAAM,EAAIJ,MAAM,CAACI,WAAW,EAAI,MAAM,CAAE,CAC9HhI,cAAc,CAAC,CAAC,CACjB,CAAC,IAAM,IAAI4H,MAAM,CAACC,MAAM,EAAI,SAAS,EAAID,MAAM,CAACI,WAAW,EAAI,SAAS,CAAE,KAAAK,uBAAA,CAAAC,uBAAA,CACzE;AACAlJ,cAAc,CAAC,CAAC,CAAC,CACjB;AACA,GAAID,cAAc,SAAdA,cAAc,YAAAkJ,uBAAA,CAAdlJ,cAAc,CAAEe,SAAS,UAAAmI,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,WAA9BA,uBAAA,CAAgCC,WAAW,CAAE,CAChD,KAAM,CAAAC,gBAAgB,CAAGlO,iBAAiB,CAAC6E,cAAc,CAACe,SAAS,CAAC,CAAC,CAAC,CAACqI,WAAW,CAAC,CACnF,GAAIC,gBAAgB,CAAE,CACrBA,gBAAgB,CAACC,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CACxD,CACD,CACD,CACD,CACD,CAAC,CACD,QAAS,CAAAC,YAAYA,CAACC,SAAiB,CAAE,CACxC,OAAQA,SAAS,EAChB,IAAK,OAAO,CACX,MAAO,YAAY,CACpB,IAAK,KAAK,CACT,MAAO,UAAU,CAClB,IAAK,QAAQ,CACb,QACC,MAAO,QAAQ,CACjB,CACD,CAEG,KAAM,CAAAnI,QAAQ,CAAG,CAAA3B,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE6C,QAAQ,GAAI,eAAe,CAC9D,KAAM,CAAAkH,aAAa,CAAG/H,uBAAuB,CAACL,QAAQ,CAAC,CACvD;AACHrH,SAAS,CAAC,IAAM,CACf,KAAM,CAAA0P,iBAAiB,CAAGA,CAAA,GAAM,CAC/B,GAAIrB,UAAU,CAACsB,OAAO,CAAE,CACvB;AACAtB,UAAU,CAACsB,OAAO,CAACvC,KAAK,CAACvD,MAAM,CAAG,MAAM,CACxC,KAAM,CAAA+F,aAAa,CAAGvB,UAAU,CAACsB,OAAO,CAACE,YAAY,CACrD,KAAM,CAAAC,eAAe,CAAG,GAAG,CAAE;AAC7B,KAAM,CAAAC,YAAY,CAAGH,aAAa,CAAGE,eAAe,CAGpD1B,iBAAiB,CAAC2B,YAAY,CAAC,CAE/B;AACA,GAAIzB,YAAY,CAACqB,OAAO,CAAE,CACzB;AACA,GAAIrB,YAAY,CAACqB,OAAO,CAACK,YAAY,CAAE,CACtC1B,YAAY,CAACqB,OAAO,CAACK,YAAY,CAAC,CAAC,CACpC,CACA;AACAC,UAAU,CAAC,IAAM,CAChB,GAAI3B,YAAY,CAACqB,OAAO,EAAIrB,YAAY,CAACqB,OAAO,CAACK,YAAY,CAAE,CAC9D1B,YAAY,CAACqB,OAAO,CAACK,YAAY,CAAC,CAAC,CACpC,CACD,CAAC,CAAE,EAAE,CAAC,CACP,CACD,CACD,CAAC,CAGDN,iBAAiB,CAAC,CAAC,CAGnB,KAAM,CAAAQ,QAAQ,CAAG,CAChBD,UAAU,CAACP,iBAAiB,CAAE,EAAE,CAAC,CACjCO,UAAU,CAACP,iBAAiB,CAAE,GAAG,CAAC,CAClCO,UAAU,CAACP,iBAAiB,CAAE,GAAG,CAAC,CAClCO,UAAU,CAACP,iBAAiB,CAAE,GAAG,CAAC,CAClC,CAGD,GAAI,CAAAS,cAAqC,CAAG,IAAI,CAChD,GAAI,CAAAC,gBAAyC,CAAG,IAAI,CAEpD,GAAI/B,UAAU,CAACsB,OAAO,EAAId,MAAM,CAACwB,cAAc,CAAE,CAChDF,cAAc,CAAG,GAAI,CAAAE,cAAc,CAAC,IAAM,CACzCJ,UAAU,CAACP,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFS,cAAc,CAACG,OAAO,CAACjC,UAAU,CAACsB,OAAO,CAAC,CAC3C,CAGA,GAAItB,UAAU,CAACsB,OAAO,EAAId,MAAM,CAAC0B,gBAAgB,CAAE,CAClDH,gBAAgB,CAAG,GAAI,CAAAG,gBAAgB,CAAC,IAAM,CAC7CN,UAAU,CAACP,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFU,gBAAgB,CAACE,OAAO,CAACjC,UAAU,CAACsB,OAAO,CAAE,CAC5Ca,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IAAI,CACbC,UAAU,CAAE,IAAI,CAChBC,eAAe,CAAE,CAAC,OAAO,CAAE,OAAO,CACnC,CAAC,CAAC,CACH,CAEA,MAAO,IAAM,CACZT,QAAQ,CAACU,OAAO,CAACC,YAAY,CAAC,CAC9B,GAAIV,cAAc,CAAE,CACnBA,cAAc,CAACW,UAAU,CAAC,CAAC,CAC5B,CACA,GAAIV,gBAAgB,CAAE,CACrBA,gBAAgB,CAACU,UAAU,CAAC,CAAC,CAC9B,CACD,CAAC,CACF,CAAC,CAAE,CAAC5L,WAAW,CAAC,CAAC,CACjB,mBACCnE,KAAA,QAAA6M,QAAA,EACE9H,YAAY,EAAI9B,gBAAgB,GAAG,MAAM,eACzCnD,IAAA,QACCuM,KAAK,CAAE,CACN/F,QAAQ,CAAE,OAAO,CACjBU,GAAG,CAAE,CAAC,CACNG,IAAI,CAAE,CAAC,CACPF,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTyD,eAAe,CAAE,oBAAoB,CACrCqF,MAAM,CAAE,GAAG,CACXC,aAAa,CAAEhN,gBAAgB,GAAK,MAAM,CAAG,MAAM,CAAG,MACvD,CAAE,CACF,CACD,cACDjD,KAAA,CAACb,OAAO,EACP+Q,SAAS,CAAC,yBAAyB,CACnCjC,IAAI,CAAEkC,OAAO,CAAC1M,QAAQ,CAAE,CACxBA,QAAQ,CAAEA,QAAS,CACnBC,OAAO,CAAEmD,SAAU,CACnBN,YAAY,CAAEA,YAAa,CAC3BG,eAAe,CAAEA,eAAgB,CACjCyF,EAAE,CAAE,CACH7F,QAAQ,CAACrD,gBAAgB,GAAG,MAAM,CAAC,qBAAqB,CAAEoE,gBAAgB,EAAItC,YAAY,GAAK,KAAK,CAAG,qBAAqB,CAAG,EAAE,CACjIiL,MAAM,CAAC/M,gBAAgB,GAAG,MAAM,CAAC,iBAAiB,CAAEoE,gBAAgB,EAAItC,YAAY,GAAK,KAAK,CAAG,iBAAiB,CAAG,EAAE,CACvH,gBAAgB,CAAEtB,QAAQ,CAAG,MAAM,CAAG,MAAM,CAC5C,8CAA8C,CAAE,CAC/CuM,MAAM,CAAE,gBAAgB,CACxB;AACA;AACA,GAAG7F,WAAW,CACd,GAAGuE,aAAa,CACEvB,MAAM,CAAE,cAAc,CACxC/F,SAAS,CAAE,GAAGsH,aAAa,CAACtH,SAAS,aAAa,CAElDgJ,QAAQ,CAAE,SACX,CACD,CAAE,CACFC,iBAAiB,CAAE,IAAK,CAAAxD,QAAA,eAExB/M,IAAA,QAAKuM,KAAK,CAAE,CAAEK,YAAY,CAAE,KAAK,CAAEF,OAAO,CAAE,MAAO,CAAE,CAAAK,QAAA,CACnD,CAAAnI,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE4L,aAAa,gBAC9BxQ,IAAA,CAACN,UAAU,EACV2M,EAAE,CAAE,CACH7F,QAAQ,CAAE,OAAO,CACjBiK,SAAS,CAAE,iCAAiC,CAC5CpJ,IAAI,CAAE,MAAM,CACZF,KAAK,CAAE,MAAM,CACbkG,MAAM,CAAE,OAAO,CACfnE,UAAU,CAAE,iBAAiB,CAC7BwH,MAAM,CAAE,gBAAgB,CACxBR,MAAM,CAAE,QAAQ,CAChB5F,YAAY,CAAE,MAAM,CACpBwC,OAAO,CAAE,gBACV,CAAE,CAAAC,QAAA,cAEF/M,IAAA,CAACJ,SAAS,EAACyM,EAAE,CAAE,CAAEsE,IAAI,CAAE,KAAK,CAAE1I,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CACtC,CACZ,CACG,CAAC,cACMjI,IAAA,CAACF,gBAAgB,EAE7B8Q,GAAG,CAAEnD,YAAa,CAClBlB,KAAK,CAAE,CACT3D,SAAS,CAAE,OAAO,CAClB,IAAIwC,cAAc,CACd,CACEyF,mBAAmB,CAAE,GAAGhM,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAE0F,MAAM,CAAG1F,gBAAgB,CAAC0F,MAAM,CAAG,CAAC,IAAI,CAClFuG,oBAAoB,CAAE,GAAGjM,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAE0F,MAAM,CAAG1F,gBAAgB,CAAC0F,MAAM,CAAG,CAAC,IAAI,CACnFwG,sBAAsB,CAAE,KAAK,CAC7BC,uBAAuB,CAAE,KAC3B,CAAC,CACD,CACE1G,YAAY,CAAE,GAAGzF,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAE0F,MAAM,CAAG1F,gBAAgB,CAAC0F,MAAM,CAAG,CAAC,IACzE,CAAC,CACP,CAAE,CACE0G,OAAO,CAAE,CACRC,eAAe,CAAE,CAAC5D,cAAc,CAChC6D,eAAe,CAAE,IAAI,CACrBC,gBAAgB,CAAE,KAAK,CACvBC,WAAW,CAAE,IAAI,CACjBC,kBAAkB,CAAE,EAAE,CACtBC,kBAAkB,CAAE,IAAI,CACxBC,mBAAmB,CAAE,CACtB,CAAE,CAAAzE,QAAA,cAGD7M,KAAA,CAACV,GAAG,EACH+M,KAAK,CAAE,CACNO,OAAO,CAAEjI,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAE4M,OAAO,CAAG,GAAG5M,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE4M,OAAO,IAAI,CAAG,KAAK,CAC7EzI,MAAM,CAAEkC,aACS,CAAE,CACF0F,GAAG,CAAEpD,UAAW,CAAAT,QAAA,eAElC/M,IAAA,CAACR,GAAG,EACHkN,OAAO,CAAC,MAAM,CACdgF,aAAa,CAAC,QAAQ,CACtBC,QAAQ,CAAC,MAAM,CACfC,cAAc,CAAC,QAAQ,CAAA7E,QAAA,CAEtBrI,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEuI,GAAG,CAAC,CAAC4E,SAAc,CAAEC,SAAiB,GACvDD,SAAS,CAACxL,WAAW,CAAC4G,GAAG,CAAC,CAAC8E,SAAc,CAAEC,QAAgB,gBAC1DhS,IAAA,CAACR,GAAG,EAEHyS,SAAS,CAAC,KAAK,CACfC,GAAG,CAAEH,SAAS,CAACI,GAAI,CACnBC,GAAG,CAAEL,SAAS,CAACM,OAAO,EAAI,OAAQ,CAClChG,EAAE,CAAE,CACHzD,SAAS,CAAEiJ,SAAS,CAAChJ,cAAc,EAAIkJ,SAAS,CAAClJ,cAAc,EAAI,OAAO,CAC1EV,SAAS,CAAE0J,SAAS,CAACzJ,SAAS,EAAI,QAAQ,CAC1CU,SAAS,CAAEiJ,SAAS,CAACzL,GAAG,EAAI,SAAS,CACrC4C,UAAU,CAAE6I,SAAS,CAAC5I,eAAe,EAAI,SAAS,CAClDJ,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,GAAG+I,SAAS,CAAC9I,aAAa,EAAI,GAAG,IAAI,CAC7C;AACAoE,MAAM,CAAE,CAAC,CACTP,OAAO,CAAE,CAAC,CACVxC,YAAY,CAAE,GACf,CAAE,CACFgI,OAAO,CAAEA,CAAA,GAAM,CACd,GAAIT,SAAS,CAACU,SAAS,CAAE,CACxB,KAAM,CAAA1E,SAAS,CAAGgE,SAAS,CAACU,SAAS,CACrCvE,MAAM,CAACG,IAAI,CAACN,SAAS,CAAE,QAAQ,CAAE,qBAAqB,CAAC,CACxD,CACD,CAAE,CACFtB,KAAK,CAAE,CAAEiG,MAAM,CAAEX,SAAS,CAACU,SAAS,CAAG,SAAS,CAAG,SAAU,CAAE,EAtB1D,GAAGV,SAAS,CAACY,EAAE,IAAIT,QAAQ,EAuBhC,CACD,CACF,CAAC,CACG,CAAC,CAGLvN,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEwI,GAAG,CACxB,CAACyF,SAAc,CAAEvF,KAAU,QAAAwF,qBAAA,OAC1B,CAAAD,SAAS,CAACE,IAAI,eACb5S,IAAA,CAACT,UAAU,EACkB;AAC5B6Q,SAAS,CAAC,eAAe,CACzB/D,EAAE,CAAE,CACHwG,UAAU,CAAE,UAAU,CACtBC,SAAS,CAAE,YAAY,CACvB3K,SAAS,CACT4K,SAAS,CAAE,CAAC,CACZ9K,KAAK,CAAE,EAAA0K,qBAAA,CAAAD,SAAS,CAAC7K,cAAc,UAAA8K,qBAAA,iBAAxBA,qBAAA,CAA0BzK,SAAS,GAAIP,SAAS,CAACM,KAAK,CAC7D6E,OAAO,CAAC,KACT,CAAE,CACFkG,uBAAuB,CAAE5J,iBAAiB,CAACsJ,SAAS,CAACE,IAAI,CAAG;AAAA,EAVvDF,SAAS,CAACD,EAAE,EAAItF,KAWrB,CACD,EACH,CAAC,CAEA8F,MAAM,CAACC,IAAI,CAACvJ,cAAc,CAAC,CAACsD,GAAG,CAAE/C,WAAW,OAAAiJ,qBAAA,CAAAC,sBAAA,oBAC5CpT,IAAA,CAACR,GAAG,EAEH6M,EAAE,CAAE,CACHK,OAAO,CAAE,MAAM,CACfkF,cAAc,CAAElD,YAAY,EAAAyE,qBAAA,CAACxJ,cAAc,CAACO,WAAW,CAAC,CAAC,CAAC,CAAC,UAAAiJ,qBAAA,iBAA9BA,qBAAA,CAAgC/K,SAAS,CAAC,CACvEuJ,QAAQ,CAAE,MAAM,CAChBtE,MAAM,CAAE,CAAC,CACTxC,eAAe,EAAAuI,sBAAA,CAAEzJ,cAAc,CAACO,WAAW,CAAC,CAAC,CAAC,CAAC,UAAAkJ,sBAAA,iBAA9BA,sBAAA,CAAgCjK,eAAe,CAC9D2D,OAAO,CAAE,MACZ,CAAE,CAAAC,QAAA,CAEDpD,cAAc,CAACO,WAAW,CAAC,CAAC+C,GAAG,CAAC,CAAChD,MAAW,CAAEkD,KAAa,QAAAkG,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAC3D1T,IAAA,CAACV,MAAM,EAENgT,OAAO,CAAEA,CAAA,GAAM5E,kBAAkB,CAACzD,MAAM,CAAC0J,YAAY,CAAE,CACvDxH,OAAO,CAAC,WAAW,CACnBE,EAAE,CAAE,CACHuH,WAAW,CAAE,MAAM,CACnBvG,MAAM,CAAE,OAAO,CACfxC,eAAe,CAAE,EAAAwI,qBAAA,CAAApJ,MAAM,CAAC4J,gBAAgB,UAAAR,qBAAA,iBAAvBA,qBAAA,CAAyBS,qBAAqB,GAAI,SAAS,CAC5E7L,KAAK,CAAE,EAAAqL,sBAAA,CAAArJ,MAAM,CAAC4J,gBAAgB,UAAAP,sBAAA,iBAAvBA,sBAAA,CAAyBS,eAAe,GAAI,MAAM,CACzDrD,MAAM,CAAE,cAAA6C,sBAAA,CAAatJ,MAAM,CAAC4J,gBAAgB,UAAAN,sBAAA,iBAAvBA,sBAAA,CAAyBS,iBAAiB,EAAE,EAAI,aAAa,CAClFC,QAAQ,CAAE,EAAAT,sBAAA,CAAAvJ,MAAM,CAAC4J,gBAAgB,UAAAL,sBAAA,iBAAvBA,sBAAA,CAAyBU,QAAQ,GAAI,MAAM,CACrDnL,KAAK,CAAE,EAAA0K,sBAAA,CAAAxJ,MAAM,CAAC4J,gBAAgB,UAAAJ,sBAAA,iBAAvBA,sBAAA,CAAyB3I,KAAK,GAAI,MAAM,CAC/C;AACA;AACA;AACA;AACAqJ,aAAa,CAAE,MAAM,CACrB7J,YAAY,CAAE,EAAAoJ,sBAAA,CAAAzJ,MAAM,CAAC4J,gBAAgB,UAAAH,sBAAA,iBAAvBA,sBAAA,CAAyBU,YAAY,GAAI,KAAK,CAC5DtH,OAAO,CAAE,kCAAkC,CACVuH,UAAU,CAAE,qCAAqC,CACjD5D,SAAS,CAAE,iBAC7C,CAAE,CAAA1D,QAAA,CAED9C,MAAM,CAACqK,UAAU,EAtBbnH,KAuBE,CAAC,EACT,CAAC,EApCGjD,WAqCD,CAAC,EACN,CAAC,EACE,CAAC,EAnIF,aAAaoD,cAAc,EAqIH,CAAC,cAClBtN,IAAA,QAAA+M,QAAA,CAESzI,UAAU,EAAI,CAAC,EAAI8G,cAAc,cAAGpL,IAAA,CAAAI,SAAA,EAAA2M,QAAA,CAAGb,cAAc,CAAC,CAAC,CAAG,CAAC,CAAG,IAAI,CAClE,CAAC,EACd,CAAC,EACN,CAAC,CAER,CAAC,CAED,cAAe,CAAA1K,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}